package com.swcares.aiot.service.impl;

import com.swcares.aiot.business.flight.service.impl.BaseFlightInfoServiceImpl;
import com.swcares.baseframe.common.core.tenant.TenantContextHolder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * ClassName：com.swcares.aiot.service.impl.BaseFlightInfoServiceImplTest <br>
 * Description： <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> 扬 <br>
 * date 2024/10/17 10:16 <br>
 * @version V <br>
 */
@SpringBootTest
class BaseFlightInfoServiceImplTest {

    @Resource
    private BaseFlightInfoServiceImpl baseFlightInfoService;

    @Test
    void cleaningFlightData() {
        TenantContextHolder.setTenant(1503987820729229312L);
        baseFlightInfoService.cleaningFlightData(LocalDate.of(2023,10,2), LocalDate.of(2024,11,2));
        Integer b = 1;
        Assertions.assertEquals(1, b);
    }
}