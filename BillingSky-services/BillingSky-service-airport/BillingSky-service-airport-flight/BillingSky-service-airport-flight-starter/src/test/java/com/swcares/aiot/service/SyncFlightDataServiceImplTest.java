package com.swcares.aiot.service;

import com.swcares.aiot.business.base.service.impl.BaseAircraftSafeGuardGenerateServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.aiot.service.SyncFlightDataServiceImplTest
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR> 扬
 * date 2024/3/30 14:11
 * @version V
 */
@SpringBootTest
class SyncFlightDataServiceImplTest {

    @Resource
    private BaseAircraftSafeGuardGenerateServiceImpl syncFlightDataService;
    @Test
    void sendGenerateNodeEvent() {
        syncFlightDataService.sendGenerateNodeEvent(1000L);
    }
}