package com.swcares.aiot.business.flight.service.impl;

import com.swcares.baseframe.common.tenant.TenantHolder;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.aiot.business.flight.service.impl.FlightInfoUmeBizServiceImplTest <br>
 * Description： <br>
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> 扬 <br>
 * date 2025/7/8 00:13 <br>
 * @version V <br>
 */
@SpringBootTest
class FlightInfoUmeBizServiceImplTest {
    @Resource
    private FlightInfoUmeBizServiceImpl flightInfoUmeBizService;

    @Test
    void syncDataToBaseFlightInfoTest() {
        // 宜宾
        TenantHolder.setTenant(1503987820729229312L);
        try {
            boolean isSu = flightInfoUmeBizService.singleTenantIncrementSync();
            Assert.assertEquals(Boolean.TRUE, isSu);
        } finally {
            TenantHolder.clear();
        }


    }

}