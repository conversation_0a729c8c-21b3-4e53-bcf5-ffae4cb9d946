package com.swcares.aiot.business.parse.service.impl;

import com.swcares.aiot.datacenter.core.enums.EnumDataSource;
import com.swcares.aiot.datacenter.core.enums.EnumXmlType;
import com.swcares.aiot.datacenter.service.ParseDataImplService;
import com.swcares.aiot.datacenter.core.model.dto.ParseDataRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import javax.annotation.Resource;

/**
 * ClassName：com.swcares.aiot.business.parse.service.impl.PaseDataBizServiceImplTest <br>
 * Description： <br>
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> 扬 <br>
 * date 2025/7/1 11:27 <br>
 * @version V <br>
 */
@SpringBootTest
@Slf4j
class PaseDataBizServiceImplTest {

    @Resource
    private ParseDataImplService parseDataImplService;

    @Test
    void parseData() {
        String xmlData = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<DATA>\n" +
                "    <META>\n" +
                "        <ORIGINSYSTEM>GMS</ORIGINSYSTEM>\n" +
                "        <DESTINATIONSYSTEM>FINANCE</DESTINATIONSYSTEM>\n" +
                "        <SEQUENCENUMBER>20250819100929000986</SEQUENCENUMBER>\n" +
                "        <SENDTIME>2025-08-19T10:09:29.986</SENDTIME>\n" +
                "        <MESSAGETYPE>FLIGHTSERVICE</MESSAGETYPE>\n" +
                "        <MESSAGESUBTYPE>REQUESTFINANCE</MESSAGESUBTYPE>\n" +
                "    </META>\n" +
                "    <FLIGHTSERVICE>\n" +
                "        <FLIGHTKEY>\n" +
                "            <ID>8a69cc099885bdd30198bcb1812e2b13</ID>\n" +
                "            <FLIGHTID>ce48049540cd49b5b95f14d1d41b3f1b</FLIGHTID>\n" +
                "            <AIRPORT>ZSFZ</AIRPORT>\n" +
                "            <IATACARRIER>MU</IATACARRIER>\n" +
                "            <CARRIER>CES</CARRIER>\n" +
                "            <FLIGHT>5505</FLIGHT>\n" +
                "            <REGISTRATION>B308Z</REGISTRATION>\n" +
                "            <FLIGHTTASK>W/Z</FLIGHTTASK>\n" +
                "            <DIRECTION>A</DIRECTION>\n" +
                "            <ORIGIN>ZSPD</ORIGIN>\n" +
                "            <DESTINATION>ZSFZ</DESTINATION>\n" +
                "            <IATAFLIGHTROUTE>PVG-FOC</IATAFLIGHTROUTE>\n" +
                "            <FLIGHTROUTECN>浦东-福州</FLIGHTROUTECN>\n" +
                "            <FLIGHTSTATUS>ARR</FLIGHTSTATUS>\n" +
                "            <FLIGHTNATURE>D</FLIGHTNATURE>\n" +
                "            <SCHEDULEDEPTIME>2025-08-19T08:35:00</SCHEDULEDEPTIME>\n" +
                "            <ACTUALDEPTIME>2025-08-19T08:47:00</ACTUALDEPTIME>\n" +
                "            <ACTUALARRTIME>2025-08-19T09:57:00</ACTUALARRTIME>\n" +
                "            <STANDTYPE>1</STANDTYPE>\n" +
                "            <FLIGHTTYPE>Z</FLIGHTTYPE>\n" +
                "            <STAND>27</STAND>\n" +
                "        </FLIGHTKEY>\n" +
                "        <SERVICEBILL>\n" +
                "            <SERVICEITEM>\n" +
                "                <DESCRIPTION>登机桥</DESCRIPTION>\n" +
                "                <CODE>BRIDGE</CODE>\n" +
                "                <BEGINTIME>2025-08-19T10:09:00</BEGINTIME>\n" +
                "                <ENDTIME></ENDTIME>\n" +
                "                <NUM>1</NUM>\n" +
                "                <NOUSEREASON></NOUSEREASON>\n" +
                "                <ISPARK></ISPARK>\n" +
                "                <FLIGHTTASKS>\n" +
                "                    <TASKINFO>\n" +
                "                        <ID></ID>\n" +
                "                        <DESCRIPTION>登机桥</DESCRIPTION>\n" +
                "                        <ACTUALBEGINTIME>2025-08-19T10:09:00</ACTUALBEGINTIME>\n" +
                "                        <ACTUALENDTIME></ACTUALENDTIME>\n" +
                "                        <EMPLOYEEID></EMPLOYEEID>\n" +
                "                        <DEVICEID></DEVICEID>\n" +
                "                    </TASKINFO>\n" +
                "                </FLIGHTTASKS>\n" +
                "            </SERVICEITEM>\n" +
                "        </SERVICEBILL>\n" +
                "    </FLIGHTSERVICE>\n" +
                "</DATA>";
        ParseDataRequestDTO parseDataRequestDTO = new ParseDataRequestDTO();
        parseDataRequestDTO.setXmlData(xmlData);
        parseDataRequestDTO.setId(1L);
        parseDataRequestDTO.setIcaoCode("ZSFZ");
        parseDataRequestDTO.setSourceType(EnumDataSource.FOC_SIGN);
        parseDataRequestDTO.setXmlType(EnumXmlType.FLIGHT_AND_SIGN);
        Boolean isSuccess = parseDataImplService.parseData(parseDataRequestDTO);
        Assert.assertEquals(Boolean.TRUE, isSuccess);
    }

}