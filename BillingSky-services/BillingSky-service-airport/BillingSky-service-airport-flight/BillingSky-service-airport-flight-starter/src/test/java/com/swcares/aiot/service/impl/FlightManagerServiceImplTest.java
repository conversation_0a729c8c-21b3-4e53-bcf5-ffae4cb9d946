package com.swcares.aiot.service.impl;

import com.swcares.aiot.core.dto.ExternalProdSysImportDTO;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Set;

/**
 * ClassName：com.swcares.aiot.service.impl.FlightManagerServiceImplTest <br>
 * Description： <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> 扬 <br>
 * date 2024/11/12 10:12 <br>
 * @version V <br>
 */
@Slf4j
class FlightManagerServiceImplTest {

    @Test
    void checkNoDbRela() {
        ExternalProdSysImportDTO externalProdSysImportDTO = new ExternalProdSysImportDTO();
        externalProdSysImportDTO.setFlightDate(LocalDate.now());
        externalProdSysImportDTO.setAirlineCode("3U");
        externalProdSysImportDTO.setAirportCode("YBPC");
        externalProdSysImportDTO.setProvinceCode(51);
        externalProdSysImportDTO.setFlightNo("CA1112");
        externalProdSysImportDTO.setAircraftModel("B1103");
        externalProdSysImportDTO.setFlightType("D");
        externalProdSysImportDTO.setAirline("CTU-PEK");
        externalProdSysImportDTO.setFlightLineAlias("贵-蓉-重");
        externalProdSysImportDTO.setFlightLineType("D");
        externalProdSysImportDTO.setFlightSegment("CTU-YBP");
        externalProdSysImportDTO.setFlightSegmentProperty("D");
        externalProdSysImportDTO.setMaxPayload(100);
        externalProdSysImportDTO.setMaxSeat(10);
        externalProdSysImportDTO.setQuotaPayload(20);
        externalProdSysImportDTO.setQuotaSeat(10);
        externalProdSysImportDTO.setAvailablePayload(30);
        externalProdSysImportDTO.setAvailableSeat(50);
        externalProdSysImportDTO.setIsArrv("I");
        externalProdSysImportDTO.setFlightFrequency(1);
        externalProdSysImportDTO.setFlightTime(LocalTime.MIN);
        externalProdSysImportDTO.setAdultNum(100);
        externalProdSysImportDTO.setChildNum(2);
        externalProdSysImportDTO.setInfantNum(2);
        externalProdSysImportDTO.setTransitAdultNum(1);
        externalProdSysImportDTO.setTransitChildNum(0);
        externalProdSysImportDTO.setTransitInfantNum(0);
        externalProdSysImportDTO.setBag(10);
        externalProdSysImportDTO.setMail(100);
        externalProdSysImportDTO.setCargo(10);
        externalProdSysImportDTO.setDiscriminateCode("10");
        externalProdSysImportDTO.setModifyFlag("1");
        externalProdSysImportDTO.setAirportFlag("1");
        externalProdSysImportDTO.setAircraftNo("B1001");
        externalProdSysImportDTO.setDiplomaticPassportTravelerNum(10);
        externalProdSysImportDTO.setBagNum(10);
        externalProdSysImportDTO.setIsNear(1);
        externalProdSysImportDTO.setStatus("1");
        externalProdSysImportDTO.setPlanFlightDate(LocalDate.now());

        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<ExternalProdSysImportDTO>> violations = validator.validate(externalProdSysImportDTO);
            for (ConstraintViolation<ExternalProdSysImportDTO> violation : violations) {
                String errorMsg = ObjectUtils.isEmpty(externalProdSysImportDTO.getErrorReason()) ? "" : externalProdSysImportDTO.getErrorReason();
                externalProdSysImportDTO.setErrorReason(errorMsg.concat(violation.getMessage()).concat(";"));
            }
            log.error(externalProdSysImportDTO.getErrorReason());
            Assertions.assertTrue(ObjectUtils.isNotEmpty(externalProdSysImportDTO.getErrorReason()), "有错误信息");

        }
    }
}