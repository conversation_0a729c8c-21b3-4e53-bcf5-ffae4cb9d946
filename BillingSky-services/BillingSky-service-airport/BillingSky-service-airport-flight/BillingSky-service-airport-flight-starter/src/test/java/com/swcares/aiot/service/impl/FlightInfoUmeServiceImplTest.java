package com.swcares.aiot.service.impl;

import com.swcares.aiot.business.flight.service.impl.FlightInfoUmeBizServiceImpl;
import com.swcares.baseframe.common.core.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * ClassName：com.swcares.aiot.service.impl.FlightInfoUmeServiceImplTest <br>
 * Description： <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> 扬 <br>
 * date 2024/5/8 21:41 <br>
 * @since V1.0 <br>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
class FlightInfoUmeServiceImplTest {

    @Resource
    private FlightInfoUmeBizServiceImpl flightInfoUmeBizService;

    /**
     * Title：getCollectFlightDataFromUmeByAirportCode <br>
     * Description: 进行ume航班数据采集测试 <br>
     * <AUTHOR> <br>
     * date 2024/5/24 <br>
     *
     *  <br>
     */
    @Test
    void getCollectFlightDataFromUmeByAirportCode() {
        LocalDate localDate = LocalDate.now();
        LocalDate startLocalDate = localDate.minusDays(9);
        LocalDate endLocalDate = localDate.minusDays(7);
        long between = ChronoUnit.DAYS.between(startLocalDate, endLocalDate);
        List<LocalDate> dateList = LongStream.range(0, between).mapToObj(startLocalDate::plusDays).collect(Collectors.toList());
        for (LocalDate date : dateList) {
            Date queryDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
            flightInfoUmeBizService.getCollectFlightDataFromUmeByAirportCode("XUZ", queryDate);
        }
        Assertions.assertTrue(true);
    }

    @Test
    void getFlightDataFromUmeByAirportCode() {
        try {
            flightInfoUmeBizService.pullFlightDataFromUmeByAirportCode("YBP", new Date());
        } catch (Exception e) {
            log.error("测试宜宾机场采集ume的航班数据失败", e);
        }
    }

    @Test
    void testCollectFlightData() {
        // 宜宾、绵阳、泸州、（徐州待定）
        flightInfoUmeBizService.collectFlightData("MIG", "2025-01-16", false);
        flightInfoUmeBizService.collectFlightData("MIG", "2025-01-17", false);
        flightInfoUmeBizService.collectFlightData("MIG", "2025-01-18", false);
        flightInfoUmeBizService.collectFlightData("MIG", "2025-01-19", false);
        // 需要改后面方法的租户id，(Long aa = Long.valueOf("1503987820729229312");TenantHolder.setTenant(aa);List<FlightInfoUme> dbFlightDataList =  new ArrayList<>();)
        Assertions.assertTrue(true);
    }

    @Test
    void testIncrementSyncOfSingleTenant() {
        TenantContextHolder.setTenant(1503987820729229312L);
        boolean b = flightInfoUmeBizService.singleTenantIncrementSync();
        Assertions.assertTrue(b);
    }
}