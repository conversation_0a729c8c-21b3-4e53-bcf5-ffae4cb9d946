# 管理操作日志表
FlightParkingManagerOperationLogDTO.fkFlightManager = 机位管理id
FlightParkingManagerOperationLogDTO.operationName = 操作名称;0代表新增，1代表编辑
FlightParkingManagerOperationLogDTO.operationContent = 操作内容
# 机位停用管理表
FlightParkingManagerDTO.fkFlightParking = 机位id
FlightParkingManagerDTO.stopStartTime = 停用开始时间
FlightParkingManagerDTO.stopEndTime = 停用结束时间
FlightParkingManagerDTO.stopReason = 停用原因
# 机位表
FlightParkingDTO.airportCode = 机场三字码
FlightParkingDTO.aircraftNumber = 机位号
FlightParkingDTO.fkAirportTerminal = 航站楼id
FlightParkingDTO.fkAircraftType = 机位类型
FlightParkingDTO.parkingProperty = 机位属性
FlightParkingDTO.isCorridorBridge = 是否廊桥机位;0代表是，1代表否
FlightParkingDTO.isPowerSupply = 有无机位电源;0代表是，1代表否
FlightParkingDTO.isAirSource = 有无机位气源;0代表是，1代表否
FlightParkingDTO.isAirConditioner = 有无空调空调;0代表是，1代表否
FlightParkingDTO.isIsolationStand = 是否隔离机位;0代表是，1代表否
FlightParkingDTO.longitude = 机位经度
FlightParkingDTO.latitude = 机位纬度
FlightParkingDTO.fkCorridorBridge = 廊桥id
FlightParkingDTO.fkBaggageTurntable = 行李转盘id
FlightParkingDTO.fkBoardingGate = 登机口id
FlightParkingDTO.status = 机位状态:0代表未占用，1已占用，2停用;0代表未占用，1已占用，2停用
# 机位分配表
FlightParkingRecordDTO.fkFlightNo = 航班id
FlightParkingRecordDTO.flightParking = 机位号
FlightParkingRecordDTO.distributeBy = 分配人
FlightParkingRecordDTO.distributeTime = 分配时间