# 移动端自升级信息
MobileTerminalAutoUpdateVersionsDTO.appId = appId
MobileTerminalAutoUpdateVersionsDTO.versionCode = 版本编码
MobileTerminalAutoUpdateVersionsDTO.operatingSystem = 操作系统
MobileTerminalAutoUpdateVersionsDTO.versionName = 版本名称
MobileTerminalAutoUpdateVersionsDTO.url = 包地址
MobileTerminalAutoUpdateVersionsDTO.isForceUpdate = 是否强制更新
MobileTerminalAutoUpdateVersionsDTO.appMsg = 版本发布信息
# （永久）过夜保障航班
FixStayOvernightSafeguardsFlightDTO.arrivalFlightNo = 进港航班号
FixStayOvernightSafeguardsFlightDTO.departureFlightNo = 出港航班号
FixStayOvernightSafeguardsFlightDTO.airLine = 航线

# 椋炴満淇℃伅琛�
AircraftInformationDTO.aircraftNum = 飞号
AircraftInformationDTO.aircraftType = 机型
AircraftInformationDTO.aircraftTypeName = 机型名称
AircraftInformationDTO.airline = 航空公司
AircraftInformationDTO.maxSeat = 最大座位
AircraftInformationDTO.optionalSeat = 可供座位
AircraftInformationDTO.maxIndustryLoad = 最大业载
AircraftInformationDTO.optionalIndustryLoad = 可供业载
AircraftInformationDTO.maxTakeoffWeight = 最大起飞全重
AircraftInformationDTO.aircraftProperty = 飞机属性
AircraftInformationDTO.wideOrNarrow = 飞机属性
AircraftInformationDTO.nation = 国家
AircraftInformationDTO.domesticAndAbroad = 国内外标记
AircraftInformationDTO.effectiveness = 有效性
AircraftInformationDTO.effectiveDate = 生效日期
AircraftInformationDTO.expiryDate = 失效日期
AircraftInformationDTO.usageUnit = 使用单位

# 航班信息管理
FlightInfoManagerDTO.id=主键
FlightInfoManagerDTO.flightNo=航班号
FlightInfoManagerDTO.flightDateTime=航班日期
FlightInfoManagerDTO.isArrv=进出港标识

# 航班货邮行数据表
BaseFlightCargoDTO.flightDate = 航班日期
BaseFlightCargoDTO.flightNo = 航班号
BaseFlightCargoDTO.flightSegment = 航段
BaseFlightCargoDTO.baseFlightId = 航班id
BaseFlightCargoDTO.cargo = 本站货物重量
BaseFlightCargoDTO.mail = 本站邮件重量
BaseFlightCargoDTO.bag = 本站行李重量
BaseFlightCargoDTO.bagNum = 本站行李件数
BaseFlightCargoDTO.tCargo = 过站货物重量
BaseFlightCargoDTO.tMail = 过站邮件重量
BaseFlightCargoDTO.tBag = 过站行李重量
BaseFlightCargoDTO.tBagNum = 过站行李件数
BaseFlightCargoDTO.cmbThroughput = 货邮吞吐量
BaseFlightCargoDTO.loadFactor = 载运率
BaseFlightCargoDTO.cmbTotalKg = 货邮行总重量
BaseFlightCargoDTO.cargoFlag = 本站货物重量指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.mailFlag = 本站邮件重量指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.bagFlag = 本站行李重量指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.bagNumFlag = 本站行李件数指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.tCargoFlag = 过站货物重量指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.tMailFlag = 过站邮件重量指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.tBagFlag = 过站行李重量指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.tBagNumFlag = 过站行李件数指令采集数据标识(1:一致,0:不一致)
BaseFlightCargoDTO.airportCode = 机场三字码
# 航班旅客数据表
BaseFlightTravelerDTO.flightDate = 航班日期
BaseFlightTravelerDTO.flightNo = 航班号
BaseFlightTravelerDTO.flightSegment = 航段
BaseFlightTravelerDTO.baseFlightId = 航班id
BaseFlightTravelerDTO.cClassNum = 商务舱人数
BaseFlightTravelerDTO.fClassNum = 头等舱人数
BaseFlightTravelerDTO.yClassNum = 经济舱人数
BaseFlightTravelerDTO.diplomaticPassportTravelerNum = 持外交护照旅客人数
BaseFlightTravelerDTO.cardholdersTravelerNum = 持卡旅客人数
BaseFlightTravelerDTO.cardholdersAccompanyTravelerNum = 持卡随行旅客人数
BaseFlightTravelerDTO.importantTravelerNum = 重要旅客人数
BaseFlightTravelerDTO.importantAccompanyTravelerNum = 重要随行旅客人数
BaseFlightTravelerDTO.adultNum = 本站成人数
BaseFlightTravelerDTO.childNum = 本站儿童数
BaseFlightTravelerDTO.infantNum = 本站婴儿数
BaseFlightTravelerDTO.tAdultNum = 过站成人数
BaseFlightTravelerDTO.tChildNum = 过站儿童数
BaseFlightTravelerDTO.tInfantNum = 过站婴儿数
BaseFlightTravelerDTO.adultFlag = 本站成人指令采集数据标识(1:一致,0:不一致)
BaseFlightTravelerDTO.childFlag = 本站儿童指令采集数据标识(1:一致,0:不一致)
BaseFlightTravelerDTO.infantFlag = 本站婴儿指令采集数据标识(1:一致,0:不一致)
BaseFlightTravelerDTO.tAdultFlag = 过站成人指令采集数据标识(1:一致,0:不一致)
BaseFlightTravelerDTO.tChildFlag = 过站儿童指令采集数据标识(1:一致,0:不一致)
BaseFlightTravelerDTO.tInfantFlag = 过站婴儿指令采集数据标识(1:一致,0:不一致)
BaseFlightTravelerDTO.psgrPlf = 客座率
BaseFlightTravelerDTO.travelerThroughput = 旅客吞吐量
BaseFlightTravelerDTO.travelerNum = 旅客总人数
BaseFlightTravelerDTO.psgrTotalKg = 旅客总重量
BaseFlightTravelerDTO.airportCode = 机场三字码

# 数据源管理表
SysDataSourceManageDTO.airportCode = 机场三字码
SysDataSourceManageDTO.dataSource = 数据源（1：ATC;2:ACDM）
SysDataSourceManageDTO.startTime = 生效时间
SysDataSourceManageDTO.endTime = 失效时间

# 同步配置设置
DataSettingDTO.effectiveCommencementDate = 生效开始时间

# 航班删除传输对象
BaseFlightDeleteDTO.id = id

#航班奋勇查询
FlightInfoManagerPageDTO.startTime= 航班查询开始日期
FlightInfoManagerPageDTO.endTime= 航班查询结束日期