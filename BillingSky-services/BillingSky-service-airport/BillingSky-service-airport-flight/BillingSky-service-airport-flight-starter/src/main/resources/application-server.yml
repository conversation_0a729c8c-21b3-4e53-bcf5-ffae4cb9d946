server:
  port: ${AIRPORT_FLIGHT_SERVER_PORT}
  servlet:
    context-path: /${spring.application.name}
  tomcat:
    connection-timeout: 15000ms
    threads:
      max: 600
    max-connections: 200
    accept-count: 200
  # 增加undertow的线程数
  undertow:
    threads:
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程
      worker: 64
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接
      io: 16
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true