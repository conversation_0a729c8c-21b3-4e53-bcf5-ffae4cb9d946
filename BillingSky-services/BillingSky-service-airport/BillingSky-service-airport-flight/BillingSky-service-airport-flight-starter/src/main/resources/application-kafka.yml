spring:
  cloud:
    stream:
      # 配置kafka的地址
      kafka:
        binder:
          brokers: ${BROKERS}
          replication-factor: ${REPLICATION_FACTOR}
          auto-add-partitions: true
      default-binder: kafka
      # 新版配置
      bindings:
        output:
          destination: ${STREAM_EXCHANGE_FLIGHT}
          content-type: "application/json"
          producer:
            partition-count: 3
            partition-key-extractor-name: flightPartitionKeyExtractor
            partition-selector-name: flightPartitionSelector