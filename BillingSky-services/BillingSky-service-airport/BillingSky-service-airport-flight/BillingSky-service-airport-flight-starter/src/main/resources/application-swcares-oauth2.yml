swcares:
  oauth2:
    auth-server-enable: false
    resource-server-enable: true
    # 资源服务器获取token信息方式：remote/redis
    token-store-type: redis
    token-service-type: redis
    free-access-path: /actuator/**,/doc.html,/swagger*,/swagger*/**,/v3/**,/v2/**,/login/**,/webjars/**,/mobile/update/versions/getMaxVersionInfo,/mobile/download/redirect/**,/3p/api/receive,/3p/flightInfo/query/**,/flightData/api/receiveFlightDataCheck,/ade/**,/pushData/**,/fileUploadChainBiz/**,/luggageOrderParseBiz/**
    login-access-path: /**
    client-id: flight-client
    client-secret: 123456
    token-info-uri: ${UC_ADDRESS}/oauth/check_token
    oauth-token-uri: ${UC_ADDRESS}/oauth/token

