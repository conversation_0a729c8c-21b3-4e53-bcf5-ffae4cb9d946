    #输出项目路径，不填自动获取当前目录
project.path=/Users/<USER>/project/javaproject/bls/BillingSky/BillingSky-services/BillingSky-service-user-center/BillingSky-service-user-center-starter
#project.path=G:/work-zy/work/projects/ybp/pss-service/system-code-generator
#类注释里的作者
project.author=code-generator
#父级包路径-----最终会在(project.root.package + project.module.name)下创建controller/dto/entity/mapper/service/vo文件夹
project.root.package=com.swcares.aiot.usercenter.business
#模块名
project.module.name=dc
#表名，多个用英文逗号分隔
project.tables=dc_flight_info
#数据源配置
datasource.driver-class-name=com.mysql.cj.jdbc.Driver
datasource.url= ***********************************************************************************************************************************************************************************
datasource.username= root
datasource.password= Aiotsw@test

