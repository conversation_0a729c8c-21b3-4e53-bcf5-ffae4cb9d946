<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain id="pageDcSignature">
        SER(
        // 分页查询
        CmpDcSignaturePage
        );
    </chain>

    <chain id="pushSignData">
        SER(
        // 1. 获取租户列表
        CmpGetTenantData,
        // 2. 发送签单数据
        CmpSendSignData
        );
    </chain>

    <chain id="pushSignItemDict">
        SER(
        // 1. 发送保障字典表数据
        CmpPushSignItemDict
        );
    </chain>

    <chain id="upUpdatedTimeById">
        SER(
        // 1. 根据id修改更新时间
        CmpUpUpdatedTimeById
        );
    </chain>

    <chain id="deleteBySourceIdAndCode">
        SER(
        // 1. 根据（签单来源id与签单项编码）删除签单数据
        CmpDeleteBySourceIdAndCode
        );
    </chain>

</flow>