<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.parking.mapper.FlightParkingManagerMapper">


    <select id="page" resultType="com.swcares.aiot.vo.FlightParkingManagerVO">
        select a.*,b.aircraft_number
        from asa_flight_parking_manager a left join
        asa_flight_parking b
        on a.fk_flight_parking = b.id
        <where>
            a.fk_flight_parking = #{dto.fkFlightParking}
            and a.deleted = 0
            and b.deleted = 0
        </where>
        order by updated_time
    </select>

    <select id="list" resultType="com.swcares.aiot.vo.FlightParkingManagerVO">
        select a.stop_start_time,a.stop_end_time,a.stop_reason,a.updated_time,a.created_by,b.aircraft_number from asa_flight_parking_manager a left join
            asa_flight_parking b
            on a.fk_flight_parking = b.id
        <where>
            b.aircraft_number = #{aircraft_number}
            and a.deleted = 0
            and b.deleted = 0
        </where>
    </select>

    <select id="listByFkFlightParking" resultType="com.swcares.aiot.vo.FlightParkingManagerVO">
        select * from asa_flight_parking_manager
        where fk_flight_parking = #{fkFlightParking} and deleted = 0
        <choose>
            <when test="gtOrLt">
                and stop_start_time &lt;= #{selectTime}
                order by stop_start_time desc
            </when>
            <otherwise>
                and stop_end_time &gt;= #{selectTime}
                order by stop_end_time
            </otherwise>
        </choose>
    </select>

    <select id="listByFkFlightParkingCondition" resultType="com.swcares.aiot.vo.FlightParkingManagerVO">
        select * from asa_flight_parking_manager
        where fk_flight_parking = #{fkFlightParking} and deleted = 0
        <choose>
            <when test="gtOrLt">
                and stop_start_time &gt;= #{selectTime}
                order by stop_start_time desc
            </when>
            <otherwise>
                and stop_end_time &gt;= #{selectTime}
                order by stop_end_time
            </otherwise>
        </choose>
    </select>

</mapper>
