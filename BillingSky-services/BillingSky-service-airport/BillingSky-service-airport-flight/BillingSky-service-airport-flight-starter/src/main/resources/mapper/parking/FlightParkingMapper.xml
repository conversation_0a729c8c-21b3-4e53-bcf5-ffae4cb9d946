<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.parking.mapper.FlightParkingMapper">
    <update id="removeFlightParking">
        update asa_flight_parking set deleted = 1 where id = #{id}
    </update>
    <update id="updateByFlight">
        update asa_flight_parking set fk_corridor_bridge = #{f.fkCorridorBridge},fk_baggage_turntable = #{f.fkBaggageTurntable}
    ,fk_boarding_gate = #{f.fkBoardingGate} where id = #{f.id}
    </update>

    <select id="page" resultType="com.swcares.aiot.vo.FlightParkingVO">
        select id,aircraft_number,fk_aircraft_type,parking_property,fk_airport_terminal,
        is_corridor_bridge,is_power_supply,is_air_source,is_air_conditioner,is_isolation_stand,
        longitude,latitude ,fk_corridor_bridge,fk_baggage_turntable,fk_boarding_gate from asa_flight_parking
        <where>
             deleted = 0
            <if test="dto.aircraftNumber != null">
                and aircraft_number like concat('%',#{dto.aircraftNumber},'%')
            </if>
        </where>
    </select>

    <select id="list" resultType="com.swcares.aiot.vo.FlightParkingVO">
        select id,aircraft_number,fk_aircraft_type,parking_property,fk_airport_terminal,
        is_corridor_bridge,is_power_supply,is_air_source,is_air_conditioner,is_isolation_stand,
        longitude,latitude from asa_flight_parking
        <where>
          and  deleted = 0
          <if test=" dto.fkAirportTerminal != null ">
              and fk_airport_terminal = #{dto.fkAirportTerminal}
          </if>
          <if test=" dto.fkCorridorBridge != null ">
              and fk_corridor_bridge = #{dto.fkCorridorBridge}
          </if>

            <if test=" dto.fkBaggageTurntable != null ">
              and fk_baggage_turntable = #{dto.fkBaggageTurntable}
          </if>
            <if test=" dto.fkBoardingGate != null ">
              and fk_boarding_gate = #{dto.fkBoardingGate}
          </if>
        </where>
    </select>

    <select id="getProperty" resultType="com.swcares.aiot.vo.FlightParkingVO">
         select parking_property from asa_flight_parking
         <where>
             deleted = 0
             and parking_property is not null
         </where>
    </select>
    <select id="selectAllFlightParking" resultType="com.swcares.aiot.core.entity.FlightParking">
        select * from asa_flight_parking where deleted = 0
    </select>

    <select id="getCorridorBridgeInfo" resultType="com.swcares.aiot.vo.AircraftNumberToIsCorridorBridgeVO">
        select
        is_corridor_bridge,
        aircraft_number
        from asa_flight_parking
        <where>
            deleted = 0
        </where>
    </select>
    <select id="selectParkingGateMapping" resultType="com.swcares.aiot.dto.FlightParkingDTO">
        SELECT aircraft_number, fk_boarding_gate
        FROM asa_flight_parking
        WHERE deleted = 0
    </select>


</mapper>
