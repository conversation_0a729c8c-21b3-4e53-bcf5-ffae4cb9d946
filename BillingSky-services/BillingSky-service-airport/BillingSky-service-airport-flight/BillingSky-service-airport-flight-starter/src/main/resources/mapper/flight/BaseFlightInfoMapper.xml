<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.flight.mapper.BaseFlightInfoMapper">
    <sql id="base_flight_info_column">
        id, airline_code, flight_no, flight_date, preorder_flight_no, flight_date_time,
        departure_airport_code, destination_airport_code, flight_segment, flight_segment_property,
        airline, airline_property, task, status, length_of_delay, is_arrv,
        acdm_connect_flight_id, atc_connect_flight_id, connect_flight_id, aircraft_no, aircraft_model,
        aircraft_parking_position, gate, check_in_counter, baggage_carousel_serial_number,
        plan_landing_datetime, predict_landing_datetime, real_landing_datetime, plan_take_off_datetime,
        predict_take_off_datetime, real_take_off_datetime, deleted, dept_id, airport_code, abn_status,
        abnrsn, flight_state_code, share_flight, acdm_deleted, manual_amendment, sync_updated_time,
        data_is_intact, modify_field, status_type, data_source_type, created_by, created_time, updated_by,
        updated_time, atc_id, acdm_id, publish_status, old_safe_id, alternate_airport
    </sql>

    <sql id="base_flight_info_column_transform_acdm_deleted">
        if('2'=clean_type or '4'=clean_type, 1 , acdm_deleted ) acdm_deleted,
        id, airline_code, flight_no, flight_date, preorder_flight_no, flight_date_time,
        departure_airport_code, destination_airport_code, flight_segment, flight_segment_property,
        airline, airline_property, task, status, length_of_delay, is_arrv,
        acdm_connect_flight_id, atc_connect_flight_id, connect_flight_id, aircraft_no, aircraft_model,
        aircraft_parking_position, gate, check_in_counter, baggage_carousel_serial_number,
        plan_landing_datetime, predict_landing_datetime, real_landing_datetime, plan_take_off_datetime,
        predict_take_off_datetime, real_take_off_datetime, deleted, dept_id, airport_code, abn_status,
        abnrsn, flight_state_code, share_flight,manual_amendment, sync_updated_time,
        data_is_intact, modify_field, status_type, data_source_type, created_by, created_time, updated_by,
        updated_time, atc_id, acdm_id, publish_status, old_safe_id, alternate_airport, extend_value, flight_busines_id
    </sql>
    
    <sql id="base_flight_info_column_api_page">
        bfi.id, bfi.airline_code, bfi.flight_no, bfi.flight_date, bfi.preorder_flight_no, bfi.flight_date_time, bfi.departure_airport_code,
        bfi.destination_airport_code, bfi.flight_segment, bfi.flight_segment_property, bfi.airline, bfi.airline_property, bfi.fltmission,
        bfi.task, bfi.status, bfi.length_of_delay, bfi.is_arrv, bfi.aircraft_no, bfi.aircraft_model, bfi.aircraft_parking_position,
        bfi.plan_landing_datetime, bfi.predict_landing_datetime, bfi.real_landing_datetime, bfi.plan_take_off_datetime, bfi.predict_take_off_datetime,
        bfi.real_take_off_datetime, bfi.airport_code, bfi.share_flight, bfi.alternate_airport, bfi.created_by, bfi.created_time, bfi.updated_by,
        bfi.updated_time,
        bfc.cargo, bfc.mail, bfc.bag, bfc.bag_num, bfc.transit_cargo, bfc.transit_mail, bfc.transit_bag, bfc.transit_bag_num,
        bfc.cmb_throughput, bfc.load_factor, bfc.cmb_total_kg,
        bft.business_class_num, bft.first_class_num, bft.economy_class_num, bft.adult_num, bft.child_num, bft.infant_num, bft.transit_adult_num,
        bft.transit_child_num, bft.transit_infant_num, bft.psgr_plf, bft.traveler_throughput, bft.traveler_num, bft.psgr_total_kg
    </sql>

    <sql id="base_flight_info_column_transform_acdm_deleted_display">
        if('2'=clean_type or '4'=clean_type, 1 , if(acdm_deleted=2, 0, acdm_deleted) ) acdm_deleted,
        id, airline_code, flight_no, flight_date, preorder_flight_no, flight_date_time,
        departure_airport_code, destination_airport_code, flight_segment, flight_segment_property,
        airline, airline_property, task, status, length_of_delay, is_arrv,
        acdm_connect_flight_id, atc_connect_flight_id, connect_flight_id, aircraft_no, aircraft_model,
        aircraft_parking_position, gate, check_in_counter, baggage_carousel_serial_number,
        plan_landing_datetime, predict_landing_datetime, real_landing_datetime, plan_take_off_datetime,
        predict_take_off_datetime, real_take_off_datetime, deleted, dept_id, airport_code, abn_status,
        abnrsn, flight_state_code, share_flight,manual_amendment, sync_updated_time,
        data_is_intact, modify_field, status_type, data_source_type, created_by, created_time, updated_by,
        updated_time, atc_id, acdm_id, publish_status, old_safe_id, alternate_airport
    </sql>

    <sql id="base_flight_info_column_stay_time">
        if('2'=clean_type or '4'=clean_type, 1 , acdm_deleted ) acdm_deleted,
        id, airline_code, flight_no, flight_date, preorder_flight_no, flight_date_time,
        departure_airport_code, destination_airport_code, flight_segment, flight_segment_property,
        airline, airline_property, task, status, length_of_delay, is_arrv,
        acdm_connect_flight_id, atc_connect_flight_id, connect_flight_id, aircraft_no, aircraft_model,
        aircraft_parking_position, gate, check_in_counter, baggage_carousel_serial_number,
        plan_landing_datetime, predict_landing_datetime, real_landing_datetime, plan_take_off_datetime,
        predict_take_off_datetime, real_take_off_datetime, deleted, dept_id, airport_code, abn_status,
        abnrsn, flight_state_code, share_flight,manual_amendment, sync_updated_time,
        data_is_intact, modify_field, status_type, data_source_type, created_by, created_time, updated_by,
        updated_time, atc_id, acdm_id, publish_status, old_safe_id, alternate_airport, extend_value
    </sql>

    <sql id="base_flight_info_column_stay_time_data_push">
        if('2'=fi.clean_type or '4'=fi.clean_type, 1 , fi.acdm_deleted ) acdm_deleted,
        fi.id, fi.airline_code, fi.flight_no, fi.flight_date, fi.preorder_flight_no, fi.flight_date_time,
        fi.departure_airport_code, fi.destination_airport_code, fi.flight_segment, fi.flight_segment_property,
        fi.airline, fi.airline_property, fi.task, fi.status, fi.length_of_delay, fi.is_arrv,
        fi.acdm_connect_flight_id, fi.atc_connect_flight_id, fi.connect_flight_id, fi.aircraft_no, fi.aircraft_model,
        fi.aircraft_parking_position, fi.gate, fi.check_in_counter, fi.baggage_carousel_serial_number,
        fi.plan_landing_datetime, fi.predict_landing_datetime, fi.real_landing_datetime, fi.plan_take_off_datetime,
        fi.predict_take_off_datetime, fi.real_take_off_datetime, fi.deleted, fi.dept_id, fi.airport_code, fi.abn_status,
        fi.abnrsn, fi.flight_state_code, fi.share_flight,fi.manual_amendment, fi.sync_updated_time,
        fi.data_is_intact, fi.modify_field, fi.status_type, fi.data_source_type, fi.created_by, fi.created_time, fi.updated_by,
        fi.updated_time, fi.atc_id, fi.acdm_id, fi.publish_status, fi.old_safe_id, fi.alternate_airport
    </sql>

    <update id="logicRemoveById">
        update base_flight_info set deleted = #{info.deleted},modify_field = #{info.modifyField},acdm_id = #{info.acdmId},manual_amendment = #{info.manualAmendment},updated_time= now(),updated_by=#{info.updatedBy} where id = #{info.id}
    </update>

    <update id="updateId">
        update base_flight_info set manual_amendment = #{b.manualAmendment} where id = #{b.id}
    </update>

    <update id="updateToUpdateTime">
        update base_flight_info set updated_time = NOW() where (id = #{dto.arriveFlightId} or id = #{dto.takeOffFlightId})
    </update>

    <!--航班动态列表详情-->
    <select id="listInfo" resultType="com.swcares.aiot.core.vo.BaseFlightlistInfoVO">
        SELECT
        a.id,
        a.flight_no,
        a.departure_airport_code,
        a.destination_airport_code,
        a.plan_landing_datetime,
        a.real_landing_datetime,
        a.predict_landing_datetime,
        a.plan_take_off_datetime,
        a.real_take_off_datetime,
        a.predict_take_off_datetime,
        a.flight_date_time,
        a.length_of_delay,
        a.status
        FROM
        base_flight_info a
        where
        a.deleted = 0
        AND a.is_arrv = 1
        <if test="dto.startFlightDateTime!=null and dto.endFlightDateTime != null ">
            AND a.flight_date_time between #{dto.startFlightDateTime} and #{dto.endFlightDateTime}
        </if>
        <choose>
            <when test="dto.status == '待进港'">
                AND a.status in ('DEP','PLA','DLY')
            </when>
            <when test="dto.status == '已进港'">
                AND a.status = 'ARR'
            </when>
            <when test="dto.status == '取消'">
                AND a.status = 'CAN'
            </when>
            <when test="dto.status == '异常'">
                AND a.status in ('DLY','RTN','ALT')
            </when>
            <otherwise>

            </otherwise>
        </choose>
        order by a.plan_landing_datetime
    </select>

    <!--航班动态列表详情-->
    <select id="listInfoOne" resultType="com.swcares.aiot.core.vo.BaseFlightlistInfoVO">
        SELECT
        a.id,
        a.flight_no,
        a.departure_airport_code,
        a.destination_airport_code,
        a.plan_landing_datetime,
        a.real_landing_datetime,
        a.predict_landing_datetime,
        a.plan_take_off_datetime,
        a.real_take_off_datetime,
        a.predict_take_off_datetime,
        a.flight_date_time,
        a.length_of_delay,
        a.status
        FROM
        base_flight_info a
        where
        a.deleted = 0
        AND a.is_arrv = 0
        <if test="dto.startFlightDateTime!=null and dto.endFlightDateTime != null ">
            AND a.flight_date_time between #{dto.startFlightDateTime} and #{dto.endFlightDateTime}
        </if>
        <choose>
            <when test="dto.status == '待出港'">
                AND a.status in ('PLA','DLY')
            </when>
            <when test="dto.status == '已出港'">
                AND a.status in ('DEP','ARR')
            </when>
            <when test="dto.status == '取消'">
                AND a.status = 'CAN'
            </when>
            <when test="dto.status == '异常'">
                AND a.status in ('DLY','RTN','ALT')
            </when>
            <otherwise>

            </otherwise>
        </choose>
        order by a.plan_take_off_datetime
    </select>

    <!--记录进港总条数-->
    <select id="count" resultType="string">
        SELECT
        count(1)
        FROM
        base_flight_info a
        where
        a.deleted = 0
        AND a.is_arrv = #{dto.isArrv}
        <if test="dto.flightDateTime!=null ">
            AND DATE_FORMAT(a.flight_date_time,'%Y-%m-%d') = DATE_FORMAT(#{dto.flightDateTime},'%Y-%m-%d')
        </if>
        <choose>
            <when test="dto.status == '待进港'">
                AND a.status in ('DEP','PLA','DLY')
            </when>
            <when test="dto.status == '已进港'">
                AND a.status = 'ARR'
            </when>
            <when test="dto.status == '待出港'">
                AND a.status in ('PLA','DLY')
            </when>
            <when test="dto.status == '已出港'">
                AND a.status in ('DEP','ARR')
            </when>
            <when test="dto.status == '取消'">
                AND a.status = 'CAN'
            </when>
            <when test="dto.status == '异常'">
                AND a.status in ('DLY','RTN','ALT')
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="selectDetails" resultType="com.swcares.aiot.core.vo.BaseFlightDetalsVO">
        select
        a.flight_no,
        a.airline_code,
        a.flight_date_time,
        a.departure_airport_code,
        a.destination_airport_code,
        a.plan_landing_datetime,
        a.real_landing_datetime,
        a.predict_landing_datetime,
        a.plan_take_off_datetime,
        a.real_take_off_datetime,
        a.predict_take_off_datetime,
        a.aircraft_no,
        a.aircraft_model,
        a.aircraft_parking_position,
        a.gate,
        a.is_arrv,
        a.check_in_counter,
        a.baggage_carousel_serial_number,
        a.status
<!--        b.dict_label as `status`-->
        from
        base_flight_info a
<!--        LEFT JOIN-->
<!--        sys_dictionary_data b-->
<!--        ON-->
<!--        a.`status` = b.dict_value-->
        where
        a.deleted = 0
<!--        AND b.deleted = 0-->
<!--        AND b.dict_type = "departure_arrive_status"-->
        AND a.id = #{id}
    </select>


    <select id="getBeforeFlight" resultType="com.swcares.aiot.dto.BaseFlightDTO">
        SELECT
        a.id,
        a.flight_no,
        a.airline_code,
        a.flight_date_time,
        a.departure_airport_code,
        a.destination_airport_code,
        a.plan_landing_datetime,
        a.real_landing_datetime,
        a.predict_landing_datetime,
        a.plan_take_off_datetime,
        a.real_take_off_datetime,
        a.predict_take_off_datetime,
        a.aircraft_no,
        a.aircraft_model,
        a.aircraft_parking_position,
        a.gate,
        a.is_arrv,
        a.check_in_counter,
        a.baggage_carousel_serial_number,
        a.status
        FROM
        base_flight_info a
<!--        LEFT JOIN-->
<!--        sys_dictionary_data b-->
<!--        ON-->
<!--        a.`status` = b.dict_value-->
        <where>
            a.deleted = 0
<!--            AND b.deleted = 0-->
            AND a.is_arrv= 1
<!--            AND b.dict_type = "departure_arrive_status"-->
            AND a.aircraft_no=#{dto.aircraftNo}
            <if test="dto.flightDateTime!=null ">
                AND DATE_SUB(date_format(#{dto.flightDateTime},'%Y-%m-%d'),INTERVAL 3 day) &lt; date_format(a.flight_date_time,'%Y-%m-%d')
                AND a.flight_date_time &lt; #{dto.flightDateTime}
            </if>
        </where>
        ORDER BY a.flight_date_time desc
        limit 1
    </select>


    <select id="getLastUpdateTime" resultType="java.time.LocalDateTime">
        SELECT
        max(updated_time)
        FROM
        base_flight_info a
    </select>






    <select id="selectStatusInfoTwo" resultType="com.swcares.aiot.vo.BaseAircraftSafeguardsInfoVO">
        select id,
        flight_no,
        departure_airport_code,
        destination_airport_code,
        status,
        aircraft_no,
        aircraft_model,
        aircraft_parking_position,
        gate,
        is_arrv,
        plan_landing_datetime,
        real_landing_datetime,
        predict_landing_datetime,
        plan_take_off_datetime,
        real_take_off_datetime,
        predict_take_off_datetime,
        flight_date_time
        from base_flight_info
        <where>
            and deleted = 0
            <if test="dto.flightDateTime != null">
                AND (DATE_FORMAT(plan_take_off_datetime,'%Y-%m-%d') = DATE_FORMAT(#{dto.flightDateTime},'%Y-%m-%d')
                or DATE_FORMAT(plan_landing_datetime,'%Y-%m-%d') = DATE_FORMAT(#{dto.flightDateTime},'%Y-%m-%d'))
            </if>
            <choose>
                <when test="dto.selectType == '进港'">
<!--                    and destination_airport_code = #{code}-->
                    and is_arrv = 1
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( flight_no like concat('%',#{dto.flightNo},'%')
                        or aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                </when>
                <when test="dto.selectType == '出港'">
<!--                    and departure_airport_code = #{code}-->
                    and is_arrv = 0
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( flight_no like concat('%',#{dto.flightNo},'%')
                        or aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                </when>
                <when test="dto.selectType == '取消'">
                    and status = 'CAN'
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( flight_no like concat('%',#{dto.flightNo},'%')
                        or aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                </when>
                <when test="dto.selectType == '异常'">
                    and status in ('DLY', 'RTN', 'ALT')
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( flight_no like concat('%',#{dto.flightNo},'%')
                        or aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                </when>
            </choose>
        </where>
    </select>



    <select id="pcCountTwo" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        base_flight_info
        <where>
            deleted = 0
            <choose>
                <when test="dto.selectType == '进港'">
<!--                    and destination_airport_code = #{code}-->
                    and is_arrv = 1
                    <if test="dto.startDateTime != null and dto.endDateTime != null">
                        AND (plan_landing_datetime between #{dto.startDateTime} and #{dto.endDateTime} )
                    </if>
                </when>
                <when test="dto.selectType == '出港'">
<!--                    and departure_airport_code = #{code}-->
                    and is_arrv = 0
                    <if test="dto.startDateTime != null and dto.endDateTime != null">
                        AND (plan_take_off_datetime between #{dto.startDateTime} and #{dto.endDateTime})
                    </if>
                </when>
                <when test="dto.selectType == '取消'">
                    and status = 'CAN'
                    <if test="dto.startDateTime != null and dto.endDateTime != null">
                        AND (plan_landing_datetime between #{dto.startDateTime} and #{dto.endDateTime}
                        or plan_take_off_datetime between #{dto.startDateTime} and #{dto.endDateTime})
                    </if>
                </when>
                <when test="dto.selectType == '异常'">
                    and status in ('DLY', 'RTN', 'ALT')
                    <if test="dto.startDateTime != null and dto.endDateTime != null">
                        AND (plan_landing_datetime between #{dto.startDateTime} and #{dto.endDateTime}
                        or plan_take_off_datetime between #{dto.startDateTime} and #{dto.endDateTime})
                    </if>
                </when>
            </choose>
        </where>
    </select>

    <select id="page" resultType="com.swcares.aiot.vo.BaseAircraftSafeguardsInfoVO">
        select
        DISTINCT
        bfi.id,
        flight_no,
        departure_airport_code,
        destination_airport_code,
        bfi.status,
        aircraft_no,
        aircraft_model,
        aircraft_parking_position,
        gate,
        is_arrv,
        plan_landing_datetime,
        real_landing_datetime,
        predict_landing_datetime,
        plan_take_off_datetime,
        real_take_off_datetime,
        predict_take_off_datetime,
        flight_date_time,
        (CASE
        when is_arrv = 0 then plan_take_off_datetime
        when is_arrv = 1 then plan_landing_datetime
        else null
        end) as plan_landing_take_off_datetime,
        baggage_carousel_serial_number
        from base_flight_info bfi
        <where>
            bfi.deleted = 0
            <choose>
                <when test="dto.selectType == '进港'">
<!--                    and destination_airport_code = 'YBP'-->
                    and is_arrv = '1'
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( bfi.flight_no like concat('%',#{dto.flightNo},'%')
                        or bfi.aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                    <if test="dto.flightDateTime != null">
                        AND (bfi.plan_landing_datetime between CONCAT(#{dto.flightDateTimeStr},' 00:00:00') and CONCAT(#{dto.flightDateTimeStr},' 23:59:59'))
                    </if>
                    order by plan_landing_datetime asc
                </when>
                <when test="dto.selectType == '出港'">
<!--                    and departure_airport_code = 'YBP'-->
                    and is_arrv = '0'
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( bfi.flight_no like concat('%',#{dto.flightNo},'%')
                        or bfi.aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                    <if test="dto.flightDateTime != null">
                        AND (bfi.plan_take_off_datetime between CONCAT(#{dto.flightDateTimeStr},' 00:00:00') and CONCAT(#{dto.flightDateTimeStr},' 23:59:59'))
                    </if>
                        order by plan_take_off_datetime asc
                </when>
                <when test="dto.selectType == '取消'">
                    and status = 'CAN'
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( bfi.flight_no like concat('%',#{dto.flightNo},'%')
                        or bfi.aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                    <if test="dto.flightDateTime != null">
                        AND (bfi.plan_take_off_datetime between CONCAT(#{dto.flightDateTimeStr},' 00:00:00') and CONCAT(#{dto.flightDateTimeStr},' 23:59:59'))
                        or (bfi.plan_landing_datetime between CONCAT(#{dto.flightDateTimeStr},' 00:00:00') and CONCAT(#{dto.flightDateTimeStr},' 23:59:59'))
                    </if>
                    order by is_arrv,
                    plan_landing_take_off_datetime
                </when>
                <when test="dto.selectType == '异常'">
                    and status in ('DLY', 'RTN', 'ALT')
                    <if test="dto.flightNo != null and dto.flightNo !=''">
                        and ( bfi.flight_no like concat('%',#{dto.flightNo},'%')
                        or bfi.aircraft_no like concat('%',#{dto.flightNo},'%'))
                    </if>
                    <if test="dto.flightDateTime != null">
                        AND (bfi.plan_take_off_datetime between CONCAT(#{dto.flightDateTimeStr},' 00:00:00') and CONCAT(#{dto.flightDateTimeStr},' 23:59:59'))
                        or (bfi.plan_landing_datetime between CONCAT(#{dto.flightDateTimeStr},' 00:00:00') and CONCAT(#{dto.flightDateTimeStr},' 23:59:59'))
                    </if>
                    order by is_arrv,
                    plan_landing_take_off_datetime
                </when>
            </choose>
        </where>
    </select>


    <select id="pageFlightInfo" resultType="com.swcares.aiot.vo.BaseFlightInfoVO">
        select
           <include refid="base_flight_info_column_transform_acdm_deleted_display"/>
        from base_flight_info
        <where>
            and deleted = 0
            <if test="dto.startTime != null and  dto.endTime != null  ">
                and flight_date BETWEEN #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.airlineCode != null and dto.airlineCode != ''">
                and airline_code  = #{dto.airlineCode}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and flight_no like concat('%',#{dto.flightNo},'%')
            </if>
            <if test="dto.flightTimeStartTime != null  and dto.flightTimeEndTime != null ">
            and ((
            is_arrv = '0'
            AND
            IF
            ( real_take_off_datetime IS NULL, IF ( predict_take_off_datetime IS NULL, plan_take_off_datetime,
            predict_take_off_datetime ), real_take_off_datetime ) BETWEEN #{dto.flightTimeStartTime} AND
                #{dto.flightTimeEndTime}
            )
            OR (
            is_arrv = '1'
            AND
            IF( real_landing_datetime IS NULL, IF ( predict_landing_datetime IS NULL, plan_landing_datetime,
            predict_landing_datetime ), real_landing_datetime ) BETWEEN #{dto.flightTimeStartTime}
            AND #{dto.flightTimeEndTime}))
            </if>
            <if test="dto.acdmDeleted != null ">
                <choose>
                    <when test="dto.acdmDeleted == 0">
                        AND (acdm_deleted in (0,2) AND clean_type != '2' AND clean_type != '4')
                    </when>
                    <otherwise>
                        AND (acdm_deleted = #{dto.acdmDeleted} OR clean_type = '2' OR clean_type = '4')
                    </otherwise>
                </choose>
            </if>
        </where>

        <if test="dto.status != null and dto.status.size() >0 ">
            and status in
            <foreach collection="dto.status" item="ststus" open="(" separator="," close=")">
                #{ststus}
            </foreach>
        </if>
        order by flight_date desc,created_time desc,id desc
    </select>

    <select id="listExportFlightInfo" resultType="com.swcares.aiot.core.vo.BaseFlightInfoExportVO">
        SELECT
        bfi.id,
        bfi.flight_date,
        bfi.airline_code,
        bfi.airport_code,
        bfi.flight_no,
        bfi.aircraft_model,
        bfi.task as flight_type,
        bfi.airline,
        bfi.airline_property as flight_line_type,
        bfi.flight_segment,
        bfi.flight_segment_property,
        bfi.is_arrv,
        CASE
        WHEN bfi.is_arrv = 1 THEN
        (
            CASE
                WHEN bfi.real_landing_datetime IS NOT NULL THEN
                bfi.real_landing_datetime
                WHEN bfi.predict_landing_datetime IS NOT NULL THEN
                bfi.predict_landing_datetime ELSE bfi.plan_landing_datetime
            END
        ) ELSE (
            CASE
                WHEN bfi.real_take_off_datetime IS NOT NULL THEN
                bfi.real_take_off_datetime
                WHEN bfi.predict_take_off_datetime IS NOT NULL THEN
                bfi.predict_take_off_datetime ELSE bfi.plan_take_off_datetime
            END
        )
        END AS flight_time,
        bft.adult_num,
        bft.child_num,
        bft.infant_num,
        bft.transit_adult_num,
        bft.transit_child_num,
        bft.transit_infant_num,
        bfc.bag,
        bfc.mail,
        bfc.cargo,
        bfi.aircraft_no,
        bft.diplomatic_passport_traveler_num,
        bfc.bag_num,
        bfi.status,
        bfi.acdm_deleted
        FROM
        base_flight_info bfi
        LEFT JOIN base_flight_cargo bfc ON bfi.id = bfc.base_flight_id
        AND bfc.deleted = 0
        LEFT JOIN base_flight_traveler bft ON bfi.id = bft.base_flight_id
        AND bft.deleted = 0
        <where>
            and bfi.airport_code = #{dto.airportCode}
            and bfi.deleted = 0
            and bfi.acdm_deleted in (0, 2)
            and bfi.clean_type in ('0','1','3')
            and (bfi.status not in('CAN') or bfi.status is null)
            <choose>
                <when test="dto.timeType == 'flightTime'">
                    and (
                        (bfi.is_arrv = '0'
                        AND DATE_FORMAT(
                        IF(bfi.real_take_off_datetime IS NULL,
                            IF (bfi.predict_take_off_datetime IS NULL,
                                bfi.plan_take_off_datetime, bfi.predict_take_off_datetime ), bfi.real_take_off_datetime ),'%Y-%m-%d')
                        BETWEEN DATE_FORMAT(#{dto.startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{dto.endTime}, '%Y-%m-%d' )
                        )
                        OR (
                        bfi.is_arrv = '1'
                        AND DATE_FORMAT(
                        IF( bfi.real_landing_datetime IS NULL,
                            IF( bfi.predict_landing_datetime IS NULL,
                                bfi.plan_landing_datetime, bfi.predict_landing_datetime ), bfi.real_landing_datetime ),'%Y-%m-%d')
                        BETWEEN DATE_FORMAT(#{dto.startTime}, '%Y-%m-%d' ) AND DATE_FORMAT(#{dto.endTime}, '%Y-%m-%d' ))
                    )
                </when>
                <otherwise>
                    AND bfi.flight_date BETWEEN DATE_FORMAT(#{dto.startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{dto.endTime}, '%Y-%m-%d')
                </otherwise>
            </choose>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and bfi.flight_no in
                <foreach collection="dto.flightNo" item="dto.flightNo" open="(" separator="," close=")">
                    #{dto.flightNo}
                </foreach>
            </if>
            <if test="dto.airlineCode != null and dto.airlineCode != ''">
                and bfi.airline_code in
                <foreach collection="dto.airlineCode" item="dto.airlineCode" open="(" separator="," close=")">
                    #{dto.airlineCode}
                </foreach>
            </if>
            <if test="dto.status != null and dto.status != ''">
                and bfi.status in
                <foreach collection="dto.status" item="dto.status" open="(" separator="," close=")">
                    #{dto.status}
                </foreach>
            </if>
            <if test="dto.task != null and dto.task != ''">
                and bfi.task in
                <foreach collection="dto.task" item="dto.task" open="(" separator="," close=")">
                    #{dto.task}
                </foreach>
            </if>
            <if test="dto.isArrv != null and dto.isArrv != ''">
                and bfi.is_arrv = #{dto.isArrv}
            </if>
        </where>
        order by bfi.flight_date desc,bfi.id desc
    </select>

    <select id="listExportSegmentFlightInfo" resultType="com.swcares.aiot.core.vo.BaseFlightInfoExportVO">
        SELECT
        bfi.flight_date,
        bfi.airline_code,
        bfi.airport_code,
        bfi.flight_no,
        bfi.aircraft_model,
        bfi.task as flightType,
        bfi.airline,
        bfi.airline_property as flight_line_type,
        bft.flight_segment,
        bfi.flight_segment_property,
        bfi.is_arrv,
        CASE WHEN bfi.is_arrv = 1 THEN
        (
            CASE
                WHEN bfi.real_landing_datetime IS NOT NULL THEN
                    bfi.real_landing_datetime
                WHEN bfi.predict_landing_datetime IS NOT NULL THEN
                    bfi.predict_landing_datetime ELSE bfi.plan_landing_datetime
            END
        ) ELSE (
        CASE
            WHEN bfi.real_take_off_datetime IS NOT NULL THEN
                bfi.real_take_off_datetime
            WHEN bfi.predict_take_off_datetime IS NOT NULL THEN
                bfi.predict_take_off_datetime ELSE bfi.plan_take_off_datetime
            END
        )END AS flight_time,
        bft.adult_num,
        bft.child_num,
        bft.infant_num,
        bft.transit_adult_num,
        bft.transit_child_num,
        bft.transit_infant_num,
        bfc.bag,
        bfc.mail,
        bfc.cargo,
        bfi.aircraft_no,
        bft.diplomatic_passport_traveler_num,
        bfc.bag_num,
        bfc.is_near
        FROM
        base_flight_info bfi
        INNER JOIN base_flight_cargo_segment bfc
            ON bfi.id = bfc.base_flight_id
            AND bfc.deleted = 0
        INNER JOIN base_flight_traveler_segment bft
            ON bfi.id = bft.base_flight_id
            AND bft.deleted = 0
            AND bft.is_near=bfc.is_near
            AND bft.flight_segment = bfc.flight_segment
        <where>
            and bfi.deleted = 0
            and bfi.acdm_deleted in (0, 2)
            and bfi.id in
            <foreach collection="splitIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        GROUP BY bfi.flight_date,
            bfi.flight_no,
            bft.flight_segment,
            bft.land_flag
        ORDER BY
            bfi.flight_date desc,
            bfi.created_time desc,
            bfi.id desc
    </select>
    <select id="getFlightLineCount" resultType="java.lang.String">
        SELECT airline,count( 1 )
                        FROM base_flight_info t1
                        WHERE t1.deleted = 0
                        AND t1.airline is not null
                        AND t1.flight_date BETWEEN date_sub( #{startTime} , INTERVAL 60 DAY )
                       AND #{endTime}
                        GROUP BY airline
    </select>
    <select id="selectAircraftNo" resultType="com.swcares.aiot.core.entity.BaseFlightInfo">
        select
            <include refid="base_flight_info_column"/>
        from
            base_flight_info
        where flight_no = #{dto.flightNo}
            and DATE_FORMAT(flight_date,'%Y-%m-%d') = DATE_FORMAT(#{dto.flightDate},'%Y-%m-%d')
            and is_arrv = #{dto.landFlag}
            and deleted = 0
            AND acdm_deleted in (0, 2)
            AND clean_type in ('0','1','3')
            AND status != 'CAN'
    </select>
    <select id="getDataToLastUpdateTime" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
            <include refid="base_flight_info_column_transform_acdm_deleted"/>
        FROM
            base_flight_info
        WHERE
            updated_time BETWEEN #{date}
            AND ( SELECT max( updated_time ) FROM base_flight_info )
    </select>

    <select id="getId" resultType="java.lang.Long">
        select id from base_flight_info where departure_airport_code = #{depAirport} and destination_airport_code = #{destinationAirport}
        and flight_no = #{no} and flight_date = STR_TO_DATE(#{date},'%Y-%m-%d') and deleted = '0'
    </select>
    <select id="getSyncHistoryFlightNo" resultType="java.lang.String">
        select DISTINCT flight_no from base_flight_info
        <where>
            airport_code = #{dto.airportCode} and flight_date &gt;= #{dto.startTime} and flight_date &lt;= #{dto.endTime}
            <if test="dto.isArrv != null and dto.isArrv != ''">
               and is_arrv = #{dto.isArrv}
            </if>
        </where>
    </select>
    <select id="getTotalTask" resultType="java.lang.Integer">
        select count(1) from base_flight_info
        <where>
            airport_code = #{dto.airportCode}
            <choose>
                <when test="dto.timeType == 'flightTime'">
                    and ((
                    is_arrv = '0'
                    AND DATE_FORMAT(
                    IF
                    ( real_take_off_datetime IS NULL, IF ( predict_take_off_datetime IS NULL, plan_take_off_datetime, predict_take_off_datetime ), real_take_off_datetime ),
                    '%Y-%m-%d'
                    ) BETWEEN DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' )
                    AND DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
                    )
                    OR (
                    is_arrv = '1'
                    AND DATE_FORMAT(
                    IF
                    ( real_landing_datetime IS NULL, IF ( predict_landing_datetime IS NULL, plan_landing_datetime, predict_landing_datetime ), real_landing_datetime ),
                    '%Y-%m-%d'
                    ) BETWEEN DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' )
                    AND DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
                    ))
                </when>
                <otherwise>
                    AND flight_date BETWEEN DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' ) AND DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
                </otherwise>
            </choose>

            and (status not in('CAN') or status is null)
            AND deleted = 0 AND  acdm_deleted in (0, 2)  and clean_type in ('0','1','3')

            <if test="dto.flightNo != null and dto.flightNo != ''">
                and flight_no in
                <foreach collection="dto.flightNo" item="dto.flightNo" open="(" separator="," close=")">
                    #{dto.flightNo}
                </foreach>
            </if>
            <if test="dto.airlineCode != null and dto.airlineCode != ''">
                and airline_code in
                <foreach collection="dto.airlineCode" item="dto.airlineCode" open="(" separator="," close=")">
                    #{dto.airlineCode}
                </foreach>
            </if>
            <if test="dto.status != null and dto.status != ''">
                and status in
                <foreach collection="dto.status" item="dto.status" open="(" separator="," close=")">
                    #{dto.status}
                </foreach>
            </if>
            <if test="dto.task != null and dto.task != ''">
                and task in
                <foreach collection="dto.task" item="dto.task" open="(" separator="," close=")">
                    #{dto.task}
                </foreach>
            </if>
            <if test="dto.isArrv != null and dto.isArrv != ''">
                and is_arrv = #{dto.isArrv}
            </if>
        </where>
    </select>
    <select id="getTransport" resultType="java.lang.Integer">
        select count(1) from base_flight_info
        <where>
--             task in (
--             'B/W','C/B','E/A','E/B','E/C','H/G','H/Y','H/Z','J/B','L/W','Q/T','T/C','W/Z','Y/B',
--             'Y/F','Z/P','Z/X','T/D','U/C','C/G')
            airport_code = #{dto.airportCode}

            <choose>
                <when test="dto.timeType == 'flightTime'">
                    and ((
                    is_arrv = '0'
                    AND DATE_FORMAT(
                    IF
                    ( real_take_off_datetime IS NULL, IF ( predict_take_off_datetime IS NULL, plan_take_off_datetime, predict_take_off_datetime ), real_take_off_datetime ),
                    '%Y-%m-%d'
                    ) BETWEEN DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' )
                    AND DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
                    )
                    OR (
                    is_arrv = '1'
                    AND DATE_FORMAT(
                    IF
                    ( real_landing_datetime IS NULL, IF ( predict_landing_datetime IS NULL, plan_landing_datetime, predict_landing_datetime ), real_landing_datetime ),
                    '%Y-%m-%d'
                    ) BETWEEN DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' )
                    AND DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
                    ))
                </when>
                <otherwise>
                    AND flight_date BETWEEN DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' ) AND DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
                </otherwise>
            </choose>

            and (status not in('CAN') or status is null)
            and task in ('B/W','C/B','E/A','E/B','E/C','H/G','H/Y','H/Z','J/B','L/W','Q/T','T/C','W/Z','Y/B',
                        'Y/F','Z/P','Z/X','T/D','U/C','C/G')
            AND deleted = 0 AND acdm_deleted in (0, 2) and clean_type in ('0','1','3')

            <if test="dto.flightNo != null and dto.flightNo != ''">
                and flight_no in
                <foreach collection="dto.flightNo" item="dto.flightNo" open="(" separator="," close=")">
                    #{dto.flightNo}
                </foreach>
            </if>
            <if test="dto.airlineCode != null and dto.airlineCode != ''">
                and airline_code in
                <foreach collection="dto.airlineCode" item="dto.airlineCode" open="(" separator="," close=")">
                    #{dto.airlineCode}
                </foreach>
            </if>
            <if test="dto.status != null and dto.status != ''">
                and status in
                <foreach collection="dto.status" item="dto.status" open="(" separator="," close=")">
                    #{dto.status}
                </foreach>
            </if>
            <if test="dto.task != null and dto.task != ''">
                and task in
                <foreach collection="dto.task" item="dto.task" open="(" separator="," close=")">
                    #{dto.task}
                </foreach>
            </if>
            <if test="dto.isArrv != null and dto.isArrv != ''">
                and is_arrv = #{dto.isArrv}
            </if>
        </where>
    </select>
    <select id="getFlightNo" resultType="com.swcares.aiot.core.vo.AirportCodeInfoVO">
        select distinct flight_no from base_flight_info
        <where>
            deleted = 0
            and airport_code = #{dto.airportCode}
            and DATE_FORMAT(flight_date,'%Y-%m-%d')
            BETWEEN DATE_FORMAT(#{dto.startTime},'%Y-%m-%d') and DATE_FORMAT(#{dto.endTime},'%Y-%m-%d')
            and airline_code in
            <foreach collection="dto.airlineCode" item="dto.airlineCode" open="(" separator="," close=")">
            #{dto.airlineCode}
            </foreach>
        </where>
    </select>
    <select id="getAirlineCode" resultType="com.swcares.aiot.core.vo.AirportCodeInfoVO">
        select distinct airline_code from base_flight_info
        <where>
            deleted = 0
            and airport_code = #{dto.airportCode}
            and DATE_FORMAT(flight_date,'%Y-%m-%d')
            BETWEEN DATE_FORMAT(#{dto.startTime},'%Y-%m-%d') and DATE_FORMAT(#{dto.endTime},'%Y-%m-%d')
        </where>
    </select>
    <select id="getTask" resultType="com.swcares.aiot.core.vo.AirportCodeInfoVO">
        select distinct task from base_flight_info
        <where>
            deleted = 0
            and task is not null
        </where>
    </select>
    <select id="getStatus" resultType="com.swcares.aiot.core.vo.AirportCodeInfoVO">
        select distinct status from base_flight_info
        <where>
            deleted = 0
            and airport_code = #{dto.airportCode}
            and DATE_FORMAT(flight_date,'%Y-%m-%d')
            BETWEEN DATE_FORMAT(#{dto.startTime},'%Y-%m-%d') and DATE_FORMAT(#{dto.endTime},'%Y-%m-%d')
            and flight_no = #{dto.flightNo}
            and airline_code = #{dto.airlineCode}
            and task = #{dto.task}
        </where>
    </select>
    <select id="getIsArry" resultType="com.swcares.aiot.core.vo.AirportCodeInfoVO">
        select distinct is_arry from base_flight_info
        <where>
            deleted = 0
            and airport_code = #{dto.airportCode}
            and DATE_FORMAT(flight_date,'%Y-%m-%d')
            BETWEEN DATE_FORMAT(#{dto.startTime},'%Y-%m-%d') and DATE_FORMAT(#{dto.endTime},'%Y-%m-%d')
            and flight_no = #{dto.flightNo}
            and airline_code = #{dto.airlineCode}
            and task = #{dto.task}
            and status = #{dto.status}
        </where>
    </select>

    <update id="logicRemoveByDataSetting">
        update base_flight_info info set info.deleted = 1,info.updated_time= now()
        <where>
            deleted = 0
            and  ( CONCAT(info.flight_no,'/',info.flight_date,'/' ,info.departure_airport_code,
            '/' ,info.destination_airport_code,'/' ,info.airport_code ) in
            <foreach collection="dto" item="uniqueId" index="index" open="(" separator="," close=")">
                <if test="(index % 999) == 998">
                    NULL ) OR CONCAT(info.flight_no,'/',info.flight_date,'/'
                    ,info.departure_airport_code,
                    '/' ,info.destination_airport_code,'/' ,info.airport_code)  in (
                </if>
                #{uniqueId}
            </foreach>
            )
        </where>
    </update>

    <update id="updateBatchById">
        <foreach collection="flightList" item="flight" separator=";">
            UPDATE base_flight_info
            <trim prefix="SET" suffixOverrides=",">
                <if test="flight.umeRepeatId != null">ume_repeat_id = ${flight.umeRepeatId},</if>
                <if test="flight.cleanType != null and flight.cleanType != ''">clean_type = ${flight.cleanType},</if>
            </trim>
             WHERE id = ${flight.id}
        </foreach>
    </update>
    <update id="updateBaseFlightId">
        UPDATE base_flight_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="flight.umeRepeatId != null">ume_repeat_id = #{flight.umeRepeatId},</if>
            <if test="flight.cleanType != null and flight.cleanType != ''">clean_type = #{flight.cleanType},</if>
        </trim>
        WHERE id = #{flight.id}
    </update>
    <update id="saveOrUpdateBatchByFlightBusinessId" parameterType="java.util.List">
        insert into base_flight_info (
            flight_busines_id, airline_code, flight_no, flight_date, flight_date_time,
            departure_airport_code, destination_airport_code, flight_segment, flight_segment_property,
            fltmission, task, status, is_arrv, aircraft_no,
            aircraft_parking_position, real_landing_datetime,
            plan_take_off_datetime, real_take_off_datetime, airport_code, updated_time, updated_by
        ) values
        <foreach collection="dtoList" item="flight" separator=",">
            (
                #{flight.flightBusinesId}, #{flight.airlineCode}, #{flight.flightNo}, #{flight.flightDate}, #{flight.flightDateTime},
                #{flight.departureAirportCode}, #{flight.destinationAirportCode}, #{flight.flightSegment}, #{flight.flightSegmentProperty},
                #{flight.fltmission}, #{flight.task}, #{flight.status}, #{flight.isArrv},#{flight.aircraftNo},
                #{flight.aircraftParkingPosition}, #{flight.realLandingDatetime},
                #{flight.planTakeOffDatetime}, #{flight.realTakeOffDatetime}, #{flight.airportCode}, now(),#{flight.updatedBy}
            )
        </foreach>
        on duplicate key update
           flight_busines_id = VALUES(flight_busines_id)
    </update>

    <select id="listByUniqueId" resultType="com.swcares.aiot.core.entity.BaseFlightInfo">
        select
          <include refid="base_flight_info_column"/>
        from base_flight_info info
        <where>
            deleted = 0
            and CONCAT(info.flight_no,'/',info.flight_date,'/' ,info.departure_airport_code,
            '/' ,info.destination_airport_code,'/' ,info.airport_code ) in
            <foreach collection="dto.uniqueIdSet" item="uniqueId" open="(" separator="," close=")">
                #{uniqueId}
            </foreach>

        </where>
    </select>

    <select id="countRecentlyUpdated" resultType="java.lang.Integer">
        select count(*) from base_flight_info
        <where>
            deleted = false
            and updated_time &gt; #{time}
        </where>
    </select>

    <select id="queryRecentlyUpdated" resultType="com.swcares.aiot.core.vo.FlightInfo3pItemVO">
        select
          <include refid="base_flight_info_column"/>
        from base_flight_info

        <where>
            deleted = false
            and updated_time &gt; #{time}
        </where>
        order by updated_time asc
        limit 500
    </select>


    <select id="queryRepeatedData" resultType="com.swcares.aiot.vo.RepeatedFlightInfoVO">
        SELECT
        a.flight_no,a.aircraft_no,a.flight_date,a.real_take_off_datetime,a.real_landing_datetime
        FROM
        base_flight_info a
        inner JOIN (
            SELECT
            aircraft_no,
            real_take_off_datetime,
            real_landing_datetime
            FROM
            base_flight_info
            <where>
                deleted = false and status != 'CAN'  and acdm_deleted = false
                and real_landing_datetime is not null and real_take_off_datetime is not null
                <if test="dto.startTime != null and  dto.endTime != null  ">
                    and flight_date BETWEEN #{dto.startTime} and #{dto.endTime}
                </if>
                <if test="dto.airlineCode != null and dto.airlineCode != ''">
                    and airline_code  = #{dto.airlineCode}
                </if>
                <if test="dto.flightNo != null and dto.flightNo != ''">
                    and flight_no like concat('%',#{dto.flightNo},'%')
                </if>
                <if test="dto.flightTimeStartTime != null  and dto.flightTimeEndTime != null ">
                    and ((
                    is_arrv = '0'
                    AND DATE_FORMAT(
                    IF
                    ( real_take_off_datetime IS NULL, IF ( predict_take_off_datetime IS NULL, plan_take_off_datetime,
                    predict_take_off_datetime ), real_take_off_datetime ),
                    '%Y-%m-%d'
                    ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                    AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                    )
                    OR (
                    is_arrv = '1'
                    AND DATE_FORMAT(
                    IF
                    ( real_landing_datetime IS NULL, IF ( predict_landing_datetime IS NULL, plan_landing_datetime,
                    predict_landing_datetime ), real_landing_datetime ),
                    '%Y-%m-%d'
                    ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                    AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                    ))
                </if>
            </where>
            GROUP BY
            aircraft_no,
            real_take_off_datetime
            HAVING
            COUNT(*) > 1
        ) b
        ON
        a.aircraft_no = b.aircraft_no
        AND a.real_take_off_datetime = b.real_take_off_datetime
        <where>
            a.deleted = false and a.status != 'CAN'  and a.acdm_deleted = false
            and a.real_landing_datetime is not null and a.real_take_off_datetime is not null
            <if test="dto.startTime != null and  dto.endTime != null  ">
                and a.flight_date BETWEEN #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.airlineCode != null and dto.airlineCode != ''">
                and a.airline_code  = #{dto.airlineCode}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and a.flight_no like concat('%',#{dto.flightNo},'%')
            </if>
            <if test="dto.flightTimeStartTime != null  and dto.flightTimeEndTime != null ">
                and ((
                a.is_arrv = '0'
                AND DATE_FORMAT(
                IF
                ( a.real_take_off_datetime IS NULL, IF ( a.predict_take_off_datetime IS NULL, a.plan_take_off_datetime,
                a.predict_take_off_datetime ), a.real_take_off_datetime ),
                '%Y-%m-%d'
                ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                )
                OR (
                a.is_arrv = '1'
                AND DATE_FORMAT(
                IF
                ( a.real_landing_datetime IS NULL, IF ( a.predict_landing_datetime IS NULL, a.plan_landing_datetime,
                a.predict_landing_datetime ), a.real_landing_datetime ),
                '%Y-%m-%d'
                ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                ))
            </if>
        </where>
        UNION
        SELECT
        a.flight_no,a.aircraft_no,a.flight_date,a.real_take_off_datetime,a.real_landing_datetime
        FROM
        base_flight_info a
        inner JOIN (
        SELECT
        aircraft_no,
        real_take_off_datetime,
        real_landing_datetime
        FROM
        base_flight_info
        <where>
            deleted = false and status != 'CAN'  and acdm_deleted = false
            and real_landing_datetime is not null and real_take_off_datetime is not null
            <if test="dto.startTime != null and  dto.endTime != null  ">
                and flight_date BETWEEN #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.airlineCode != null and dto.airlineCode != ''">
                and airline_code  = #{dto.airlineCode}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and flight_no like concat('%',#{dto.flightNo},'%')
            </if>
            <if test="dto.flightTimeStartTime != null  and dto.flightTimeEndTime != null ">
                and ((
                is_arrv = '0'
                AND DATE_FORMAT(
                IF
                ( real_take_off_datetime IS NULL, IF ( predict_take_off_datetime IS NULL, plan_take_off_datetime,
                predict_take_off_datetime ), real_take_off_datetime ),
                '%Y-%m-%d'
                ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                )
                OR (
                is_arrv = '1'
                AND DATE_FORMAT(
                IF
                ( real_landing_datetime IS NULL, IF ( predict_landing_datetime IS NULL, plan_landing_datetime,
                predict_landing_datetime ), real_landing_datetime ),
                '%Y-%m-%d'
                ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                ))
            </if>
        </where>
        GROUP BY
        aircraft_no,
        real_landing_datetime
        HAVING
        COUNT(*) > 1
        ) b
        ON
        a.aircraft_no = b.aircraft_no
        AND a.real_landing_datetime = b.real_landing_datetime
        <where>
            a.deleted = false and a.status != 'CAN'  and a.acdm_deleted = false
            and a.real_landing_datetime is not null and a.real_take_off_datetime is not null
            <if test="dto.startTime != null and  dto.endTime != null  ">
                and a.flight_date BETWEEN #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.airlineCode != null and dto.airlineCode != ''">
                and a.airline_code  = #{dto.airlineCode}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and a.flight_no like concat('%',#{dto.flightNo},'%')
            </if>
            <if test="dto.flightTimeStartTime != null  and dto.flightTimeEndTime != null ">
                and ((
                a.is_arrv = '0'
                AND DATE_FORMAT(
                IF
                ( a.real_take_off_datetime IS NULL, IF ( a.predict_take_off_datetime IS NULL, a.plan_take_off_datetime,
                a.predict_take_off_datetime ), a.real_take_off_datetime ),
                '%Y-%m-%d'
                ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                )
                OR (
                a.is_arrv = '1'
                AND DATE_FORMAT(
                IF
                ( a.real_landing_datetime IS NULL, IF ( a.predict_landing_datetime IS NULL, a.plan_landing_datetime,
                a.predict_landing_datetime ), a.real_landing_datetime ),
                '%Y-%m-%d'
                ) BETWEEN DATE_FORMAT( #{dto.flightTimeStartTime}, '%Y-%m-%d' )
                AND DATE_FORMAT( #{dto.flightTimeEndTime}, '%Y-%m-%d' )
                ))
            </if>
        </where>
    </select>
    <select id="querySimpleFlightDataFromUmeWithDiff" resultType="com.swcares.aiot.core.dto.BaseFlightInfoSimpleDTO">
        (
            SELECT
                e1.id,
                e1.aircraft_no,
                e1.is_arrv arrv_or_dept,
                e1.flight_no,
                e1.flight_date,
                e1.ume_repeat_id,
                concat(e1.aircraft_no, '-' ,e1.is_arrv)  group_column,
                e1.real_landing_datetime real_take_off_and_landing_time,
                TIMESTAMPDIFF(MINUTE, e1.real_landing_datetime, e2.real_landing_datetime)  diff_minute
            FROM base_flight_info e1
            JOIN base_flight_info e2 ON e1.aircraft_no = e2.aircraft_no
                AND e1.id != e2.id
                AND ABS(TIMESTAMPDIFF(MINUTE, e1.real_landing_datetime, e2.real_landing_datetime)) &lt;= 5
            WHERE
                e1.flight_date BETWEEN #{minDate} and #{maxDate}
                and e1.is_arrv = '1' and e2.is_arrv = '1'
                and e1.real_landing_datetime is not null and e2.real_landing_datetime is not null
                and e1.deleted = 0 and e2.deleted = 0
                and e1.acdm_deleted = 0 and e2.acdm_deleted = 0
                and e1.clean_type in ('0','1')  and e2.clean_type  in ('0','1')
            GROUP BY e1.aircraft_no,e1.id,e1.is_arrv
            ORDER BY e1.aircraft_no,e1.real_landing_datetime ASC
        )
        UNION ALL
        (
            SELECT
                e1.id,
                e1.aircraft_no,
                e1.is_arrv arrv_or_dept,
                e1.flight_no,
                e1.flight_date,
                e1.ume_repeat_id,
                concat(e1.aircraft_no, '-' ,e1.is_arrv)  group_column,
                e1.real_take_off_datetime real_take_off_and_landing_time,
                TIMESTAMPDIFF(MINUTE, e1.real_take_off_datetime, e2.real_take_off_datetime)  diff_minute
            FROM base_flight_info e1
            JOIN base_flight_info e2 ON e1.aircraft_no = e2.aircraft_no
                AND e1.id != e2.id
                AND ABS(TIMESTAMPDIFF(MINUTE, e1.real_take_off_datetime, e2.real_take_off_datetime)) &lt;= 5
                WHERE
                e1.flight_date BETWEEN #{minDate} and #{maxDate}
                and e1.is_arrv = '0' and e2.is_arrv ='0'
                and e1.real_take_off_datetime is not null and e2.real_take_off_datetime is not null
                and e1.deleted = 0  and e2.deleted = 0
                and e1.acdm_deleted= 0 and 	e2.acdm_deleted = 0
                and e1.clean_type  in ('0','1','2') and e2.clean_type  in ('0','1','2')
            GROUP BY e1.aircraft_no,e1.id
            ORDER BY e1.aircraft_no,e1.real_take_off_datetime ASC
        )
    </select>

    <select id="querySimpleFlightByUmeRepeatId" resultType="com.swcares.aiot.core.dto.BaseFlightInfoSimpleDTO">
        SELECT
            e1.id,
            e1.aircraft_no,
            e1.is_arrv arrv_or_dept,
            e1.flight_no,
            e1.flight_date,
            e1.ume_repeat_id,
            concat(e1.aircraft_no, '-' ,e1.is_arrv)  group_column,
            e1.real_take_off_datetime real_take_off_and_landing_time,
            TIMESTAMPDIFF(MINUTE, e1.real_take_off_datetime, e2.real_take_off_datetime)  diff_minute
        FROM base_flight_info e1
            JOIN base_flight_info e2 ON e1.aircraft_no = e2.aircraft_no
            AND e1.id != e2.id
            AND ABS(TIMESTAMPDIFF(MINUTE, e1.real_take_off_datetime, e2.real_take_off_datetime)) &lt;= 5
        WHERE
            <if test="umeRepeatIdList != null and umeRepeatIdList.size() != 0">
                e1.id in
                <foreach collection="umeRepeatIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            and e1.is_arrv = '0' and e2.is_arrv ='0'
            and e1.real_take_off_datetime is not null and e2.real_take_off_datetime is not null
            and e1.deleted = 0  and e2.deleted = 0
            and e1.acdm_deleted= 0 and 	e2.acdm_deleted = 0
            and e1.clean_type  in ('0','1','2') and e2.clean_type  in ('0','1','2')
            GROUP BY e1.aircraft_no,e1.id
            ORDER BY e1.aircraft_no,e1.real_take_off_datetime ASC
    </select>

    <select id="flightPage" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
            <include refid="base_flight_info_column_api_page"/>
        FROM
            base_flight_info bfi
            LEFT JOIN base_flight_cargo bfc
                ON bfi.id = bfc.base_flight_id
                AND bfc.deleted = 0
            LEFT JOIN base_flight_traveler bft
                ON bfi.id = bft.base_flight_id
                AND bft.deleted = 0
        WHERE
            bfi.deleted = 0
            <if test="query.startTime != null and  query.endTime != null  ">
                and bfi.flight_date BETWEEN #{query.startTime} and #{query.endTime}
            </if>
            <if test="query.airlineCode != null and query.airlineCode != ''">
                and bfi.airline_code = #{query.airlineCode}
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                and bfi.flight_no like concat('%',#{query.flightNo},'%')
            </if>
            <if test="query.updatedTime != null ">
                and bfi.updated_time &gt;= #{query.updatedTime}
            </if>
        ORDER BY bfi.updated_time ASC
    </select>

    <select id="getByGtUpdatedTime" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
            <include refid="base_flight_info_column_stay_time"/>,
            IF(is_arrv = '0',
                IF(real_take_off_datetime IS NULL,
                    IF(predict_take_off_datetime IS NULL,
                    plan_take_off_datetime, predict_take_off_datetime), real_take_off_datetime)
            ,
                IF(real_landing_datetime IS NULL,
                    IF(predict_landing_datetime IS NULL,
                    plan_landing_datetime, predict_landing_datetime), real_landing_datetime)
                    ) AS flightTime
        FROM
            base_flight_info
        WHERE deleted = 0
            AND updated_time <![CDATA[ > ]]> #{startTime}
    </select>

    <select id="getPreorderFlight" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
            <include refid="base_flight_info_column_stay_time"/>,
            (IF(real_landing_datetime IS NULL,
                IF(predict_landing_datetime IS NULL,
                plan_landing_datetime, predict_landing_datetime), real_landing_datetime)
            ) AS flightTime
        FROM
            base_flight_info
        WHERE deleted = 0
            AND status != 'CAN'
            AND clean_type NOT IN ('2','4')
            AND acdm_deleted != 1
            <if test="aircraftNo != null and aircraftNo != ''">
                AND aircraft_no = #{aircraftNo}
            </if>
            <if test="airportCode != null and airportCode != ''">
                AND airport_code = #{airportCode}
            </if>
            <if test="id != null">
                AND id != #{id}
            </if>
            <if test="startFlightDate != null and endFlightDate != null">
                AND flight_date between #{startFlightDate} and #{endFlightDate}
            </if>
            <if test="flightTime != null">
                AND IF(real_landing_datetime IS NULL,
                        IF(predict_landing_datetime IS NULL,
                            plan_landing_datetime, predict_landing_datetime), real_landing_datetime)
                            <![CDATA[ <= ]]> #{flightTime}
            </if>
            ORDER BY (IF(real_landing_datetime IS NULL,
                        IF(predict_landing_datetime IS NULL,
                            plan_landing_datetime, predict_landing_datetime), real_landing_datetime)
            ) DESC  limit 5
    </select>

    <select id="getPrologueFlight" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
        <include refid="base_flight_info_column_stay_time"/>,
            (IF(real_take_off_datetime IS NULL,
                    IF(predict_take_off_datetime IS NULL,
                    plan_take_off_datetime, predict_take_off_datetime), real_take_off_datetime)
            ) AS flightTime
        FROM
            base_flight_info
        WHERE deleted = 0
            AND status != 'CAN'
            AND clean_type NOT IN ('2','4')
            AND acdm_deleted != 1
            <if test="aircraftNo != null and aircraftNo != ''">
                AND aircraft_no = #{aircraftNo}
            </if>
            <if test="airportCode != null and airportCode != ''">
                AND airport_code = #{airportCode}
            </if>
            <if test="id != null">
                AND id != #{id}
            </if>
            <if test="startFlightDate != null and endFlightDate != null">
                AND flight_date between #{startFlightDate} and #{endFlightDate}
            </if>
            <if test="flightTime != null">
                AND IF(real_take_off_datetime IS NULL,
                    IF(predict_take_off_datetime IS NULL,
                        plan_take_off_datetime, predict_take_off_datetime), real_take_off_datetime)
                        <![CDATA[ >= ]]> #{flightTime}
            </if>
            ORDER BY (IF(real_take_off_datetime IS NULL,
                        IF(predict_take_off_datetime IS NULL,
                        plan_take_off_datetime, predict_take_off_datetime), real_take_off_datetime)
            ) ASC limit 5
    </select>

    <select id="getFlightInfoByStayTimeAndRegNo" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
            <include refid="base_flight_info_column_stay_time"/>,
            IF(is_arrv = '0',
                IF(real_take_off_datetime IS NULL,
                    IF(predict_take_off_datetime IS NULL,
                    plan_take_off_datetime, predict_take_off_datetime), real_take_off_datetime)
            ,
                IF(real_landing_datetime IS NULL,
                    IF(predict_landing_datetime IS NULL,
                    plan_landing_datetime, predict_landing_datetime), real_landing_datetime)
            ) AS flightTime
        FROM
            base_flight_info
        WHERE deleted = 0
            <if test="aircraftNo != null and aircraftNo != ''">
                AND aircraft_no = #{aircraftNo}
            </if>
            <if test="airportCode != null and airportCode != ''">
                AND airport_code = #{airportCode}
            </if>
            <if test="baseFlightIdList != null and baseFlightIdList.size() != 0">
                AND id in
                <foreach collection="baseFlightIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            limit 1
    </select>

    <select id="getStayTimeDataToLastUpdateTime" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
            <include refid="base_flight_info_column_stay_time_data_push"/>,
            fie.stay_start_time,
            fie.stay_end_time,
            fie.stay_time
        FROM
            base_flight_info_ext fie
            LEFT JOIN base_flight_info fi
                ON fi.id = fie.base_flight_id
        WHERE
            fie.updated_time BETWEEN #{date}
            AND ( SELECT max( updated_time ) FROM base_flight_info_ext )
    </select>

    <select id="getFlightInfoById" resultType="com.swcares.aiot.vo.FlightInfoVO">
        SELECT
            <include refid="base_flight_info_column_transform_acdm_deleted"/>
        FROM
            base_flight_info
        WHERE
            id = #{baseFlightId}
    </select>

</mapper>