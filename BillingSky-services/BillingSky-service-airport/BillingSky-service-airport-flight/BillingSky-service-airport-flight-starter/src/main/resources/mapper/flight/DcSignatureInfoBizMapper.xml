<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.parse.mapper.DcSignatureInfoBizMapper">

    <sql id="sign_data_column">
        dsi.id, dsi.item_id, dsi.flight_source_id, dsi.flight_busines_id, dsi.type, dsi.sign_id, dsi.arrive_flight_id, dsi.arrival_flight_no,
        dsi.arrival_flight_date, dsi.arrival_take_off_airport, dsi.arrival_landing_airport, dsi.departure_flight_id, dsi.departure_flight_no,
        dsi.departure_flight_date, dsi.departure_take_off_airport, dsi.departure_landing_airport, dsi.aircraft_parking, dsi.sign_url,
        dsi.sign_status, dsi.item_name, dsi.item_code, dsi.start_time, dsi.end_time, dsi.item_type, dsi.times, dsi.selector_info, dsi.unit, dsi.bridge_type,
        dsi.sign_type, dsi.report_by, dsi.report_time, dsi.use_duration, dsi.deleted, dsi.created_by, dsi.created_time, dsi.updated_by, dsi.updated_time,
        dsi.airport_code, dsi.data_source_type, dsi.other, dsi.business_delete, dsi.batch_no
    </sql>

    <select id="getStayTimeDataToLastUpdateTime" resultType="com.swcares.aiot.vo.DcSignatureInfoVo">
        SELECT
            <include refid="sign_data_column"/>,
            bfi.id AS baseFlightId
        FROM
            dc_signature_info dsi
            LEFT JOIN base_flight_info bfi
                ON dsi.flight_busines_id = bfi.flight_busines_id
                AND bfi.deleted = false
        WHERE
            dsi.updated_time BETWEEN #{date}
            AND (SELECT max(updated_time) FROM dc_signature_info where flight_busines_id is not null)
            AND dsi.flight_busines_id is not null
            ORDER BY dsi.updated_time;
    </select>

    <select id="getListByUpdateDate" resultType="com.swcares.aiot.vo.DcSignatureInfoVo">
        SELECT
        <include refid="sign_data_column"/>,
        bfi.id AS baseFlightId
        FROM
        dc_signature_info dsi
        LEFT JOIN base_flight_info bfi
        ON dsi.flight_busines_id = bfi.flight_busines_id
        AND bfi.deleted = false
        WHERE
        dsi.updated_time BETWEEN CONCAT(#{startTime}, ' ', '00:00:00')  AND CONCAT(#{startTime} , ' ', '23:59:59')
        AND dsi.flight_busines_id is not null
        ORDER BY dsi.updated_time;
    </select>

</mapper>
