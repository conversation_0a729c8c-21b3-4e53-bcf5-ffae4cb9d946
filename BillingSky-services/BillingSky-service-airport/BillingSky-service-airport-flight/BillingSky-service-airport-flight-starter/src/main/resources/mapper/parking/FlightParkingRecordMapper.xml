<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.parking.mapper.FlightParkingRecordMapper">

    <select id="page" resultType="com.swcares.aiot.core.vo.FlightParkingRecordVO">

        select * from asa_flight_parking_record
        <where>
        <if test="dto.fkFlightId !=null and dto.fkFlightId !='' ">
            and fk_flight_no = #{dto.fkFlightId}
        </if>
            and deleted = 0
        </where>
        order by distribute_time desc
    </select>

    <select id="list" resultType="com.swcares.aiot.core.vo.FlightParkingRecordVO">
        select * from asa_flight_parking_record
        <where>
            and deleted = 0
        </where>
    </select>

    <select id="getTheNewRecord" resultType="com.swcares.aiot.core.vo.FlightParkingRecordVO">
            SELECT
            * FROM asa_flight_parking_record AS re
            WHERE
            distribute_time =(SELECT MAX(distribute_time) as distribute_time FROM
            asa_flight_parking_record AS rec
            where deleted=0 and fk_flight_no=#{flightInfoId}
            GROUP BY rec.fk_flight_no)  AND fk_flight_no=#{flightInfoId}
    </select>

    <select id="getTheNewRecordList" resultType="com.swcares.aiot.core.vo.FlightParkingRecordVO">
        SELECT id,fk_flight_no,flight_parking,distribute_by,MAX(distribute_time) as distribute_time FROM
            asa_flight_parking_record AS rec
        where deleted=0
        <if test="startTime!=null">
            and plan_landing_datetime &gt;= #{startTime}
        </if>
        <if test="endTime!=null">
            and plan_landing_datetime &lt;= #{endTime}
        </if>
        GROUP BY rec.fk_flight_no
    </select>

    <select id="getTheNewRecordPageList" resultType="com.swcares.aiot.core.vo.FlightParkingRecordVO">
        WITH latest_records AS (
            SELECT
                arr_dep_flight_no,
                air_line,
                aircraft_no,
                aircraft_model,
                flight_date_time,
                id,
                fk_flight_no,
                distribute_by,
                distribute_time,
                remarks,
                update_reason,
                flight_parking,
                ROW_NUMBER() OVER (PARTITION BY arr_dep_flight_no ORDER BY id DESC) AS rn
            FROM
                asa_flight_parking_record
            WHERE
                deleted = 0
        <if test="dto.startTime!=null">
            and flight_date_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null">
            and flight_date_time &lt;= #{dto.endTime}
        </if>
        )
        SELECT
            arr_dep_flight_no,
            air_line,
            aircraft_no,
            aircraft_model,
            flight_date_time,
            id,
            fk_flight_no,
            distribute_by,
            distribute_time,
            remarks,
            update_reason,
            flight_parking
        FROM
        latest_records
        WHERE
        rn = 1
        ORDER BY
        distribute_time DESC, id DESC
    </select>


</mapper>
