<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.upgrade.mapper.MobileTerminalAutoUpdateVersionsMapper">

    <select id="page" resultType="com.swcares.aiot.core.vo.MobileTerminalAutoUpdateVersionsVO">
        select * from mobile_terminal_auto_update_versions
        <where>
            and deleted = 0
        </where>
    </select>

    <select id="list" resultType="com.swcares.aiot.core.vo.MobileTerminalAutoUpdateVersionsVO">
        select * from mobile_terminal_auto_update_versions
        <where>
            and deleted = 0
            <if test="dto.operatingSystem != null and dto.operatingSystem != ''">
                and operating_system = #{dto.operatingSystem}
            </if>
            <if test="dto.appId != null and dto.appId != ''">
                and app_id = #{dto.appId}
            </if>
            <if test="dto.versionCode != null and dto.versionCode != ''">
                and version_code = #{dto.versionCode}
            </if>

            <if test="dto.versionName != null and dto.versionName != ''">
                and version_name like concat('%',#{dto.versionName},'%')
            </if>
            <if test="dto.environment != null and dto.environment != ''">
                and environment = #{dto.environment}
            </if>
        </where>
    </select>

    <select id="getNowVersionInfo" resultType="com.swcares.aiot.core.vo.MobileTerminalAutoUpdateVersionsVO">
        select * from mobile_terminal_auto_update_versions
        <where>
            and deleted = 0
            <if test="dto.operatingSystem != null and dto.operatingSystem != ''">
                and operating_system = #{dto.operatingSystem}
            </if>
            <if test="dto.appId != null and dto.appId != ''">
                and app_id = #{dto.appId}
            </if>
            <if test="dto.versionCode != null and dto.versionCode != ''">
                and version_code = #{dto.versionCode}
            </if>

            <if test="dto.versionName != null and dto.versionName != ''">
                and version_name like concat('%',#{dto.versionName},'%')
            </if>
            <if test="dto.environment != null and dto.environment != ''">
                and environment = #{dto.environment}
            </if>

        </where>
        order by version_code desc,updated_time desc limit 1;

    </select>

</mapper>
