<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.parking.mapper.FlightParkingManagerOperationLogMapper">

    <select id="page" resultType="com.swcares.aiot.core.vo.FlightParkingManagerOperationLogVO">
      SELECT b.operation_name,b.operation_content,b.updated_by,b.updated_time
	        from asa_flight_parking_manager a
		        left join asa_flight_parking_manager_operation_log b
			        on a.id = b.fk_flight_manager
			            <where>
                            b.fk_flight_manager = #{dto.fkFlightManager}
                                and b.deleted=0
                        </where>
        order by updated_time desc
    </select>

    <select id="list" resultType="com.swcares.aiot.core.vo.FlightParkingManagerOperationLogVO">
        SELECT b.operation_name,b.operation_content,b.updated_by,b.updated_time
        from asa_flight_parking_manager a
        left join asa_flight_parking_manager_operation_log b
        on a.id = b.fk_flight_manager
        <where>
            b.fk_flight_manager = #{b.fk_flight_manager}
            and b.deleted=0
        </where>
    </select>

</mapper>
