<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.business.flight.mapper.BaseFlightTravelerSegmentMapper">
    <sql id = "base_column_list">
        id,flight_date,flight_no,land_flag,flight_segment,base_flight_id,business_class_num,first_class_num,economy_class_num,
        diplomatic_passport_traveler_num,cardholders_traveler_num,cardholders_accompany_traveler_num,important_traveler_num,
        important_accompany_traveler_num,adult_num,child_num,infant_num,transit_adult_num,transit_child_num,transit_infant_num,
        adult_flag,child_flag,infant_flag,transit_adult_flag,transit_child_flag,transit_infant_flag,psgr_plf,traveler_throughput,
        traveler_num,psgr_total_kg,airport_code
    </sql>

    <select id="listByFlightId" resultType="com.swcares.aiot.core.vo.BaseFlightTravelerSegmentVO">
        select
            <include refid="base_column_list"/>,
            (CASE WHEN updated_by is null
                    OR updated_by = 'null'
                    OR updated_by = 'DCS_AUTO_SYNC_PROG'
                THEN '自动采集'
                ELSE '手动添加' END )                                             AS dataSources,
            (CASE WHEN updated_by is null
                    OR updated_by = 'null'
                    OR updated_by = 'DCS_AUTO_SYNC_PROG'
                THEN 'PIC'
                ELSE updated_by END )                                            AS updatedBy
        from
            base_flight_traveler_segment
        <where>
            and deleted = 0 and base_flight_id=#{flightId}
        </where>
        order by flight_date desc,id desc
    </select>

    <select id="getDataToLastUpdateTime" resultType="com.swcares.aiot.core.entity.BaseFlightTravelerSegment">
        SELECT
            *
        FROM
            base_flight_traveler_segment
        WHERE
            updated_time >= #{date}
    </select>

    <update id="deleteByBaseFlightID">
        update base_flight_traveler_segment
        set deleted ='1'
        where base_flight_id = #{flightId}
    </update>

    <update id="confirm">
        update base_flight_traveler_segment set confirm=1 where base_flight_id in (select DISTINCT base_flight_id from base_flight_traveler where id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
        and (confirm=0 or confirm=2 or confirm is null)
    </update>
</mapper>
