<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.swcares.aiot</groupId>
        <artifactId>BillingSky-service-airport-flight</artifactId>
        <version>2.39.0-SNAPSHOT</version>
    </parent>
    <artifactId>BillingSky-service-airport-flight-starter</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-modules-code-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-override-baseframe</artifactId>
        </dependency>
        <!-- 基础框架 -->
        <dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>base-frame-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-oauth2</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-pool2</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-auditlog</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!--swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!--rabbitmq-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-synergy-sensor-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-metrics-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.prometheus</groupId>
                    <artifactId>simpleclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-flight-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-flight-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-uc-extend-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-message-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-nodeGuard-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.worm</groupId>
            <artifactId>swagger3-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.worm</groupId>
            <artifactId>worm-toolkit-hutool</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-synergy-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-airport-commonplus-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-mq-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-common-airport-mq-message</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-dictionary-cfg</artifactId>
        </dependency>
        <!--   openTelemetry     -->
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-tracelog-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-file-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-data-adapter-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-pool2</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.11.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.swcares.aiot</groupId>-->
<!--            <artifactId>BillingSky-module-dataPermission-starter</artifactId>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
