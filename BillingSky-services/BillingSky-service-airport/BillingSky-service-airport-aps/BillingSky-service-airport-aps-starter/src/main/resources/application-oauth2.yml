swcares:
  oauth2:
    auth-server-enable: false
    resource-server-enable: true
    token-store-type: redis
    token-service-type: redis
    front-url:
    free-access-path: /actuator/**,/doc.html,/swagger*,/swagger*/**,/v3/**,/v2/**,/webjars/**,/3p/api/receive
    login-access-path: /**
    client-id: aps
    client-secret: 123456
    token-info-uri: ${UC_ADDRESS}/oauth/check_token
    oauth-token-uri: ${UC_ADDRESS}/oauth/token