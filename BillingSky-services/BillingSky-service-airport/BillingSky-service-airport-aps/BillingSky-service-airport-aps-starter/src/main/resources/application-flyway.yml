spring:
  flyway:
    enabled: ${FLYWAY_TOGGLE}
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration/system
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 是否允许迁移乱序运行 true 允许
    out-of-order: false
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # 是否禁用数据库清理功能。
    clean-disabled: true
    # 基础版本号
    baseline-version: 2.5.6.0.20240708
    # 在读取架构历史记录表时，是否忽略缺失的迁移。 false 不忽略
    ignore-missing-migrations: false

