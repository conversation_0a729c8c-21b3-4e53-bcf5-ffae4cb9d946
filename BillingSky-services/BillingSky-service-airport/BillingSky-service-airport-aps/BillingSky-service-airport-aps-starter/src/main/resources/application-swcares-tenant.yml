swcares:
  tenant:
    multi-ds-enable: false
    #开启多租户
    enable: true
    #子系统编号，与sys_feature_resource表中字段对应
    subsystem-code: aps-web
    #子系统域名、数据源缓存时间,单位：分钟
    cache-expire-minutes: 30
    #是否通过redis请求获取缓存，否则通过http,默认redis
    redis-cache: true
    #redis获取缓存失败一定次数后，通过HTTP访问UC接口获取并缓存
    redis-cache-lose-count: 10
    #是否开启租户库的flyway
    flyway-enable: ${FLYWAY_TOGGLE}
    #租户库的flyway脚本地址
    flyway-locations: classpath:db/migration/tenant
