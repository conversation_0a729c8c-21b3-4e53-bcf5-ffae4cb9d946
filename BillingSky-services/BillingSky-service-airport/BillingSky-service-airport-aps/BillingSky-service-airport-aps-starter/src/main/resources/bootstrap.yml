spring:
  application:
    name: aps
    description: 机场结算服务
  cloud:
    ########################################## Spring nacos 配置  ###################################
    nacos:
      server-addr: ${NACOS_HOST}:${NACOS_PORT}
      discovery:
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_GROUP}
        register-enabled: true
        username: ${NACOS_USERNAME}
        password: ${NACOS_PASSWORD}