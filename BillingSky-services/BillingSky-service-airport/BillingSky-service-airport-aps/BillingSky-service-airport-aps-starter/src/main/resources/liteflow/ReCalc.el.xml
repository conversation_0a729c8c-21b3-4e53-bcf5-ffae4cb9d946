<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="apsCalcBill">
    THEN(
        //获取航班信息
        CmpCalcGetFlightInfoMap,
        //获取业务保障数据
        CmpCalcGetServiceRecordMap,
        // 航司对应计算需要指标项list的map
        CmpCalcGetCustomerIndicatMap,
        //获取指标项字典
        CmpCalcGetIndicatorDictMap,
        // 组装请求计算参数
        CmpCalcAssembleDto,
        // 调用计算引擎，生成账单
        CmpCalcGetFlightBills,
        //保存账单
        CmpCalcSaveFlightBills,
        //生成航司账单
        CmpCalcSaveAirlineBills
    )
    </chain>
</flow>