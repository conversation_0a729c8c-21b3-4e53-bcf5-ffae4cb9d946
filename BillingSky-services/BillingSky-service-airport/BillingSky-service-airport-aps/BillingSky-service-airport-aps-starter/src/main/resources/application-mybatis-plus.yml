mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.swcares.**.entity
  type-enums-package: com.swcares.**.enums
  configuration:
    log-impl: ${MYBATIS_LOG_IMPL}
  global-config:
    banner: false
    db-config:
      #主键类型  AUTO:"数据库ID自增",NONE:"不设置主键策略",INPUT:"插入前自行设置主键值（多用于通过数据库主键序列生成ID）",ASSIGN_ID:"全局唯一数字类型ID (默认采用雪花算法)", ASSIGN_UUID:"全局唯一ID UUID";
      id-type: ASSIGN_ID
      # 逻辑删除配置
      logic-delete-field: deleted