<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.swcares.aiot.mapper.FlightInfoManageBizMapper">
    <select id="retrieveAirlineSettleCodes" resultType="java.lang.String">
        SELECT DISTINCT (tai.settle_code) AS settle_code
        FROM t_flight_info tfi
                 LEFT JOIN t_aircraft_info tai ON tfi.reg_no = tai.reg_no
        <where>
            tai.settle_code IS NOT NULL
        </where>
    </select>
</mapper>