<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.ServiceRecordMapper">

    <select id="exportFlightBusinessData" resultType="com.swcares.aiot.core.model.vo.FlightBusinessDataExcelVO">
        select tfi.airport_code as airportCode,
        tfi.flight_date,
        tfi.flight_no,
        tfi.reg_no,
        tfi.flight_segment,
        tfi.flight_flag,
        tsr.service_name,
        tsr.start_time,
        tsr.end_time,
        tsr.used_number as usedNumberDB
        from t_flight_info as tfi
            left join t_service_record as tsr on tsr.flight_id = tfi.id and tsr.invalid='1'
        <where>
            tfi.invalid='1'
            <if test="airportCode != null and airportCode != ''">
                and tfi.airport_code = #{airportCode}
            </if>
            <if test="startDate != null and endDate != null ">
                and tfi.flight_date between #{startDate} and #{endDate}
            </if>
            <if test="airlineCode != null and airlineCode != '' ">
                and tfi.airline_code = #{airlineCode}
            </if>
            <if test="flightNo != null and flightNo != '' ">
                and tfi.flight_no = #{flightNo}
            </if>
            <if test="flightFlag != null and flightFlag != '' ">
                and tfi.flight_flag = #{flightFlag}
            </if>
        </where>
    </select>
</mapper>
