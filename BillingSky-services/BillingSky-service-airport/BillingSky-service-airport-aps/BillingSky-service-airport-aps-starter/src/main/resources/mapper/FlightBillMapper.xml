<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.FlightBillMapper">

    <select id="pageBillCountByCondition" resultType="com.swcares.aiot.core.model.entity.FlightBill">
        select tfb.* from t_flight_bill as tfb
        <where>
            tfb.invalid = '1'
            <if test="startDate != null and endDate != null">
                and tfb.settle_month between #{startDate} and #{endDate}
            </if>
            <if test="airportCode != null and airportCode != ''">
                and tfb.airport_code=#{airportCode}
            </if>
            <if test="choseFeeInfos != null and choseFeeInfos.size()>0">
                and tfb.fee_code in
                <foreach collection="choseFeeInfos" item="feeCode" index="index" open="(" separator="," close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tfb.fee_code in (
                    </if>#{feeCode}
                </foreach>
            </if>
            <if test="settleCodeList != null and settleCodeList.size()>0">
                and tfb.settle_code in
                <foreach collection="settleCodeList" item="settleCode" index="index" open="(" separator="," close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tfb.settle_code in (
                    </if>#{settleCode}
                </foreach>
            </if>
            <if test="fromAirportCode != null and fromAirportCode != ''">
                and tfb.from_airport_code = #{fromAirportCode}
            </if>
            <if test="toAirportCode != null and toAirportCode != ''">
                and tfb.to_airport_code = #{toAirportCode}
            </if>
            <if test="flightNo != null and flightNo != ''">
                and tfb.flight_no = #{flightNo}
            </if>
            <if test="regNo != null and regNo != ''">
                and tfb.reg_no = #{regNo}
            </if>
            <if test="flightNo != null and flightNo != ''">
                and tfb.flight_no = #{flightNo}
            </if>
            <if test="flightFlag!=null and flightFlag != ''">
                and tfb.flight_flag &lt;= #{flightFlag}
            </if>
            <if test="submit != null and submit != ''">
                and tfb.submit=#{submit}
            </if>
            <if test="revocation != null and submit != ''">
                and tfb.revocation=#{revocation}
            </if>
        </where>
            ORDER BY
            tfb.settle_month,tfb.id asc
    </select>

    <select id="listFeeBillInfoByCondition" resultType="com.swcares.aiot.core.model.vo.FeeBillExcelVO">
        select tfb.fee_name as feeName,
               GROUP_CONCAT(distinct tfb.airline_short_name)as airlineShortName,
               SUM(tfb.charge_price) as totalSettleAmount
        from t_flight_bill as tfb USE INDEX(idx_combine)
        <where>
            tfb.invalid = '1'
            <if test="sDate != null and eDate != null">
                and tfb.settle_month between #{sDate} and #{eDate}
            </if>
            <if test="airportCode != null and airportCode != ''">
                and tfb.airport_code=#{airportCode}
            </if>
            <if test="airlineShortName != null and airlineShortName != ''">
                and tfb.airline_short_name=#{airlineShortName}
            </if>
            <if test="feeName != null and feeName != ''">
                and tfb.fee_name=#{feeName}
            </if>
        </where>
        group by tfb.fee_name
        order by tfb.fee_name
    </select>
</mapper>
