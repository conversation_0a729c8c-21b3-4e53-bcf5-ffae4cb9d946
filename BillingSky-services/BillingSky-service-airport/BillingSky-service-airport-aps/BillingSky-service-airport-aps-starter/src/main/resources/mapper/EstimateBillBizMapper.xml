<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.EstimateBillBizMapper">

    <select id="page" resultType="com.swcares.aiot.vo.EstimateBillPageVo">
        select
        *
        from
        estimate_bill eb
        where eb.deleted = 0
        <if test="form.status != null">
            and eb.status = #{form.status}
        </if>
        <if test="form.paymentPeriod != null and form.paymentPeriod != ''">
            and eb.payment_period like concat ('%', #{form.paymentPeriod}, '%')
        </if>
        order by field(status, 8, 3) desc, operate_last_time desc
    </select>
</mapper>
