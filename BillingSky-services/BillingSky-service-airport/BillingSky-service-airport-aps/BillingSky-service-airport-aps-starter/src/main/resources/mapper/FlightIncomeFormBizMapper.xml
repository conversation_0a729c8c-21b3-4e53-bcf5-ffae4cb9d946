<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.FlightIncomeFormBizMapper">

    <select id="page" resultType="com.swcares.aiot.core.model.vo.FlightIncomeFormVO">
        SELECT
            tfb.flight_date,
            tfb.airline_short_name,
            tai.airline_code,
            tfb.settle_code,
            TRUNCATE(sum(tfb.charge_price), 2) chargePrice
        FROM t_flight_bill tfb
            LEFT JOIN
            t_aircraft_info tai
            ON tfb.reg_no = tai.reg_no
            AND tai.invalid = '1'
            AND now() BETWEEN tai.start_date and tai.end_date
        WHERE tfb.invalid = '1'
            AND tfb.submit = '1'
            <if test="query.startFlightDate != null and query.startFlightDate != null ">
                AND tfb.flight_date BETWEEN #{query.startFlightDate} and #{query.endFlightDate}
            </if>
            <if test="query.airlineShortName != null and query.airlineShortName != ''">
                AND tfb.airline_short_name LIKE concat ('%', #{query.airlineShortName},'%')
            </if>
            <if test="query.airlineCode != null and query.airlineCode != ''">
                AND tai.airline_code LIKE concat ('%', #{query.airlineCode},'%')
            </if>
            <if test="query.settleCode != null and query.settleCode != ''">
                AND tfb.settle_code LIKE concat ('%', #{query.settleCode},'%')
            </if>
            GROUP BY tfb.flight_date, tfb.airline_short_name, tfb.settle_code
            ORDER BY tfb.flight_date ASC
    </select>

    <select id="incomeCount" resultType="java.lang.String">
        SELECT
            TRUNCATE(sum(tfb.charge_price), 2)
        FROM t_flight_bill tfb
            LEFT JOIN
            t_aircraft_info tai
            ON tfb.reg_no = tai.reg_no
            AND tai.invalid = '1'
            AND now() BETWEEN tai.start_date and tai.end_date
        WHERE tfb.invalid = '1'
            AND tfb.submit = '1'
            <if test="query.startFlightDate != null and query.startFlightDate != null ">
                AND tfb.flight_date BETWEEN #{query.startFlightDate} and #{query.endFlightDate}
            </if>
            <if test="query.airlineShortName != null and query.airlineShortName != ''">
                AND tfb.airline_short_name LIKE concat ('%', #{query.airlineShortName},'%')
            </if>
            <if test="query.airlineCode != null and query.airlineCode != ''">
                AND tai.airline_code LIKE concat ('%', #{query.airlineCode},'%')
            </if>
            <if test="query.settleCode != null and query.settleCode != ''">
                AND tfb.settle_code LIKE concat ('%', #{query.settleCode},'%')
            </if>
    </select>

    <select id="exportFlightIncomeForm" resultType="com.swcares.aiot.core.model.vo.FlightIncomeFormExcelVO">
        SELECT
            DATE_FORMAT(tfb.flight_date, '%Y-%m-%d') flightDate,
            tfb.airline_short_name,
            tai.airline_code,
            tfb.settle_code,
            TRUNCATE(sum(tfb.charge_price), 2) chargePrice
        FROM t_flight_bill tfb
            LEFT JOIN
            t_aircraft_info tai
            ON tfb.reg_no = tai.reg_no
            AND tai.invalid = '1'
            AND now() BETWEEN tai.start_date and tai.end_date
        WHERE tfb.invalid = '1'
            AND tfb.submit = '1'
            <if test="query.startFlightDate != null and query.startFlightDate != null ">
                AND tfb.flight_date BETWEEN #{query.startFlightDate} and #{query.endFlightDate}
            </if>
            <if test="query.airlineShortName != null and query.airlineShortName != ''">
                AND tfb.airline_short_name LIKE concat ('%', #{query.airlineShortName},'%')
            </if>
            <if test="query.airlineCode != null and query.airlineCode != ''">
                AND tai.airline_code LIKE concat ('%', #{query.airlineCode},'%')
            </if>
            <if test="query.settleCode != null and query.settleCode != ''">
                AND tfb.settle_code LIKE concat ('%', #{query.settleCode},'%')
            </if>
            GROUP BY tfb.flight_date, tfb.airline_short_name, tfb.settle_code
            ORDER BY tfb.flight_date ASC
    </select>

</mapper>
