<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.FeeBillMapper">
    <resultMap id="FeeBillVoResultMap" type="com.swcares.aiot.core.model.vo.FeeBillVO">
        <result column="feeCode" property="feeCode"/>
        <result column="feeName" property="feeName"/>
        <result column="airlineCode" property="airlineCode"/>
        <result column="totalSettleAmount" property="totalSettleAmount"/>
        <result column="afterTaxAmount" property="afterTaxAmount"/>
    </resultMap>
    <select id="pageFeeBillInfoByCondition" resultMap="FeeBillVoResultMap">
        SELECT tfb.fee_code                                       as feeCode,
               tfb.fee_name                                       AS feeName,
               GROUP_CONCAT(DISTINCT tai.airline_code)            AS airlineCode,
               SUM(tfb.charge_price)                              AS totalSettleAmount,
               SUM(tfb.charge_price *100 / (100 + tab.tax_rate) ) AS afterTaxAmount
        FROM
        t_airline_bill_new tab
            LEFT JOIN t_flight_bill AS tfb USE INDEX (idx_combine) ON tfb.settle_code = tab.settle_code
        <choose>
            <when test="dateType == 1">
                AND tab.settle_month = DATE_FORMAT(tfb.flight_date, '%Y-%m')
            </when>
            <otherwise>
                AND tab.settle_month = DATE_FORMAT(tfb.flight_time, '%Y-%m')
            </otherwise>
        </choose>
        AND tab.invalid = '1'
            AND (tab.date_type IS NULL OR tab.date_type = #{dateType})
        LEFT JOIN (
        SELECT settle_code, airline_code
        FROM t_airline_info where invalid = '1'
        GROUP BY settle_code  -- 去重逻辑
        ) tai ON tfb.settle_code = tai.settle_code
        <where>
            AND tfb.airport_code = #{dto.airportCode}
            <if test="dto.startDate != null and dto.endDate != null">
                AND tfb.flight_date BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
            <if test="dto.flightTimeStartDate != null and dto.flightTimeEndDate != null">
                AND tfb.flight_time BETWEEN #{dto.flightTimeStartDate} AND #{dto.flightTimeEndDate}
            </if>
            AND tfb.invalid = '1'
            <if test="dto.feeCodeList!=null and dto.feeCodeList.size() > 0">
                and tfb.fee_code in
                <foreach collection="dto.feeCodeList" item="feeCode" index="index" open="(" separator="," close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tfb.fee_code in (
                    </if>#{feeCode}
                </foreach>
            </if>
            <if test="dto.airlineCodeList != null and dto.airlineCodeList.size() > 0">
                AND tai.airline_code in
                <foreach collection="dto.airlineCodeList" item="airlineCode" index="index" open="(" separator=","
                         close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tai.airline_code in (
                    </if>#{airlineCode}
                </foreach>
            </if>
        </where>
        GROUP BY tfb.fee_code
        ORDER BY tfb.fee_code
    </select>

    <select id="countTotal" resultType="com.swcares.aiot.core.model.vo.FeeBillCountVO">
        SELECT
            SUM(tfb.charge_price) AS totalSettleAmount,
            SUM(tfb.charge_price *100 / (100 + tab.tax_rate) ) AS afterTaxAmount
        FROM t_flight_bill tfb
        LEFT JOIN t_airline_bill_new tab ON
        tfb.settle_code = tab.settle_code
        AND tab.invalid = '1'
        AND (tab.date_type IS NULL OR tab.date_type = #{dateType})
        <choose>
            <when test="dateType == 1">
                AND tab.settle_month = DATE_FORMAT(tfb.flight_date, '%Y-%m')
            </when>
            <otherwise>
                AND tab.settle_month = DATE_FORMAT(tfb.flight_time, '%Y-%m')
            </otherwise>
        </choose>
        LEFT JOIN (
        SELECT settle_code, airline_code
        FROM t_airline_info where invalid = '1'
        GROUP BY settle_code  -- 去重逻辑
        ) tai ON tfb.settle_code = tai.settle_code
        <where>
            tfb.airport_code = #{dto.airportCode}
            AND tfb.invalid = '1'

            <!-- 航班日期条件 -->
            <if test="dto.startDate != null and dto.endDate != null">
                AND tfb.flight_date BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
            <!-- 起降时间条件（带DATE转换） -->
            <if test="dto.flightTimeStartDate != null and dto.flightTimeEndDate != null">
                AND tfb.flight_time BETWEEN #{dto.flightTimeStartDate} AND #{dto.flightTimeEndDate}
            </if>
            <if test="dto.feeCodeList!=null and dto.feeCodeList.size() > 0">
                and tfb.fee_code in
                <foreach collection="dto.feeCodeList" item="feeCode" index="index" open="(" separator="," close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tfb.fee_code in (
                    </if>#{feeCode}
                </foreach>
            </if>
            <if test="dto.airlineCodeList != null and dto.airlineCodeList.size() > 0">
                AND tai.airline_code in
                <foreach collection="dto.airlineCodeList" item="airlineCode" index="index" open="(" separator=","
                         close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tai.airline_code in (
                    </if>#{airlineCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listFeeBillInfoGroupByAirlineCode" resultType="com.swcares.aiot.core.model.vo.FeeBillExportVo">
        SELECT
        tfb.fee_name AS feeName,
        tfb.fee_code AS feeCode,
        LEFT(tfb.flight_no, 2) AS airlineCode,
        SUM(tfb.charge_price) AS totalSettleAmount,
        SUM(tfb.charge_price * 100 /(100 + tab.tax_rate)) AS afterTaxAmount
        FROM t_flight_bill tfb
        LEFT JOIN t_airline_bill_new tab ON
        tfb.settle_code = tab.settle_code
        AND tab.invalid = '1'
        AND (tab.date_type IS NULL OR tab.date_type = #{dateType})
        <choose>
            <when test="dateType == 1">
                AND tab.settle_month = DATE_FORMAT(tfb.flight_date, '%Y-%m')
            </when>
            <otherwise>
                AND tab.settle_month = DATE_FORMAT(tfb.flight_time, '%Y-%m')
            </otherwise>
        </choose>
        LEFT JOIN (
        SELECT settle_code, airline_code
        FROM t_airline_info where invalid = '1'
        GROUP BY settle_code  -- 去重逻辑
        ) tai ON tfb.settle_code = tai.settle_code
        <where>
            tfb.airport_code = #{dto.airportCode}
            AND tfb.invalid = '1'

            <!-- 航班日期条件 -->
            <if test="dto.startDate != null and dto.endDate != null">
                AND tfb.flight_date BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
            <!-- 起降时间条件 -->
            <if test="dto.flightTimeStartDate != null and dto.flightTimeEndDate != null">
                AND tfb.flight_time BETWEEN #{dto.flightTimeStartDate} AND #{dto.flightTimeEndDate}
            </if>

            <if test="dto.startDate != null and dto.endDate != null">
                AND tfb.flight_date BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
            <!-- 起降时间条件（带DATE转换） -->
            <if test="dto.flightTimeStartDate != null and dto.flightTimeEndDate != null">
                AND tfb.flight_time BETWEEN #{dto.flightTimeStartDate} AND #{dto.flightTimeEndDate}
            </if>
            <if test="dto.feeCodeList!=null and dto.feeCodeList.size() > 0">
                and tfb.fee_code in
                <foreach collection="dto.feeCodeList" item="feeCode" index="index" open="(" separator="," close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tfb.fee_code in (
                    </if>#{feeCode}
                </foreach>
            </if>
            <if test="dto.airlineCodeList != null and dto.airlineCodeList.size() > 0">
                AND tai.airline_code in
                <foreach collection="dto.airlineCodeList" item="airlineCode" index="index" open="(" separator=","
                         close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tai.airline_code in (
                    </if>#{airlineCode}
                </foreach>
            </if>
        </where>
        GROUP BY tfb.fee_code, LEFT(tfb.flight_no, 2)
    </select>

    <select id="listFeeBillInfoGroupBySettleCode" resultType="com.swcares.aiot.core.model.vo.FeeBillExportVo">
        SELECT
        tfb.fee_name AS feeName,
        tfb.fee_code AS feeCode,
        tai.settle_code AS settleCode,
        SUM(tfb.charge_price) AS totalSettleAmount,
        SUM(tfb.charge_price * 100 /(100 + tab.tax_rate)) AS afterTaxAmount
        FROM t_flight_bill tfb
        LEFT JOIN t_airline_bill_new tab ON
        tfb.settle_code = tab.settle_code
        AND tab.invalid = '1'
        AND (tab.date_type IS NULL OR tab.date_type = #{dateType})
        <choose>
            <when test="dateType == 1">
                AND tab.settle_month = DATE_FORMAT(tfb.flight_date, '%Y-%m')
            </when>
            <otherwise>
                AND tab.settle_month = DATE_FORMAT(tfb.flight_time, '%Y-%m')
            </otherwise>
        </choose>
        LEFT JOIN (
        SELECT settle_code, airline_code
        FROM t_airline_info where invalid = '1'
        GROUP BY settle_code
        ) tai ON tfb.settle_code = tai.settle_code
        <where>
            tfb.airport_code = #{dto.airportCode}
            AND tfb.invalid = '1'
            <!-- 航班日期条件 -->
            <if test="dto.startDate != null and dto.endDate != null">
                AND tfb.flight_date BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
            <!-- 起降时间条件 -->
            <if test="dto.flightTimeStartDate != null and dto.flightTimeEndDate != null">
                AND tfb.flight_time BETWEEN #{dto.flightTimeStartDate} AND #{dto.flightTimeEndDate}
            </if>
            <if test="dto.feeCodeList!=null and dto.feeCodeList.size() > 0">
                and tfb.fee_code in
                <foreach collection="dto.feeCodeList" item="feeCode" index="index" open="(" separator="," close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tfb.fee_code in (
                    </if>#{feeCode}
                </foreach>
            </if>
            <if test="dto.airlineCodeList != null and dto.airlineCodeList.size() > 0">
                AND tai.airline_code in
                <foreach collection="dto.airlineCodeList" item="airlineCode" index="index" open="(" separator=","
                         close=")">
                    <if test="(index % 999) == 998">
                        NULL)
                        OR tai.airline_code in (
                    </if>#{airlineCode}
                </foreach>
            </if>
        </where>
        GROUP BY tfb.fee_code, tai.settle_code
    </select>
</mapper>
