<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.AirlineBillMapper">

    <sql id="selectAirlineBill">
        select
            tabn.id,
            tabn.settle_month,
            tabn.settle_code,
            tfbt.charge_price as settle_amount,
            tabn.adjust_amount,
            tabn.refuse_amount,
            tabn.tax_rate,
            tfbt.charge_price+tabn.adjust_amount+tabn.refuse_amount as actual_amount,
            tfbt.charge_price+tabn.adjust_amount+tabn.refuse_amount as total_settle_amount,
            tabn.airport_code
        from t_airline_bill_new tabn
            inner join (
        <choose>
            <when test="dateType == 2">
                SELECT
                settle_code,
                sum(ROUND(charge_price,2)) AS charge_price
                FROM t_flight_bill
                WHERE invalid = '1'
                AND airport_code = #{airportCode}
                AND flight_time BETWEEN STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d') AND
                DATE_ADD(LAST_DAY(LAST_DAY((STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d')))), INTERVAL 1 DAY)
                GROUP BY settle_code
            </when>
            <otherwise>
                SELECT
                settle_code,
                SUM(ROUND(charge_price,2)) AS charge_price
                FROM t_flight_bill
                WHERE invalid = '1'
                AND airport_code = #{airportCode}
                AND flight_date BETWEEN STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d') AND
                DATE_ADD(LAST_DAY(STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d')), INTERVAL 1 DAY)
                GROUP BY settle_code
            </otherwise>
        </choose>) tfbt on tabn.settle_code = tfbt.settle_code
        where tabn.settle_month = #{settleMoth}
            and tabn.invalid = '1'
            and tabn.date_type = #{dateType}
            and tabn.airport_code = #{airportCode}
        <if test="settleCodeList!=null and settleCodeList.size() > 0">
            and tabn.settle_code in
            <foreach collection="settleCodeList" item="settleCode" index="index" open="(" separator="," close=")">
                <if test="(index % 999) == 998">
                    NULL)
                    OR tabn.settle_code in (
                </if>#{settleCode}
            </foreach>
        </if>
    </sql>
    <select id="listMissAirlineBillByFlightDate" resultType="com.swcares.aiot.core.model.vo.AirlineBillMissVo">
        select tfbt.flight_date, tfbt.settle_code
        from (select DATE_FORMAT(tfb.flight_date, '%Y-%m') as flight_date, tfb.settle_code
              from t_flight_bill tfb
              where tfb.invalid = '1'
                and tfb.flight_date is not null
                and tfb.airport_code=#{airportCode}
              group by DATE_FORMAT(tfb.flight_date, '%Y-%m'), tfb.settle_code) tfbt
                 left join t_airline_bill_new tabn
                           on tfbt.flight_date = tabn.settle_month and tfbt.settle_code = tabn.settle_code and
                              tabn.invalid = '1' and tabn.date_type='1' and tabn.airport_code=#{airportCode}
        where tabn.id is null
    </select>
    <select id="listMissAirlineBillByFlightTime" resultType="com.swcares.aiot.core.model.vo.AirlineBillMissVo">
        select tfbt.flight_date , tfbt.settle_code
        from (select DATE_FORMAT(tfb.flight_time, '%Y-%m') as flight_date, tfb.settle_code
              from t_flight_bill tfb
              where tfb.invalid = '1'
                and tfb.flight_time is not null
                and tfb.airport_code=#{airportCode}
              group by DATE_FORMAT(tfb.flight_time, '%Y-%m'), tfb.settle_code) tfbt
                 left join t_airline_bill_new tabn
                           on tfbt.flight_date = tabn.settle_month and tfbt.settle_code = tabn.settle_code and
                              tabn.invalid = '1' and tabn.date_type='2' and tabn.airport_code=#{airportCode}
        where tabn.id is null
    </select>

    <select id="listMissAirlineBillByFlightInfoAndDate" resultType="com.swcares.aiot.core.model.vo.AirlineBillMissVo">
        select tfbt.flight_date, tfbt.settle_code
        from (select DISTINCT DATE_FORMAT(tfi.flight_date, '%Y-%m') as flight_date, tal.settle_code
              from t_flight_info tfi
                       INNER JOIN t_aircraft_info tai on tfi.reg_no=tai.reg_no and tfi.flight_date BETWEEN tai.start_date and tai.end_date
                       INNER JOIN t_airline_info tal on tai.airline_short_name=tal.airline_short_name
              where tfi.invalid = '1'
                and tai.invalid='1'
                and tal.invalid='1'
                and tfi.flight_date is not null
                and tfi.airport_code=#{airportCode}
                and tfi.flight_date between #{startDate} and #{endDate}
              group by DATE_FORMAT(tfi.flight_date, '%Y-%m'), tal.settle_code) tfbt
                 left join t_airline_bill_new tabn
                           on tfbt.flight_date = tabn.settle_month and tfbt.settle_code = tabn.settle_code and
                              tabn.invalid = '1' and tabn.date_type='1' and tabn.airport_code=#{airportCode}
        where tabn.id is null
    </select>
    <select id="listMissAirlineBillByFlightInfoAndTime" resultType="com.swcares.aiot.core.model.vo.AirlineBillMissVo">
        select tfbt.flight_date, tfbt.settle_code
        from (select DATE_FORMAT(tfi.flight_time, '%Y-%m') as flight_date, tal.settle_code
              from t_flight_info tfi
                       INNER JOIN t_aircraft_info tai on tfi.reg_no=tai.reg_no and tfi.flight_date BETWEEN tai.start_date and tai.end_date
                       INNER JOIN t_airline_info tal on tai.airline_short_name=tal.airline_short_name
              where tfi.invalid = '1'
                and tai.invalid='1'
                and tal.invalid='1'
                and tfi.flight_date is not null
                and tfi.airport_code=#{airportCode}
                and tfi.flight_time between #{startDate} and #{endDate}
              group by DATE_FORMAT(tfi.flight_time, '%Y-%m'), tal.settle_code) tfbt
                 left join t_airline_bill_new tabn
                           on tfbt.flight_date = tabn.settle_month and tfbt.settle_code = tabn.settle_code and
                              tabn.invalid = '1' and tabn.date_type='2' and tabn.airport_code=#{airportCode}
        where tabn.id is null
    </select>

    <select id="pageAirlineBillInfoByCondition" resultType="com.swcares.aiot.core.model.vo.AirlineBillPageVo">
        <include refid="selectAirlineBill"/>
    </select>

    <select id="listAirlineBillInfoByCondition" resultType="com.swcares.aiot.core.model.vo.AirlineBillPageVo">
        <include refid="selectAirlineBill"/>
    </select>


    <select id="countTotalAmount" resultType="com.swcares.aiot.core.model.vo.AirlineBillCountVoNew">
        select
            sum(tfbt.charge_price) as settleAmountTotal,
            sum(tabn.adjust_amount) as adjustAmountTotal,
            sum(tabn.refuse_amount) as refuseAmountTotal,
            sum(tfbt.charge_price+tabn.adjust_amount+tabn.refuse_amount) as actualAmountTotal,
            sum(tfbt.charge_price+tabn.adjust_amount+tabn.refuse_amount) as totalSettleAmountTotal,
            tax_rate as taxRate
        from t_airline_bill_new as tabn
        inner join (
            <choose>
                <when test="dateType == 2">
                    SELECT
                        settle_code,
                        sum(ROUND(charge_price,2)) AS charge_price
                    FROM t_flight_bill
                    WHERE invalid = '1'
                        AND airport_code = #{airportCode}
                        AND flight_time BETWEEN STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d') AND
                            DATE_ADD(LAST_DAY(STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d')), INTERVAL 1 DAY)
                    GROUP BY settle_code
                </when>
                <otherwise>
                    SELECT
                    settle_code,
                    SUM(ROUND(charge_price,2)) AS charge_price
                    FROM t_flight_bill
                    WHERE invalid = '1'
                    AND airport_code = #{airportCode}
                    AND flight_date BETWEEN STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d') AND
                        DATE_ADD(LAST_DAY(STR_TO_DATE(CONCAT(#{settleMoth}, '-01'), '%Y-%m-%d')), INTERVAL 1 DAY)
                    GROUP BY settle_code
                </otherwise>
            </choose>
        ) tfbt on tabn.settle_code = tfbt.settle_code
        where tabn.airport_code=#{airportCode}
            and  tabn.settle_month = #{settleMoth}
            and tabn.invalid='1'
        <if test="settleCodeList!=null and settleCodeList.size() > 0">
            and tabn.settle_code in
            <foreach collection="settleCodeList" item="settleCode" index="index" open="(" separator="," close=")">
                <if test="(index % 999) == 998">
                    NULL)
                    OR tabn.settle_code in (
                </if>#{settleCode}
            </foreach>
        </if>
            and (tabn.date_type is null or tabn.date_type =#{dateType})
            group by tabn.tax_rate
    </select>

    <select id="newCalclistMissAirlineBillByDate" resultType="com.swcares.aiot.core.model.vo.AirlineBillMissVo">
        select DISTINCT DATE_FORMAT( tfb.flight_date, '%Y-%m') as flight_date,tfb.settle_code
        from t_flight_bill tfb
                 LEFT JOIN t_airline_bill_new tabn
                           on tfb.settle_code=tabn.settle_code and DATE_FORMAT( tfb.flight_date, '%Y-%m' )=tabn.settle_month and tabn.invalid='1' and tabn.date_type='1' and tfb.airport_code=tabn.airport_code
        where tfb.invalid='1'
          and tfb.airport_code=#{airportCode}
          and tabn.id is null
    </select>
    <select id="newCalclistMissAirlineBillByTime" resultType="com.swcares.aiot.core.model.vo.AirlineBillMissVo">
        select DISTINCT DATE_FORMAT( tfb.flight_time, '%Y-%m') as flight_date,tfb.settle_code
        from t_flight_bill tfb
                 LEFT JOIN t_airline_bill_new tabn
                           on tfb.settle_code=tabn.settle_code and DATE_FORMAT( tfb.flight_time, '%Y-%m' )=tabn.settle_month and tabn.invalid='1' and tabn.date_type='2' and tfb.airport_code=tabn.airport_code
        where tfb.invalid='1'
          and tfb.airport_code=#{airportCode}
          and tabn.id is null
    </select>

</mapper>