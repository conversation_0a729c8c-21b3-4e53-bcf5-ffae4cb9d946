<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.FlightBillHistoryMapper">


    <insert id="copyBillHistory">
        INSERT INTO `t_flight_bill_history` (id, `flight_bill_id`, `create_by`, `create_time`, `modified_by`, `modified_time`, `airport_code`, `airline_short_name`, `charge_price`, `fee_code`, `fee_name`, `flight_flag`, `flight_line`, `flight_no`, `flight_segment`, `flight_time`, `from_airport_code`, `pricing_amount`, `reg_no`, `settle_month`, `to_airport_code`, `unit_price`, `flight_date`, `flight_id`, `invalid`, `da_airport_code`, `flight_line_type`, `flight_model`, `flight_segment_type`, `settle_code`, `task_flag`, `service_record`, `submit`, `feedback`, `indicator_code`, `indicator_name`, `indicator_value`, `bill_bus_data_item_id`, `revocation`, operation, `prove_file`)
            SELECT UUID_SHORT(), `id`,
                   if(#{name} is not null, #{name}, settle_code),
                   now(), `modified_by`, `modified_time`, `airport_code`, `airline_short_name`, `charge_price`, `fee_code`, `fee_name`, `flight_flag`, `flight_line`, `flight_no`, `flight_segment`, `flight_time`, `from_airport_code`, `pricing_amount`, `reg_no`, `settle_month`, `to_airport_code`, `unit_price`, `flight_date`, `flight_id`, `invalid`, `da_airport_code`, `flight_line_type`, `flight_model`, `flight_segment_type`, `settle_code`, `task_flag`, `service_record`, `submit`, `feedback`, `indicator_code`, `indicator_name`, `indicator_value`, `bill_bus_data_item_id`, `revocation`,
                   #{operation}, `prove_file`
            from t_flight_bill
            WHERE id in <foreach collection="billIds" item="id" separator="," open="(" close=")"> #{id} </foreach>
    </insert>

    <select id="getTFlightBillHistoryByFlightBillIds" resultType="com.swcares.aiot.core.entity.TFlightBillHistory">
        select
            *
        from
            t_flight_bill_history
        where
            operation = #{operation}
            and flight_bill_id in <foreach collection="flightBillIds" item="id" separator="," open="(" close=")"> #{id} </foreach>
        order by
            create_time desc;
    </select>

</mapper>
