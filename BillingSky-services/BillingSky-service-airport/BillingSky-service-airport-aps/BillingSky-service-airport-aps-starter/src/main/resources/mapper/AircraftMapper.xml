<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.AircraftMapper">

    <select id="listAirlineInfoByRegno" resultType="com.swcares.aiot.core.entity.AircraftAirlineInfoVo">
        select distinct settle_code, airline_code, airline_short_name
        from t_aircraft_info
        where invalid = '1'
          and reg_no = #{regNo}
          and #{effectiveDate} between start_date and end_date
    </select>

    <select id="listAirlineInfoByAirlineShortName" resultType="com.swcares.aiot.core.entity.AircraftAirlineInfoVo">
        select distinct settle_code, airline_code, airline_short_name
        from t_aircraft_info
        where invalid = '1'
          and airline_short_name like concat(concat("%", #{airlineShortName}), "%")
    </select>
</mapper>