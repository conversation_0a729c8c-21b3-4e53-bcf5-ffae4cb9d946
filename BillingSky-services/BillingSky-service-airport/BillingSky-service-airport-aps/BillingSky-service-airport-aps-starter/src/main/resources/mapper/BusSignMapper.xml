<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.BusSignMapper">

    <update id="updateBusSignSettleCode">
        update t_bus_sign tbs
        SET settle_code = (select settle_code
                           from t_aircraft_info
                           where invalid = '1'
                             and reg_no = tbs.flight_reg
                             and curdate() between start_date and end_date limit 1)
        where submit='0'
          and invalid='1'
          and flight_reg is not null
          and airport_code=#{airportCode}
          and flight_date between #{startDate}
          and #{endDate}
          and tbs.settle_code is null
    </update>

</mapper>
