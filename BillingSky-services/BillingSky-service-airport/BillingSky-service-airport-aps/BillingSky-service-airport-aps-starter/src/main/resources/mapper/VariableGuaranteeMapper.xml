<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.VariableGuaranteeMapper">
    <select id="selectMatchingRelationship" resultType="com.swcares.aiot.core.model.vo.VariableGuaranteeVo">
        SELECT 
              trg.id,
              trg.variable_id,
              trg.item_id,
              trg.land_flag,
              trg.data_update,
              trg.conversion_rules,
              trg.remark,
              trg.invalid_date,
              trg.airport_code,
              tbi.item_name ,
              trg.modified_time,
              trg.type
             FROM
              t_variable_guarantee trg,
              t_bill_item tbi
             <where>
              tbi.id = trg.item_id
              AND tbi.deleted = '0'
              AND trg.invalid = '1'
              AND trg.type='1'
                 <if test="airportCode != null and airportCode != ''">
                     and trg.airport_code = #{airportCode}
                 </if>
                 <if test="variableId != null and variableId != ''">
                     and trg.variable_id = #{variableId}
                 </if>
                <if test="itemId != null and itemId != ''">
                    and trg.item_id = #{itemId}
                </if>
             </where>
    </select>

    <select id="selectMatchingRelationshipDataCenter" resultType="com.swcares.aiot.core.model.vo.VariableGuaranteeVo">
         SELECT
              trg.id,
              trg.variable_id,
              trg.item_id,
              trg.land_flag,
              trg.data_update,
              trg.conversion_rules,
              trg.remark,
              trg.invalid_date,
              trg.airport_code,
              tasi.item_name ,
              trg.modified_time,
              trg.type
             FROM
              t_variable_guarantee trg,
              t_airport_sign_item tasi
        <where>
            tasi.id = trg.item_id
            AND tasi.deleted = '0'
            AND trg.invalid = '1'
            <if test="airportCode != null and airportCode != ''">
                and trg.airport_code = #{airportCode}
            </if>
            <if test="variableId != null and variableId != ''">
                and trg.variable_id = #{variableId}
            </if>
        </where>
    </select>
</mapper>