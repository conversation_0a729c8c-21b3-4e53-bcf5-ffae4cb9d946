<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.swcares.aiot.mapper.FlightFeeBillBizMapper">

    <select id="listFlightBillInfoByCondition" resultType="com.swcares.aiot.core.model.entity.FlightBill">
        select *
        from t_flight_bill tfb
        <where>
            tfb.invalid = '1'
            <if test="airportCode != null and airportCode != ''">
                and tfb.airport_code = #{airportCode}
            </if>
            <if test="startDate != null  and endDate != null">
                and tfb.create_time between #{startDate} and #{endDate}
            </if>
            <if test="flightTimeStartDate != null  and flightTimeEndDate != null">
                and tfb.flight_time between #{flightTimeStartDate} and #{flightTimeEndDate}
            </if>
            <if test="choseFeeInfos != null and choseFeeInfos !='' and choseFeeInfos.size() != 0">
                and tfb.fee_code in
                <foreach collection="choseFeeInfos" item="choseFeeInfo" open="(" close=")" separator=",">
                    #{choseFeeInfo}
                </foreach>
            </if>
            <if test="settleCodeList != null and settleCodeList !='' and settleCodeList.size() != 0">
                and tfb.settle_code in
                <foreach collection="settleCodeList" item="settleCode" open="(" close=")" separator=",">
                    #{settleCode}
                </foreach>
            </if>
            <if test="fromAirportCode != null and fromAirportCode != ''">
                and tfb.from_airport_code = #{fromAirportCode}
            </if>
            <if test="toAirportCode != null and toAirportCode !=''">
                and tfb.to_airport_code = #{toAirportCode}
            </if>
            <if test="flightNo != null and flightNo != ''">
                and tfb.flight_no = #{flightNo}
            </if>
            <if test="regNo != null and regNo !=''">
                and tfb.reg_no = #{regNo}
            </if>
            <if test="flightFlag != null and flightFlag !=''">
                and tfb.flight_flag = #{flightFlag}
            </if>
            <if test="submit != null and submit != ''">
                and tfb.submit = #{submit}
            </if>
            order by tfb.flight_date, tfb.flight_Time, tfb.flight_no
        </where>
    </select>

    <select id="listServiceRecordByFlightIdAndServiceCode" resultType="com.swcares.aiot.core.model.entity.ServiceRecord">
        select *
        from t_service_record t
        <where>
            t.invalid = '1'
            <if test="powercarFlightIdList != null and powercarFlightIdList.size() != 0">
                and t.flight_id in
                <foreach collection="powercarFlightIdList" item="flightId" open="(" close=")" separator=",">
                    #{flightId}
                </foreach>
            </if>
            <if test="airportCode != null and airportCode !=''">
                and t.airport_code = #{airportCode}
            </if>
            <if test="serviceCode != null and serviceCode != ''">
                and t.service_code = #{serviceCode}
            </if>
        </where>
    </select>
</mapper>