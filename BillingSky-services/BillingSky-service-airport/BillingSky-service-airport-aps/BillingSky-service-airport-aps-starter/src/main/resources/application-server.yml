# 服务端口
server:
  port: ${AIRPORT_APS_SERVER_PORT}
  servlet:
    context-path: /${spring.application.name}
  tomcat:
    uri-encoding: UTF-8
# spring 配置
spring:
  mvc:
    async:
      request-timeout: 360000
  ## 数据库连接配置
  datasource:
    url: jdbc:mysql://${DATASOURCE_IP}:${DATASOURCE_PORT}/${AIRPORT_APS_DATASOURCE_DBNAME}?useUnicode=true&autoReconnect=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai&failOverReadOnly=false
    driverClassName: com.mysql.cj.jdbc.Driver
    username: ${DATASOURCE_USERNAME}
    password: ${DATASOURCE_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    #Redis数据库索引（默认为0）
    database: ${REDIS_DATABASE}
    # Redis服务器地址
    host: ${REDIS_HOST}
    # Redis服务器连接端口
    port: ${REDIS_PORT_VAR}
    password: ${REDIS_PASSWORD}
  #JPA设置
  jpa:
    show-sql: ${AIRPORT_APS_JPA_SHOW_SQL_TOGGLE}
    open-in-view: false
    database-platform: org.hibernate.dialect.MySQLDialect
    gen-ddl: false
    properties:
      hibernate:
        # 开启启动项目自动更新表结构
        # hbm2ddl.auto: update
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        format_sql: true
        # 开启懒加载事务
        #enable_lazy_load_no_trans: false
        jdbc:
          #为spring data jpa saveAll方法提供批量插入操作 此处可以随时更改大小 建议500哦
          batch_size: 500
          batch_versioned_data: true
          order_inserts: true

  # 设置单个文件大小
  servlet:
    multipart:
      max-request-size: 32MB
      max-file-size: 32MB
  rabbitmq:
    host: ${RABBITMQ_HOST}
    port: ${RABBITMQ_PORT}
    username: ${RABBITMQ_USERNAME}
    password: ${RABBITMQ_PASSWORD}
    virtual-host: ${RABBITMQ_VIRTUAL_HOST}
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 24
          initial-interval: 3600000
        acknowledge-mode: manual
cors:
  allowed:
    origins: ${AIRPORT_APS_CORS_ALLOWED_ORIGINS}
########################################## System环境配置  ##########################################
swcares:
  version: '@project.version@'
  build:
    time: '@timestamp@'
  name: ${spring.application.name}
  user-center:
    enable: true
    service:
      do-main: ${UC_ADDRESS}
      sign: ${AIRPORT_APS_SIGN_URL}
      datacenter: ${AIRPORT_APS_DATACENTER_URL}
  dict-cfg:
    enable: true
    login-cfg-keys: # 配置登录时可加载的参数key
      - sys.login.failedNumAfterLockAccount
      - sys.user.initPasswordModify
  swagger:
    # 是否开启swagger
    enable: ${SWAGGER_TOGGLE}
    application-name: ${spring.application.name}
    application-version: ${swcares.version}-${swcares.build.time}
  captcha:
    enable: true
    type: default # default,clickWord,blockPuzzle,bladePatchca
    jigsaw: classpath:images/jigsaw
    pic-click: classpath:images/pic-click
    water-mark: swcares
    cache-type: redis # local/redis
    slip-offset: 5
    aes-status: true
    interference-options: 2
    cache-number: 1000
    timing-clear: 180
    font-type: 宋体
    water-font: SourceHanSansCN-Light.otf
  audit-log:
    enable: ${AUDIT_LOG_ENABLED:true}
  ############################ Field 加解密配置参数##################################
  secret-field:
    enable: true
    encryptor: AES
    key-accessor: config
    key-config:
      currentVersion: v1
      keys:
        v1: ZXVwc2kjMjAyMHN3Y2FyZXMkZXVwc2lAMjAyMHRyYXZlbHNreQ==
        v2: encrypted
  quartz:
    enable: true
  message:
    secretKey: ${AIRPORT_APS_SSSS_SECRETKEY:d12ca33feb28499d20ef1f5f7948772a}
    url: ${AIRPORT_APS_SSSS_URL}
