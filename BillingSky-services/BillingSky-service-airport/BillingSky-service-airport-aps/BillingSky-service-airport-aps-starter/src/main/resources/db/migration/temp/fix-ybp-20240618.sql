-- table sys_config_info
alter table sys_config_info
     MODIFY COLUMN created_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
alter table sys_config_info
		 MODIFY COLUMN  updated_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- table sys_config_value
alter table sys_config_value
     MODIFY COLUMN  created_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
alter table sys_config_value
		 MODIFY COLUMN  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';



INSERT INTO `sys_config_info` (`id`, `config_name`, `config_key`, `status`, `is_sys`, `is_single`, `remark`, `created_by`, `updated_by`,  `deleted`) VALUES (5, '基础配置', 'base_config', 1, '1', 0, '基础配置', '周扬',  '周扬',  0);

INSERT INTO  `sys_config_value` (`config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`,  `updated_by`,`deleted`, `remark`) VALUES (5, 1503987820729229312, 'AIRPORT_CODE', 'YBP', 1, '1', '1', 0, '宜宾租户的机场三字码');

INSERT INTO  `sys_config_value` (`config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`,  `updated_by`,`deleted`, `remark`) VALUES (5, 1504286650209165312, 'AIRPORT_CODE', 'LZO', 1, '1', '1', 0, '宜宾租户的机场三字码');
