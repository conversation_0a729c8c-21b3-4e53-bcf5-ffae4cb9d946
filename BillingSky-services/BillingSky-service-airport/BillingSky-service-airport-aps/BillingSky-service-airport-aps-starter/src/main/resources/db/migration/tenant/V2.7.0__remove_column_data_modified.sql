DROP PROCEDURE IF EXISTS `remove_column_data_modified`;

<PERSON><PERSON><PERSON><PERSON><PERSON> $$
CREATE PROCEDURE remove_column_data_modified()
BEGIN
    select DATABASE() into @db_name;
    IF EXISTS(SELECT * FROM  information_schema.columns WHERE table_schema=@db_name AND
       table_name='t_flight_info' AND column_name='data_modified')
    THEN
        update t_flight_info set data_status = 2 where data_modified = 2;
        ALTER TABLE t_flight_info DROP COLUMN data_modified;
    END IF;
    IF EXISTS(SELECT * FROM  information_schema.columns WHERE table_schema=@db_name AND
        table_name='t_flight_info' AND column_name='data_confirmed')
    THEN
        ALTER TABLE t_flight_info DROP COLUMN data_confirmed;
    END IF;
    IF EXISTS(SELECT * FROM  information_schema.columns WHERE table_schema=@db_name AND
        table_name='t_flight_info_cache' AND column_name='variable_status')
    THEN
        ALTER TABLE t_flight_info_cache DROP COLUMN variable_status;
    END IF;
END $$
DELIMITER ;
CALL remove_column_data_modified;
DROP PROCEDURE IF EXISTS `remove_column_data_modified`;