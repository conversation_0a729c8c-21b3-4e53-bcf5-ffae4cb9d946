SET FOREIGN_KEY_CHECKS=0;
DROP PROCEDURE IF EXISTS `drop_flight_bill_indexes_proc`;
/* 删除t_flight_bill表的指定索引 */
DELIMITER $$
CREATE PROCEDURE drop_flight_bill_indexes_proc()
BEGIN
    select DATABASE() into @db_name;

    -- 删除idx_flight_bill_fee_name索引
    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill' AND
        index_name='idx_flight_bill_fee_name')
    THEN
        DROP INDEX idx_flight_bill_fee_name ON t_flight_bill;
    END IF;

    -- 删除idx_flight_bill_airline_short_name索引
    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill' AND
        index_name='idx_flight_bill_airline_short_name')
    THEN
        DROP INDEX idx_flight_bill_airline_short_name ON t_flight_bill;
    END IF;

    -- 删除idx_flight_bill_fee_code索引
    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill' AND
        index_name='idx_flight_bill_fee_code')
    THEN
        DROP INDEX idx_flight_bill_fee_code ON t_flight_bill;
    END IF;

END $$
DELIMITER ;

CALL drop_flight_bill_indexes_proc;
DROP PROCEDURE IF EXISTS `drop_flight_bill_indexes_proc`;

DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    -- t_aircraft_info表索引
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_aircraft_info' AND
        index_name='idx_aircraft_info_recalc')
    THEN
        create index idx_aircraft_info_recalc on t_aircraft_info(`invalid`, `reg_no`, `start_date`, `end_date`, `airline_short_name`) USING BTREE;
    END IF;

    -- t_airline_info表索引
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_airline_info' AND
        index_name='idx_airline_info_recalc')
    THEN
        create index idx_airline_info_recalc on t_airline_info(`airport_code`, `airline_short_name`) USING BTREE;
    END IF;

    -- t_fee_info表索引
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_fee_info' AND
        index_name='idx_fee_info_airline_id')
    THEN
        create index idx_fee_info_airline_id on t_fee_info(`airline_id`) USING BTREE;
    END IF;

    -- t_flight_bill表索引
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill' AND
        index_name='idx_flight_bill_flight_id')
    THEN
        create index idx_flight_bill_flight_id on t_flight_bill(`flight_id`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill' AND
        index_name='idx_flight_bill_flight_Time')
    THEN
        create index idx_flight_bill_flight_Time on t_flight_bill(`flight_time`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill' AND
        index_name='idx_airport_code_settle_month_invalid_id')
    THEN
        create index idx_airport_code_settle_month_invalid_id on t_flight_bill(`settle_month`, `airport_code`, `invalid`, `id`) USING BTREE;
    END IF;

    -- t_flight_info表索引
    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_info' AND
        index_name='index_flight_date')
    THEN
        DROP INDEX index_flight_date ON t_flight_info;
    END IF;

    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_info' AND
        index_name='index_airline_code')
    THEN
        DROP INDEX index_airline_code ON t_flight_info;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_info' AND
        index_name='idx_flight_info_airport_code')
    THEN
        create index idx_flight_info_airport_code on t_flight_info(`airport_code`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_info' AND
        index_name='idx_flight_info_flight_time')
    THEN
        create index idx_flight_info_flight_time on t_flight_info(`flight_time`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_info' AND
        index_name='index_flight_info_flight_date')
    THEN
        create index index_flight_info_flight_date on t_flight_info(`flight_date`, `invalid`, `reg_no`) USING BTREE;
    END IF;

    -- t_formula_info表索引
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_formula_info' AND
        index_name='idx_formula_info_recalc')
    THEN
        create index idx_formula_info_recalc on t_formula_info(`invalid`, `fee_rules_id`, `start_date`, `end_date`, `flight_type`, `airline_id`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_formula_info' AND
        index_name='idx_formula_info_fee_rules_id')
    THEN
        create index idx_formula_info_fee_rules_id on t_formula_info(`fee_rules_id`) USING BTREE;
    END IF;

    -- t_service_record表索引
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_service_record' AND
        index_name='idx_service_record_flight_id')
    THEN
        create index idx_service_record_flight_id on t_service_record(`flight_id`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_service_record' AND
        index_name='idx_service_record_flight_date')
    THEN
        create index idx_service_record_flight_date on t_service_record(`flight_date`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_service_record' AND
        index_name='idx_service_record_airport_code')
    THEN
        create index idx_service_record_airport_code on t_service_record(`airport_code`) USING BTREE;
    END IF;

    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_service_record' AND
        index_name='idx_service_record_recalc')
    THEN
        create index idx_service_record_recalc on t_service_record(`flight_id`, `invalid`, `service_code`, `used_number`) USING BTREE;
    END IF;

    -- 修改表引擎为InnoDB
    ALTER TABLE `t_airport_bill` ENGINE = InnoDB;
    ALTER TABLE `t_fee_bill` ENGINE = InnoDB;
    ALTER TABLE `t_unmaintained_record` ENGINE = InnoDB;

    -- 删除现有的非主键索引
    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill_history' AND
        index_name='idx_flight_bill_fee_name')
    THEN
        DROP INDEX idx_flight_bill_fee_name ON t_flight_bill_history;
    END IF;

    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill_history' AND
        index_name='idx_flight_bill_airline_short_name')
    THEN
        DROP INDEX idx_flight_bill_airline_short_name ON t_flight_bill_history;
    END IF;

    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill_history' AND
        index_name='idx_flight_bill_airline_flight_no')
    THEN
        DROP INDEX idx_flight_bill_airline_flight_no ON t_flight_bill_history;
    END IF;

    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill_history' AND
        index_name='idx_flight_bill_fee_code')
    THEN
        DROP INDEX idx_flight_bill_fee_code ON t_flight_bill_history;
    END IF;

    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill_history' AND
        index_name='idx_flight_bill_settle_month_airport_code')
    THEN
        DROP INDEX idx_flight_bill_settle_month_airport_code ON t_flight_bill_history;
    END IF;

    IF EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill_history' AND
        index_name='idx_flight_bill_fee_bill_export')
    THEN
        DROP INDEX idx_flight_bill_fee_bill_export ON t_flight_bill_history;
    END IF;

    -- 创建新索引
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND
        table_name='t_flight_bill_history' AND
        index_name='idx_flight_bill_id')
    THEN
        create index idx_flight_bill_id on t_flight_bill_history(`flight_bill_id`);
    END IF;

END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;

SET FOREIGN_KEY_CHECKS=1;