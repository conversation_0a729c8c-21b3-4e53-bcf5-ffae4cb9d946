
DROP PROCEDURE IF EXISTS alter_table_bill_item_and_formula_add_flight_selector_item;
DELIMITER $$
CREATE PROCEDURE alter_table_bill_item_and_formula_add_flight_selector_item()
BEGIN
select DATABASE()into @db_name;

IF NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_bill_item' and column_name = 'flight_selector_item')
    THEN
ALTER TABLE t_bill_item ADD COLUMN `flight_selector_item` JSON DEFAULT NULL COMMENT '航班签单选项';
END IF;

IF NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_formula_info' and column_name = 'safeguard_type')
    THEN
ALTER TABLE t_formula_info ADD COLUMN `safeguard_type` TINYINT DEFAULT NULL COMMENT '保障类型（规则同步电子签单配置）';
END IF;

END $$
DELIMITER ;

CALL alter_table_bill_item_and_formula_add_flight_selector_item;
DROP PROCEDURE alter_table_bill_item_and_formula_add_flight_selector_item;


DROP PROCEDURE IF EXISTS insert_variable_record;
DELIMITER $$
CREATE PROCEDURE insert_variable_record()
BEGIN
    DECLARE v_code VARCHAR(255);

    SELECT airport_code into v_code  from t_variable_record limit 1;

    IF NOT EXISTS(
       select * from t_variable_record where variable='ST'
    )
    THEN
    INSERT INTO `t_variable_record` (`id`, `aps_input`, `dcs_input`, `flight_input`, `variable`, `variable_name`, `variable_type`, `variable_unit_name`, `match_table`, `match_word`, `operation_symbol`, `variable_unit`, `airport_code`)
    VALUES (UUID(), '0', '0', '0', 'ST', '保障类型', '其他1', NULL, NULL, NULL, NULL, 'O', v_code);
   END IF;
END  $$
DELIMITER ;

CALL insert_variable_record;
DROP PROCEDURE insert_variable_record;