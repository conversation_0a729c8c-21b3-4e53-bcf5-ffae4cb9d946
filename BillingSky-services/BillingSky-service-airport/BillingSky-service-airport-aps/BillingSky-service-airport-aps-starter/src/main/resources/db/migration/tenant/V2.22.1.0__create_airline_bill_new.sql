
CREATE TABLE `t_airline_bill_new` (
  `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
  `created_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
  `adjust_amount` decimal(32,4) DEFAULT '0.0000' COMMENT '调整金额',
  `invalid` varchar(1) DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
  `refuse_amount` decimal(32,4) DEFAULT '0.0000' COMMENT '拒付金额',
  `tax_rate` int DEFAULT '6' COMMENT '税率',
  `settle_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '结算代码',
  `settle_month` varchar(7) DEFAULT NULL COMMENT '结算月份',
  `submit` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '是否提交（0为否；1为是）',
  `date_type` tinyint(1) DEFAULT NULL COMMENT '日期类型(1:航班日期 2:起降日期)',
  PRIMARY KEY (`id`),
  KEY `idx_t_airline_bill_date_type` (`date_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='航司账单表';