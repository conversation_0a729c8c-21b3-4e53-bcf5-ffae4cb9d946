ALTER TABLE t_bus_sign ADD submit char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' NULL COMMENT '账单状态（0未提交 1已确认 2有争议 3待审核 4拒绝处理 5已撤销）';
ALTER TABLE t_bus_sign ADD feedback varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '反馈备注';
ALTER TABLE t_bus_sign ADD settle_code varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '结算代码';
ALTER TABLE t_bus_sign ADD revocation tinyint DEFAULT 0 NULL COMMENT '撤销状态（0''-''1申请撤销 2拒绝撤销 3同意撤销）';
ALTER TABLE t_bus_sign ADD flight_reg varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '机号';
ALTER TABLE t_bus_sign ADD issue varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '数据异常原因';
ALTER TABLE t_bus_sign ADD airline_code varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航司字码';
ALTER TABLE t_bus_sign ADD airport_name varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '机场名字';
ALTER TABLE t_bus_sign ADD sign_pdf_url varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT 'pdf地址';

CREATE TABLE `t_bus_sign_history` (
                                      `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                      `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                      `sign_created_time` datetime DEFAULT NULL COMMENT '签单创建时间',
                                      `flight_flag` char(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                      `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                      `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                                      `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司简称',
                                      `license_plate_number` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '车牌号',
                                      `remark` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '备注',
                                      `unit_price` double DEFAULT NULL COMMENT '收费单价',
                                      `settlement_amount` double DEFAULT NULL COMMENT '结算金额',
                                      `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                      `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                                      `airport_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场名字',
                                      `sign_pdf_url` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'pdf地址',
                                      `sign_id` bigint DEFAULT NULL COMMENT '电子签单id',
                                      `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                      `submit` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '账单状态（0未提交 1已确认 2有争议 3待审核 4拒绝处理 5已撤销）',
                                      `feedback` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '反馈备注',
                                      `settle_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算代码',
                                      `revocation` tinyint DEFAULT '0' COMMENT '撤销状态（0"-"1申请撤销 2拒绝撤销 3同意撤销）',
                                      `operation` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '账单操作(0账单生成 1提交账单 2接受反馈 3重新结算 4申请撤销 5同意撤销 6拒绝撤销 7拒绝处理)',
                                      `service_records_string` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '服务项内容',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;