ALTER TABLE `t_airline_bill`
    ADD COLUMN `flight_time` datetime(0) NULL DEFAULT NULL COMMENT '起降时间' AFTER `submit`,
    ADD COLUMN `flight_date` date NULL DEFAULT NULL COMMENT '航班日期' AFTER `flight_time`;

-- 航司账单表增加日期类型
DROP PROCEDURE IF EXISTS alter_table_t_airline_bill_add_date_type;
delimiter //
CREATE PROCEDURE alter_table_t_airline_bill_add_date_type()
BEGIN
select DATABASE() into @db_name;
IF NOT EXISTS(
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = @db_name
          and table_name = 't_airline_bill'
          and column_name = 'date_type'
    )
    THEN
ALTER TABLE `t_airline_bill`
    ADD COLUMN `date_type` tinyint(1) DEFAULT NULL COMMENT '日期类型(1:航班日期 2:起降日期)';
END IF;
END//
delimiter ;
CALL alter_table_t_airline_bill_add_date_type;
DROP PROCEDURE alter_table_t_airline_bill_add_date_type;

-- 航司账单表增加日期类型的索引
CREATE INDEX idx_t_airline_bill_date_type ON t_airline_bill (date_type);