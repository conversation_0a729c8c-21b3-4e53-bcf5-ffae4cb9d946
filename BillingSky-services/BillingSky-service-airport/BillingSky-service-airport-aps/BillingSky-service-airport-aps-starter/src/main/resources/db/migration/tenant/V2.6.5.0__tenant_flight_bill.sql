DROP PROCEDURE IF EXISTS `add_flight_bill_column`;

DE<PERSON><PERSON>ITER $$
CREATE PROCEDURE add_flight_bill_column()
BEGIN
    select DATABASE() into @db_name;
    IF NOT EXISTS(SELECT * FROM  information_schema.columns WHERE table_schema=@db_name AND
       table_name='t_flight_bill' AND column_name='manually_delete')
    THEN
        ALTER TABLE t_flight_bill
            ADD COLUMN manually_delete tinyint DEFAULT 0 COMMENT '手动删除状态（0正常，1删除）' AFTER `bill_bus_data_item_id`;
END IF;
END $$
DELIMITER ;
CALL add_flight_bill_column;
DROP PROCEDURE IF EXISTS `add_flight_bill_column`;