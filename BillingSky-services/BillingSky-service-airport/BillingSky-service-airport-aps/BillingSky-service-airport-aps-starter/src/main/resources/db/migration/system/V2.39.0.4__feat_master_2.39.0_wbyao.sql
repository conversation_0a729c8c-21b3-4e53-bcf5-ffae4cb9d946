/* --------------- 修改表 --------------- */
-- 修改表
DROP PROCEDURE IF EXISTS modify_table_data_module_calculate;
DELIMITER $$
CREATE PROCEDURE modify_table_data_module_calculate()
BEGIN
    -- 声明变量
    DECLARE maxIdConfigInfo INT DEFAULT 0;
    declare maxIdConfigValue int default 0;
    DECLARE actualConfigId INT DEFAULT 0;
    select DATABASE() into @db_name;
    -- ： 新增表数据
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = @db_name
                and table_name = 'sys_config_info')
        and NOT EXISTS(SELECT *
                       FROM bls_airport_aps.sys_config_info
                       WHERE config_key = 'tenant_settle_code_map'
                         and deleted = 0)
    THEN
        set maxIdConfigInfo = (select max(id) from bls_airport_aps.sys_config_info);
        set maxIdConfigInfo = maxIdConfigInfo + 1;
        insert into bls_airport_aps.sys_config_info(id, config_name, config_key, status, is_sys, is_single, remark, created_by, created_time, updated_by, updated_time, deleted)
        values (maxIdConfigInfo, '租户id结算code映射关系临时存储方案', 'tenant_settle_code_map', 1, 1, 0, '结算代码映射关系', 'system', now(), 'system', now(), 0);
    END IF;


    -- ： 新增表数据
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = @db_name
                and table_name = 'sys_config_value')
        and EXISTS(SELECT *
                   FROM bls_airport_aps.sys_config_info
                   WHERE config_key = 'tenant_settle_code_map'
                     AND deleted = 0)
    THEN
        -- 获取实际的 config_id
        SELECT id
        INTO actualConfigId
        FROM bls_airport_aps.sys_config_info
        WHERE config_key = 'tenant_settle_code_map'
          AND deleted = 0
        LIMIT 1;

        IF actualConfigId > 0 AND NOT EXISTS(SELECT *
                                             FROM bls_airport_aps.sys_config_value
                                             WHERE config_id = actualConfigId
                                               AND corp_code = '1503987820729229312'
                                               AND value_key = 'settle_code'
                                               AND deleted = 0)
        THEN
            SET maxIdConfigValue = (SELECT MAX(id) FROM bls_airport_aps.sys_config_value);
            SET maxIdConfigValue = maxIdConfigValue + 1;
            INSERT INTO bls_airport_aps.sys_config_value(id, config_id, corp_code, value_key, config_value, remark, created_by, created_time, updated_by, updated_time, deleted)
            VALUES (maxIdConfigValue, actualConfigId, '1503987820729229312', 'settle_code', 'YBP', '结算代码映射关系', 'system', NOW(), 'system', NOW(), 0);
        END IF;
    END IF;

END $$
DELIMITER ;

CALL modify_table_data_module_calculate;
DROP PROCEDURE modify_table_data_module_calculate;