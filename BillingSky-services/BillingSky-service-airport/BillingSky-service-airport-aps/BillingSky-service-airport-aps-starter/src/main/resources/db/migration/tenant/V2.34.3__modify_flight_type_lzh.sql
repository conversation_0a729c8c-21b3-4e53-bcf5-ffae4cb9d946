/** 公式航班性质字段改为多选*/
DROP PROCEDURE IF EXISTS alter_table_aps_modify_flight_type;
DELIMITER $$
CREATE PROCEDURE alter_table_aps_modify_flight_type()
BEGIN
select DATABASE()into @db_name;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_formula_info' and column_name = 'flight_type')
    THEN
ALTER TABLE t_formula_info MODIFY COLUMN flight_type varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航班性质 国际:I|国内:D';
END IF;


END $$
DELIMITER ;


/** 新增机场保障字典表*/
CALL alter_table_aps_modify_flight_type;
DROP PROCEDURE alter_table_aps_modify_flight_type;
DROP TABLE IF EXISTS `t_airport_sign_item`;
CREATE TABLE `t_airport_sign_item` (
                                       `id` varchar(128) NOT NULL  COMMENT '主键ID',
                                       `item_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据项分类',
                                       `item_name` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据项名称',
                                       `item_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据项代码',
                                       `data_format` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据格式',
                                       `unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位',
                                       `deleted` tinyint(1) NOT NULL COMMENT '删除标识（1：删除 0：正常）',
                                       `created_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                       `created_time` datetime NOT NULL COMMENT '创建时间',
                                       `updated_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                                       `updated_time` datetime NOT NULL COMMENT '更新时间',
                                       `airport_code` varchar(4) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1927243230114246657 DEFAULT CHARSET=utf8mb3 COMMENT='机场签单字典表';

SET FOREIGN_KEY_CHECKS = 1;



/** 新增业务保障字典表 廊桥类型*/
DROP PROCEDURE IF EXISTS insert_variable_record_ct;
DELIMITER $$
CREATE PROCEDURE insert_variable_record_ct()
BEGIN
    DECLARE v_code VARCHAR(255);

SELECT airport_code into v_code  from t_variable_record limit 1;

IF NOT EXISTS(
       select * from t_variable_record where variable='CT'
    )
    THEN
    INSERT INTO `t_variable_record` (`id`, `aps_input`, `dcs_input`, `flight_input`, `variable`, `variable_name`, `variable_type`, `variable_unit_name`, `match_table`, `match_word`, `operation_symbol`, `variable_unit`, `airport_code`)
    VALUES (UUID(), '0', '0', '0', 'CT', '廊桥类型', '其他1', NULL, NULL, NULL, NULL, 'O', v_code);
END IF;

IF NOT EXISTS(
       select * from t_airport_sign_item where item_name='保障类型'
    )
    THEN
/** 新增机场保障字典表 保障类型 和 廊桥类型*/
INSERT INTO `t_airport_sign_item` (`id`, `item_type`, `item_name`, `item_code`, `data_format`, `unit`, `deleted`, `created_by`, `created_time`, `updated_by`, `updated_time`, `airport_code`) VALUES ('001', NULL, '保障类型', 'PROTECTION_TYPE', 'selector_format', NULL, 0, NULL, '2025-07-14 13:54:28', NULL, '2025-07-14 13:54:31', v_code);
END IF;

IF NOT EXISTS(
       select * from t_airport_sign_item where item_name='廊桥类型'
    )
    THEN
INSERT INTO `t_airport_sign_item` (`id`, `item_type`, `item_name`, `item_code`, `data_format`, `unit`, `deleted`, `created_by`, `created_time`, `updated_by`, `updated_time`, `airport_code`) VALUES ('002', NULL, '廊桥类型', 'COVERED_BRIDGE_TYPE', 'selector_format', NULL, 0, NULL, '2025-07-14 13:57:16', NULL, '2025-07-14 13:57:19', v_code);
END IF;

   IF NOT EXISTS(
       select * from t_variable_record where variable='CLF'
    )
    THEN
   /** 新增业务保障字典，升降平台使用次数 */
INSERT INTO `t_variable_record` (`id`, `aps_input`, `dcs_input`, `flight_input`, `variable`, `variable_name`, `variable_type`, `variable_unit_name`, `match_table`, `match_word`, `operation_symbol`, `variable_unit`, `airport_code`) VALUES (UUID(), '1', '0', '0', 'CLF', '升降平台使用次数', '特车/设备要素', '次', NULL, NULL, NULL, 'F', v_code);
END IF;

select DATABASE()into @db_name;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_service_record' and column_name = 'service_number')
   THEN
ALTER TABLE t_service_record MODIFY COLUMN `service_number` int DEFAULT NULL COMMENT '服务使用数量';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_service_record_confirm' and column_name = 'service_number')
   THEN
ALTER TABLE t_service_record_confirm MODIFY COLUMN `service_number` int DEFAULT NULL COMMENT '服务使用数量';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_variable_guarantee' and column_name = 'item_id')
   THEN
ALTER TABLE t_variable_guarantee MODIFY COLUMN `item_id` varchar(128) DEFAULT NULL COMMENT '节点保障系统t_bill_item特车设备id';
END IF;
END  $$
DELIMITER ;

CALL insert_variable_record_ct;
DROP PROCEDURE insert_variable_record_ct;