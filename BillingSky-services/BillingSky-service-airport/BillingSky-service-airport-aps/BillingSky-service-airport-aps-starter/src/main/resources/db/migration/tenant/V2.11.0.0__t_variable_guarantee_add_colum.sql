ALTER TABLE t_variable_guarantee ADD COLUMN item_code VARCHAR(10) DEFAULT null COMMENT '第三方服务项代码';
ALTER TABLE t_variable_guarantee ADD COLUMN item_name VARCHAR(10) DEFAULT null COMMENT '第三方服务项名';
ALTER TABLE t_variable_guarantee ADD COLUMN data_format VARCHAR(32) DEFAULT null COMMENT '第三方服务项数据格式:number_format为数字格式；time_format为时间段格式；boolean_format为布尔格式';
ALTER TABLE t_variable_guarantee ADD COLUMN aps_service_code VARCHAR(10) DEFAULT null COMMENT '对账通服务代码';
ALTER TABLE t_variable_guarantee ADD COLUMN type VARCHAR(1) DEFAULT null COMMENT '匹配关系类型：1为电子签单匹配，2为第三方匹配';

update t_variable_guarantee set type='1' where type is null;

ALTER TABLE t_variable_guarantee MODIFY COLUMN `item_id` bigint  COMMENT '节点保障系统t_bill_item特车设备id';
ALTER TABLE t_variable_guarantee MODIFY COLUMN   `variable_id` varchar(36)  COMMENT '结算系统中的特车设备id';