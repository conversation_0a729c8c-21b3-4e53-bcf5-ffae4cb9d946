-- 机场租户库新增 estimate_bill 表
CREATE TABLE `estimate_bill` (
                                 `id` bigint NOT NULL COMMENT 'id',
                                 `payment_period` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '账期',
                                 `organization_id` bigint DEFAULT NULL COMMENT '分支机构id',
                                 `organization_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分支机构',
                                 `airport_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机场名称',
                                 `airport_code` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '三字码',
                                 `customer_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户名称',
                                 `area_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '地区',
                                 `open_account_name` varchar(64) DEFAULT NULL COMMENT '开户名称',
                                 `open_account_bank` varchar(64) DEFAULT NULL COMMENT '开户行名',
                                 `open_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '开户账号',
                                 `agreement_num` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '协议编号',
                                 `system_guarantee` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '系统保障',
                                 `amount_sg` decimal(16,2) DEFAULT NULL COMMENT '上年度系统保障服务费付款金额',
                                 `amount_pid` decimal(16,2) DEFAULT NULL COMMENT '上年度离港PID维护费收款金额',
                                 `estimate_bill_time` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本次预估账单时间',
                                 `amount_invoice` decimal(16,2) DEFAULT NULL COMMENT '本次应付系统保障服务费金额(开发票金额)',
                                 `amount_maintenance` decimal(16,2) DEFAULT NULL COMMENT '本次应收离港PID维护费金额',
                                 `status` tinyint NOT NULL DEFAULT '1' COMMENT '账单状态（1：已导入 2：待分支确认 3：有异议 4：待机场确认 5：已确认）',
                                 `headquarters_send_time` datetime DEFAULT NULL COMMENT '总部账单分发时间',
                                 `branch_send_time` datetime DEFAULT NULL COMMENT '分支账单分发时间',
                                 `pdf` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '数据溯源',
                                 `deleted` tinyint(1) NOT NULL COMMENT '删除标识（1：删除 0：正常）',
                                 `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='离港返还账单表';











