SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- add 离港返还账单存证
-- ----------------------------
DROP TABLE IF EXISTS `estimate_bill_prove`;
CREATE TABLE `estimate_bill_prove`  (
        `id` bigint NOT NULL COMMENT 'id',
        `estimate_bill_id` bigint NOT NULL COMMENT '离岗返还账单表id',
        `remarks` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
        `file_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '存证文件URL',
        `deleted` tinyint(1) NOT NULL COMMENT '删除标识（1：删除 0：正常）',
        `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
        `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
        `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`) USING BTREE,
        INDEX `idx_estimate_bill_prove_estimate_bill_id`(`estimate_bill_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '离港返还账单存证' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;