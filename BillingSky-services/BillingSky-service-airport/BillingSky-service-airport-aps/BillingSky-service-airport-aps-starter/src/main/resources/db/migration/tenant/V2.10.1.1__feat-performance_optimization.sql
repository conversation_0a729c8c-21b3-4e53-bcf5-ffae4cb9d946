DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_flight_bill_airport_code_invalid_flight_date')
    THEN
        create index idx_flight_bill_airport_code_invalid_flight_date on t_flight_bill(`airport_code`,`invalid`,`flight_date`);
    END IF;
END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;