

DROP PROCEDURE IF EXISTS alter_t_service_record_add_selector_option;
DELIMITER $$
CREATE PROCEDURE alter_t_service_record_add_selector_option()
BEGIN
select DATABASE()into @db_name;

IF NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_service_record' and column_name = 'selector_option')
    THEN
ALTER TABLE t_service_record ADD COLUMN `selector_option` varchar(255) DEFAULT NULL COMMENT '选择器-选中值';
END IF;

IF NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_service_record' and column_name = 'selector_dict')
    THEN
ALTER TABLE t_service_record ADD COLUMN`selector_dict` json DEFAULT NULL COMMENT '选择器-字典';
END IF;

IF NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_service_record_confirm' and column_name = 'selector_option')
    THEN
ALTER TABLE t_service_record_confirm ADD COLUMN `selector_option` varchar(255) DEFAULT NULL COMMENT '选择器-选中值';
END IF;

IF NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_service_record_confirm' and column_name = 'selector_dict')
    THEN
ALTER TABLE t_service_record_confirm ADD COLUMN`selector_dict` json DEFAULT NULL COMMENT '选择器-字典';
END IF;

IF NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_bill' and column_name = 'is_service_fee')
    THEN
ALTER TABLE t_flight_bill ADD COLUMN`is_service_fee` tinyint DEFAULT 0 COMMENT '是否为业务保障费用 1:是，0为否';
END IF;

END  $$
DELIMITER ;

CALL alter_t_service_record_add_selector_option;
DROP PROCEDURE alter_t_service_record_add_selector_option;
