SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

ALTER TABLE `estimate_bill`
    DROP COLUMN `area_name`;
ALTER TABLE `estimate_bill`
    DROP COLUMN `amount_sg`;
ALTER TABLE `estimate_bill`
    DROP COLUMN `amount_pid`;
ALTER TABLE `estimate_bill`
    DROP COLUMN `estimate_bill_time`;
ALTER TABLE `estimate_bill`
    DROP COLUMN `pdf`;
ALTER TABLE `estimate_bill`
    DROP COLUMN `invoice`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `payment_period` int NOT NULL COMMENT '年度' AFTER `id`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `organization_id` bigint NOT NULL COMMENT '分支机构id' AFTER `payment_period`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `organization_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分支机构' AFTER `organization_id`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `airport_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机场' AFTER `organization_name`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `airport_code` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '三字码' AFTER `airport_name`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `system_guarantee` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '离港保障服务费比例' AFTER `agreement_num`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `amount_invoice` decimal(16, 2) NULL DEFAULT NULL COMMENT '离港保障服务费开票金额（元）' AFTER `system_guarantee`;
ALTER TABLE `estimate_bill`
    MODIFY COLUMN `amount_maintenance` decimal(16, 2) NULL DEFAULT NULL COMMENT '应收离港配置维护费金额（元）' AFTER `amount_invoice`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `amount_payment` decimal(16, 2) NULL DEFAULT NULL COMMENT '离港保障服务费付款金额（元）' AFTER `amount_maintenance`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `boarding_pass_source` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '登机牌来源' AFTER `invoice_type`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `all_use_travelsky_products` tinyint NULL DEFAULT NULL COMMENT '是否100%使用航信产品' AFTER `boarding_pass_source`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `charging_standard` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计费标准' AFTER `all_use_travelsky_products`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `pic_charge_unit_price` decimal(16, 2) NULL DEFAULT NULL COMMENT '离港配置收费单价' AFTER `charging_standard`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `pic_charge_count` int NULL DEFAULT NULL COMMENT '离港配置收费数量' AFTER `pic_charge_unit_price`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `meet_billing_conditions` tinyint NULL DEFAULT NULL COMMENT '是否符合开账条件' AFTER `pic_charge_count`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `host_total_amount` decimal(16, 2) NULL DEFAULT NULL COMMENT 'HOST航金额合计' AFTER `meet_billing_conditions`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `non_host_total_amount_app` decimal(16, 2) NULL DEFAULT NULL COMMENT '非HOST航金额-app' AFTER `host_total_amount`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `non_host_total_amount_multiple` decimal(16, 2) NULL DEFAULT NULL COMMENT '非HOST航金额-多主机' AFTER `non_host_total_amount_app`;
ALTER TABLE `estimate_bill`
    ADD COLUMN `operate_last_time` datetime NULL COMMENT '最后操作时间' AFTER `non_host_total_amount_multiple`;

#离港返还账单存证
DROP TABLE IF EXISTS `estimate_bill_prove`;
CREATE TABLE `estimate_bill_prove`
(
    `id`               bigint     NOT NULL COMMENT 'id',
    `estimate_bill_id` bigint     NOT NULL COMMENT '离岗返还账单表id',
    `type`             tinyint                                                       DEFAULT '0' COMMENT '文件类型（1=非host-app 2=非host-多主机 3=host 4=账单溯源 5=发票 默认0=其他）',
    `remarks`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
    `operate_type`     varchar(16)                                                   DEFAULT NULL COMMENT '操作类型',
    `deleted`          tinyint(1) NOT NULL COMMENT '删除标识（1：删除 0：正常）',
    `created_by`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '创建人',
    `created_time`     datetime   NOT NULL                                           DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '更新人',
    `updated_time`     datetime   NOT NULL                                           DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_estimate_bill_prove_estimate_bill_id` (`estimate_bill_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = DYNAMIC COMMENT ='离港返还账单存证';

# 附件表
drop table  IF EXISTS bill_file_attachment;
CREATE TABLE `bill_file_attachment`
(
    `id`            bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '数据的唯一标识，没有业务含义',
    `original_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '原文件名',
    `file_name`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名',
    `suffix`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '文件类型拓展名',
    `file_key`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件标识，存储key',
    `file_type`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '文件类型',
    `file_path`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '文件存储路径',
    `table_id`      bigint                                                                 DEFAULT NULL COMMENT '业务表主键',
    `business_type` int                                                                    DEFAULT NULL COMMENT '归属业务类型',
    `created_by`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '数据的创建人',
    `created_time`  timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据的创建时间',
    `updated_by`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '最后修改数据的人',
    `updated_time`  timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改数据的时间',
    `deleted`       tinyint(1)                                                    NOT NULL DEFAULT '0' COMMENT '删除标记',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `file_business_type_table_id_index` (`table_id`, `business_type`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='账单附件表';

-- 添加host航表
DROP TABLE IF EXISTS `host_aviation`;
CREATE TABLE `host_aviation`
(
    `id`                              bigint                                                       NOT NULL COMMENT '主键id',
    `estimate_bill_prove_id`          bigint                                                       NULL     DEFAULT NULL COMMENT '离岗返还账单存证主键id',
    `airport_code`                    varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '客户三字码',
    `customer_name`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户名称',
    `bill_number`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账单编号',
    `bill_status`                     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账单状态(正式账单、非正式账单)',
    `bill_month`                      varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '月份(YYYY-MM)',
    `by_air_traveler_num`             varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '值机计费旅客量',
    `by_air_usage_rate`               varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '值机使用率',
    `by_air_price`                    varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '值机单价',
    `by_air_service_fee`              varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '值机保障服务费',
    `security_fee_traveler_num`       varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '安检计费旅客量',
    `security_data_return_rate`       varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '安检数据回传率',
    `security_price`                  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '安检单价',
    `security_service_fee`            varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '安检保障服务费',
    `boarding_fee_traveler_num`       varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '登机计费旅客量',
    `boarding_data_return_rate`       varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '登机数据回传率',
    `boarding_price`                  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '登机单价',
    `boarding_service_fee`            varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '登机保障服务费',
    `stowage_usage_number`            varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '配载使用量',
    `stowage_usage_rate`              varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '配载使用率',
    `stowage_price`                   varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '配载单价',
    `stowage_service_fee`             varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '配载保障服务费',
    `small_model_stowage_number`      varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '小机型配载量',
    `small_model_stowage_price`       varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '小机型配载单价',
    `small_model_stowage_service_fee` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '小机型配载保障服务费',
    `host_aviation_fee_total`         varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT 'HOST航费用合计',
    `deleted`                         tinyint(1)                                                   NOT NULL COMMENT '删除标识（1：删除 0：正常）',
    `created_by`                      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '创建人',
    `created_time`                    datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`                      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '更新人',
    `updated_time`                    datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_host_aviation_airport_code` (`airport_code` ASC) USING BTREE,
    INDEX `idx_host_aviation_airport_prove_id` (`estimate_bill_prove_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
    COMMENT = 'HOST航信息表'
  ROW_FORMAT = DYNAMIC;

-- 增加非host航表
DROP TABLE IF EXISTS `not_host_aviation`;
CREATE TABLE `not_host_aviation`
(
    `id`                     bigint                                                       NOT NULL COMMENT '主键id',
    `estimate_bill_prove_id` bigint                                                       NULL     DEFAULT NULL COMMENT '离岗返还账单存证主键id',
    `data_type`              smallint                                                     NOT NULL COMMENT '数据类型（1：APP、2：多主机）',
    `airport_code`           varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '机场三字码',
    `finance_examine_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '财务审核状态标识',
    `airline_code`           varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '航空公司两字码',
    `by_air_total`           varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '值机量小计',
    `refund_money`           varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '返还金额',
    `year`                   int                                                          NULL     DEFAULT NULL COMMENT '数据归属年份',
    `january_refund_money`   varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '1月份返还金额',
    `february_refund_money`  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '2月份返还金额',
    `march_refund_money`     varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '3月份返还金额',
    `april_refund_money`     varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '4月份返还金额',
    `may_refund_money`       varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '5月份返还金额',
    `june_refund_money`      varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '6月份返还金额',
    `july_refund_money`      varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '7月份返还金额',
    `august_refund_money`    varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '8月份返还金额',
    `september_refund_money` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '9月份返还金额',
    `october_refund_money`   varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '10月份返还金额',
    `november_refund_money`  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '11月份返还金额',
    `december_refund_money`  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '12月份返还金额',
    `deleted`                tinyint(1)                                                   NOT NULL COMMENT '删除标识（1：删除 0：正常）',
    `created_by`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '创建人',
    `created_time`           datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '更新人',
    `updated_time`           datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_not_host_aviation_type_and_airprod_code` (`data_type` ASC, `airport_code` ASC) USING BTREE,
    INDEX `idx_not_host_aviation_type_and_airprod_prove_id` (`estimate_bill_prove_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
    COMMENT = '非HOST航'
  ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

