/* --------------- 修改表 --------------- */
-- 修改表：t_flight_bill[机场明细账单]
-- 添加字段：
DELIMITER $$
CREATE PROCEDURE alter_table_add_service_time()
BEGIN
select DATABASE() into @db_name;

IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = @db_name
                    and table_name = 't_flight_bill'
                    and column_name = 'service_start_time')
    THEN
ALTER TABLE t_flight_bill ADD service_start_time datetime  COMMENT '指标项开始时间';
END IF;

IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = @db_name
                    and table_name = 't_flight_bill'
                    and column_name = 'service_end_time')
    THEN
ALTER TABLE t_flight_bill ADD service_end_time datetime  COMMENT '指标项结束时间';
END IF;
END $$
DELIMITER ;

CALL alter_table_add_service_time;
DROP PROCEDURE alter_table_add_service_time;