drop table if exists t_flight_bill_history_snapshot;
CREATE TABLE IF NOT EXISTS t_flight_bill_history_snapshot
(
    id              varchar(32) NOT NULL COMMENT '数据的唯一标识',
    bill_history_id varchar(128) DEFAULT NULL COMMENT '机场航班账单历史ID',
    snapshot_data   json        DEFAULT NULL COMMENT '快照数据，根据比对情况存储不同的数据结构',
    deleted         tinyint(1)  DEFAULT '0' COMMENT '删除标识（1：删除 0：正常）',
    created_by      varchar(32) DEFAULT NULL COMMENT '创建人',
    created_time    datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by      varchar(32) DEFAULT NULL COMMENT '更新人',
    updated_time    datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY index_bill_id (bill_history_id) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='快照表';


/* --------------- 修改表 --------------- */
-- 修改表
DROP PROCEDURE IF EXISTS alter_table_column_module_calculate;
DELIMITER $$
CREATE PROCEDURE alter_table_column_module_calculate()
BEGIN
    select DATABASE() into @db_name;
    -- 修改字段： customer_iata
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = @db_name
                and table_name = 'calc_customer'
                and column_name = 'customer_iata')
    THEN
        ALTER TABLE `calc_customer`
            MODIFY COLUMN `customer_iata` varchar(16) NOT NULL COMMENT '客户编码[结算代码][航司结算代码]' AFTER `contract_id`;
    END IF;

    -- 修改字段： settlement_code
    IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = @db_name
                    and table_name = 'calc_customer'
                    and column_name = 'settlement_code')
    THEN
        ALTER TABLE `calc_customer`
            ADD COLUMN `settlement_code` varchar(16) NULL DEFAULT NULL COMMENT '付款结算代码[付款航司结算代码]' AFTER `customer_iata`;
    END IF;

    -- 修改字段： service_airport
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = @db_name
                and table_name = 'calc_customer'
                and column_name = 'service_airport')
    THEN
        ALTER TABLE `calc_customer`
            MODIFY COLUMN `service_airport` varchar(32) NULL DEFAULT NULL COMMENT '服务机场[航司二字码]' AFTER `customer_type`;
    END IF;

    -- 添加字段： ground_handling_type
    IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = @db_name
                    and table_name = 'calc_formula'
                    and column_name = 'protection_type')
    THEN
        ALTER TABLE `calc_formula`
            ADD COLUMN `protection_type` tinyint(4) NULL COMMENT '保障类型' AFTER `aggregation_sort`;
    END IF;

END $$
DELIMITER ;

CALL alter_table_column_module_calculate;
DROP PROCEDURE alter_table_column_module_calculate;