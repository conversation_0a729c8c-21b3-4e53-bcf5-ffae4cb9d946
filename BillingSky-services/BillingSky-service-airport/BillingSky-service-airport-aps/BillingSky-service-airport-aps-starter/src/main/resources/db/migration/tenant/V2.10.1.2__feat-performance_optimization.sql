DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_combine')
    THEN
        create index idx_combine on t_flight_bill(`settle_month`,`airport_code`, `invalid`, `fee_name`, `airline_short_name`, `charge_price`);
    END IF;
END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;