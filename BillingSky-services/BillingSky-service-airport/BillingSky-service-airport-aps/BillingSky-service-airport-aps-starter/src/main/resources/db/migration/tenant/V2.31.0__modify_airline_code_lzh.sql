
DROP PROCEDURE IF EXISTS alter_table_aps_modify_airline;

DEL<PERSON>ITER $$
CREATE PROCEDURE alter_table_aps_modify_airline()
BEGIN
select DATABASE()into @db_name;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_info' and column_name = 'airline_code')
    THEN
ALTER TABLE t_flight_info MODIFY COLUMN airline_code varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航空公司';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_info' and column_name = 'flight_line')
    THEN
ALTER TABLE t_flight_info MODIFY COLUMN flight_line varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航线';
END IF;


IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_info_cache' and column_name = 'airline_code')
    THEN
ALTER TABLE t_flight_info_cache MODIFY COLUMN airline_code varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航空公司';
END IF;


IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_info_cache' and column_name = 'flight_line')
    THEN
ALTER TABLE t_flight_info_cache MODIFY COLUMN flight_line varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航线';
END IF;


IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_bill' and column_name = 'flight_line')
    THEN
ALTER TABLE t_flight_bill MODIFY COLUMN flight_line varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航线';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_bill_history' and column_name = 'flight_line')
    THEN
ALTER TABLE t_flight_bill_history MODIFY COLUMN flight_line varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航线';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_airline_info' and column_name = 'airline_code')
    THEN
ALTER TABLE t_airline_info MODIFY COLUMN airline_code varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航司二字码';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_line_info' and column_name = 'airline_code')
    THEN
ALTER TABLE t_flight_line_info MODIFY COLUMN airline_code varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航空公司';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_flight_line_info' and column_name = 'flight_line')
    THEN
ALTER TABLE t_flight_line_info MODIFY COLUMN flight_line varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航线';
END IF;

IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = @db_name
              and table_name = 't_aircraft_info' and column_name = 'airline_code')
    THEN
ALTER TABLE t_aircraft_info MODIFY COLUMN airline_code varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '航空公司二字码';
END IF;


END $$
DELIMITER ;

CALL alter_table_aps_modify_airline;
DROP PROCEDURE alter_table_aps_modify_airline;