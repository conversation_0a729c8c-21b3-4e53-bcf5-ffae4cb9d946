/*
 Navicat Premium Dump SQL

 Source Server         : bls-pro-write-root
 Source Server Type    : MySQL
 Source Server Version : 80031 (8.0.31)
 Source Host           : *************:3306
 Source Schema         : aps_ten_ybp

 Target Server Type    : MySQL
 Target Server Version : 80031 (8.0.31)
 File Encoding         : 65001

 Date: 08/07/2024 14:32:09
*/


SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for department
-- ----------------------------
-- DROP TABLE IF EXISTS `department`;
CREATE TABLE `department` (
                              `ID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '部门id',
                              `TOID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级组织代码',
                              `TOPID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '父部门id',
                              `TOBM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '组织编码',
                              `TOFNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '组织全称',
                              `TOSNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '组织简称',
                              `TOTYPE` int DEFAULT NULL COMMENT '机构类型',
                              `TOORDER` int DEFAULT NULL COMMENT ' 组织排序',
                              `TOBGNF` int DEFAULT '0' COMMENT '启用标志',
                              `IS_D` int DEFAULT NULL COMMENT '是否钉钉(0否,1是)',
                              `DEPART_STATE` int DEFAULT '0' COMMENT '部门启用标志  0 启用 1禁用',
                              `TO_PHONE` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '部门电话',
                              `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新日期',
                              `DEPT_HIDING` int DEFAULT '0' COMMENT '是否隐藏部门(0否,1是)',
                              PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='部门表';

-- ----------------------------
-- Table structure for msg_info
-- ----------------------------
-- DROP TABLE IF EXISTS `msg_info`;
CREATE TABLE `msg_info` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                            `batch_no` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '批次号（用于问题排查）',
                            `role_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '角色名',
                            `msg_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '业务类型：1账单生成消息、',
                            `msg_title` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息标题',
                            `msg_content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息内容',
                            `msg_status` tinyint DEFAULT NULL COMMENT '状态：1进行中、2已完成',
                            `data_error_file_url` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据错误文件url',
                            `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识（1：删除 0：正常）',
                            `created_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '创建人',
                            `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '更新人',
                            `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `msg_info_business_type_index` (`msg_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='消息信息表';

-- ----------------------------
-- Table structure for msg_read
-- ----------------------------
-- DROP TABLE IF EXISTS `msg_read`;
CREATE TABLE `msg_read` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                            `msg_id` bigint NOT NULL COMMENT '消息id',
                            `user_standing` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '阅读用户',
                            `read_status` tinyint(1) NOT NULL COMMENT '已经读取 0 未读 1 读取',
                            `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识（1：删除 0：正常）',
                            `created_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '创建人',
                            `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '更新人',
                            `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `msg_read_msg_id_index` (`msg_id`,`user_standing`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=155 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='消息阅读信息表';

-- ----------------------------
-- Table structure for msg_role
-- ----------------------------
-- DROP TABLE IF EXISTS `msg_role`;
CREATE TABLE `msg_role` (
                            `id` bigint NOT NULL COMMENT 'id',
                            `role_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色名',
                            `role_desc` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色描述',
                            `user_standing` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户标识',
                            `deleted` tinyint(1) DEFAULT NULL COMMENT '是否逻辑删除',
                            `created_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `updated_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                            `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='角色对应的用户列表';

-- ----------------------------
-- Table structure for msg_type
-- ----------------------------
-- DROP TABLE IF EXISTS `msg_type`;
CREATE TABLE `msg_type` (
                            `id` bigint NOT NULL COMMENT 'id',
                            `msg_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息值',
                            `msg_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息名',
                            `msg_template` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息模板',
                            `hidden` tinyint(1) DEFAULT NULL COMMENT '不显示标识（1 显示 0 隐藏）',
                            `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识（1：删除 0：正常）',
                            `created_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                            `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                            `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for p_aircraft_record
-- ----------------------------
-- DROP TABLE IF EXISTS `p_aircraft_record`;
CREATE TABLE `p_aircraft_record` (
                                     `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                                     `reg_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机号',
                                     `flight_model_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机型名称',
                                     `flight_model` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机型',
                                     `airline_code` char(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司二字码',
                                     `airline_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司',
                                     `max_seat` int DEFAULT NULL COMMENT '最大座位数',
                                     `available_seat` int DEFAULT NULL COMMENT '可供座位数',
                                     `max_payload` int DEFAULT NULL COMMENT '最大业载',
                                     `available_payload` int DEFAULT NULL COMMENT '可供业载',
                                     `max_weight` int DEFAULT NULL COMMENT '最大起飞重量',
                                     `country` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '国家',
                                     `flag` char(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '国内外标识',
                                     `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT '有效标识（1：0 有效：无效）',
                                     `effective_date` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '有效时间',
                                     `expiration_date` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '失效时间',
                                     `create_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                     `create_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建日期',
                                     `modify_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                     `modify_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='飞机信息表（机场子系统导入）';

-- ----------------------------
-- Table structure for p_flight_record
-- ----------------------------
-- DROP TABLE IF EXISTS `p_flight_record`;
CREATE TABLE `p_flight_record` (
                                   `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                                   `flight_line` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线英文标识',
                                   `flight_line_cn` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线中文标识',
                                   `flight_line_short` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线中文简称',
                                   `flight_flag` char(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线标识',
                                   `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT '有效标识（1：0 有效：无效）',
                                   `create_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建时间',
                                   `create_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                   `modify_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                   `modify_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航线信息对照表';

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log` (
                           `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'id',
                           `operator_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作人id',
                           `operator_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作人名称',
                           `create_time` datetime DEFAULT NULL COMMENT '操作时间',
                           `operator_content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '操作内容',
                           `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否有效  0：无效 1：有效',
                           `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作机场三字码',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
                            `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                            `parent_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '父级菜单id',
                            `menu_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单名称',
                            `level` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单等级',
                            `remarks` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
                            `menu_order` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '排序',
                            `menu_type` int NOT NULL DEFAULT '2' COMMENT '菜单类型   0：目录   1：菜单   2：按钮',
                            `menu_href` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '前端路径',
                            `url` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '后端url路径',
                            `components_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '组件id',
                            `contain_path` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '路由合集',
                            `create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                            `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                            `menu_icon` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单图片',
                            `perms` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '授权(多个用逗号分隔，如：user:list,user:create)',
                            `status` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态（0：1；无效：有效）',
                            `update_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                            `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                            `visible` int NOT NULL DEFAULT '0' COMMENT '菜单是否展示标识（0：1；展示：隐藏）',
                            `port` varchar(25) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'H h5端，W web端',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='系统菜单管理';

-- ----------------------------
-- Table structure for sys_number_role
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_number_role`;
CREATE TABLE `sys_number_role` (
                                   `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                                   `role_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色关联键',
                                   `user_number_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户关联键',
                                   `create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='系统用户与角色对应关系';

-- ----------------------------
-- Table structure for sys_operate_record
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_operate_record`;
CREATE TABLE `sys_operate_record` (
                                      `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                      `operate_id` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作对象id',
                                      `operate_object` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作对象',
                                      `operate_record` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作记录',
                                      `operate_time` datetime DEFAULT NULL COMMENT '操作时间',
                                      `operate_type` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作类型',
                                      `user_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作人账号',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
                            `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                            `create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                            `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                            `is_sys` int DEFAULT NULL COMMENT '（弃用）',
                            `remarks` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
                            `role_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色名称',
                            `status` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态（1：0；有效：无效）',
                            `update_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                            `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                            `departmentid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '部门关联键',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='系统角色';

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
                                 `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                                 `menu_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单关联键',
                                 `role_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色关联键',
                                 `create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                 `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='系统角色与菜单对应关系';

-- ----------------------------
-- Table structure for sys_settlement_status
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_settlement_status`;
CREATE TABLE `sys_settlement_status` (
                                         `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                         `flight_nos` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算航班（‘，’号分割，''/''表示所有航班）',
                                         `from_date` datetime DEFAULT NULL COMMENT '结算日期开始时间',
                                         `status` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算状态（0表示进行中，1表示完成，-1表示失败,2为等待中，-2表示取消）',
                                         `to_date` datetime DEFAULT NULL COMMENT '结算日期结束时间',
                                         `type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息类型：1为重新结算；2为航线补贴结算',
                                         `airline_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线补贴 航司代码',
                                         `flight_line` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线补贴 航线中文',
                                         `fail_file` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '失败文件minio地址',
                                         `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                         `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
                                         `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                         `flight_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班id',
                                         `formula_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '公式参数类型1：航班公式重新结算；2：业务保障公式重新结算\r\n',
                                         `fee_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '結算費用',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_upload_status
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_upload_status`;
CREATE TABLE `sys_upload_status` (
                                     `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                     `upload_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上传文件名',
                                     `status` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态：（0表示进行中，1表示完成，-1表示失败）',
                                     `fail_reason` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '失败原因',
                                     `fail_file` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '失败文件minio地址',
                                     `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                     `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
                                     `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_user_number
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_user_number`;
CREATE TABLE `sys_user_number` (
                                   `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                                   `department` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '部门',
                                   `status` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态（0：1；无效：有效）',
                                   `user_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户名称',
                                   `user_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户工号',
                                   `is_on_duty` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否值班',
                                   `photo` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '图片',
                                   `to_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '部门ID',
                                   `birth_day` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '出生日期',
                                   `login_date` datetime DEFAULT NULL COMMENT '最近登录时间',
                                   `modifi_date` datetime DEFAULT NULL COMMENT '最后修改时间',
                                   `phone` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '员工照',
                                   `email` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '邮箱',
                                   `city_3_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '三字码',
                                   `modifi_user` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人',
                                   `tu_ext2` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '员工岗位2',
                                   `tu_specialty` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '专业',
                                   `user_ename` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '英文名',
                                   `create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                   `update_date` datetime DEFAULT NULL COMMENT '修改时间',
                                   `password` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '密码',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_user_read
-- ----------------------------
-- DROP TABLE IF EXISTS `sys_user_read`;
CREATE TABLE `sys_user_read` (
                                 `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                 `settlement_upload_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算信息或上传信息id',
                                 `type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '分类：1为结算信息，2为上传信息',
                                 `user_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户信息',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_aircraft_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_aircraft_info`;
CREATE TABLE `t_aircraft_info` (
                                   `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                   `reg_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '飞机注册号',
                                   `airplane_model` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机型',
                                   `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航空公司二字码',
                                   `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航空公司简称',
                                   `airplane_att` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机属性 客机',
                                   `airplane_type` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机类型 普通：1|宽体：2',
                                   `alter_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '变更标识',
                                   `available_payload` int DEFAULT NULL COMMENT '可供业载',
                                   `available_seat` int DEFAULT NULL COMMENT '可供座位',
                                   `max_payload` int DEFAULT NULL COMMENT '最大业载',
                                   `max_seat` int DEFAULT NULL COMMENT '最大座位',
                                   `max_takeoff_weight` int DEFAULT NULL COMMENT '最大起飞重量',
                                   `quota_payload` int DEFAULT NULL COMMENT '配额业载',
                                   `quota_seat` int DEFAULT NULL COMMENT '配额座位',
                                   `settle_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航空公司结算代码',
                                   `start_date` date DEFAULT NULL COMMENT '开始日期',
                                   `end_date` date DEFAULT NULL COMMENT '结束日期',
                                   `remark` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
                                   `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                   `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='飞机信息表';

-- ----------------------------
-- Table structure for t_airline_bill
-- ----------------------------
-- DROP TABLE IF EXISTS `t_airline_bill`;
CREATE TABLE `t_airline_bill` (
                                  `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                  `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                                  `adjust_amount` decimal(32,4) DEFAULT '0.0000' COMMENT '调整金额',
                                  `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航司简称',
                                  `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
                                  `refuse_amount` decimal(32,4) DEFAULT '0.0000' COMMENT '拒付金额',
                                  `tax_rate` int DEFAULT '6' COMMENT '税率',
                                  `settle_amount` decimal(32,4) DEFAULT '0.0000' COMMENT '结算金额',
                                  `settle_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '结算代码',
                                  `settle_month` date DEFAULT NULL COMMENT '结算月份',
                                  `submit` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '是否提交（0为否；1为是）',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航司账单表';

-- ----------------------------
-- Table structure for t_airline_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_airline_info`;
CREATE TABLE `t_airline_info` (
                                  `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                  `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司二字码',
                                  `airline_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司全称',
                                  `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航司简称',
                                  `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                  `settle_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '结算代码',
                                  `flight_number` int DEFAULT '0' COMMENT '上月服务航班数量',
                                  `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
                                  `special_cmb_fee` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否特殊航司服务费;0:空白|1:特殊',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航司信息表';

-- ----------------------------
-- Table structure for t_airport_bill
-- ----------------------------
-- DROP TABLE IF EXISTS `t_airport_bill`;
CREATE TABLE `t_airport_bill` (
                                  `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                  `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                  `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司二字码',
                                  `details_date` date DEFAULT NULL COMMENT '日期',
                                  `arrival_flight_number` int DEFAULT NULL COMMENT '降落航班数',
                                  `arrival_psg_number` int DEFAULT NULL COMMENT '进港旅客数',
                                  `arrive_plf` decimal(5,2) DEFAULT NULL COMMENT '进港客座率',
                                  `departure_flight_number` int DEFAULT NULL COMMENT '起飞航班数',
                                  `departure_plf` decimal(5,2) DEFAULT NULL COMMENT '离港客座率',
                                  `departure_psg_number` int DEFAULT NULL COMMENT '离港旅客数',
                                  `flight_number` int DEFAULT NULL COMMENT '航班起降架次',
                                  `cargo` int DEFAULT NULL COMMENT '货物重量',
                                  `mail` int DEFAULT NULL COMMENT '邮件重量',
                                  `plf` decimal(5,2) DEFAULT NULL COMMENT '客座率',
                                  `psg_throughput` int DEFAULT NULL COMMENT '旅客吞吐量',
                                  `transit_psg_number` int DEFAULT NULL COMMENT '过站旅客数',
                                  `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='机场账单表';

-- ----------------------------
-- Table structure for t_airport_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_airport_info`;
CREATE TABLE `t_airport_info` (
                                  `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                  `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                  `airport_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场名称',
                                  `airport_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场简称',
                                  `airport_level` int DEFAULT NULL COMMENT '机场级别',
                                  `company` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所属集团公司',
                                  `airport_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '是否出入境机场 是:1|否:0',
                                  `airport_province` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场所在省市代码，2位数字',
                                  `yesterday_flight_number` int DEFAULT NULL COMMENT '昨日航班起降架次',
                                  `yesterday_mail_cargo` int DEFAULT NULL COMMENT '昨日货邮吞吐量',
                                  `yesterday_psg_throughput` int DEFAULT NULL COMMENT '昨日旅客吞吐量',
                                  `service_airline_number` int DEFAULT NULL COMMENT '服务航司',
                                  `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='机场信息表';

-- ----------------------------
-- Table structure for t_airport_three_char_code
-- ----------------------------
-- DROP TABLE IF EXISTS `t_airport_three_char_code`;
CREATE TABLE `t_airport_three_char_code` (
                                             `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `code` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                             `airport_name` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场名称',
                                             `is_domestic` tinyint(1) DEFAULT NULL COMMENT '是否国内',
                                             `last_edit_time` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后编辑时间',
                                             `create_time` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2616 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_bill_item
-- ----------------------------
-- DROP TABLE IF EXISTS `t_bill_item`;
CREATE TABLE `t_bill_item` (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                               `item_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据项分类;数据项分类',
                               `item_name` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据项名称;数据项名称',
                               `data_format` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据格式;数据格式类型',
                               `associated_node` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '是否关联节点;关联节点（1-关联，0-不关联）',
                               `associated_node_name` bigint DEFAULT NULL COMMENT '关联名称id',
                               `unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位;单位(仅供数据格式选择数值使用)',
                               `start_node` bigint DEFAULT NULL COMMENT '开始节点;开始节点ID',
                               `end_node` bigint DEFAULT NULL COMMENT '结束节点;结束节点ID',
                               `independent_node` bigint DEFAULT NULL COMMENT '独立节点;独立节点ID（用于有无格式的类型）',
                               `update_before_sign` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签前修改;签前是否允许修改（1-允许，0-不允许）',
                               `sync_after_sign` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签后同步;签后是否允许同步（1-允许，0-不允许）',
                               `update_after_sign` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签后修改;签后是否允许修改（1-允许，0-不允许）',
                               `time_interval` int DEFAULT NULL COMMENT '时间段;每超过时间间隔+1次（用于数值格式计算次数）',
                               `only_once` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '固定1次;数值格式是否固定为1次（1-是，2-否）',
                               `deleted` tinyint(1) NOT NULL COMMENT '删除标识（1：删除 0：正常）',
                               `dept_id` bigint DEFAULT NULL COMMENT '记录所属组织Id',
                               `created_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                               `created_time` datetime NOT NULL COMMENT '创建时间',
                               `updated_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                               `updated_time` datetime NOT NULL COMMENT '更新时间',
                               `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1774092161499582465 DEFAULT CHARSET=utf8mb3 COMMENT='签单内容表';

-- ----------------------------
-- Table structure for t_bill_item_bus
-- ----------------------------
-- DROP TABLE IF EXISTS `t_bill_item_bus`;
CREATE TABLE `t_bill_item_bus` (
                                   `item_id` bigint NOT NULL COMMENT '签单内容项ID',
                                   `item_name` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据项名称',
                                   `data_format` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据格式类型',
                                   `item_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据项分类',
                                   `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                   `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                   `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                                   `template_id` bigint DEFAULT NULL COMMENT '签单模板ID;签单模板ID',
                                   PRIMARY KEY (`item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_bus_sign
-- ----------------------------
-- DROP TABLE IF EXISTS `t_bus_sign`;
CREATE TABLE `t_bus_sign` (
                              `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                              `flight_date` date DEFAULT NULL COMMENT '航班日期',
                              `sign_created_time` datetime DEFAULT NULL COMMENT '签单创建时间',
                              `flight_flag` char(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                              `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                              `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                              `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司简称',
                              `license_plate_number` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '车牌号',
                              `remark` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '备注',
                              `unit_price` double DEFAULT NULL COMMENT '收费单价',
                              `settlement_amount` double DEFAULT NULL COMMENT '结算金额',
                              `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                              `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                              `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                              `sign_id` bigint DEFAULT NULL COMMENT '电子签单id',
                              `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_bus_sign_service
-- ----------------------------
-- DROP TABLE IF EXISTS `t_bus_sign_service`;
CREATE TABLE `t_bus_sign_service` (
                                      `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT 'ID',
                                      `bill_sign_id` bigint NOT NULL COMMENT '签单表id',
                                      `item_id` bigint NOT NULL COMMENT '签单内容项ID',
                                      `item_name` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据项名称',
                                      `item_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据值',
                                      `data_format` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据格式类型',
                                      `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                      `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                      `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_cargo_free
-- ----------------------------
-- DROP TABLE IF EXISTS `t_cargo_free`;
CREATE TABLE `t_cargo_free` (
                                `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1',
                                `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
                                `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
                                `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_fee_bill
-- ----------------------------
-- DROP TABLE IF EXISTS `t_fee_bill`;
CREATE TABLE `t_fee_bill` (
                              `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                              `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                              `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                              `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                              `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司简称',
                              `fee_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用名称',
                              `settle_amount` decimal(32,4) DEFAULT NULL COMMENT '结算金额',
                              `settle_month` date DEFAULT NULL COMMENT '结算月份',
                              `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='费用账单表';

-- ----------------------------
-- Table structure for t_fee_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_fee_info`;
CREATE TABLE `t_fee_info` (
                              `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                              `fee_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用代码',
                              `fee_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用名称',
                              `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                              `airline_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '关联航司ID',
                              `fee_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用类别 S:民航标准基准价 C:自定义费用',
                              `fee_priority` int DEFAULT NULL COMMENT '数据优先级;0:后计算|1:先计算',
                              `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                              `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                              `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
                              `is_service_fee` int DEFAULT NULL COMMENT '是否为机务类，0代表否，1代表是',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='费用信息表';

-- ----------------------------
-- Table structure for t_flight_bill
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_bill`;
CREATE TABLE `t_flight_bill` (
                                 `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                 `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                 `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                 `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司简称',
                                 `charge_price` decimal(32,4) DEFAULT NULL COMMENT '收费金额',
                                 `fee_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用代码',
                                 `fee_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用名称',
                                 `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                 `flight_line` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线',
                                 `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                 `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                                 `flight_time` datetime DEFAULT NULL COMMENT '起降时间',
                                 `from_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起飞机场三字码',
                                 `pricing_amount` decimal(32,4) DEFAULT NULL COMMENT '计价量 计价量条件|=0 计价量指标项目的数量|=1 和归集方式相关',
                                 `reg_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机注册号',
                                 `settle_month` date DEFAULT NULL COMMENT '结算月份',
                                 `to_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '到达机场三字码',
                                 `unit_price` decimal(32,4) DEFAULT NULL COMMENT '收费单价',
                                 `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                 `flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班ID',
                                 `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                 `da_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '采集机场三字码',
                                 `flight_line_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线性质 国际:I|国内:D',
                                 `flight_model` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机型',
                                 `flight_segment_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段性质 国际:I|国内:D',
                                 `settle_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算代码',
                                 `task_flag` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '任务标识',
                                 `service_record` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '特车服务代码',
                                 `submit` char(1) DEFAULT '0' COMMENT '是否提交（0未提交 1已确认 2有争议 3待审核 4拒绝处理）',
                                 `feedback` varchar(255) DEFAULT NULL COMMENT '航司反馈',
                                 `indicator_code` varchar(128) DEFAULT NULL COMMENT '指标项代码',
                                 `indicator_name` varchar(512) DEFAULT NULL COMMENT '指标项名称',
                                 `indicator_value` varchar(255) DEFAULT NULL COMMENT '指标项值',
                                 `bill_bus_data_item_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签单保障项id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航班账单表';

-- ----------------------------
-- Table structure for t_flight_bill_history
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_bill_history`;
CREATE TABLE `t_flight_bill_history` (
                                         `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                         `flight_bill_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '原始账单数据id',
                                         `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                         `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                         `airline_short_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司简称',
                                         `charge_price` decimal(32,4) DEFAULT NULL COMMENT '收费金额',
                                         `fee_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用代码',
                                         `fee_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '费用名称',
                                         `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                         `flight_line` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线',
                                         `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                         `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                                         `flight_time` datetime DEFAULT NULL COMMENT '起降时间',
                                         `from_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起飞机场三字码',
                                         `pricing_amount` decimal(32,4) DEFAULT NULL COMMENT '计价量 计价量条件|=0 计价量指标项目的数量|=1 和归集方式相关',
                                         `reg_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机注册号',
                                         `settle_month` date DEFAULT NULL COMMENT '结算月份',
                                         `to_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '到达机场三字码',
                                         `unit_price` decimal(32,4) DEFAULT NULL COMMENT '收费单价',
                                         `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                         `flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班ID',
                                         `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                         `da_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '采集机场三字码',
                                         `flight_line_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线性质 国际:I|国内:D',
                                         `flight_model` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机型',
                                         `flight_segment_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段性质 国际:I|国内:D',
                                         `settle_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算代码',
                                         `task_flag` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '任务标识',
                                         `service_record` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '特车服务代码',
                                         `submit` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '是否提交（0未提交 1已确认 2有争议 3待审核 4拒绝处理）',
                                         `feedback` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司反馈',
                                         `indicator_code` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '指标项代码',
                                         `indicator_name` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '指标项名称',
                                         `indicator_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '指标项值',
                                         `bill_bus_data_item_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签单保障项id',
                                         `operation` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '账单操作(0账单生成 1重新结算 2提交账单 3接受反馈 4拒绝处理)',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         KEY `idx_flight_bill_fee_name` (`fee_name`) USING BTREE,
                                         KEY `idx_flight_bill_airline_short_name` (`airline_short_name`) USING BTREE,
                                         KEY `idx_flight_bill_airline_flight_no` (`flight_no`) USING BTREE,
                                         KEY `idx_flight_bill_fee_code` (`fee_code`) USING BTREE,
                                         KEY `idx_flight_bill_settle_month_airport_code` (`airport_code`,`settle_month`,`invalid`) USING BTREE,
                                         KEY `idx_flight_bill_fee_bill_export` (`airport_code`,`airline_short_name`,`charge_price`,`fee_name`,`settle_month`,`invalid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航班账单表';

-- ----------------------------
-- Table structure for t_flight_data_msg
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_data_msg`;
CREATE TABLE `t_flight_data_msg` (
                                     `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                     `class_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '类名',
                                     `message` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT 'Json消息',
                                     `airport_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `flight_info_flag` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班标识（日期_航班号_起降标识_机场三字码）',
                                     `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据有效 有效:1|无效:0',
                                     `flight_or_service` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '1为航班数据；2为保障服务数据',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_flight_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_info`;
CREATE TABLE `t_flight_info` (
                                 `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                 `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT 'ID',
                                 `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                 `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                 `accompanying_card_holder_number` int DEFAULT '0' COMMENT '持卡旅客随行人数',
                                 `accompanying_important_number` int DEFAULT '0' COMMENT '重要旅客随行人数',
                                 `adult_number` int DEFAULT '0' COMMENT '成人数',
                                 `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航空公司',
                                 `bag` decimal(32,4) DEFAULT '0.0000' COMMENT '行李重量',
                                 `bag_number` int DEFAULT '0' COMMENT '行李件数',
                                 `card_holder_number` int DEFAULT '0' COMMENT '持卡旅客人数',
                                 `cargo` decimal(32,4) DEFAULT NULL COMMENT '货物重量',
                                 `child_number` int DEFAULT '0' COMMENT '儿童数',
                                 `club_class_number` int DEFAULT '0' COMMENT '商务舱人数',
                                 `da_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '采集机场三字码',
                                 `da_time` datetime DEFAULT NULL COMMENT '采集时间',
                                 `diplomatic_passport_number` int DEFAULT '0' COMMENT '持外交护照人数',
                                 `economy_class_number` int DEFAULT '0' COMMENT '经济舱人数',
                                 `first_class_number` int DEFAULT '0' COMMENT '头等舱人数',
                                 `flight_fee` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降费用标识（起飞费用：1；降落费用：0）',
                                 `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                 `flight_line` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线',
                                 `flight_line_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线性质 国际:I|国内:D',
                                 `flight_model` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机型',
                                 `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                 `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                                 `flight_segment_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段性质 国际:I|国内:D',
                                 `flight_time` datetime DEFAULT NULL COMMENT '起降时间',
                                 `flight_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班性质 国际:I|国内:D',
                                 `from_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '出发航站三字码',
                                 `ground_fee` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '地面服务费用标识',
                                 `important_number` int DEFAULT '0' COMMENT '重要旅客人数',
                                 `infant_number` int DEFAULT '0' COMMENT '婴儿数',
                                 `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                 `mail` decimal(32,4) DEFAULT '0.0000' COMMENT '邮件重量',
                                 `plf` decimal(5,2) DEFAULT NULL COMMENT '客坐率',
                                 `psg_number` int DEFAULT '0' COMMENT '进出港人数',
                                 `reg_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机注册号',
                                 `stay_end_time` datetime DEFAULT NULL COMMENT '停场结束时间',
                                 `stay_start_time` datetime DEFAULT NULL COMMENT '停场开始时间',
                                 `stay_time` decimal(32,2) DEFAULT '0.00' COMMENT '停场时间',
                                 `task_flag` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '任务标识',
                                 `to_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '到达航站三字码',
                                 `transit_adult_number` int DEFAULT '0' COMMENT '过站成人数',
                                 `transit_child_number` int DEFAULT '0' COMMENT '过站儿童数',
                                 `transit_infant_number` int DEFAULT '0' COMMENT '过站婴儿数',
                                 `weight_uints` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '重量单位',
                                 `confirm_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据确认代码',
                                 `flight_status` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班状态CAN取消',
                                 `merge_flag` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '合并标识',
                                 `data_status` int DEFAULT NULL COMMENT '数据状态 (0:未确认|1:已确认)',
                                 `data_confirmed` int DEFAULT '0' COMMENT '0:未确认过|1:已确认过',
                                 `data_modified` int DEFAULT NULL COMMENT '2:已修改',
                                 `is_modify_flight_time` int DEFAULT '0' COMMENT '是否手动修改起降时间（0为否，1为是）',
                                 `pre_flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '前序航班id',
                                 `variable_status` int DEFAULT '0' COMMENT '保障数据状态（0:未确认|1:已确认',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `index_airline_code` (`airline_code`) USING BTREE,
                                 KEY `index_flight_date` (`flight_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航班信息表';

-- ----------------------------
-- Table structure for t_flight_info_cache
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_info_cache`;
CREATE TABLE `t_flight_info_cache` (
                                       `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                       `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT 'ID',
                                       `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                       `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                       `accompanying_card_holder_number` int DEFAULT '0' COMMENT '持卡旅客随行人数',
                                       `accompanying_important_number` int DEFAULT '0' COMMENT '重要旅客随行人数',
                                       `adult_number` int DEFAULT '0' COMMENT '成人数',
                                       `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航空公司',
                                       `bag` decimal(32,4) DEFAULT '0.0000' COMMENT '行李重量',
                                       `bag_number` int DEFAULT '0' COMMENT '行李件数',
                                       `card_holder_number` int DEFAULT '0' COMMENT '持卡旅客人数',
                                       `cargo` decimal(32,4) DEFAULT NULL COMMENT '货物重量',
                                       `child_number` int DEFAULT '0' COMMENT '儿童数',
                                       `club_class_number` int DEFAULT '0' COMMENT '商务舱人数',
                                       `da_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '采集机场三字码',
                                       `da_time` datetime DEFAULT NULL COMMENT '采集时间',
                                       `diplomatic_passport_number` int DEFAULT '0' COMMENT '持外交护照人数',
                                       `economy_class_number` int DEFAULT '0' COMMENT '经济舱人数',
                                       `first_class_number` int DEFAULT '0' COMMENT '头等舱人数',
                                       `flight_fee` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降费用标识（起飞费用：1；降落费用：0）',
                                       `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                       `flight_line` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线',
                                       `flight_line_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线性质 国际:I|国内:D',
                                       `flight_model` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机型',
                                       `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                       `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                                       `flight_segment_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段性质 国际:I|国内:D',
                                       `flight_time` datetime DEFAULT NULL COMMENT '起降时间',
                                       `flight_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班性质 国际:I|国内:D',
                                       `from_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '出发航站三字码',
                                       `ground_fee` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '地面服务费用标识',
                                       `important_number` int DEFAULT '0' COMMENT '重要旅客人数',
                                       `infant_number` int DEFAULT '0' COMMENT '婴儿数',
                                       `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                       `mail` decimal(32,4) DEFAULT '0.0000' COMMENT '邮件重量',
                                       `plf` decimal(5,2) DEFAULT NULL COMMENT '客坐率',
                                       `psg_number` int DEFAULT '0' COMMENT '进出港人数',
                                       `reg_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机注册号',
                                       `stay_end_time` datetime DEFAULT NULL COMMENT '停场结束时间',
                                       `stay_start_time` datetime DEFAULT NULL COMMENT '停场开始时间',
                                       `stay_time` decimal(32,2) DEFAULT '0.00' COMMENT '停场时间',
                                       `task_flag` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '任务标识',
                                       `to_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '到达航站三字码',
                                       `transit_adult_number` int DEFAULT '0' COMMENT '过站成人数',
                                       `transit_child_number` int DEFAULT '0' COMMENT '过站儿童数',
                                       `transit_infant_number` int DEFAULT '0' COMMENT '过站婴儿数',
                                       `weight_uints` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '重量单位',
                                       `confirm_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据确认代码',
                                       `flight_status` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班状态CAN取消',
                                       `merge_flag` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '合并标识',
                                       `is_modify_flight_time` int DEFAULT '0' COMMENT '是否手动修改起降时间（0为否，1为是）',
                                       `pre_flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '前序航班id',
                                       `variable_status` int DEFAULT '0' COMMENT '保障数据状态（0:未确认|1:已确认',
                                       `connect_flight_id` bigint DEFAULT NULL COMMENT '关联航班id',
                                       PRIMARY KEY (`id`),
                                       KEY `index_airline_code` (`airline_code`) USING BTREE,
                                       KEY `index_flight_date` (`flight_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='航班信息表';

-- ----------------------------
-- Table structure for t_flight_info_segment
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_info_segment`;
CREATE TABLE `t_flight_info_segment` (
                                         `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT 'ID',
                                         `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                         `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                         `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                         `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                                         `is_near` tinyint(1) NOT NULL COMMENT '是否为航线中相邻的航段（1为是，0为否）',
                                         `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                         `base_flight_id` bigint DEFAULT NULL COMMENT '航班id',
                                         `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                         `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `card_holder_number` int DEFAULT '0' COMMENT '持卡旅客人数',
                                         `accompanying_card_holder_number` int DEFAULT '0' COMMENT '持卡旅客随行人数',
                                         `important_number` int DEFAULT '0' COMMENT '重要旅客人数',
                                         `accompanying_important_number` int DEFAULT '0' COMMENT '重要旅客随行人数',
                                         `diplomatic_passport_number` int DEFAULT '0' COMMENT '持外交护照人数',
                                         `club_class_number` int DEFAULT '0' COMMENT '商务舱人数',
                                         `first_class_number` int DEFAULT '0' COMMENT '头等舱人数',
                                         `economy_class_number` int DEFAULT '0' COMMENT '经济舱人数',
                                         `adult_number` int DEFAULT '0' COMMENT '成人数',
                                         `child_number` int DEFAULT '0' COMMENT '儿童数',
                                         `infant_number` int DEFAULT '0' COMMENT '婴儿数',
                                         `transit_adult_number` int DEFAULT '0' COMMENT '过站成人数',
                                         `transit_child_number` int DEFAULT '0' COMMENT '过站儿童数',
                                         `transit_infant_number` int DEFAULT '0' COMMENT '过站婴儿数',
                                         `psg_number` int DEFAULT '0' COMMENT '进出港人数',
                                         `plf` decimal(5,2) DEFAULT NULL COMMENT '客坐率',
                                         `cargo` decimal(32,4) DEFAULT NULL COMMENT '货物重量',
                                         `mail` decimal(32,4) DEFAULT '0.0000' COMMENT '邮件重量',
                                         `bag` decimal(32,4) DEFAULT '0.0000' COMMENT '行李重量',
                                         `bag_number` int DEFAULT '0' COMMENT '行李件数',
                                         `transit_cargo` decimal(10,0) DEFAULT '0' COMMENT '过站货物重量',
                                         `transit_mail` decimal(10,0) DEFAULT '0' COMMENT '过站邮件重量',
                                         `transit_bag` decimal(10,0) DEFAULT '0' COMMENT '过站行李重量',
                                         `transit_bag_num` int DEFAULT '0' COMMENT '过站行李件数',
                                         `weight_uints` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '重量单位',
                                         `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         KEY `index_flight_date` (`flight_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航班信息表';

-- ----------------------------
-- Table structure for t_flight_info_segment_cache
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_info_segment_cache`;
CREATE TABLE `t_flight_info_segment_cache` (
                                               `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT 'ID',
                                               `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                               `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                               `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                               `flight_segment` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航段',
                                               `is_near` tinyint(1) NOT NULL COMMENT '是否为航线中相邻的航段（1为是，0为否）',
                                               `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                               `base_flight_id` bigint DEFAULT NULL COMMENT '航班id',
                                               `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                               `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               `card_holder_number` int DEFAULT '0' COMMENT '持卡旅客人数',
                                               `accompanying_card_holder_number` int DEFAULT '0' COMMENT '持卡旅客随行人数',
                                               `important_number` int DEFAULT '0' COMMENT '重要旅客人数',
                                               `accompanying_important_number` int DEFAULT '0' COMMENT '重要旅客随行人数',
                                               `diplomatic_passport_number` int DEFAULT '0' COMMENT '持外交护照人数',
                                               `club_class_number` int DEFAULT '0' COMMENT '商务舱人数',
                                               `first_class_number` int DEFAULT '0' COMMENT '头等舱人数',
                                               `economy_class_number` int DEFAULT '0' COMMENT '经济舱人数',
                                               `adult_number` int DEFAULT '0' COMMENT '成人数',
                                               `child_number` int DEFAULT '0' COMMENT '儿童数',
                                               `infant_number` int DEFAULT '0' COMMENT '婴儿数',
                                               `transit_adult_number` int DEFAULT '0' COMMENT '过站成人数',
                                               `transit_child_number` int DEFAULT '0' COMMENT '过站儿童数',
                                               `transit_infant_number` int DEFAULT '0' COMMENT '过站婴儿数',
                                               `psg_number` int DEFAULT '0' COMMENT '进出港人数',
                                               `plf` decimal(5,2) DEFAULT NULL COMMENT '客坐率',
                                               `cargo` decimal(32,4) DEFAULT NULL COMMENT '货物重量',
                                               `mail` decimal(32,4) DEFAULT '0.0000' COMMENT '邮件重量',
                                               `bag` decimal(32,4) DEFAULT '0.0000' COMMENT '行李重量',
                                               `bag_number` int DEFAULT '0' COMMENT '行李件数',
                                               `transit_cargo` decimal(10,0) DEFAULT '0' COMMENT '过站货物重量',
                                               `transit_mail` decimal(10,0) DEFAULT '0' COMMENT '过站邮件重量',
                                               `transit_bag` decimal(10,0) DEFAULT '0' COMMENT '过站行李重量',
                                               `transit_bag_num` int DEFAULT '0' COMMENT '过站行李件数',
                                               `weight_uints` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '重量单位',
                                               `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                               PRIMARY KEY (`id`),
                                               KEY `index_flight_date` (`flight_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='航班信息表';

-- ----------------------------
-- Table structure for t_flight_line_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_line_info`;
CREATE TABLE `t_flight_line_info` (
                                      `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                      `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航空公司',
                                      `airline_short_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航司简称',
                                      `flight_line` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航线',
                                      `flight_line_cn` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '中文航线',
                                      `budget` double(32,2) NOT NULL DEFAULT '0.00' COMMENT '预算金额',
  `used` double(32,2) NOT NULL DEFAULT '0.00' COMMENT '已使用金额',
  `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
  `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
  `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_flight_plan_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_plan_info`;
CREATE TABLE `t_flight_plan_info` (
                                      `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'id',
                                      `flight_date` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班日期',
                                      `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                      `flight_flag` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班起降标识',
                                      `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                      `flag` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '采集标识 0:未采集 1：已采集',
                                      `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据有效 1:有效|0:无效',
                                      `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                      `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_flight_terminal
-- ----------------------------
-- DROP TABLE IF EXISTS `t_flight_terminal`;
CREATE TABLE `t_flight_terminal` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                     `airport_chs` varchar(25) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场中文名称',
                                     `frcd` varchar(4) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场四字码',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=574 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_formula_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_formula_info`;
CREATE TABLE `t_formula_info` (
                                  `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                  `formula_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '公式名称',
                                  `description` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '公式描述',
                                  `airline_id` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '使用航司ID',
                                  `fee_rules_id` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所属费用规则ID',
                                  `airport_level` int DEFAULT NULL COMMENT '机场分类',
                                  `airport_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否出入境机场 是:1|否:0',
                                  `flight_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班性质 国际:I|国内:D',
                                  `belong_way` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '归集方式 D:仅起飞航班|A:仅到降落班|H:起飞降落各0.5|C:不限，按实际情况归集',
                                  `calc_way` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '计算方式 0:标准价,*:标准价*系数,+:标准价+系数',
                                  `pricing_way` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '计价量条件 1:单位单价|2:带条件的单位单价|3:固定价格，可不与任何项关联（含市场调节价）',
                                  `calc_variable` varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '计算变量',
                                  `fee_unit` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '计费单位',
                                  `formula` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '公式',
                                  `formula_status` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '公式状态 0:已过期|1:待生效|2:生效中',
                                  `active` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '采用公式标识 0:未采用 1：采用',
                                  `start_date` date DEFAULT NULL COMMENT '开始日期',
                                  `end_date` date DEFAULT NULL COMMENT '结束日期',
                                  `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据有效 1:有效|0:无效',
                                  `template_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否模板公式 是:1|否:0',
                                  `formula_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '公式参数类型1：纯数字|2：航班数据类型|3：业务保障数据类型|4：混合类型',
                                  `is_transit` char(1) DEFAULT '0' COMMENT '是否过站 0不限；1过站；2非过站',
                                  `alt_special` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '备降航班特殊收费:1是；0否',
                                  `special_variable` tinytext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '关联数据项',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='公式信息表';

-- ----------------------------
-- Table structure for t_method_record
-- ----------------------------
-- DROP TABLE IF EXISTS `t_method_record`;
CREATE TABLE `t_method_record` (
                                   `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                   `description` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '函数描述',
                                   `function_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '函数名称',
                                   `function_value` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '函数内容',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='公式计算函数';

-- ----------------------------
-- Table structure for t_passenger_info
-- ----------------------------
-- DROP TABLE IF EXISTS `t_passenger_info`;
CREATE TABLE `t_passenger_info` (
                                    `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                    `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                    `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    `cabin` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '舱位',
                                    `cn_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '中文姓名',
                                    `en_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '英文姓名',
                                    `flight_date` date DEFAULT NULL COMMENT '航班日期',
                                    `flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航班ID',
                                    `from_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '始发站',
                                    `pnr_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'PNR',
                                    `seat_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '座位号',
                                    `tk_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '票号',
                                    `to_airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '到达站',
                                    `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='旅客信息表';

-- ----------------------------
-- Table structure for t_service_record
-- ----------------------------
-- DROP TABLE IF EXISTS `t_service_record`;
CREATE TABLE `t_service_record` (
                                    `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                    `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                    `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '发生地机场三字码',
                                    `end_time` datetime DEFAULT NULL COMMENT '使用结束时间',
                                    `flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航班ID',
                                    `seat` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机位',
                                    `service_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务代码',
                                    `service_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '名称',
                                    `service_number` int DEFAULT '1' COMMENT '服务使用数量',
                                    `start_time` datetime DEFAULT NULL COMMENT '使用开始时间',
                                    `used_number` decimal(5,2) DEFAULT NULL COMMENT '次数/时长/人工',
                                    `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                    `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                    `flight_date` date DEFAULT NULL COMMENT '起降日期',
                                    `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                    `in_scope` varchar(1) DEFAULT '1' COMMENT '是否在航班停场范围内：1为是；0为否',
                                    `sign_delete_or_update` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '电子签单是否删除:1为删除；0为正常',
                                    `bill_bus_data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签单id',
                                    `sign_pdf_url` varchar(255) DEFAULT NULL COMMENT '签单pdf文件服务器地址',
                                    `bill_bus_data_item_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签单保障项id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='服务记录表';

-- ----------------------------
-- Table structure for t_service_record_confirm
-- ----------------------------
-- DROP TABLE IF EXISTS `t_service_record_confirm`;
CREATE TABLE `t_service_record_confirm` (
                                            `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                            `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                            `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '发生地机场三字码',
                                            `end_time` datetime DEFAULT NULL COMMENT '使用结束时间',
                                            `flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航班ID',
                                            `seat` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机位',
                                            `service_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务代码',
                                            `service_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '名称',
                                            `service_number` int DEFAULT '1' COMMENT '服务使用数量',
                                            `start_time` datetime DEFAULT NULL COMMENT '使用开始时间',
                                            `used_number` decimal(5,2) DEFAULT NULL COMMENT '次数/时长/人工',
                                            `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                            `flight_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班号',
                                            `flight_date` date DEFAULT NULL COMMENT '起降日期',
                                            `flight_flag` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '起降标识',
                                            `in_scope` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '是否在航班停场范围内：1为是；0为否',
                                            `bill_bus_data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签单id',
                                            `sign_pdf_url` varchar(255) DEFAULT NULL COMMENT '签单pdf文件服务器地址',
                                            `bill_bus_data_item_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '签单保障项id',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='服务记录表';

-- ----------------------------
-- Table structure for t_subsidy_bill
-- ----------------------------
-- DROP TABLE IF EXISTS `t_subsidy_bill`;
CREATE TABLE `t_subsidy_bill` (
                                  `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'Id',
                                  `flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航班id',
                                  `formula_bill_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '公式账单id',
                                  `guaranteed_income` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '保底收入',
                                  `settle_result` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算金额',
                                  `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                  `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                  `manual_modified` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否手动修改 0为否，1为是',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_subsidy_bill_param_join
-- ----------------------------
-- DROP TABLE IF EXISTS `t_subsidy_bill_param_join`;
CREATE TABLE `t_subsidy_bill_param_join` (
                                             `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                             `param_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数表id',
                                             `param_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数值',
                                             `param_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数名字',
                                             `param_belong` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数归属 ：\r\n1为公式参数；\r\n2为航班参数；\r\n3为公式航班都有的参数（参考轮档时间）',
                                             `param_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数类型：1为数值；2为公式; 3为公式类型参数的子参数',
                                             `unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位（公式类型无单位）',
                                             `formula_bill_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '公式账单表id',
                                             `bill_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '账单详情表id',
                                             `num_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字符格式：1为正整数，2为整数，3为浮点数',
                                             `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                             `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                             `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                             `is_manual_modification` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否手动修改：0为否，1为是',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_subsidy_flight_param_join
-- ----------------------------
-- DROP TABLE IF EXISTS `t_subsidy_flight_param_join`;
CREATE TABLE `t_subsidy_flight_param_join` (
                                               `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                               `param_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数表id',
                                               `param_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数值',
                                               `param_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数名字',
                                               `param_belong` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数归属 ：\r\n1为公式参数；\r\n2为航班参数；\r\n3为公式航班都有的参数（参考轮档时间）',
                                               `param_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数类型：1为数值；2为公式; 3为公式类型参数的参数',
                                               `parent_param_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '父参数id（参数类型为子参数时）',
                                               `unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位（公式类型无单位）',
                                               `flight_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航班id',
                                               `num_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字符格式：1为正整数，2为整数，3为浮点数',
                                               `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                               `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                               `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_subsidy_formula
-- ----------------------------
-- DROP TABLE IF EXISTS `t_subsidy_formula`;
CREATE TABLE `t_subsidy_formula` (
                                     `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                     `flight_line_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航线id',
                                     `formula` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '公式',
                                     `start_date` date DEFAULT NULL COMMENT '开始日期',
                                     `end_date` date DEFAULT NULL COMMENT '结束日期',
                                     `applicable_models` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '适用机型',
                                     `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                     `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                     `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_subsidy_formula_bill
-- ----------------------------
-- DROP TABLE IF EXISTS `t_subsidy_formula_bill`;
CREATE TABLE `t_subsidy_formula_bill` (
                                          `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'id',
                                          `subsidy_formula_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '补贴公式id',
                                          `start_date` date DEFAULT NULL COMMENT '结算开始时间',
                                          `end_date` date DEFAULT NULL COMMENT '结算结束时间',
                                          `guaranteed_income` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '保底收入',
                                          `settle_result` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算金额',
                                          `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                          `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                          `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_subsidy_formula_param_join
-- ----------------------------
-- DROP TABLE IF EXISTS `t_subsidy_formula_param_join`;
CREATE TABLE `t_subsidy_formula_param_join` (
                                                `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                                `param_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数表id',
                                                `param_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数值',
                                                `param_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数名字',
                                                `param_belong` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数归属 ：\r\n1为公式参数；\r\n2为航班参数；\r\n3为公式航班都有的参数（参考轮档时间）',
                                                `param_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数类型：1为数值；2为公式; 3为公式类型参数的子参数',
                                                `parent_param_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '父参数id（参数类型为子参数时）',
                                                `unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位（公式类型无单位）',
                                                `subsidy_formula_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '补贴公式表id',
                                                `num_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字符格式：1为正整数，2为整数，3为浮点数',
                                                `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                                `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                                `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_subsidy_param
-- ----------------------------
-- DROP TABLE IF EXISTS `t_subsidy_param`;
CREATE TABLE `t_subsidy_param` (
                                   `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                   `param_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数名字',
                                   `param_belong` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数归属 ：\r\n1为公式参数；\r\n2为航班参数；\r\n3为公式航班都有的参数（参考轮档时间）',
                                   `param_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数类型：1为数值；2为公式; 3为公式类型参数的参数;\r\n4为航班信息参数(非手工填入)',
                                   `param_formula` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '公式（公式类型参数才有值）',
                                   `unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位（公式类型无单位）',
                                   `num_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字符格式：1为正整数，2为整数，3为浮点数',
                                   `bill_param` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否允许在账单中修改：0为否，1为是（参考定额补贴单价）',
                                   `create_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `modified_by` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                   `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 有效:1|无效:0',
                                   `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE KEY `param_name` (`param_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Table structure for t_unmaintained_record
-- ----------------------------
-- DROP TABLE IF EXISTS `t_unmaintained_record`;
CREATE TABLE `t_unmaintained_record` (
                                         `id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '1' COMMENT 'ID',
                                         `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '机场三字码',
                                         `airline_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航司二字码',
                                         `create_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
                                         `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `invalid` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '数据有效 1:有效|0:无效',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='未维护公式航司记录';

-- ----------------------------
-- Table structure for t_variable_guarantee
-- ----------------------------
-- DROP TABLE IF EXISTS `t_variable_guarantee`;
CREATE TABLE `t_variable_guarantee` (
                                        `id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
                                        `variable_id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '结算系统中的特车设备id',
                                        `item_id` bigint NOT NULL COMMENT '节点保障系统t_bill_item特车设备id',
                                        `land_flag` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '过站签单归集   A为降落 D为起飞  B为起飞降落',
                                        `data_update` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '人工录入数据后同步更新   1为允许，0为不允许',
                                        `conversion_rules` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '转换规则    0为无规则\r\n1为“有”统计计算为1次，“无”统计计算为0次\r\n2为60分钟内计为1次，每30分钟数值加0.5，每超过不足30分钟数值加0.5\r\n3为统计计算为1次',
                                        `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '说明',
                                        `invalid` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '数据有效 有效:1|无效:0',
                                        `invalid_date` datetime NOT NULL COMMENT '数据生效日期',
                                        `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                                        `create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `modified_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '修改人',
                                        `modified_time` datetime NOT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_variable_record
-- ----------------------------
-- DROP TABLE IF EXISTS `t_variable_record`;
CREATE TABLE `t_variable_record` (
                                     `id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
                                     `aps_input` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '电子签单采集状态 0:否|1:是',
                                     `dcs_input` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '离岗采集状态 0:否|1:是',
                                     `flight_input` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机信息录入 0:否|1:是',
                                     `variable` tinytext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
                                     `variable_name` tinytext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
                                     `variable_type` tinytext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
                                     `variable_unit_name` tinytext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
                                     `match_table` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '匹配表',
                                     `match_word` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '匹配字段',
                                     `operation_symbol` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '运算符号',
                                     `variable_unit` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '收费单位 T：吨|N：人|H：小时|F：次|M：人工时|S：架次',
                                     `airport_code` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '机场三字码',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='指标量记录';

SET FOREIGN_KEY_CHECKS = 1;
