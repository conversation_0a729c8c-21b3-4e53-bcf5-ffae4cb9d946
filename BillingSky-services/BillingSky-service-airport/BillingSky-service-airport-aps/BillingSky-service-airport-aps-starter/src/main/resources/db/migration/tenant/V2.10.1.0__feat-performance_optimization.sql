DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_flight_bill_settle_month_airport_code')
    THEN
        create index idx_flight_bill_settle_month_airport_code on t_flight_bill(`airport_code`,`settle_month`,`invalid`);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_flight_bill_fee_name')
    THEN
        create index idx_flight_bill_fee_name on t_flight_bill(`fee_name`);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_flight_bill_airline_short_name')
    THEN
        create index idx_flight_bill_airline_short_name on t_flight_bill(`airline_short_name`);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_flight_bill_flight_no')
    THEN
        create index idx_flight_bill_flight_no on t_flight_bill(`flight_no`);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_flight_bill_fee_code')
    THEN
        create index idx_flight_bill_fee_code on t_flight_bill(`fee_code`);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_bill' AND
            index_name='idx_flight_bill_fee_bill_export')
    THEN
        create index idx_flight_bill_fee_bill_export on t_flight_bill(`airport_code`,`airline_short_name`,
                                                                      `charge_price`,`fee_name`,`settle_month`,`invalid`);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='t_flight_info' AND
            index_name='idx_airport_code_flight_date_invalid')
    THEN
        create index idx_airport_code_flight_date_invalid on t_flight_info(`flight_date`,`airport_code`,`invalid`);
    END IF;
END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;