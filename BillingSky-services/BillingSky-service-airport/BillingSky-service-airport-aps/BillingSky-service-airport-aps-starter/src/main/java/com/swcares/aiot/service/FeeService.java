package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.FeeForm;
import com.swcares.aiot.core.form.ServiceFeeForm;
import com.swcares.aiot.core.model.entity.FeeInfo;
import com.swcares.aiot.core.model.vo.FeeCodeAndNameVo;
import com.swcares.aiot.core.model.vo.FeeVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a>
 * Title：FeeService.java
 * Package：com.swcares.modules.settlement.service
 * Description：
 * Copyright © 2020-6-4 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * date 2020-6-4 17:10
 */
public interface FeeService {
    /**
     * Title: pageFeeRulesByCondition<br>
     * Author: 李龙<br>
     * Description: 根据费用代码或费用名称，分页查询费用信息，未输入查询条件则查全部<br>
     * Date:  10:59 <br>
     *
     * @param airportCode :
     * @param feeType     :
     * @param pageParam   :
     * @return : ResultBuilder
     */
    ResultBuilder<Page<FeeInfo>> pageFeeRulesByCondition(String condition, String airportCode, String feeType, PageParam pageParam);

    /**
     * Title: getFeeRulesById<br>
     * Author: 李龙<br>
     * Description: 根据费用id费用<br>
     * Date:  11:23 <br>
     *
     * @param feeId return: ResultBuilder
     */
    ResultBuilder<FeeInfo> getFeeRulesById(String feeId);

    /**
     * Title: delFeeRulesByid<br>
     * Author: 李龙<br>
     * Description: 根据费用id删除费用<br>
     * Date:  13:09 <br>
     *
     * @param feeId :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> delFeeRulesById(String feeId, LoginUserDetails user, String urlName);

    /**
     * Title: saveFeeRule<br>
     * Author: 李龙<br>
     * Description: 新增费用<br>
     * Date:  17:12 <br>
     *
     * @param feeForm :
     * @return ResultBuilder
     */
    ResultBuilder<String> saveFeeRule(FeeForm feeForm, LoginUserDetails user, String urlName);

    /**
     * Title: listFeeRulesByFeeName<br>
     * Author: 李龙<br>
     * Description: 根据费用名称查询费用<br>
     * Date:  15:48 <br>
     *
     * @param feeName return: ResultBuilder
     */
    ResultBuilder<List<FeeInfo>> listFeeRulesByFeeName(String feeName, String airportCode);

    /**
     * Title: listFeeRuleByAirlineId<br>
     * Author: 李龙<br>
     * Description: 根据航司id查询费用<br>
     * Date:  14:23 <br>
     *
     * @param airlineId :
     * @param flag      :
     * @return : ResultBuilder
     */
    ResultBuilder<List<FeeVo>> listFeeRuleByAirlineId(String airlineId, String flag, String airportCode);

    /**
     * Title: updateFeeRulesByFeeId<br>
     * Author: 李龙<br>
     * Description: 修改费用<br>
     * Date:  13:48 <br>
     *
     * @param feeForm return: ResultBuilder
     */
    ResultBuilder<Object> updateFeeRulesByFeeId(FeeForm feeForm, LoginUserDetails user, String urlName);

    /**
     * Title: listFeeByFeeCodeAndFeeType<br>
     * Author: 李龙<br>
     * Description: 根据费用代码和费用类别查询机场下的费用<br>
     * Date:  10:15 <br>
     *
     * @param feeType     :
     * @param airportCode :
     * @param feeCode     return: ResultBuilder
     */
    ResultBuilder<List<FeeVo>> listFeeByFeeCodeAndFeeType(String feeType, String airportCode, String feeCode, String airlineId);

    /**
     * Title: getFeeByFeeCode<br>
     * Author: 李龙<br>
     * Description: 根据费用代码查询费用<br>
     * Date:  15:42 <br>
     *
     * @param feeCode return: ResultBuilder
     */
    ResultBuilder<FeeInfo> getFeeByFeeCode(String feeCode, String feeType);

    /**
     * Title: copyFeeByFeeId<br>
     * Author: 李龙<br>
     * Description: 复制机场下费用到航司<br>
     * Date:  11:09 <br>
     *
     * @param airlineId return: ResultBuilder
     */
    ResultBuilder<List<FeeInfo>> copyFeeByFeeId(List<String> feeIds, String airlineId, LoginUserDetails user);

    /**
     * Title: listFeeByFeeTypeAndAirportCode<br>
     * Author: 李龙<br>
     * Description: (根据费用类别和机场三字码查询模板费用信息)<br>
     * Date:  10:19 <br>
     *
     * @param feeType     :
     * @param airportCode return: ResultBuilder
     */
    ResultBuilder<List<FeeInfo>> listFeeByFeeTypeAndAirportCode(String feeType, String airportCode, String airlineId, String name);

    /**
     * Title: setServiceFeeByFeeId<br>
     * Author: lilong<br>
     * Description: (根据费用id设置费用为机务类费用)<br>
     * Date:  14:40 <br>
     *
     * @param listServiceFee :
     * @param airportCode    :
     *                       return: com.swcares.modules.settlement.common.ResultBuilder
     */
    ResultBuilder<Object> updateServiceFeeByFeeId(List<ServiceFeeForm> listServiceFee, String airportCode);

    /**
     * Title：getDistinctAllFee <br>
     * Description： <br>
     * author ：lihuanyu <br>
     * date：2022/10/25 10:54 <br>
     *
     * @param airportCode :
     * @return : null
     */
    ResultBuilder<List<String>> getDistinctAllFee(String airportCode);

    ResultBuilder<List<FeeInfo>> getFeeByFeeCodeAndFeeNameAndAirlineId(String airportCode, String airlineId, String feeCode, String feeName);

    /**
     * Title：getFeeCodeAndNameByAirportCode <br>
     * Description： <br>
     * author ：liuzhiheng <br>
     * date：2022/12/20 10:54 <br>
     *
     * @param airportCode :
     * @return : null
     */
    ResultBuilder<List<FeeCodeAndNameVo>> getFeeCodeAndNameByAirportCode(String airportCode);

    /**
     * Title: allFormulaExpiredJudgment
     * Description : 定时任务判断全部租户下的全部费用是否过期
     */
    void allFormulaExpiredJudgment();
}
