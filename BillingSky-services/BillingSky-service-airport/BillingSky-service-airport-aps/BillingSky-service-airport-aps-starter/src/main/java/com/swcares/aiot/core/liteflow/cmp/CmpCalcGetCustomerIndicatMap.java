package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.model.dto.ActExpensesIndicatorRetrDto;
import com.swcares.aiot.model.vo.ActCustomerVo;
import com.swcares.aiot.model.vo.ActExpensesIndicatorRetVo;
import com.swcares.aiot.model.vo.ActExpensesVo;
import com.swcares.aiot.service.IActuarialBizService;
import com.swcares.baseframe.common.exception.BusinessException;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.ReCalcGetCustomerIndicatMap
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/12 10:07
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcGetCustomerIndicatMap", name = "组件-对账通机场端-计算模块-获取计算需要的指标项")
public class CmpCalcGetCustomerIndicatMap extends NodeComponent {
    @Resource
    private IActuarialBizService iActuarialBizService;

    @Override
    public void process()  {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        ReCalcDto dto = ctxCalc.getDto();
        Map<String, Set<ActExpensesVo>> customerIndicatMap ;
        // 组装当前客户对应不同客户计算所需要的指标项
        try {
            ActExpensesIndicatorRetrDto indicatorRetrDto = new ActExpensesIndicatorRetrDto();
            List<String> customerIatas=new ArrayList<>();
            customerIatas.add(dto.getAirportCode());
            indicatorRetrDto.setCustomerIatas(customerIatas);
            // 获取目标客户的所有费用项以及费用项对应的指标项
            log.info("获取目标客户的所有费用项以及费用项对应的指标项,远程请求的参数{}", JSONUtil.toJsonStr(indicatorRetrDto));
            // 获取与当前客户签署合同的所有客户、费用项、指标项
            List<ActExpensesIndicatorRetVo> indicatorRetrRes = iActuarialBizService.retrActExpensesIndicator(indicatorRetrDto);
            if (CollUtil.isEmpty(indicatorRetrRes)) {
                log.error("获取目标客户的所有费用项以及费用项对应的指标项为空");
            }
            customerIndicatMap = this.getIndicatorMap(indicatorRetrRes, dto.getAirportCode(), dto.getFeeCodes());
        } catch (Exception e) {
            log.error("获取目标客户的所有费用项以及费用项对应的指标项出错", e);
            throw new BusinessException(ExceptionCodes.FEE_ERROR);
        }
        ctxCalc.setCustomerIndicatMap(customerIndicatMap);
    }

    /**
     * Title: getIndicatorMap
     * 组装指标项map
     */
    private Map<String, Set<ActExpensesVo>> getIndicatorMap(List<ActExpensesIndicatorRetVo> actExpensesIndicatorRetVos, String settleCode, List<String> feeCodes) {
        Map<String, Set<ActExpensesVo>> customerIndicatMap = new HashMap<>();
        for (ActExpensesIndicatorRetVo actExpensesIndicatorRetVo : actExpensesIndicatorRetVos) {
            String customerIataA = actExpensesIndicatorRetVo.getCustomerIataA();
            log.info("甲方客户列表:{}", customerIataA);
            // 乙方客户列表
            List<ActCustomerVo> actCustomerVos = actExpensesIndicatorRetVo.getActCustomerVos();
            if (customerIataA.equals(settleCode)) {
                for (ActCustomerVo actCustomerVo : actCustomerVos) {
                    mapCustomerIataA(actCustomerVo, feeCodes, customerIndicatMap);
                }
            } else {
                for (ActCustomerVo actCustomerVo : actCustomerVos) {
                    mapCustomerIataB(actCustomerVo, feeCodes, customerIndicatMap, settleCode, customerIataA);
                }
            }
        }
        return customerIndicatMap;
    }

    private void mapCustomerIataB(ActCustomerVo actCustomerVo, List<String> feeCodes, Map<String, Set<ActExpensesVo>> customerIndicatMap,
                                  String settleCode, String customerIataA) {
        if (actCustomerVo.getCustomerIataB().equals(settleCode)) {
            for (ActExpensesVo actExpensesVo : actCustomerVo.getActExpensesVos()) {
                if (!feeCodes.isEmpty() && !feeCodes.contains(actExpensesVo.getExpensesCode())) {
                    continue;
                }
                Set<ActExpensesVo> customerCodeList = customerIndicatMap.getOrDefault(customerIataA, new HashSet<>());
                customerCodeList.add(actExpensesVo);
                customerIndicatMap.put(customerIataA, customerCodeList);
            }
        }
    }

    private void mapCustomerIataA(ActCustomerVo actCustomerVo, List<String> feeCodes, Map<String, Set<ActExpensesVo>> customerIndicatMap) {
        List<ActExpensesVo> actExpensesVos = actCustomerVo.getActExpensesVos();
        // 每个乙方客户包含的费用项
        for (ActExpensesVo actExpensesVo : actExpensesVos) {
            if (CollUtil.isNotEmpty(feeCodes) && !feeCodes.contains(actExpensesVo.getExpensesCode())) {
                continue;
            }
            Set<ActExpensesVo> customerCodeList = customerIndicatMap.getOrDefault(actCustomerVo.getCustomerIataB(), new HashSet<>());
            customerCodeList.add(actExpensesVo);
            customerIndicatMap.put(actCustomerVo.getCustomerIataB(), customerCodeList);
        }
    }
}
