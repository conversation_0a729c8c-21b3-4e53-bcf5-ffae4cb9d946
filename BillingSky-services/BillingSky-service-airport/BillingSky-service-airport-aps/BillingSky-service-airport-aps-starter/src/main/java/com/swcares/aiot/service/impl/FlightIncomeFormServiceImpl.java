package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.swcares.aiot.core.model.dto.FlightIncomeFormDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.model.vo.FlightIncomeFormExcelVO;
import com.swcares.aiot.core.model.vo.FlightIncomeFormVO;
import com.swcares.aiot.core.common.util.EasyExcelUtil;
import com.swcares.aiot.mapper.FlightIncomeFormBizMapper;
import com.swcares.aiot.service.FlightIncomeFormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：FlightIncomeFormServiceImpl
 * Description：机场收入报表 实现类
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/9/20 16:41
 * Version v1.0
 */
@Service
@Slf4j
public class FlightIncomeFormServiceImpl implements FlightIncomeFormService {

    @Resource
    private FlightIncomeFormBizMapper flightIncomeFormBizMapper;

    private static final String INCOME_EXCEL_NAME = "机场收入报表";

    @Override
    public IPage<FlightIncomeFormVO> page(FlightIncomeFormDTO dto) {
        return flightIncomeFormBizMapper.page(dto, new Page<>(dto.getPage(), dto.getLimit()));
    }

    @Override
    public String incomeCount(FlightIncomeFormDTO dto) {
        return flightIncomeFormBizMapper.incomeCount(dto);
    }

    @Override
    public void exportFlightIncomeForm(FlightIncomeFormDTO dto, HttpServletResponse response) {
        List<FlightIncomeFormExcelVO> resultList = flightIncomeFormBizMapper.exportFlightIncomeForm(dto);
        EasyExcelUtil.responseExcel(response, INCOME_EXCEL_NAME, "sheet1", FlightIncomeFormExcelVO.class, resultList,
                CollUtil.toList(new LongestMatchColumnWidthStyleStrategy()));
    }
}
