package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.cons.ConsAirportAps;
import com.swcares.aiot.core.form.FlightInfoSearchForm;
import com.swcares.aiot.core.form.ServiceSecordBatchForm;
import com.swcares.aiot.core.model.dto.ServiceRecordSaveBatchDto;
import com.swcares.aiot.core.model.dto.ServiceRecordSaveByFlightDto;
import com.swcares.aiot.core.model.entity.ServiceRecord;
import com.swcares.aiot.core.model.vo.NewServiceRecordVo;
import com.swcares.aiot.core.model.vo.ServiceRecordVo;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.aiot.service.NewServiceRecordService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.controller.NewServiceRecordController
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/7 15:41
 * @version v1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/newServiceRecord")
@Api(value = "NewServiceRecordController", tags = {"业务保障数据接口"})
@ApiVersion(value = ConsAirportAps.API_MODULE_NAME)
public class NewServiceRecordController {

    @Resource
    private NewServiceRecordService newServiceRecordService;

    @ApiOperation(value = "获取所有的保障指标项")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/getAllIndicator")
    public BaseResult<List<IndAllIndicatorRetrVo>> getAllIndicator() {
        return BaseResult.ok(newServiceRecordService.getServiceDict());
    }

    @PostMapping("/saveDeviceUsedListData")
    @ApiOperation(value = "批量保存code的特车/设备数据)")
    public Object saveDeviceUsedListData(@RequestBody @Validated List<ServiceRecordSaveBatchDto> serviceRecordSaveBatchDtoList) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return BaseResult.ok(newServiceRecordService.saveDeviceUsedListData(serviceRecordSaveBatchDtoList, user));
    }

    @PostMapping("/saveDeviceUsedListByFlight")
    @ApiOperation(value = "批量保存航班下的所有的特车/设备数据)")
    public Object saveDeviceUsedListByFlight(@RequestBody @Validated  ServiceRecordSaveByFlightDto saveByFlightDto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return BaseResult.ok(newServiceRecordService.saveDeviceUsedListByFlight(saveByFlightDto, user));
    }

    @ApiOperation(value = "航班视图-查询某个航班的特车/设备数据")
    @GetMapping("/listDeviceUsedDataByFlightId")
    public BaseResult<Map<String, List<NewServiceRecordVo>>> listDeviceUsedDataByFlightId(@ApiParam(name = "flightId", value = "航班id", required = true) @RequestParam @NotBlank(message = "flightId不能为空") String flightId) {
        return BaseResult.ok( newServiceRecordService.listDeviceUsedDataByFlightId(flightId));
    }

    @PostMapping("/importFlightBusinessData")
    @ApiOperation(value = "业务保障数据导入")
    public BaseResult<String> importFlightBusinessData(@RequestBody @ApiParam(name = "file", value = "导入文件") MultipartFile file,
                                                       @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return BaseResult.ok(newServiceRecordService.importFlightBusinessData(file, airportCode, user));
    }


    @ApiOperation(value = "业务保障数据导出")
    @GetMapping(value = "/exportFlightBusinessData")
    public void exportFlightBusinessData(@Validated @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件") FlightInfoSearchForm flightInfoSearchForm,
                                         HttpServletResponse response) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        newServiceRecordService.exportFlightBusinessData(flightInfoSearchForm, response, user);
    }

    @GetMapping(value = "/exportFlightBusinessDataTemplate")
    @ApiOperation(value = "业务保障数据-模板下载")
    public void exportFlightBusinessDataTemplate(HttpServletResponse response) {
        newServiceRecordService.exportFlightBusinessDataTemplate(response);
    }



}
