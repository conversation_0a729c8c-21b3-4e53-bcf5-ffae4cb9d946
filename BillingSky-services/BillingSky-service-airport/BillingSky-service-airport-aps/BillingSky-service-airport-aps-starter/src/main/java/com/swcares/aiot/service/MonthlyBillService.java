package com.swcares.aiot.service;

import com.swcares.aiot.core.model.dto.ErAutoConfirmDto;
import com.swcares.aiot.core.model.dto.ErBillFulfillAndConfirmDto;
import com.swcares.aiot.core.model.dto.ErMonthlyBillPageDto;
import com.swcares.aiot.core.model.dto.ErMonthlyObjectionDto;
import com.swcares.aiot.core.model.vo.*;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.MonthlyBillService
 * Description：离港返还月账单管理Service
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/28 11:26
 * @version v1.0
 */
public interface MonthlyBillService {

    /**
     * 机场端-月账单分页查询
     *
     * @param monthlyBillPageDto 参数
     * @return 结果
     */
    PagedResult<List<ErMonthlyBillPageVo>> page(ErMonthlyBillPageDto monthlyBillPageDto);

    /**
     * 机场端-分页-统计[每月账单]
     *
     * @param monthlyBillPageDto 参数
     * @return 结果
     */
    BaseResult<ErMonthlyBillAccVo> count(ErMonthlyBillPageDto monthlyBillPageDto);

    /**
     * 机场端-确认[每月账单]
     *
     * @param id 参数
     * @return 结果
     */
    BaseResult<Boolean> confirmId( Long id);

    /**
     * 开启/关闭月账单自动确认
     *
     * @param erAutoConfirmDto 参数
     * @return 结果
     */
    BaseResult<Boolean> switchMonthlyAutoConfirm( ErAutoConfirmDto erAutoConfirmDto);

    /**
     * 月账单自动确认查询
     *
     * @param airportCode 参数
     * @return 结果
     */
    BaseResult<Boolean> monthlyAutoConfirmQuery(String airportCode);

    /**
     * 异议记录查询
     *
     * @param id 月账单id
     * @return 结果
     */
    BaseResult<List<ErMonthlyBillHistoryVo>> listErMonthlyBillObjectionHistoryVo(String id);


    /**
     * 提出异议
     *
     * @param erMonthlyObjectionDto 异议dto
     * @return 结果
     */
    BaseResult<Boolean> objection(ErMonthlyObjectionDto erMonthlyObjectionDto);

    /**
     * 查询Host航费用总计的明细
     *
     * @param billingBillId 参数
     * @return 结果
     */
    BaseResult<ErBillingBillPageVo> getBillingBillHis( String billingBillId);


    /**
     *  确认支付
     * @param erBillFulfillAndConfirmDto 参数
     * @return 结果
     */
    BaseResult<ErBillFulfillAndConfirmVo> confirmPay(ErBillFulfillAndConfirmDto erBillFulfillAndConfirmDto);

    /**
     * 下载月账单
     * @param erMonthlyBillPageDto 参数
     *
     */
    void downloadErMonthlyBillData(ErMonthlyBillPageDto erMonthlyBillPageDto, HttpServletResponse response) throws IOException;

    /**
     * 月账单异议附件上传
     *
     * @param file 上传文件
     * @return fileKey
     */
    BaseResult<String> uploadFile( MultipartFile file);
}
