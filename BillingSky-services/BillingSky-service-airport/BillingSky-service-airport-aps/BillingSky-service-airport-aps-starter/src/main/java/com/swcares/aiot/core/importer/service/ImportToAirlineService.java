package com.swcares.aiot.core.importer.service;

import com.swcares.aiot.core.model.entity.FlightInfo;

import java.util.List;

/**
 * ClassName：com.swcares.importer.service.ImportToAirlineService
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/1/23 10:18
 * @version v1.0
 */
public interface ImportToAirlineService {
    void sendToAirline(String fiId,Long tenanId);

    void sendToAirline(List<FlightInfo> flightInfoList, Long tenant);

    void sendToAirlineById(List<String> flightIdList, Long tenant);
}
