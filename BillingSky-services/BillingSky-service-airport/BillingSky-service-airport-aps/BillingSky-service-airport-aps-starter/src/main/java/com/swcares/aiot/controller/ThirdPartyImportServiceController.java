package com.swcares.aiot.controller;

import com.swcares.aiot.core.model.dto.ThirdPartyImportFlightDto;
import com.swcares.aiot.core.model.dto.ThirdPartyRelaDto;
import com.swcares.aiot.core.model.dto.ThirdPartyServiceDeleteDto;
import com.swcares.aiot.core.model.vo.ThirdPartyServiceVo;
import com.swcares.aiot.service.ThirdPartyImportServiceService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.ThirdPartyImportServiceController
 * Description：第三方导入业务保障数据
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/23 09:42
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/import/service")
@Api(value = "ThirdPartyImportServiceController", tags = {"导入业务保障数据接口"})
@Slf4j
public class ThirdPartyImportServiceController extends BaseController {

    @Resource
    private ThirdPartyImportServiceService thirdPartyImportServiceService;

    @ApiOperation(value = "查询所有业务保障服务信息 ")
    @GetMapping("/getAllServiceInfo")
    public BaseResult<List<ThirdPartyServiceVo>> getAllServiceInfo(@RequestParam String airportCode) {
        return ok(thirdPartyImportServiceService.getAllServiceInfo(airportCode));
    }

    @ApiOperation(value = "新增或更新业务保障服务匹配关系")
    @PostMapping("/saveRela")
    public BaseResult<Boolean> saveOrUpdateRela(@RequestBody @Validated ThirdPartyRelaDto thirdPartyRelaDto) {
        return ok(thirdPartyImportServiceService.saveRela(thirdPartyRelaDto));
    }

    @ApiOperation(value = "删除业务保障服务匹配关系")
    @PutMapping("/deleteRela")
    public BaseResult<Boolean> deleteRela(@RequestBody @Validated ThirdPartyServiceDeleteDto thirdPartyServiceDeleteDto) {
        return ok(thirdPartyImportServiceService.deleteRela(thirdPartyServiceDeleteDto));
    }

    @ApiOperation(value = "导入业务保障数据")
    @PostMapping("/importServiceRecord")
    public BaseResult<Boolean> importServiceRecord(@RequestBody @Validated ThirdPartyImportFlightDto thirdPartyImportFlightDto) {
        return ok(thirdPartyImportServiceService.importServiceRecord(thirdPartyImportFlightDto));
    }

}
