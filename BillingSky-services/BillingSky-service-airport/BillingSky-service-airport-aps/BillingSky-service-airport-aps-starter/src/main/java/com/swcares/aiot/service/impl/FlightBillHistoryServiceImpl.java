package com.swcares.aiot.service.impl;

import com.swcares.aiot.core.entity.TFlightBillHistory;
import com.swcares.aiot.mapper.FlightBillHistoryMapper;
import com.swcares.aiot.service.FlightBillHistoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 航班账单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Service
public class FlightBillHistoryServiceImpl implements FlightBillHistoryService {

    @Resource
    private FlightBillHistoryMapper flightBillHistoryMapper;

    @Override
    public void copyBillHistory(List<String> billIds, String name, String operation) {
        flightBillHistoryMapper.copyBillHistory(billIds, name, operation);
    }

    @Override
    public List<TFlightBillHistory> getTFlightBillHistoryByFlightBillIds(List<String> flightBillIds, String operation) {
        return flightBillHistoryMapper.getTFlightBillHistoryByFlightBillIds(flightBillIds, operation);
    }
}
