package com.swcares.aiot.core.mapstruct;

import com.swcares.aiot.core.entity.TServiceRecord;
import com.swcares.aiot.core.entity.TServiceRecordConfirm;
import com.swcares.aiot.core.model.dto.ServiceRecordDataDto;
import com.swcares.aiot.core.model.vo.NewServiceRecordVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.core.mapstruct.MsServiceRecord
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/20 11:07
 * @version v1.0
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MsServiceRecord {

    TServiceRecord saveBatchDtoToEntity(ServiceRecordDataDto serviceRecordDataDto);

    List<TServiceRecordConfirm> srToConfirm(List<TServiceRecord> tServiceRecordList);

    NewServiceRecordVo entityToVo(TServiceRecord tServiceRecord);

    @Mapping(target = "id", ignore = true)
    TServiceRecord copyServiceRecord(TServiceRecord serviceRecord);
}
