package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.model.dto.FeeBillPagedDTO;
import com.swcares.aiot.core.model.vo.FeeBillCountVO;
import com.swcares.aiot.core.model.vo.FeeBillVO;
import com.swcares.baseframe.common.security.LoginUserDetails;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * ClassName：FeeBillService <br>
 * Description：(费用账单业务逻辑层接口)<br>
 * Copyright © 2020/6/16 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/16 14:42<br>
 * @version v1.0 <br>
 */
public interface FeeBillService {

    /**
     * Title: pageFeeBillInfoByCondition<br>
     * Author: 叶咏秋<br>
     * Description: (费用账单动态条件分页查询)<br>
     * Date:  14:42 <br>
     *
     * @param dto ：
     */
    IPage<FeeBillVO> pageFeeBillInfoByCondition(FeeBillPagedDTO dto);

    /**
     * Title: countTotal<br>
     * Author: 叶咏秋<br>
     * Description: (费用账单总计)<br>
     * Date:  15:12 <br>
     *
     * @param dto return: ResultBuilder
     */
    FeeBillCountVO countTotal(FeeBillPagedDTO dto);

    /**
     * Title: exportFeeBillInfo<br>
     * Author: 叶咏秋<br>
     * Description: (导出费用账单)<br>
     * Date:  16:28 <br>
     *
     * @param dto :
     * @param response    return: void
     */
    void exportFeeBillInfo(FeeBillPagedDTO dto, HttpServletResponse response, LoginUserDetails user, String urlName) throws IOException;
}
