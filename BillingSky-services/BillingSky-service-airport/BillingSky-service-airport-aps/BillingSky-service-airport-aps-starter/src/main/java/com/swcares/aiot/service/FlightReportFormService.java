package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.model.dto.FlightReportFormDTO;
import com.swcares.aiot.core.model.vo.FlightReportFormVO;
import com.swcares.aiot.core.model.vo.FlightTrendVO;
import com.swcares.aiot.core.param.PageParam;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * ClassName：FlightReportFormService
 * Description：航班客座率报表 service接口
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2024/6/25 14:23
 * @version v1.0
 */
public interface FlightReportFormService {

    /***
     * Title：page
     * Description：航班客座率报表分页
     * author：李军呈
     * date： 2024/6/25 16:54
     * param pageParam
     * param dto
     * return com.swcares.aiot.core.common.ResultBuilder
     */
    ResultBuilder<Page<FlightReportFormVO>> page(PageParam pageParam, FlightReportFormDTO dto);

    /***
     * Title：getThroughputAndPlfTrend
     * Description：客座率趋势和吞吐量趋势接口
     * author：李军呈
     * date： 2024/6/25 16:56
     * @param dto :
     * @return com.swcares.aiot.core.common.ResultBuilder
     */
    ResultBuilder<List<FlightTrendVO>> getThroughputAndPlfTrend(FlightReportFormDTO dto);

    /***
     * Title：exportFlightReportForm
     * Description：航班客座率报表导出
     * author：李军呈
     * date： 2024/6/27 13:33
     * @param dto :
     */
    void exportFlightReportForm(FlightReportFormDTO dto, HttpServletResponse response);
}
