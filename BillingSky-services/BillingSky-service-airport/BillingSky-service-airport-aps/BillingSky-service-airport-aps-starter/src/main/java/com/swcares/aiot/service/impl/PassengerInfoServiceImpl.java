package com.swcares.aiot.service.impl;

import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.aiot.dao.PassengerInfoDao;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.core.model.entity.PassengerInfo;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.PassengerInfoForm;
import com.swcares.aiot.core.form.PassengerInfoSearchForm;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.LogService;
import com.swcares.aiot.service.PassengerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * ClassName：PassengerInfoServiceImpl <br>
 * Description：(旅客信息业务逻辑处理)<br>
 * Copyright © 2020/6/3 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/3 14:19<br>
 * @version v1.0 <br>
 */

@Slf4j
@Service
public class PassengerInfoServiceImpl implements PassengerInfoService {

    @Resource
    private PassengerInfoDao passengerInfoDao;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private LogService logService;

    /**
     * Title: pagePassengerInfoByFlightId<br>
     * Author: 叶咏秋<br>
     * Description: (通过航班id分页查询该航班所有旅客信息)<br>
     * Date:  14:22 <br>
     *
     * @param pageParam
     * @param flightId  return: ResultBuilder
     */
    @Override
    public ResultBuilder pagePassengerInfoByFlightId(PageParam pageParam, String flightId) {
        Pageable pageable = PageRequest.of(pageParam.getPage() - 1, pageParam.getLimit());
        Page<PassengerInfo> pageList = passengerInfoDao.pagePassengerInfoByFlightId(pageable, flightId);
        return new ResultBuilder.Builder().data(pageList).builder();
    }

    /**
     * Title: pagePassengerInfoByCondition<br>
     * Author: 叶咏秋<br>
     * Description: (动态条件分页查询指定航班的旅客信息)<br>
     * Date:  17:52 <br>
     *
     * @param pageParam
     * @param passengerInfoSearchForm return: ResultBuilder
     */
    @Override
    public ResultBuilder pagePassengerInfoByCondition(PageParam pageParam,
                                                      PassengerInfoSearchForm passengerInfoSearchForm) {
        Pageable pageable = PageRequest.of(pageParam.getPage() - 1, pageParam.getLimit());
        Page<PassengerInfo> pageList = passengerInfoDao.pagePassengerInfoByCondition(pageable,
                passengerInfoSearchForm.getFlightId(),
                passengerInfoSearchForm.getTkNo(),
                passengerInfoSearchForm.getPname(),
                passengerInfoSearchForm.getCabin());
        return new ResultBuilder.Builder().data(pageList).builder();
    }

    /**
     * Title: savePassengerInfo<br>
     * Author: 叶咏秋<br>
     * Description: (新增旅客信息)<br>
     * Date:  18:21 <br>
     *
     * @param passengerInfoForm
     * @param user           return: ResultBuilder
     */
    @Transactional
    @Override
    public ResultBuilder savePassengerInfo(PassengerInfoForm passengerInfoForm, LoginUserDetails user, String urlName) {
        PassengerInfo passengerInfo = new PassengerInfo();
        //新增不需要id,但必须要航班id
        passengerInfoForm.setId(null);
        try {
            //passengerInfoForm copy到passengerInfo
            BeanUtils.copyProperties(passengerInfo, passengerInfoForm);
        } catch (Exception e) {
            log.error("copyProperties()------ error,caused by:", e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(),
                    BusinessMessageEnum.COPY_ATTR_ERROR.getCode());
        }
        passengerInfo.setCreateBy(user.getUsername());
        passengerInfo.setCreateTime(new Date());
        passengerInfoDao.save(passengerInfo);
        String content =Constants.LogEnum.LOG_MEG_1.getValue()+
                urlName+
                Constants.LogEnum.LOG_MEG_SAVE.getValue();
        FlightInfo flightInfo = flightInfoDao.getFlightInfoById(passengerInfoForm.getFlightId());
        logService.addLogForSave(passengerInfoForm,user,content,flightInfo.getAirportCode());
        return new ResultBuilder.Builder().builder();
    }

    /**
     * Title: getPassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (通过旅客id查询旅客信息)<br>
     * Date:  9:43 <br>
     *
     * @param pid return: ResultBuilder
     */
    @Override
    public ResultBuilder getPassengerInfoById(String pid) {
        PassengerInfo passengerInfo = passengerInfoDao.getPassengerInfoById(pid);
        return new ResultBuilder.Builder().data(passengerInfo).builder();
    }

    /**
     * Title: updatePassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (更新旅客信息)<br>
     * Date:  18:37 <br>
     *
     * @param passengerInfoForm
     * @param user            return: ResultBuilder
     */
    @Transactional
    @Override
    public ResultBuilder updatePassengerInfoById(PassengerInfoForm passengerInfoForm, LoginUserDetails user,String urlName) {
        //更新前，先查询数据是否存在，即数据是否有效
        PassengerInfo passengerInfo = passengerInfoDao.getPassengerInfoById(passengerInfoForm.getId());
        PassengerInfoForm passengerInfoForm1 = new PassengerInfoForm();
        //若为空,抛出数据不存在异常
        if (passengerInfo == null) {
            throw new GenericException(BusinessMessageEnum.DATA_NOT_EXIST.getCode(),
                    BusinessMessageEnum.DATA_NOT_EXIST.getMsg());
        }
        try {
            BeanUtils.copyProperties(passengerInfoForm1,passengerInfo);
            BeanUtils.copyProperties(passengerInfo, passengerInfoForm);
        } catch (Exception e) {
            log.error("copyProperties()------ error,caused by:", e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(),
                    BusinessMessageEnum.COPY_ATTR_ERROR.getCode());
        }
        passengerInfo.setModifiedBy(user.getUsername());
        passengerInfo.setModifiedTime(new Date());
        passengerInfoDao.save(passengerInfo);
        String content = Constants.LogEnum.LOG_MEG_1.getValue()+
                urlName+
                Constants.LogEnum.LOG_MEG_UPDATE.getValue();
        passengerInfoForm1.setId(null);
        FlightInfo flightInfo = flightInfoDao.getFlightInfoById(passengerInfo.getFlightId());
        logService.addLogForUpdate(passengerInfoForm1,passengerInfoForm,user,content,flightInfo.getAirportCode());
        return new ResultBuilder.Builder().builder();
    }

    /**
     * Title: delPassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (批量删除旅客信息,即数据有效状态设置为0)<br>
     * Date:  9:22 <br>
     *
     * @param ids
     * @param user return: ResultBuilder
     */
    @Transactional
    @Override
    public ResultBuilder delPassengerInfoById(String ids, LoginUserDetails user,String urlName) {
        //主键拆分
        String[] id = ids.split(",");
        List<String> listId = new ArrayList<>();
        Collections.addAll(listId, id);
        //先查询数据是否存在
        List<PassengerInfo> passengerInfoList = passengerInfoDao.getPassengerInfoByIds(listId);
        if (passengerInfoList.size() == 0) {
            throw new GenericException(BusinessMessageEnum.DATA_NOT_EXIST.getCode(),
                    BusinessMessageEnum.DATA_NOT_EXIST.getMsg());
        }

        String content =Constants.LogEnum.LOG_MEG_1.getValue()+
                urlName+
                Constants.LogEnum.LOG_MEG_DELETE.getValue();
        for (String idd:listId){
            PassengerInfoForm form = new PassengerInfoForm();
            PassengerInfo passengerInfo = passengerInfoDao.getPassengerInfoById(idd);
            FlightInfo flightInfo = flightInfoDao.getFlightInfoById(passengerInfo.getFlightId());
            try {
                BeanUtils.copyProperties(form,passengerInfo);
            } catch (IllegalAccessException e) {
                log.error("BeanUtils.copyProperties fail ! form = {}", form, e);
            } catch (InvocationTargetException e) {
                log.error("BeanUtils.copyProperties fail ! form = {}", form, e);
            }
            form.setId(null);
            form.setFlightId(null);
            logService.addLogForDelete(form,user,content,flightInfo.getAirportCode());
        }

        //将旅客信息数据状态设置为无效
        passengerInfoDao.updatePassengerInfoBatchById(listId, user.getUsername(), new Date());

        return new ResultBuilder.Builder().builder();
    }

    /**
     * Title: exportPassengerInfoBatch<br>
     * Author: 叶咏秋<br>
     * Description: (批量导出旅客信息)<br>
     * Date:  11:26 <br>
     *
     * @param passengerInfoSearchForm
     * @param response                return: void
     */
    @Override
    public void exportPassengerInfoBatch(PassengerInfoSearchForm passengerInfoSearchForm,
                                         HttpServletResponse response,String urlName,LoginUserDetails user) {
        //先从数据库查询数据
        List<PassengerInfo> resultList = passengerInfoDao.listPassengerInfoByCondition(
                passengerInfoSearchForm.getFlightId(),
                passengerInfoSearchForm.getTkNo(),
                passengerInfoSearchForm.getPname(),
                passengerInfoSearchForm.getCabin());
        //导出excel
        XSSFWorkbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet("PassengerInfo");//创建一张表
        Row titleRow = sheet.createRow(0);//创建第一行，起始为0
        titleRow.createCell(0).setCellValue(Constants.PassengerInfoEnum.TK_NO.getValue());//第一列
        titleRow.createCell(1).setCellValue(Constants.PassengerInfoEnum.PNR_NO.getValue());
        titleRow.createCell(2).setCellValue(Constants.PassengerInfoEnum.CABIN.getValue());
        titleRow.createCell(3).setCellValue(Constants.PassengerInfoEnum.CN_NAME.getValue());
        titleRow.createCell(4).setCellValue(Constants.PassengerInfoEnum.EN_NAME.getValue());
        titleRow.createCell(5).setCellValue(Constants.PassengerInfoEnum.FROM_AIRPORT_CODE.getValue());
        titleRow.createCell(6).setCellValue(Constants.PassengerInfoEnum.TO_AIRPORT_CODE.getValue());
        titleRow.createCell(7).setCellValue(Constants.PassengerInfoEnum.SEAT_NO.getValue());
        int cell = 1;
        for (PassengerInfo passengerInfo : resultList) {
            Row row = sheet.createRow(cell);//从第二行开始保存数据
            //将数据库的数据遍历出来
            row.createCell(0).setCellValue(passengerInfo.getTkNo() == null ? "" : passengerInfo.getTkNo());
            row.createCell(1).setCellValue(passengerInfo.getPnrNo() == null ? "" : passengerInfo.getPnrNo());
            row.createCell(2).setCellValue(passengerInfo.getCabin() == null ? "" : passengerInfo.getCabin());
            row.createCell(3).setCellValue(passengerInfo.getCnName() == null ? "" : passengerInfo.getCnName());
            row.createCell(4).setCellValue(passengerInfo.getEnName() == null ? "" : passengerInfo.getEnName());
            row.createCell(5).setCellValue(passengerInfo.getFromAirportCode() == null ? "" :
                    passengerInfo.getFromAirportCode());
            row.createCell(6).setCellValue(passengerInfo.getToAirportCode() == null ? "" :
                    passengerInfo.getToAirportCode());
            row.createCell(7).setCellValue(passengerInfo.getSeatNo() == null ? "" : passengerInfo.getSeatNo());
            cell++;
        }
        String fileName = Constants.PassengerInfoEnum.PASSENGER_INFO_EXCEL_NAME.getValue() + "_" +
                FormatUtils.formatDateToString(new Date()) +
                Constants.PassengerInfoEnum.PASSENGER_INFO_EXCEL_NAME_SUFFIX.getValue();
        FileUtils.exportToExcel(wb, fileName, response);
        String content = Constants.LogEnum.LOG_MEG_1.getValue()
                +urlName+
                Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        FlightInfo flightInfo = flightInfoDao.getFlightInfoById(passengerInfoSearchForm.getFlightId());
        logService.addLogForExport(user,passengerInfoSearchForm,content,flightInfo.getAirportCode(),"旅客信息");
    }
}
