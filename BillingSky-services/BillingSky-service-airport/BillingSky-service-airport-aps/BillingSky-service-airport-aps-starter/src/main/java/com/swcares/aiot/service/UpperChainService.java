package com.swcares.aiot.service;

import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.form.SubmitForm;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * Description
 * date 2024/9/29
 */
public interface UpperChainService {

    /**
     * 查询机场端上链数据
     */
    List<TFlightBill> getTFlightBill(SubmitForm form);

    /**
     * 机场端上链
     */
    void upperChain(List<TFlightBill> tFlightBillList);

}
