package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.form.DeleteForm;
import com.swcares.aiot.core.form.NoticePageForm;
import com.swcares.aiot.core.model.vo.UploadStatusVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.UploadStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.controller.NoticeController
 * Description：消息通知接口
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/10/24 10:25
 * @version v1.0
 */
@RestController
@RequestMapping("/api/notice")
@Api(value = "NoticeController", tags = {"消息通知接口"})
public class NoticeController {

    @Resource
    private UploadStatusService uploadStatusService;

    @ApiOperation(value = "获取当前用户未读上传记录 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/getUnreadUploadCount")
    public ResultBuilder<Integer> getUnreadUploadCount(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        int data = uploadStatusService.getUnreadUploadCount(airportCode, user);
        return new ResultBuilder.Builder<Integer>().data(data).builder();
    }

    @ApiOperation(value = "获取当前用户上传与结算总共未读条数 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/getUnreadTotal")
    public ResultBuilder<Integer> getUnreadTotal(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        int data = uploadStatusService.getUnreadTotal(airportCode, user);
        return new ResultBuilder.Builder<Integer>().data(data).builder();
    }

    @ApiOperation(value = "分页查询上传记录 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/pageUploadStatus")
    public ResultBuilder<Pager<UploadStatusVo>> pageUploadStatus(@Validated NoticePageForm form,
                                                 @ApiParam(name = "pageParam", value = "分页与条件") @Validated PageParam pageParam) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        Pager<UploadStatusVo> data = uploadStatusService.pageUploadStatus(form.getAirportCode(), form.getStartDate(), form.getEndDate(), pageParam, user);
        return new ResultBuilder.Builder<Pager<UploadStatusVo>>().data(data).builder();
    }

    @ApiOperation(value = "查询最近十条上传记录 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/listLatelyUploadStatus")
    public ResultBuilder<List<UploadStatusVo>> pageUploadStatus(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        List<UploadStatusVo> data = uploadStatusService.listLatelyUploadStatus(airportCode, user);
        return new ResultBuilder.Builder<List<UploadStatusVo>>().data(data).builder();
    }

    @ApiOperation(value = "批量删除上传记录 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @DeleteMapping("/delete")
    public ResultBuilder<Object> delete(@RequestBody @Validated DeleteForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        uploadStatusService.delete(form.getIds(), user);
        return new ResultBuilder.Builder<>().builder();
    }

}
