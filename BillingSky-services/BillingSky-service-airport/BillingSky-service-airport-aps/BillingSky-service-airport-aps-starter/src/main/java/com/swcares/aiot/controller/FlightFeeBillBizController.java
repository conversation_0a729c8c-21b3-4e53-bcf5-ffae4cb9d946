package com.swcares.aiot.controller;

import com.swcares.aiot.core.cons.ConsAirportAps;
import com.swcares.aiot.core.entity.TFlightBillHistorySnapshot;
import com.swcares.aiot.dto.FlightFeeBillDownloadDto;
import com.swcares.aiot.service.IFlightFeeBillBizService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.NewFlightBillController
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/14 10:46
 * @version v1.0
 */
@Slf4j
@RestController
@RequestMapping("/flightFeeBillBiz")
@Api(tags = {"航班明细账单接口[新]"})
@ApiVersion(value = ConsAirportAps.API_MODULE_NAME)
public class FlightFeeBillBizController {

    @Resource
    private IFlightFeeBillBizService iFlightFeeBillBizService;

    @PostMapping(value = "/download")
    @ApiOperation(value = "导出航班明细账单")
    public void download(@RequestBody @Valid FlightFeeBillDownloadDto dto, HttpServletResponse response) {
        this.iFlightFeeBillBizService.download(dto, response);
    }

    @ApiOperation(value = "获取航班明细账单快照")
    @GetMapping(value = "/getSnapshot/{billHistoryId}")
    public BaseResult<List<TFlightBillHistorySnapshot>> getSnapshot(@PathVariable("billHistoryId") String billHistoryId) {
        List<TFlightBillHistorySnapshot> snapshots = this.iFlightFeeBillBizService.getSnapshot(billHistoryId);
        return BaseResult.ok(snapshots);
    }
}
