package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.AirlineForm;
import com.swcares.aiot.core.model.entity.AirlineInfoAps;
import com.swcares.aiot.core.model.vo.AirlineVo;
import com.swcares.aiot.core.model.vo.UnmaintainedAirlineVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.AirlineService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：AirlineController <br>
 * Description：航司接口类<br>
 * Copyright © 2020-5-14 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-5-14 15:47<br>
 * @version v1.0 <br>
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/airline")
@Api(value = "AirlineController", tags = {"航司操作接口"})
@Slf4j
public class AirlineController {
    @Resource
    private AirlineService airlineService;

    @ApiImplicitParams({
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airlineShortName", value = "航司简称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "settleCode", value = "结算代码", dataType = "String", paramType = "query"),
    })
    @ApiOperation(value = "根据航司二字码、航司简称、结算代码查询机场下的航司信息，未输入则查全部")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirlineVo.class)})
    @GetMapping(value = "/pageAirlineInfoByCondition")
    public Object pageAirlineInfoByCondition(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true) String airportCode,
                                             @ApiParam(name = "pageParam", value = "分页信息") @Validated PageParam pageParam,
                                             String airlineCode, String airlineShortName, String settleCode, String isExpired) {
        return airlineService.pageAirlineInfoByCondition(airportCode, airlineCode, airlineShortName, settleCode, isExpired, pageParam);
    }

    @ApiOperation(value = "新增航司信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/saveAirlineInfo")
    public Object saveAirlineInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                  @RequestBody @ApiParam(name = "airline", value = "航司信息") @Valid AirlineForm airlineForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airlineService.saveAirlineInfo(airlineForm, user, urlName);
    }

    @ApiOperation(value = "根据航司简称查询航司")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirlineInfoAps.class)})
    @GetMapping(value = "/getAirlineInfoByShortName")
    public Object getAirlineInfoByShortName(@RequestParam @ApiParam(name = "shortName", value = "航司简称", required = true) String shortName) {
        return airlineService.getAirlineInfoByShortName(shortName);
    }

    @ApiOperation(value = "查询机场未维护的航司")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = UnmaintainedAirlineVo.class),})
    @GetMapping(value = "/listNotStoredAirlineInfoByAirport")
    public Object listNotStoredAirlineInfoByAirport(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true) String airportCode) {
        return airlineService.listNotStoredAirlineInfoByAirport(airportCode);
    }

    @ApiOperation(value = "NEW根据航司id查询航司信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirlineInfoAps.class),})
    @GetMapping(value = "/getAirlineById")
    public Object getAirlineById(@RequestParam @ApiParam(name = "id", value = "航司id", required = true) String id) {
        return airlineService.getAirlineById(id);
    }

    @ApiOperation(value = "根据航司id删除航司信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class),})
    @DeleteMapping(value = "/delAirlineById")
    public Object delAirlineById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                 @RequestParam @ApiParam(name = "id", value = "航司id", required = true) String id) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airlineService.delAirlineById(id, user, urlName);
    }

    @ApiOperation(value = "修改航司信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PutMapping(value = "/updateAirlineInfoById")
    public Object updateAirlineInfoById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                        @RequestBody @ApiParam(name = "airlineForm", value = "航司信息") @Validated({Update.class}) AirlineForm airlineForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airlineService.updateAirlineInfoById(airlineForm, user, urlName);
    }

    @ApiOperation(value = "查询机场已维护的航司信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirlineInfoAps.class)})
    @GetMapping(value = "/listMaintainedAirlineByAirport")
    public Object listMaintainedAirlineByAirport(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return airlineService.listMaintainedAirlineByAirport(airportCode);
    }

    @ApiOperation(value = "根据航司2字码查询机场信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirlineInfoAps.class)})
    @GetMapping(value = "/listAirlineByAirlineCode")
    public Object listAirlineByAirlineCode(@RequestParam @ApiParam(name = "airlineCode", value = "航司二字码", required = true) String airlineCode,
                                           @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return airlineService.listAirlineByAirlineCode(airlineCode, airportCode);
    }

    @ApiOperation(value = "导入航司基础数据")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "importAirlineInfo")
    public Object importAirlineInfo(@RequestParam @ApiParam(name = "file", value = "上传航司信息") MultipartFile file) {
        return airlineService.importAirlineInfo(file);
    }

    @ApiOperation(value = "查询已维护航司的航司代码（不重复）")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirlineVo.class)})
    @GetMapping(value = "/listMaintainedAirlineCodeByAirport")
    public Object listMaintainedAirlineCodeByAirport(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return airlineService.listMaintainedAirlineCodeByAirport(airportCode);
    }

    @ApiOperation(value = "根据机场三字码查询该机场已维护的航司二字码")
    @GetMapping(value = "/listAirlineCodeByAirportCode")
    public Object listAirlineCodeByAirportCode(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return airlineService.listAirlineCodeByAirportCode(airportCode);
    }

    @ApiOperation(value = "航空公司简称模糊搜索")
    @GetMapping(value = "/listByAirlineShortName")
    public Object listByAirlineShortName(@RequestParam @ApiParam(name = "airlineShortName", value = "航空公司简称") String airlineShortName) {
        return airlineService.listByAirlineShortName(airlineShortName);
    }

    @ApiOperation(value = "根据机场三字码查询该机场已维护的航司结算代码")
    @GetMapping(value = "/listSettleCodeByAirportCode")
    public Object listSettleCodeByAirportCode(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return airlineService.listSettleCodeByAirportCode(airportCode);
    }

    @ApiOperation(value = "根据当前日期，查询近一个月有效航班数据对应的机号对应的航司，是否配置有效费用规则")
    @GetMapping(value = "findInvalidAirline")
    public Object findInvalidAirline() {
        return airlineService.findInvalidAirline();
    }

    @ApiOperation(value = "NEW查询全部已维护航空公司简称")
    @GetMapping(value = "/listAllValidAirline")
    public Object listAllValidAirline(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return airlineService.listAllValidAirline(airportCode);
    }

    @ApiOperation(value = "NEW批量修改航司特殊货邮服务费")
    @PutMapping(value = "/changeAirlineSpecialCmbFee")
    public Object changeAirlineSpecialCmbFee(@RequestBody @ApiParam(name = "airlineIds", value = "航司id集合") List<String> airLineIds,
                                             @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return airlineService.changeAirlineSpecialCmbFee(airLineIds, airportCode);
    }

    @ApiOperation(value = "获取有过期费用的航司")
    @GetMapping(value = "/listExpiredAirlineShortName")
    public Object listExpiredAirlineShortName() {
        return airlineService.listExpiredAirlineShortName();
    }
}
