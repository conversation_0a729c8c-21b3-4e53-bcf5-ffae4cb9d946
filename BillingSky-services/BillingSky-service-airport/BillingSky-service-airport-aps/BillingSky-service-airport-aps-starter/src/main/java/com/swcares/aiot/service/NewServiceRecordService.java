package com.swcares.aiot.service;

import com.swcares.aiot.core.form.FlightInfoSearchForm;
import com.swcares.aiot.core.model.dto.ServiceRecordSaveBatchDto;
import com.swcares.aiot.core.model.dto.ServiceRecordSaveByFlightDto;
import com.swcares.aiot.core.model.vo.NewServiceRecordVo;
import com.swcares.aiot.core.model.vo.ServiceRecordVo;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.service.NewServiceRecordService
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/7 15:52
 * @version v1.0
 */
public interface NewServiceRecordService {

    List<IndAllIndicatorRetrVo> getServiceDict();


    /**
     * saveDeviceUsedListData
     * 批量保存某一code下的业务保障数据
     *
     * @param serviceRecordSaveBatchDtoList 参数
     * @param user 参数
     * @return 结果
     */
    boolean saveDeviceUsedListData( List<ServiceRecordSaveBatchDto> serviceRecordSaveBatchDtoList, LoginUserDetails user);


    /**
     * saveDeviceUsedListByFlight
     * 批量保存某一航班下的业务保障数据
     *
     * @param saveByFlightDto 参数
     * @param user 参数
     * @return 结果
     */
    boolean saveDeviceUsedListByFlight( ServiceRecordSaveByFlightDto saveByFlightDto, LoginUserDetails user);

    Map<String, List<NewServiceRecordVo>> listDeviceUsedDataByFlightId(String flightId);

    String importFlightBusinessData(MultipartFile file, String airportCodeParam, LoginUserDetails user) ;

    void exportFlightBusinessData(FlightInfoSearchForm flightInfoSearchForm, HttpServletResponse response, LoginUserDetails user);

    void exportFlightBusinessDataTemplate(HttpServletResponse response);
}
