package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.form.SubsidyDetailBillUpdateForm;
import com.swcares.aiot.core.form.SubsidyFormulaBillCountForm;
import com.swcares.aiot.core.form.SubsidyFormulaBillExportForm;
import com.swcares.aiot.core.form.SubsidyFormulaBillForm;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.SubsidyBillService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.controller.SubsidyBillController
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/25 16:48
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/subsidyBill")
@Api(value = "SubsidyBillController", tags = {"航线补贴z账单接口"})
@Slf4j
public class SubsidyBillController {
    @Resource
    private SubsidyBillService subsidyBillService;

    @ApiOperation(value = "获取当前时间段航线下公式数量 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/getFormulaNums")
    public ResultBuilder getFormulaNums(SubsidyFormulaBillCountForm form) {
        List list = subsidyBillService.getFormulaNums(form);
        return new ResultBuilder.Builder().data(list).builder();
    }

    @ApiOperation(value = "分页查询账单汇总数据 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/pageSubsidyFormulaBill")
    public ResultBuilder<Object[]> pageSubsidyFormulaBill(SubsidyFormulaBillForm form, @Validated PageParam pageParam) {
        Object[] resObj = subsidyBillService.pageSubsidyFormulaBill(form, pageParam);
        return new ResultBuilder.Builder().data(resObj).builder();
    }

    @ApiOperation(value = "分页查询账单航班详情数据 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/pageSubsidyDetailBill")
    public ResultBuilder<Object[]> pageSubsidyDetailBill(String formulaBillId, @Validated PageParam pageParam) {
        Object[] resObj = subsidyBillService.pageSubsidyDetailBill(formulaBillId, pageParam);
        return new ResultBuilder.Builder().data(resObj).builder();
    }

    @ApiOperation(value = "分页查询账单航班详情数据总数 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/countPageSubsidyDetailBill")
    public ResultBuilder countPageSubsidyDetailBill(String formulaBillId) {
        Object[] resObj = subsidyBillService.countPageSubsidyDetailBill(formulaBillId);
        return new ResultBuilder.Builder().data(resObj).builder();
    }

    @ApiOperation(value = "删除公式账单 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @DeleteMapping("/deleteBill")
    public ResultBuilder deleteBill(String formulaBillIds) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyBillService.deleteBill(formulaBillIds, user);
        return new ResultBuilder.Builder().builder();
    }

    @ApiOperation(value = "修改详情账单中的参数值 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/updateDetailBillParam")
    public ResultBuilder updateDetailBillParam(@RequestBody SubsidyDetailBillUpdateForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyBillService.updateDetailBill(form, user);
        return new ResultBuilder.Builder().builder();
    }

    @ApiOperation(value = "账单导出")
    @PostMapping("/exportSubsidyFomrlaBill")
    public void exportSubsidyFomrlaBill(@RequestBody SubsidyFormulaBillExportForm form, HttpServletResponse res) {
        subsidyBillService.exportSubsidyFomrlaBill(form, res);
    }
}
