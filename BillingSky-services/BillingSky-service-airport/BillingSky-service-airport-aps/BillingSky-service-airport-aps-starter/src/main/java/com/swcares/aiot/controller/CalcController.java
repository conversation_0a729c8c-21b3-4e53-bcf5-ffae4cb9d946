package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.cons.ConsAirportAps;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.vo.ExpensesRetrVo;
import com.swcares.aiot.core.model.vo.ForExpensesRetrVo;
import com.swcares.aiot.service.CalcService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.CalcController
 * Description：计算相关接口
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/25 13:18
 * @version v1.0
 */
@RestController
@RequestMapping("/calc")
@Api(value = "CalcController", tags = {"计算相关接口"})
@ApiVersion(value = ConsAirportAps.API_MODULE_NAME)
public class CalcController {
    @Resource
    private CalcService calcService;

    @ApiOperation(value = "结算航班相关费用")
    @PostMapping(value = "/calcFlightBill")
    public void calc(@RequestBody @Validated ReCalcDto dto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        calcService.execute(dto, user, TenantHolder.getTenant());
    }

    @PostMapping("/retrForExpenses")
    @ApiOperation(value = "查询-获取当前客户的所有费用项")
    public BaseResult<List<ForExpensesRetrVo>> retrForExpenses(@RequestBody @Valid ExpensesRetrVo dto) {
        List<ForExpensesRetrVo> vos = calcService.retrForExpenses(dto);
        return BaseResult.ok(vos);
    }
}
