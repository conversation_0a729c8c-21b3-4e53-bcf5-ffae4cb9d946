package com.swcares.aiot;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a>
 * Title：SettlementApplication.java
 * Package：com.swcares
 * Description：
 * Copyright © 2020-5-13 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 */

@Slf4j
@MapperScan("com.swcares.aiot.**.mapper")
@SpringBootApplication(scanBasePackages = "com.swcares")
//开启事务
@EnableTransactionManagement
// 开启配置注入
@EnableConfigurationProperties
@EntityScan({"com.swcares.aiot.core.model", "com.swcares.aiot.core.importer.entity"})
@EnableCreateCacheAnnotation
// 扫描Filter
@ServletComponentScan
@EnableAsync
@EnableWebSecurity
//开启并扫描Feign
@EnableFeignClients(basePackages = {"com.swcares.aiot.**.client",
        "com.swcares.aiot.core.importer",
        "com.swcares.common.api",
        "com.swcares.aiot.client"})
//开启定时任务
@EnableScheduling
public class AirportApsServiceStarter {
    public static void main(String[] args) {
        SpringApplication.run(AirportApsServiceStarter.class, args);
    }
}
