package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.vo.FeeBillExcelVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 航班账单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
public interface FlightBillMapper extends BaseMapper<TFlightBill> {


    IPage<FlightBill> pageBillCountByCondition(@Param("airportCode") String airportCode, @Param("startDate")Date startDate,
                                               @Param("endDate")Date endDate, @Param("fromAirportCode")String fromAirportCode,
                                               @Param("toAirportCode")String toAirportCode, @Param("flightNo")String flightNo,
                                               @Param("choseFeeInfos")List<String> choseFeeInfos, @Param("settleCodeList")List<String> settleCodeList,
                                               @Param("regNo")String regNo, @Param("flightFlag")String flightFlag,
                                               @Param("submit")String submit, @Param("revocation")Integer revocation, Page page);

    List<FeeBillExcelVO> listFeeBillInfoByCondition(@Param("airportCode") String airportCode,
                                                    @Param("sDate") Date sDate,
                                                    @Param("eDate") Date eDate, @Param("feeName") String feeName,
                                                    @Param("airlineShortName") String airlineShortName);
}
