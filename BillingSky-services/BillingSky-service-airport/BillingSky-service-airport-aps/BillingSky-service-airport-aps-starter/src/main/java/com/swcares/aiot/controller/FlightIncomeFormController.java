package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.model.dto.FlightIncomeFormDTO;
import com.swcares.aiot.service.FlightIncomeFormService;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


/**
 * ClassName：FlightIncomeFormController
 * Description：机场收入报表 前端控制器
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/9/20 16:24
 * Version v1.0
 */
@RestController
@RequestMapping("/flightIncomeForm")
@Api(tags = "机场收入报表接口")
public class FlightIncomeFormController extends BaseController {

    @Resource
    private FlightIncomeFormService flightIncomeFormService;

    @PostMapping("/page")
    @ApiOperation(value = "机场收入报表分页")
    public ResultBuilder<Object> page(@Validated @RequestBody FlightIncomeFormDTO dto) {
        return new ResultBuilder.Builder<>().data(flightIncomeFormService.page(dto)).builder();
    }

    @PostMapping("/incomeCount")
    @ApiOperation(value = "机场收入报表(收入合计)")
    public ResultBuilder<Object> incomeCount(@Validated @RequestBody FlightIncomeFormDTO dto) {
        return new ResultBuilder.Builder<>().data(flightIncomeFormService.incomeCount(dto)).builder();
    }

    @PostMapping("/exportFlightIncomeForm")
    @ApiOperation(value = "机场收入报表导出")
    public void exportFlightIncomeForm(@Validated @RequestBody FlightIncomeFormDTO dto, HttpServletResponse response) {
        flightIncomeFormService.exportFlightIncomeForm(dto, response);
    }



}
