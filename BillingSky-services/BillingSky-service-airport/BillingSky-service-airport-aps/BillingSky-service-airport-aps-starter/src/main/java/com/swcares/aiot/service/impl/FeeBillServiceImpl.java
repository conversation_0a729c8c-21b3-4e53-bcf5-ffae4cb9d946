package com.swcares.aiot.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.client.IConfDictMgtBizClient;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.FlightDateTypeEnum;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.easyexcel.SkipHeaderRowColumnWidthStyleStrategy;
import com.swcares.aiot.core.model.dto.FeeBillDetailedDto;
import com.swcares.aiot.core.model.dto.FeeBillPagedDTO;
import com.swcares.aiot.core.model.vo.FeeBillCountVO;
import com.swcares.aiot.core.model.vo.FeeBillExportVo;
import com.swcares.aiot.core.model.vo.FeeBillVO;
import com.swcares.aiot.mapper.FeeBillMapper;
import com.swcares.aiot.model.vo.ConfDictCacheVo;
import com.swcares.aiot.service.FeeBillService;
import com.swcares.aiot.service.LogService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.api.bd.BaseDataCommonRemoteService;
import com.swcares.components.bd.vo.AirlineInfoVO;
import com.swcares.components.bd.vo.AirportInfoComboVO;
import com.worm.hutool.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.swcares.baseframe.common.cons.CommonErrors.PARAM_VALUE_INVALID_ERROR;

/**
 * ClassName：FeeBillServiceImpl <br>
 * Description：(费用账单业务逻辑处理)<br>
 * Copyright © 2020/6/16 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/16 14:43<br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class FeeBillServiceImpl implements FeeBillService {
    private static final String AIRLINE_MAP = "AIRLINE_CODE_TO_NAME_MAP";
    @Resource
    private LogService logService;
    @Resource
    private BaseDataCommonRemoteService baseDataCommonRemoteService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private FeeBillMapper feeBillMapper;

    @Resource
    private IConfDictMgtBizClient confDictMgtBizClient;

    /**
     * Title : pageFeeBillInfoByCondition <br>
     * Description : 费用账单动态条件分页查询 <br>
     * <AUTHOR>  <br>
     * @date 2025/4/29 22:09<br>
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aiot.core.model.vo.FeeBillVO>
     */
    @Override
    public IPage<FeeBillVO> pageFeeBillInfoByCondition(FeeBillPagedDTO dto) {
        // 数据类型
        Integer dateType = ObjectUtil.isNotEmpty(dto.getStartDate()) && ObjectUtil.isNotEmpty(dto.getEndDate()) ?
                FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue() : FlightDateTypeEnum.TAKE_OFF_DATE_TYPE.getValue();
        IPage<FeeBillVO> feeBillVOIPage = feeBillMapper.pageFeeBillInfoByCondition(dto, dateType, dto.createPage());
        //临时精度处理
        feeBillVOIPage.getRecords().forEach(item->{
            item.setAfterTaxAmount(item.getAfterTaxAmount().divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP));
            item.setTotalSettleAmount(item.getTotalSettleAmount().divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP));
        });
        return feeBillVOIPage;
    }

    /**
     * Title: countTotal<br>
     * Author: 叶咏秋<br>
     * Description: (费用账单总计)<br>
     * Date:  15:16 <br>
     *
     * @param dto return: ResultBuilder
     */
    @Override
    public FeeBillCountVO countTotal(FeeBillPagedDTO dto) {
        // 数据类型
        Integer dateType = ObjectUtil.isNotEmpty(dto.getStartDate()) && ObjectUtil.isNotEmpty(dto.getEndDate()) ?
                FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue() : FlightDateTypeEnum.TAKE_OFF_DATE_TYPE.getValue();
        FeeBillCountVO feeBillCountVO = feeBillMapper.countTotal(dto, dateType);
        //临时精度处理
        feeBillCountVO.setAfterTaxAmount(feeBillCountVO.getAfterTaxAmount().setScale(2, RoundingMode.HALF_UP));
        feeBillCountVO.setTotalSettleAmount(feeBillCountVO.getTotalSettleAmount().setScale(2, RoundingMode.HALF_UP));
        return feeBillCountVO;
    }

    /**
     * Title: exportFeeBillInfo<br>
     * Author: 叶咏秋<br>
     * Description: (导出费用账单)<br>
     * Date:  10:43 <br>
     *
     * @param dto :
     * @param response    :
     */
    @Override
    public void exportFeeBillInfo(FeeBillPagedDTO dto, HttpServletResponse response, LoginUserDetails user, String urlName) {
        String airportCode = dto.getAirportCode();
        LocalDate exportStartDate = getExportDate(dto.getStartDate(), dto.getFlightTimeStartDate());
        LocalDate exportEndDate = getExportDate(dto.getEndDate(), dto.getFlightTimeEndDate());
        Integer dateType = getDateType(dto);
        log.info("export dateType:{}", dateType);
        //获取机场中文名
        String airportName = getAirportNameByCode(airportCode);
        boolean isGroupBySettleCode = isGroupBySettleCode(dto);
        Map<String, Map<String, FeeBillDetailedDto>> airlineMap;
        Map<String, String> airlineNameMap;
        List<FeeBillExportVo> resultList;
        if (isGroupBySettleCode) {
            resultList = feeBillMapper.listFeeBillInfoGroupBySettleCode(dto, dateType);
            resultList.forEach(item -> {
                item.setTotalSettleAmount(item.getTotalSettleAmount().setScale(2, RoundingMode.HALF_UP));
                item.setAfterTaxAmount(item.getAfterTaxAmount().setScale(2, RoundingMode.HALF_UP));
            });
            airlineNameMap = getSettleCodeAirlineAbbrMap();
            airlineMap = buildAirlineMap(resultList, true);
            resultList.forEach(item -> {
                item.setTotalSettleAmount(item.getTotalSettleAmount().setScale(2, RoundingMode.HALF_UP));
                item.setAfterTaxAmount(item.getAfterTaxAmount().setScale(2, RoundingMode.HALF_UP));
                item.setAirlineShortName(airlineNameMap.getOrDefault(item.getSettleCode(), item.getSettleCode()));
            });
        } else {
            resultList = feeBillMapper.listFeeBillInfoGroupByAirlineCode(dto, dateType);
            resultList.forEach(item -> {
                item.setTotalSettleAmount(item.getTotalSettleAmount().setScale(2, RoundingMode.HALF_UP));
                item.setAfterTaxAmount(item.getAfterTaxAmount().setScale(2, RoundingMode.HALF_UP));
            });
            airlineNameMap = getAirlineMap();
            airlineMap = buildAirlineMap(resultList, false);
        }
        String fileName = getExportFileName(exportStartDate, exportEndDate);
        ExcelWriter excelWriter = null;
        try {
            excelWriter = initExcelWriter(response, getExportFileName(exportStartDate, exportEndDate));
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyExcel没有关系
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(fileName, CharsetUtil.UTF_8) + ".xlsx");
            int sheetIndex = 0;

            if (Boolean.TRUE.equals(dto.getGroupType() != null && dto.getGroupType() == 1)) {
                List<List<String>> summaryHead = new ArrayList<>();
                List<List<Object>> summaryData = new ArrayList<>();
                buildSummaryData(resultList, airlineNameMap, airlineMap.keySet(), summaryHead, summaryData, airportName,
                        exportStartDate, exportEndDate);
                WriteSheet summarySheet = EasyExcelFactory.writerSheet(sheetIndex++, "汇总数据")
                        .head(summaryHead)
                        .registerWriteHandler(new SkipHeaderRowColumnWidthStyleStrategy(1)).build();
                excelWriter.write(summaryData, summarySheet);

            }
            for (Map.Entry<String, Map<String, FeeBillDetailedDto>> entry : airlineMap.entrySet()) {
                List<List<String>> detailHead = new ArrayList<>();
                List<List<Object>> detailData = new ArrayList<>();
                Map<String, FeeBillDetailedDto> airlineData = entry.getValue();
                String airlineName = airlineNameMap.get(entry.getKey());
                // 获取当前 Airline 的数据
                buildDetailData(airlineData,
                        airlineName,
                        detailHead,
                        detailData,
                        airportName,
                        exportStartDate, exportEndDate);
                // 创建 Sheet 并写入
                WriteSheet detailSheet = EasyExcelFactory.writerSheet(sheetIndex++, airlineName)
                        .head(detailHead)
                        .registerWriteHandler(new SkipHeaderRowColumnWidthStyleStrategy(1))
                        .build();
                excelWriter.write(detailData, detailSheet);
            }
        } catch (Exception e) {
            log.error("费用账单导出报错！", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }

        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, dto, content, dto.getAirportCode(), "费用账单");
    }

    private ExcelWriter initExcelWriter(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()) + ".xlsx";
        response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
        return EasyExcelFactory.write(response.getOutputStream()).build();
    }

    private boolean isGroupBySettleCode(FeeBillPagedDTO dto) {
        return dto.getGroupType() != null && dto.getGroupType() == 1;
    }

    private void buildDetailData(Map<String, FeeBillDetailedDto> feeCodeDataMap,
                                 String airlineName,
                                 List<List<String>> detailHead,
                                 List<List<Object>> detailData, String airportName, LocalDate exportStartDate,
                                 LocalDate exportEndDate) {
        // 1. 清空传入的集合 防止意外数据
        detailHead.clear();
        detailData.clear();
        //2.费用代码排序，固定前五个，其它随意
        List<String> sortedFeeCodes = getSortedFeeCodes(feeCodeDataMap.keySet());
        //3. 构建表头
        buildDetailHeaders(detailHead, airportName, airlineName, exportStartDate, exportEndDate);
        //4.构建数据
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal afterTaxAmount = BigDecimal.ZERO;
        for (String feeCode : sortedFeeCodes) {
            List<Object> row =new ArrayList<>();
            FeeBillDetailedDto feeDataDetail = feeCodeDataMap.get(feeCode);
            String feeName = feeDataDetail.getFeeName();
            row.add(feeName + "(" + feeCode + ")");
            row.add(feeDataDetail.getTotalAmount());
            row.add(feeDataDetail.getAfterTaxAmount());
            totalAmount = totalAmount.add(feeDataDetail.getTotalAmount());
            afterTaxAmount = afterTaxAmount.add(feeDataDetail.getAfterTaxAmount());
            detailData.add(row);
        }

        //5. 添加合计行
        List<Object> totalRow = new ArrayList<>();
        totalRow.add("合计");
        totalRow.add(totalAmount.setScale(2, RoundingMode.HALF_UP));
        totalRow.add(afterTaxAmount.setScale(2, RoundingMode.HALF_UP));
        detailData.add(totalRow);
    }

    private void buildDetailHeaders(List<List<String>> detailHead, String airportName, String airlineName, LocalDate exportStartDate, LocalDate exportEndDate) {
        // 表头
        String title = airportName + "起降费估价入账清单";
        String dateRange = LocalDateTimeUtil.format(exportStartDate, DatePattern.PURE_DATE_FORMATTER) + "-" +
                LocalDateTimeUtil.format(exportEndDate, DatePattern.PURE_DATE_FORMATTER);
        detailHead.add(Arrays.asList(title, "航空公司：" + airlineName, "项目"));
        detailHead.add(Arrays.asList(title, "", "金额"));
        detailHead.add(Arrays.asList(title, dateRange, "不含税金额"));
    }

    private List<String> getSortedFeeCodes(Set<String> feeCodes) {
        List<String> priorityOrder = Arrays.asList("LAND", "PARK", "PA-D", "SP-D", "SC-D");
        Map<String, Integer> priorityIndexMap = new HashMap<>();
        for (int i = 0; i < priorityOrder.size(); i++) {
            priorityIndexMap.put(priorityOrder.get(i), i);
        }
        return feeCodes.stream()
                .sorted(Comparator.comparingInt(
                        code -> priorityIndexMap.getOrDefault(code, Integer.MAX_VALUE)
                ))
                .collect(Collectors.toList());
    }


    private Map<String, String> getSettleCodeAirlineAbbrMap() {
        BaseResult<List<ConfDictCacheVo>> apsSettleCodeAirlineAbbr = confDictMgtBizClient.getDict(
                "APS_SETTLE_CODE_AIRLINE_ABBR", TenantHolder.getTenant().toString());
        if(apsSettleCodeAirlineAbbr.getCode() != BaseResult.OK_CODE || CollectionUtils.isEmpty(apsSettleCodeAirlineAbbr.getData())){
            throw new BusinessException(ExceptionCodes.AIRLINE_DICT_CONFIG_MISS);
        }
        return apsSettleCodeAirlineAbbr.getData().stream().collect(Collectors.toMap(ConfDictCacheVo::getDictKey,
                ConfDictCacheVo::getDictValue));
    }

    private String getExportFileName(LocalDate exportStartDate, LocalDate exportEndDate) {
        return "费用账单报表_" +
                LocalDateTimeUtil.format(exportStartDate, DatePattern.PURE_DATE_FORMATTER) + "-" +
                LocalDateTimeUtil.format(exportEndDate, DatePattern.PURE_DATE_FORMATTER);
    }

    private Integer getDateType(FeeBillPagedDTO dto) {
        return ObjectUtil.isNotEmpty(dto.getStartDate()) && ObjectUtil.isNotEmpty(dto.getEndDate())
                ? FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue()
                : FlightDateTypeEnum.TAKE_OFF_DATE_TYPE.getValue();
    }

    private Map<String, Map<String, FeeBillDetailedDto>> buildAirlineMap(List<FeeBillExportVo> list, boolean groupBySettleCode) {
        Map<String, Map<String, FeeBillDetailedDto>> airlineMap = new HashMap<>();
        for (FeeBillExportVo vo : list) {
            String key = groupBySettleCode ? vo.getSettleCode() : vo.getAirlineCode();
            airlineMap.computeIfAbsent(key, k -> new HashMap<>())
                    .put(vo.getFeeCode(), buildDetailedDto(vo));
        }
        return airlineMap;
    }

    private FeeBillDetailedDto buildDetailedDto(FeeBillExportVo vo) {
        FeeBillDetailedDto dto = new FeeBillDetailedDto();
        dto.setFeeName(vo.getFeeName());
        dto.setTotalAmount(vo.getTotalSettleAmount());
        dto.setAfterTaxAmount(vo.getAfterTaxAmount());
        return dto;
    }


    private void buildSummaryData(List<FeeBillExportVo> resultList,
                                  Map<String, String> settleCodeMap,
                                  Set<String> settleCodeSet,
                                  List<List<String>> summaryHead,
                                  List<List<Object>> summaryData,
                                  String airportName,
                                  LocalDate exportStartDate,
                                  LocalDate exportEndDate) {
        //1.费用代码排序，固定前五个，其它随意
        List<String> priority = Arrays.asList("LAND", "PARK", "PA-D", "SP-D", "SC-D");
        List<String> feeCodeList= resultList.stream()
                .map(FeeBillExportVo::getFeeCode)
                .distinct().sorted((a, b) -> {
                    int indexA = priority.contains(a) ? priority.indexOf(a) : Integer.MAX_VALUE;
                    int indexB = priority.contains(b) ? priority.indexOf(b) : Integer.MAX_VALUE;
                    return Integer.compare(indexA, indexB);
                }).collect(Collectors.toList());

        // 2. 构建表头（保持不变）
        String head1 = airportName + "起降费估价入账清单" + "（" + LocalDateTimeUtil.format(exportStartDate,
                DatePattern.PURE_DATE_FORMATTER) + "-" +
                LocalDateTimeUtil.format(exportEndDate, DatePattern.PURE_DATE_FORMATTER) + "）";
        //起将非估价入账清单
        summaryHead.add(Arrays.asList(head1, "项目"));
        settleCodeSet.forEach(settleCode -> summaryHead.add(Arrays.asList(head1, settleCodeMap.getOrDefault(settleCode, settleCode))));
        summaryHead.add(Arrays.asList(head1, "合计"));
        summaryHead.add(Arrays.asList(head1, "不含税金额"));
        summaryHead.add(Arrays.asList(head1, "税金"));
        //3.构建数据
        Map<String, FeeBillExportVo> combinedKeyMap = new HashMap<>();
        for (FeeBillExportVo vo : resultList) {
            String combinedKey = vo.getSettleCode() + "|" + vo.getFeeCode();
            combinedKeyMap.putIfAbsent(combinedKey, vo);
        }
        for (String feeCode : feeCodeList) {
            List<Object> row =new ArrayList<>();
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal afterTaxAmount = BigDecimal.ZERO;
            BigDecimal taxAmount = BigDecimal.ZERO;
            String feeName = resultList.stream()
                    .filter(vo -> vo.getFeeCode().equals(feeCode))
                    .findFirst()
                    .map(FeeBillExportVo::getFeeName)
                    .orElse("未知费用");
            row.add(feeName + "(" + feeCode + ")");
            for (String settleCode : settleCodeSet) {
                FeeBillExportVo feeBillExportVo = combinedKeyMap.get(settleCode + "|" + feeCode);
                if(Objects.isNull(feeBillExportVo)) {
                    row.add(0);
                }else {
                    row.add(feeBillExportVo.getTotalSettleAmount());
                    totalAmount = totalAmount.add(feeBillExportVo.getTotalSettleAmount());
                    afterTaxAmount = afterTaxAmount.add(feeBillExportVo.getAfterTaxAmount());
                    taxAmount =taxAmount.add(feeBillExportVo.getTotalSettleAmount()
                            .subtract(feeBillExportVo.getAfterTaxAmount()));
                }

            }
            row.add(totalAmount);
            row.add(afterTaxAmount);
            row.add(taxAmount);
            summaryData.add(row);
        }
        // 4. 添加合计行（保持不变）
        addTotalRows(settleCodeSet, summaryData, resultList);
    }

    private void addTotalRows(Set<String> settleCodeSet, List<List<Object>> summaryData,
                              List<FeeBillExportVo> resultList) {
        // 总金额合计行
        List<Object> totalRow = addAirlineTotals(settleCodeSet, resultList);
        summaryData.add(totalRow);
        // 不含税金额合计行
        List<Object> afterTaxRow =addAirlineAfterTaxRow(settleCodeSet, resultList);
        summaryData.add(afterTaxRow);
        // 税金合计行
        List<Object> taxRow = addAirlineTotalTaxRow(settleCodeSet, resultList);
        summaryData.add(taxRow);
    }

    private List<Object> addAirlineTotalTaxRow(Set<String> settleCodeSet, List<FeeBillExportVo> resultList) {
        List<Object> taxRow = new ArrayList<>();
        taxRow.add("税金合计");
        BigDecimal total = BigDecimal.ZERO;
        for (String settleCode : settleCodeSet) {
            BigDecimal columnTotal = BigDecimal.ZERO;
            for (FeeBillExportVo item :resultList){
                if(item.getSettleCode().equals(settleCode)) {
                    columnTotal = columnTotal.add(item.getTotalSettleAmount().subtract(item.getAfterTaxAmount()));
                }
            }
            taxRow.add(columnTotal);
            total = total.add(columnTotal);
        }
        taxRow.add(total);
        taxRow.add("-");
        taxRow.add("-");
        return taxRow;
    }

    private List<Object> addAirlineAfterTaxRow(Set<String> settleCodeSet, List<FeeBillExportVo> resultList) {
        List<Object> afterTaxRow = new ArrayList<>();
        afterTaxRow.add("不含税合计");
        BigDecimal total = BigDecimal.ZERO;
        for (String settleCode : settleCodeSet) {
            BigDecimal columnTotal = BigDecimal.ZERO;

            for (FeeBillExportVo item :resultList){
                if(item.getSettleCode().equals(settleCode)) {
                    columnTotal = columnTotal.add(item.getAfterTaxAmount());
                }
            }
            afterTaxRow.add(columnTotal);
            total = total.add(columnTotal);
        }
        afterTaxRow.add(total);
        afterTaxRow.add("-");
        afterTaxRow.add("-");
        return afterTaxRow;
    }

    private List<Object> addAirlineTotals(Set<String> settleCodeSet, List<FeeBillExportVo> resultList) {
        List<Object> totalRow = new ArrayList<>();
        totalRow.add("总金额合计");
        BigDecimal total = BigDecimal.ZERO;
        for (String settleCode : settleCodeSet) {
            BigDecimal columnTotal = BigDecimal.ZERO;
            for (FeeBillExportVo item :resultList){
                if(item.getSettleCode().equals(settleCode)) {
                    columnTotal = columnTotal.add(item.getTotalSettleAmount());
                }
            }
            totalRow.add(columnTotal);
            total = total.add(columnTotal);
        }
        totalRow.add(total);
        totalRow.add("-");
        totalRow.add("-");
        return totalRow;
    }

    private LocalDate getExportDate(LocalDate flightDate, LocalDateTime flightDateTime) {
        if (flightDate != null && flightDateTime == null ) {
            return flightDate;
        } else if (flightDate == null  && flightDateTime != null ) {
            return flightDateTime.toLocalDate();
        } else if (flightDate != null) {
            return flightDate.isAfter(flightDateTime.toLocalDate()) ? flightDate : flightDateTime.toLocalDate();
        } else {
            throw new BusinessException(PARAM_VALUE_INVALID_ERROR, " 航班日期和起降日期 ");
        }
    }


    /**
     * Title: getAirportNameByCode
     * <p>
     * Description: 根据机场三字码获取机场名称
     * Author: liuzhiheng
     * Date: 2024-08-08 13:39:33
     *
     * @param airportCode :
     * @return : String
     */
    private String getAirportNameByCode(String airportCode) {
        String airportName = redisUtil.get(airportCode);
        if (airportName == null) {
            BaseResult<List<AirportInfoComboVO>> combos = baseDataCommonRemoteService.getCombo(null);
            if (combos.getCode() == 200) {
                Optional<AirportInfoComboVO> airp = combos.getData().stream().filter(v -> v.getCode().equalsIgnoreCase(airportCode)).findAny();
                if (airp.isPresent()) {
                    airportName = airp.get().getAirportName();
                    redisUtil.set(airportCode, airportName, 1800);
                }
            }
        }
        return airportName;
    }

    /**
     * Title: getAirlineMap
     * <p>
     * description: 获取航司二字码和航司名称对应map
     */
    private Map<String, String> getAirlineMap() {
        Map<String, String> airlineCodeToNameMap = redisUtil.get(AIRLINE_MAP);
        if (airlineCodeToNameMap == null) {
            BaseResult<List<AirlineInfoVO>> airlineCombos = baseDataCommonRemoteService.getAirlineCombo();
            if (airlineCombos.getCode() == 200) {
                List<AirlineInfoVO> airlineInfoVOList = airlineCombos.getData();
                airlineCodeToNameMap = airlineInfoVOList.stream().collect(Collectors.toMap(AirlineInfoVO::getAirline2code, AirlineInfoVO::getAirlineAbbr, (value1, value2) -> value1));
                redisUtil.set(AIRLINE_MAP, airlineCodeToNameMap, 1800);
            }
        }
        return airlineCodeToNameMap;
    }
}
