package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.model.dto.FlightIncomeFormDTO;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.vo.FlightIncomeFormExcelVO;
import com.swcares.aiot.core.model.vo.FlightIncomeFormVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FlightIncomeFormBizMapper extends BaseMapper<FlightBill> {

    /**
     * Title：page
     * Description：机场收入报表分页
     * author：李军呈
     * date： 2024/9/23 15:24
     * @param dto 查询参数
     * @param page 分页
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aiot.core.model.vo.FlightIncomeFormVO>
     */
    IPage<FlightIncomeFormVO> page(@Param("query") FlightIncomeFormDTO dto, Page<FlightBill> page);
    
    /***
     * Title：incomeCount 
     * Description：机场收入报表(收入合计)
     * author：李军呈
     * date： 2024/9/23 15:57
     * @param dto 查询入参
     * @return java.lang.String
     */
    String incomeCount(@Param("query") FlightIncomeFormDTO dto);

    /**
     * Title：exportFlightIncomeForm
     * Description：机场收入报表导出
     * author：李军呈
     * date： 2024/9/23 16:31
     * @param dto 查询入参
     * @return java.util.List<com.swcares.aiot.core.model.vo.FlightIncomeFormExcelVO>
     */
    List<FlightIncomeFormExcelVO> exportFlightIncomeForm(@Param("query") FlightIncomeFormDTO dto);

}
