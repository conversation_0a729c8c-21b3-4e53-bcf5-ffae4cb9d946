package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.swcares.aiot.client.IBillBusDataClient;
import com.swcares.aiot.client.IBillItemClient;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.client.remote.IIndicatorBizRemoteClient;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.enums.InvalidEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TAirportSignItem;
import com.swcares.aiot.core.entity.TBillItem;
import com.swcares.aiot.core.entity.TVariableGuarantee;
import com.swcares.aiot.core.enums.EnumIndicatorDataFormat;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.model.dto.VariableGuaranteeSaveDto;
import com.swcares.aiot.core.model.dto.VariableGuaranteeUpdateDto;
import com.swcares.aiot.core.model.vo.VariableGuaranteeVo;
import com.swcares.aiot.core.model.vo.VariableIdVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.core.service.ITAirportSignItemService;
import com.swcares.aiot.core.service.ITBillItemService;
import com.swcares.aiot.core.service.ITFlightInfoService;
import com.swcares.aiot.core.service.ITVariableGuaranteeService;
import com.swcares.aiot.core.vo.SettlementItemVo;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;
import com.swcares.aiot.mapper.VariableGuaranteeMapper;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.aiot.service.MqSignService;
import com.swcares.aiot.service.NewAssureService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.NewAssureServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/21 13:48
 * @version v1.0
 */
@Service
@Slf4j
public class NewAssureServiceImpl implements NewAssureService {
    @Resource
    private IIndicatorBizRemoteClient iIndicatorBizRemoteClient;
    @Resource
    private ITVariableGuaranteeService variableGuaranteeService;
    @Resource
    private VariableGuaranteeMapper variableGuaranteeMapper;
    @Resource
    private IConfConfigBizClient iConfConfigBizClient;
    @Resource
    private ITBillItemService iBillItemService;
    @Resource
    private ITAirportSignItemService iTAirportSignItemService;
    @Resource
    private IBillItemClient iBillItemClient;
    @Resource
    private ITFlightInfoService flightInfoService;
    @Resource
    private IBillBusDataClient iBillBusDataClient;
    @Resource
    private MqSignService mqSignService;

    @Override
    public Object getVariableId(String variableName, String airportCode) {
        List<IndAllIndicatorRetrVo> indicatorDicResList = getIndAllIndicatorRetrVoList();
        List<VariableIdVo> resList = new ArrayList<>();
        for (IndAllIndicatorRetrVo vo : indicatorDicResList) {
            VariableIdVo vi = new VariableIdVo();
            vi.setVariableId(vo.getId());
            vi.setVariableName(vo.getName());
            resList.add(vi);
        }
        return resList;
    }

    private List<IndAllIndicatorRetrVo> getIndAllIndicatorRetrVoList() {
        BaseResult<List<IndAllIndicatorRetrVo>> indicatorDicRes = iIndicatorBizRemoteClient.list();
        if (indicatorDicRes == null || indicatorDicRes.getCode() != 200 || CollUtil.isEmpty(indicatorDicRes.getData())) {
            throw new BusinessException(ExceptionCodes.SERVICE_DICT_ERROR);
        }
        List<IndAllIndicatorRetrVo> indicatorDicResList = indicatorDicRes.getData();
        indicatorDicResList = indicatorDicResList.stream()
                .filter(indAllIndicatorRetrVo -> Integer.valueOf(1).equals(indAllIndicatorRetrVo.getSort()))
                .collect(Collectors.toList());
        return indicatorDicResList;
    }

    @Override
    public Object getMatchingRelationship(PageParam pageParam, String variableId,
                                          String airportCode) {
        String org = getSignDataOrg(airportCode);
        List<VariableGuaranteeVo> resList = new ArrayList<>();
        if ("DATA_CENTER".equals(org)) {
            //查询数据中心匹配关系
            dataCenterQuery(variableId, airportCode, resList);
        } else {
            //查询电子签单匹配关系
            newSignQuery(variableId, airportCode, resList);
        }
        return new Pager<>(pageParam.getPage(), pageParam.getLimit(), resList);
    }


    private void newSignQuery(String variableId, String airportCode, List<VariableGuaranteeVo> resList) {
        List<IndAllIndicatorRetrVo> indicatorDicResList = getIndAllIndicatorRetrVoList();
        List<VariableGuaranteeVo> singleList = new ArrayList<>();
        for (IndAllIndicatorRetrVo indAllIndicatorRetrVo : indicatorDicResList) {
            if (StringUtils.isBlank(variableId) || variableId.equals(indAllIndicatorRetrVo.getId())) {
                VariableGuaranteeVo vgo = variableGuaranteeMapper.selectMatchingRelationship(airportCode, indAllIndicatorRetrVo.getId(),null);
                if (vgo == null) {
                    vgo = new VariableGuaranteeVo();
                    fillVgo(indAllIndicatorRetrVo, vgo);
                    singleList.add(vgo);
                    continue;
                }
                fillVgo(indAllIndicatorRetrVo, vgo);
                resList.add(vgo);
            }
        }
        resList.addAll(singleList);
    }

    private static void fillVgo(IndAllIndicatorRetrVo indAllIndicatorRetrVo, VariableGuaranteeVo vgo) {
        vgo.setVariableId(indAllIndicatorRetrVo.getId());
        vgo.setVariableName(indAllIndicatorRetrVo.getName());
        String dataFmt = indAllIndicatorRetrVo.getDataFmt();
        if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_RANGE.getCode().equals(dataFmt)) {
            vgo.setDataFormat("时间范围");
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_NUMBER.getCode().equals(dataFmt)) {
            vgo.setDataFormat("数值");
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_SELECTOR.getCode().equals(dataFmt)) {
            vgo.setDataFormat("选择器");
        } else {
            vgo.setDataFormat("其他");
        }
    }


    private String getSignDataOrg(String airportCode) {
        String org = "SIGN";
        try {
            BaseResult<JSONObject> jsonObjectBaseResult = iConfConfigBizClient.getConfig("ELECTRONIC_SIGNING_DICTIONARY_TABLE");
            if (ObjectUtil.isEmpty(jsonObjectBaseResult) || ObjectUtil.isEmpty(jsonObjectBaseResult.getData())) {
                log.error("{}机场未配置签单数据来源 ！ jsonObjectBaseResult = {}", airportCode, jsonObjectBaseResult);
            }

            Map<String, String> collectionSourceMap =
                    jsonObjectBaseResult.getData().toJavaObject(new TypeReference<Map<String, String>>() {
                    });
            String orgTemp = collectionSourceMap.get(airportCode);
            if (StringUtils.isNotBlank(orgTemp)) {
                org = orgTemp;
            }
        } catch (Exception e) {
            log.error("获取配置中心签单来源异常，e:{0}", e);
        }
        return org;
    }


    private void dataCenterQuery(String variableId, String airportCode, List<VariableGuaranteeVo> resList) {
        List<IndAllIndicatorRetrVo> indicatorDicResList = getIndAllIndicatorRetrVoList();
        List<VariableGuaranteeVo> singleList = new ArrayList<>();
        for (IndAllIndicatorRetrVo indAllIndicatorRetrVo : indicatorDicResList) {
            if (StringUtils.isBlank(variableId) || variableId.equals(indAllIndicatorRetrVo.getId())) {
                VariableGuaranteeVo vgo = variableGuaranteeMapper.selectMatchingRelationshipDataCenter(airportCode, indAllIndicatorRetrVo.getId());
                if (vgo == null) {
                    vgo = new VariableGuaranteeVo();
                    fillVgo(indAllIndicatorRetrVo, vgo);
                    singleList.add(vgo);
                    continue;
                }
                fillVgo(indAllIndicatorRetrVo, vgo);
                resList.add(vgo);
            }
        }
        resList.addAll(singleList);
    }


    @Override
    public Object addMatchingRelationship(VariableGuaranteeSaveDto dto, LoginUserDetails user) {
        String org = getSignDataOrg(dto.getAirportCode());
        String type;
        if ("SIGN".equals(org)) {
            type = "1";
        } else {
            type = "3";
        }
        String variableId = dto.getVariableId();
        if (CharSequenceUtil.isEmpty(variableId)) {
            throw new GenericException(BusinessMessageEnum.ASSURE_SAVE_ERROR_1.getCode(), BusinessMessageEnum.ASSURE_SAVE_ERROR_1.getMsg());
        }
        VariableGuaranteeVo vgo;
        if ("SIGN".equals(org)) {
            vgo = variableGuaranteeMapper.selectMatchingRelationship(variableId, type,null);
        } else {
            vgo = variableGuaranteeMapper.selectMatchingRelationshipDataCenter(variableId, type);
        }
        if (vgo != null) {
            throw new GenericException(BusinessMessageEnum.ADD_MATCHING_RELATIONSHIP_ERROR.getCode(), BusinessMessageEnum.ADD_MATCHING_RELATIONSHIP_ERROR.getMsg());
        }
        TVariableGuarantee vg = new TVariableGuarantee();
        copyPropertiesIgnoreNull(dto, vg);
        vg.setInvalidDate(LocalDateTime.now());
        vg.setModifiedTime(LocalDateTime.now());
        vg.setCreateTime(LocalDateTime.now());
        vg.setModifiedBy(user.getUsername());
        vg.setCreateBy(user.getUsername());
        vg.setInvalid("1");
        vg.setType(type);
        variableGuaranteeService.save(vg);
        return Boolean.TRUE;
    }

    @Override
    public Object updateMatchingRelationship(VariableGuaranteeUpdateDto dto,
                                             LoginUserDetails user) {
        TVariableGuarantee vg = variableGuaranteeService.lambdaQuery()
                .eq(TVariableGuarantee::getId, dto.getId())
                .eq(TVariableGuarantee::getInvalid, InvalidEnum.NORMAL.getValue())
                .one();
        if (vg==null) {
            throw new GenericException(BusinessMessageEnum.ASSURE_UPDATE_ERROR_1.getCode(),
                    BusinessMessageEnum.ASSURE_UPDATE_ERROR_1.getMsg());
        }
        copyPropertiesIgnoreNull(dto, vg);
        vg.setInvalidDate(LocalDateTime.now());
        vg.setModifiedBy(user.getUsername());
        vg.setModifiedTime(LocalDateTime.now());
        variableGuaranteeService.updateById(vg);
        return Boolean.TRUE;
    }

    @Override
    public Object deletedMatchingRelationship(String id, LoginUserDetails user) {
        TVariableGuarantee vg = variableGuaranteeService.lambdaQuery()
                .eq(TVariableGuarantee::getId, id)
                .eq(TVariableGuarantee::getInvalid, InvalidEnum.NORMAL.getValue())
                .one();
        if (vg==null) {
            throw new GenericException(BusinessMessageEnum.ASSURE_UPDATE_ERROR_1.getCode(),
                    BusinessMessageEnum.ASSURE_UPDATE_ERROR_1.getMsg());
        }
        vg.setInvalid(InvalidEnum.DELETED.getValue());
        vg.setModifiedBy(user.getUsername());
        vg.setModifiedTime(LocalDateTime.now());
        variableGuaranteeService.updateById(vg);
        return Boolean.TRUE;
    }

    @Override
    public Object getBillItemList(String airportCode) {
        String org = getSignDataOrg(airportCode);
        if ("SIGN".equals(org)) {
            return iBillItemService.lambdaQuery().eq(TBillItem::getAirportCode, airportCode)
                    .eq(TBillItem::getDeleted, Boolean.FALSE).list();
        } else {
            return iTAirportSignItemService.lambdaQuery().eq(TAirportSignItem::getAirportCode, airportCode)
                    .eq(TAirportSignItem::getDeleted, Boolean.FALSE).list();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importItemAll() {
        List<SettlementItemVo> list = iBillItemClient.getItemAll(TenantHolder.getTenant());
        if (list.isEmpty()) {
            throw new GenericException(BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getCode(), BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getMsg());
        }
        List<TBillItem> oldBiAll = iBillItemService.lambdaQuery()
                .eq(TBillItem::getDeleted, Boolean.FALSE).list();
        Map<Long, TBillItem> map = oldBiAll.stream().collect(Collectors.toMap(TBillItem::getId, Function.identity()));
        // 字段保存list
        List<TBillItem> saveList = new ArrayList<>();
        // 匹配关系删除list
        List<Long> vgDeleteList = new ArrayList<>();
        for (SettlementItemVo ssiv : list) {
            if (ObjectUtil.isEmpty(ssiv) || ObjectUtil.isEmpty(ssiv.getTenantId())) {
                throw new GenericException(BusinessMessageEnum.SIGN_ENTITY_TENANT_ID_NULL.getCode(), BusinessMessageEnum.SIGN_ENTITY_TENANT_ID_NULL.getMsg());
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
            ssiv.setAirportCode(airportCode);
            TBillItem oldBi = getTBillItem(ssiv, map, vgDeleteList);
            saveList.add(oldBi);

        }
        // 将map中剩下的item设置为删除，并删除匹配关系
        for (Map.Entry<Long, TBillItem> entry : map.entrySet()) {
            TBillItem it = entry.getValue();
            it.setDeleted(Boolean.TRUE);
            saveList.add(it);
            vgDeleteList.add(it.getId());
        }
        // 删除匹配关系
        variableGuaranteeService.lambdaUpdate().in(TVariableGuarantee::getId,vgDeleteList)
                .set(TVariableGuarantee::getInvalid,InvalidEnum.DELETED.getValue()).update();
        // 保存新的item
        iBillItemService.saveBatch(saveList);

        log.info("导入字段信息：");
        log.info(saveList.toString());
    }

    private static TBillItem getTBillItem(SettlementItemVo ssiv, Map<Long, TBillItem> map, List<Long> vgDeleteList) {
        TBillItem oldBi;
        if (map.containsKey(ssiv.getId())) {
            oldBi = map.get(ssiv.getId());
            map.remove(ssiv.getId());
        } else {
            oldBi = new TBillItem();
        }
        if (Integer.valueOf(1).equals(ssiv.getDeleted().getValue())
                && oldBi.getDeleted() != null && Boolean.FALSE.equals(oldBi.getDeleted())) {
            // 删除匹配关系
            vgDeleteList.add(oldBi.getId());

        }
        copyPropertiesIgnoreNull(ssiv, oldBi);
        // 设置id
        oldBi.setId(ssiv.getId());
        // 设置删除字段
        oldBi.setDeleted(Integer.valueOf(0).equals(ssiv.getDeleted().getValue())?Boolean.FALSE:Boolean.TRUE);

        if (oldBi.getCreatedTime() == null) {
            oldBi.setCreatedBy("电子签单自动导入-sign");
            oldBi.setCreatedTime(LocalDateTime.now());
        }
        oldBi.setUpdatedBy("电子签单自动导入-sign");
        oldBi.setUpdatedTime(LocalDateTime.now());
        return oldBi;
    }

    @Transactional
    @Override
    public void importSignBusDataSettlement(String synStartDate, String synEndDate,
                                            String syncflightNos, String serviceRecordId, String airportCode) {
        // 判断日期格式是否正确
        if ((!DateUtils.isValidDate(synStartDate)) || !DateUtils.isValidDate(synEndDate)) {
            throw new GenericException(BusinessMessageEnum.DATE_FORMAT_ERROR.getCode(),
                    BusinessMessageEnum.DATE_FORMAT_ERROR.getMsg());
        }
        //如果当前输入航班不为空，判断输入航班是否存在
        if (CharSequenceUtil.isNotEmpty(syncflightNos)) {
            List<String> flightNoList = Arrays.asList(syncflightNos.replace(CharSequenceUtil.SPACE, CharSequenceUtil.EMPTY).split(StrPool.COMMA));
            List<FlightInfoMb> flightInfoMbList = flightInfoService.lambdaQuery().in(FlightInfoMb::getFlightNo, flightNoList)
                    .eq(FlightInfoMb::getAirportCode, airportCode)
                    .between(FlightInfoMb::getFlightDate, synStartDate, synEndDate)
                    .eq(FlightInfoMb::getInvalid, InvalidEnum.NORMAL.getValue())
                    .list();
            if (flightInfoMbList.isEmpty()) {
                throw new GenericException(BusinessMessageEnum.FLIGHT_NOT_FOUND_ERROR.getCode(), BusinessMessageEnum.FLIGHT_NOT_FOUND_ERROR.getMsg());
            }
        }
        // 判断当前serviceRecordId是否建立关联
        List<TVariableGuarantee> vgList = variableGuaranteeService.lambdaQuery().eq(StringUtils.isNotBlank(serviceRecordId), TVariableGuarantee::getVariableId, serviceRecordId)
                .eq(TVariableGuarantee::getAirportCode, airportCode)
                .eq(TVariableGuarantee::getInvalid, InvalidEnum.NORMAL.getValue())
                .list();
        if (vgList.isEmpty() || vgList.get(0).getItemId() == null) {
            throw new GenericException(BusinessMessageEnum.VARIABLE_GUARANTEE_ERROR.getCode(),
                    BusinessMessageEnum.VARIABLE_GUARANTEE_ERROR.getMsg());
        }
        if (vgList.size() > 1) {
            throw new GenericException(
                    BusinessMessageEnum.VARIABLE_GUARANTEE_TOO_MUCH_ERROR.getCode(),
                    BusinessMessageEnum.VARIABLE_GUARANTEE_TOO_MUCH_ERROR.getMsg());
        }
        Long itemId = Long.valueOf((vgList.get(0).getItemId()));
        synStartDate += " 00:00:00";
        synEndDate += " 23:59:59";
        List<SignBusDataSettlementVO> list;
        try {
            list = iBillBusDataClient.selectSignItemByFlightDate(synStartDate, synEndDate, TenantHolder.getTenant(), itemId);
        } catch (Exception e) {
            log.error("连接签单系统异常", e);
            throw new GenericException(BusinessMessageEnum.SIGN_CONNECT_ERROR.getCode(), BusinessMessageEnum.SIGN_CONNECT_ERROR.getMsg());
        }
        if (list == null || list.isEmpty()) {
            throw new GenericException(BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getCode(), BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getMsg());
        }

        mqSignService.saveServiceRecordByCondition(list, syncflightNos, itemId);
    }


    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    public static String[] getNullPropertyNames(Object source) {
        BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }


}
