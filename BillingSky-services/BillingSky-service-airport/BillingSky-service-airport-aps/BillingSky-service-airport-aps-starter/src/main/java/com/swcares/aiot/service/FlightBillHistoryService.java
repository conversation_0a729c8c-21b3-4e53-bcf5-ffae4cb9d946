package com.swcares.aiot.service;

import com.swcares.aiot.core.entity.TFlightBillHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 航班账单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
public interface FlightBillHistoryService {

    void copyBillHistory(@Param("billIds") List<String> billIds, @Param("name") String name, @Param("operation") String operation);


    List<TFlightBillHistory> getTFlightBillHistoryByFlightBillIds(List<String> flightBillIds, String operation);
}
