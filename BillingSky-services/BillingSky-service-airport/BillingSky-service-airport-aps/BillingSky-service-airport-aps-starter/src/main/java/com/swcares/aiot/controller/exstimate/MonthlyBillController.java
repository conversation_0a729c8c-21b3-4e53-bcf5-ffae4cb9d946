package com.swcares.aiot.controller.exstimate;

import com.swcares.aiot.core.model.dto.ErAutoConfirmDto;
import com.swcares.aiot.core.model.dto.ErBillFulfillAndConfirmDto;
import com.swcares.aiot.core.model.dto.ErMonthlyBillPageDto;
import com.swcares.aiot.core.model.dto.ErMonthlyObjectionDto;
import com.swcares.aiot.core.model.vo.*;
import com.swcares.aiot.service.MonthlyBillService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.exstimate.MonthlyBillController
 * Description：月账单controller
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/27 10:47
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/monthly/bill")
@Api(value = "MonthlyBillController", tags = {"月账单接口"})
@ApiVersion("对账通-机场端-离港返还-api")
@Slf4j
public class MonthlyBillController {

    @Resource
    private MonthlyBillService monthlyBillService;

    @PostMapping("/page")
    @ApiOperation(value = "月账单分页查询")
    public PagedResult<List<ErMonthlyBillPageVo>> page(@RequestBody @Validated ErMonthlyBillPageDto monthlyBillPageDto) {
        monthlyBillPageDto.setIsAirport(Boolean.TRUE);
        return monthlyBillService.page(monthlyBillPageDto);
    }

    @PostMapping("/count")
    @ApiOperation(value = "月账单分页汇总数据查询")
    public BaseResult<ErMonthlyBillAccVo> count(@RequestBody @Validated ErMonthlyBillPageDto monthlyBillPageDto) {
        monthlyBillPageDto.setIsAirport(Boolean.TRUE);
        return monthlyBillService.count(monthlyBillPageDto);
    }


    @PutMapping("/confirm/{id}")
    @ApiOperation(value = "确认月账单")
    public BaseResult<Boolean> confirmId(@PathVariable("id") Long id) {
        return monthlyBillService.confirmId(id);
    }

    @PutMapping("/switchMonthlyAutoConfirm")
    @ApiOperation(value = "机场端-月账单自动确认开关")
    public BaseResult<Boolean> switchMonthlyAutoConfirm(@RequestBody ErAutoConfirmDto erAutoConfirmDto) {
        return monthlyBillService.switchMonthlyAutoConfirm(erAutoConfirmDto);
    }

    @GetMapping("/monthlyAutoConfirmQuery")
    @ApiOperation(value = "机场端-月账单自动确认开关查询")
    public BaseResult<Boolean> monthlyAutoConfirmQuery(@RequestParam("airportCode") String airportCode) {
        return monthlyBillService.monthlyAutoConfirmQuery(airportCode);
    }

    @GetMapping("/listErMonthlyBillObjectionHistoryVo/{id}")
    @ApiOperation(value = "月账单异议记录查询")
    public BaseResult<List<ErMonthlyBillHistoryVo>> listErMonthlyBillObjectionHistoryVo(@PathVariable("id") String id) {
        return monthlyBillService.listErMonthlyBillObjectionHistoryVo(id);
    }

    @PostMapping("/objection")
    @ApiOperation(value = "月账单提出异议")
    public BaseResult<Boolean> objection(@RequestBody @Validated ErMonthlyObjectionDto erMonthlyObjectionDto){
        return monthlyBillService.objection(erMonthlyObjectionDto);
    }


    @GetMapping("/getBillingBillDetail/{billingBillId}")
    @ApiOperation(value = "查询Bill账单导入明细")
    public BaseResult<ErBillingBillPageVo> getBillingBillHis(@PathVariable @ApiParam(value = "主键id", required = true) String billingBillId) {
        return monthlyBillService.getBillingBillHis(billingBillId);
    }


    @PutMapping("/confirmPay")
    @ApiOperation(value = "支付-[每日账单]确认支付")
    public BaseResult<ErBillFulfillAndConfirmVo> confirmPay(@RequestBody @Validated ErBillFulfillAndConfirmDto erBillFulfillAndConfirmDto) {
        return monthlyBillService.confirmPay(erBillFulfillAndConfirmDto);
    }

    @PostMapping("/downloadErMonthlyBill")
    @ApiOperation(value = "查询-下载[每日账单]")
    public void downloadErMonthlyBill(@RequestBody @Validated ErMonthlyBillPageDto erMonthlyBillPageDto, HttpServletResponse response) throws IOException {
        erMonthlyBillPageDto.setIsAirport(Boolean.TRUE);
        monthlyBillService.downloadErMonthlyBillData(erMonthlyBillPageDto,response);
    }

    @PostMapping(value = "/monthlyUpload", headers = "content-type=" + MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "月账单异议附件上传", notes = "返回文件信息", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResult<String> uploadFile(@RequestPart("file") MultipartFile file) {
        return monthlyBillService.uploadFile(file);
    }


}
