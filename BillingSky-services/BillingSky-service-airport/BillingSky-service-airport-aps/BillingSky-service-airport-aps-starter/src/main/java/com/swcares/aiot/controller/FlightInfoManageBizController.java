package com.swcares.aiot.controller;

import com.swcares.aiot.client.local.ICustomerBizLocalClient;
import com.swcares.aiot.core.cons.ConsCalculate;
import com.swcares.aiot.service.IFlightInfoManageBizService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.ass.business.base.controller.AirlineFlightInfoController <br>
 * Description：航班信息表 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024-03-12 <br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@RequestMapping("/flightInfoManageBiz")
@Api(tags = "航班信息表接口")
@ApiVersion(ConsCalculate.SWAGGER_API_VERSION_INFO)
public class FlightInfoManageBizController extends BaseController {
    @Resource
    private IFlightInfoManageBizService iFlightInfoManageBizService;

    @Resource
    private ICustomerBizLocalClient iCustomerBizLocalClient;

    @GetMapping("/retrieveCustomerNoExpense/{customerIataA}")
    @ApiOperation(value = "查询-获取所有未创建航司费用信息的航司数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerIataA", value = "甲方编码", required = true, dataType = "String", paramType = "path")
    })
    public BaseResult<List<String>> retrieveCustomerNoExpense(@PathVariable("customerIataA") String customerIataA) {
        List<String> allAirlineCodes = iFlightInfoManageBizService.retrieveAirlineCodes();
        log.info("航班数据的所有航司代码:{}", allAirlineCodes);
        BaseResult<List<String>> resp = iCustomerBizLocalClient.retrieveAirportCodes(customerIataA);
        List<String> expenseAirlineCodes = resp.getData();
        log.info("计算中心创建了航司的代码:{}", expenseAirlineCodes);
        List<String> airlineNames = iFlightInfoManageBizService.getAirlineNames(allAirlineCodes, expenseAirlineCodes);
        return BaseResult.ok(airlineNames);
    }
}
