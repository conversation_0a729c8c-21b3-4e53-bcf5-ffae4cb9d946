package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.entity.TFlightBillHistory;
import com.swcares.aiot.core.entity.TFlightBillHistorySnapshot;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.mapstruct.MsReCalcProcessBiz;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.model.vo.FlightBillHistorySnapShotVo;
import com.swcares.aiot.core.service.ITFlightBillHistoryService;
import com.swcares.aiot.core.service.ITFlightBillHistorySnapshotService;
import com.swcares.aiot.core.service.ITFlightBillService;
import com.swcares.aiot.model.dto.ActIndexCaluDto;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.CmpCalcSaveFlightBills
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/13 13:32
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcSaveFlightBills", name = "组件-对账通机场端-计算模块-保存账单")
public class CmpCalcSaveFlightBills extends NodeComponent {

    private static final String TEMPLATE_FOUR_BRACKETS = "{}{}{}";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    static {
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
        OBJECT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }
    @Resource
    private ITFlightBillService tFlightBillService;
    @Resource
    private ITFlightBillHistoryService tFlightBillHistoryService;
    @Resource
    private MsReCalcProcessBiz msReCalcProcessBiz;
    @Resource
    private ITFlightBillHistorySnapshotService tFlightBillHistorySnapshotService;

    @Override
    public void process() {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        //已生成账单数据
        List<TFlightBill> flightBillList = ctxCalc.getFlightBillList();
        LoginUserDetails user = ctxCalc.getUser();
        List<TFlightBill> insertFlightBillList = new ArrayList<>();
        List<TFlightBill> updateFlightBillList = new ArrayList<>();
        List<TFlightBillHistory> flightBillHistoryList = new ArrayList<>();
        List<TFlightBillHistorySnapshot> flightBillHistorySnapshotList = new ArrayList<>();
        //账单对应快照信息
        Map<TFlightBill, FlightBillHistorySnapShotVo> snapShotMap = ctxCalc.getSnapShotMap();
        //获取航班费用对应指标项数据
        Map<String, List<ActIndexCaluDto>> flightFeeIndexCaluMap = ctxCalc.getFlightFeeIndexCaluMap();
        //获取已生成账单数据
        Map<String, List<TFlightBill>> oldFlightBillMap = getOldFlightBillMap(flightBillList);
        //循环处理新生成、更新账单
        for (TFlightBill flightBill : flightBillList) {
            String key = CharSequenceUtil.format(TEMPLATE_FOUR_BRACKETS, flightBill.getFlightId(), "-", flightBill.getFeeCode());
            List<TFlightBill> tFlightBills = oldFlightBillMap.get(key);
            TFlightBillHistory tFlightBillHistory;
            //获取暂存账单对应快照(flightBill未变化之前取)
            FlightBillHistorySnapShotVo flightBillHistorySnapShotVo = snapShotMap.get(flightBill);
            flightBill.setModifiedTime(LocalDateTime.now());
            flightBill.setModifiedBy(user.getUsername());
            if (CollUtil.isNotEmpty(tFlightBills)) {
                TFlightBill oldFlightBill = tFlightBills.get(0);
                flightBill.setId(oldFlightBill.getId());
                updateFlightBillList.add(flightBill);

                tFlightBills.remove(0);
                if (tFlightBills.isEmpty()) {
                    oldFlightBillMap.remove(key);
                }
                tFlightBillHistory = msReCalcProcessBiz.saveToHistoryEntity(flightBill, BillOperation.GENERATE.getCode(), user);
            } else {
                long id = IdUtil.getSnowflake(RandomUtil.randomLong(1, 31), RandomUtil.randomLong(1, 31)).nextId();
                flightBill.setId(Long.toString(id));
                flightBill.setCreateTime(LocalDateTime.now());
                flightBill.setCreateBy(user.getUsername());
                insertFlightBillList.add(flightBill);
                tFlightBillHistory = msReCalcProcessBiz.saveToHistoryEntity(flightBill, BillOperation.RESETTLEMENT.getCode(), user);
            }
            tFlightBillHistory.setModifiedTime(LocalDateTime.now());
            tFlightBillHistory.setModifiedBy(user.getUsername());
            flightBillHistoryList.add(tFlightBillHistory);
            List<ActIndexCaluDto> indexCaluDtoList = flightFeeIndexCaluMap.get(flightBill.getFlightId() + "_" + flightBill.getFeeCode());
            //  生成快照数据
            generateSnapshot( flightBillHistorySnapShotVo, tFlightBillHistory, flightBillHistorySnapshotList,indexCaluDtoList);
        }
        //处理老账单中需要删除的账单
        handleDeleteBill(oldFlightBillMap, updateFlightBillList, user, flightBillHistoryList);


        tFlightBillService.saveBatch(insertFlightBillList);
        tFlightBillService.updateBatchById(updateFlightBillList);
        tFlightBillHistoryService.saveBatch(flightBillHistoryList);
        tFlightBillHistorySnapshotService.saveBatch(flightBillHistorySnapshotList);
        log.info("=======================================结算，把生成的账单记录到 历史账单表,tenantId{}，time{}", TenantHolder.getTenant(), LocalDateTime.now());
        log.info("insertFlightBillList条数:{}", insertFlightBillList.size());
        log.info("updateFlightBillList条数:{}", updateFlightBillList.size());
        log.info("flightBillHistoryList条数:{}", flightBillHistoryList.size());
        log.info("flightBillHistorySnapshotList条数:{}", flightBillHistorySnapshotList.size());

    }

    private void handleDeleteBill(Map<String, List<TFlightBill>> oldFlightBillMap, List<TFlightBill> updateFlightBillList, LoginUserDetails user, List<TFlightBillHistory> flightBillHistoryList) {
        oldFlightBillMap.forEach((k, oldFlightBills) ->
                    oldFlightBills.forEach(flightBill -> {
                        flightBill.setInvalid("0");
                        updateFlightBillList.add(flightBill);
                        TFlightBillHistory tFlightBillHistory = msReCalcProcessBiz.saveToHistoryEntity(flightBill, BillOperation.RESETTLEMENT.getCode(), user);
                        flightBillHistoryList.add(tFlightBillHistory);
                    })
        );
    }

    private Map<String, List<TFlightBill>> getOldFlightBillMap(List<TFlightBill> flightBillList) {
        List<String> flightIdList = flightBillList.stream().map(TFlightBill::getFlightId).collect(Collectors.toList());
        List<TFlightBill> oldFlightBillList = tFlightBillService.lambdaQuery()
                .in(TFlightBill::getFlightId, flightIdList)
                .eq(TFlightBill::getInvalid, "1")
                .list();
        Map<String, List<TFlightBill>> oldFlightBillMap = new HashMap<>(oldFlightBillList.size());
        for (TFlightBill flightBill : oldFlightBillList) {
            String key = CharSequenceUtil.format(TEMPLATE_FOUR_BRACKETS, flightBill.getFlightId(), "-", flightBill.getFeeCode());
            List<TFlightBill> billTempList = oldFlightBillMap.getOrDefault(key, new ArrayList<>());
            billTempList.add(flightBill);
            oldFlightBillMap.put(key, billTempList);
        }
        return oldFlightBillMap;
    }

    private static void generateSnapshot( FlightBillHistorySnapShotVo flightBillHistorySnapShotVo, TFlightBillHistory tFlightBillHistory,
                                          List<TFlightBillHistorySnapshot> flightBillHistorySnapshotList,List<ActIndexCaluDto> indexCaluDtoList) {
        if(flightBillHistorySnapShotVo!=null){
            TFlightBillHistorySnapshot tFlightBillHistorySnapshot = new TFlightBillHistorySnapshot();
            tFlightBillHistorySnapshot.setBillHistoryId(tFlightBillHistory.getId());
            flightBillHistorySnapShotVo.getActExpensesCaluVo().setIndexCaluDtos(indexCaluDtoList);
            tFlightBillHistorySnapshot.setSnapshotData(OBJECT_MAPPER.valueToTree(flightBillHistorySnapShotVo));
            flightBillHistorySnapshotList.add(tFlightBillHistorySnapshot);
        }
    }


}
