package com.swcares.aiot.service.impl;

import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.ExcelFlightUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.*;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.*;
import com.swcares.aiot.service.SubsidyBillService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.service.impl.SubsidyBillServiceImpl
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/23 10:36
 * @version v1.0
 */
@Service
@Slf4j
public class SubsidyBillServiceImpl implements SubsidyBillService {
    @Resource
    private SubsidyFormulaBillDao subsidyFormulaBillDao;
    @Resource
    private SubsidyFormulaDao subsidyFormulaDao;
    @Resource
    private FlightLineInfoDao flightLineInfoDao;
    @Resource
    private SubsidyParamDao subsidyParamDao;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private SubsidyFormulaParamJoinDao subsidyFormulaParamJoinDao;
    @Resource
    private SubsidyFlightParamJoinDao subsidyFlightParamJoinDao;
    @Resource
    private SubsidyBillDao subsidyBillDao;
    @Resource
    private AircraftDao aircraftDao;
    @Resource
    private SubsidyBillParamJoinDao subsidyBillParamJoinDao;

    @Override
    public List getFormulaNums(SubsidyFormulaBillCountForm form) {
        if (form.getEndDate() == null || form.getStartDate() == null
                || form.getStartDate().after(form.getEndDate())) {
            // 抛出时间异常
            throw new GenericException(BusinessMessageEnum.SUBSIDY_BILL_DATE_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_BILL_DATE_ERROR.getMsg());
        }

        return subsidyFormulaDao.getSubsidyFormulaByFlightLineIdFlightIdStartEndDate(
                form.getStartDate(), form.getEndDate(), form.getFlightLineId(), "",
                form.getAirportCode());
    }

    @Override
    public Object[] pageSubsidyFormulaBill(SubsidyFormulaBillForm form, PageParam pageParam) {
        // 获取航线id下所有公式账单列表
        List<SubsidyFormulaBill> sfbList = subsidyFormulaBillDao.getSubsidyFormulaBillByCondition(
                form.getStartDate(), form.getEndDate(), form.getSubsidyFormulaId());
        // 将查出来的航线数据按照分页需求分页
        Pager<?> pagerList = new Pager<>(pageParam.getPage(), pageParam.getLimit(), sfbList);
        Object[] resObjs = getSubsidyFormulaBill(form, pagerList.getList());
        pagerList.setList((List) resObjs[1]);
        resObjs[1] = pagerList;
        return resObjs;
    }

    private Object[] getSubsidyFormulaBill(SubsidyFormulaBillForm form, List billList) {
        String subsidyFormulaId = form.getSubsidyFormulaId();
        SubsidyFormula sf = subsidyFormulaDao.getFormulaById(subsidyFormulaId);
        if (sf == null) {
            // 抛出公式為空异常
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_BILL_FORMULA_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_BILL_FORMULA_NULL_ERROR.getMsg());
        }
        FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoById(sf.getFlightLineId());
        if (fli == null) {
            // 抛出航线为空异常
            throw new GenericException(BusinessMessageEnum.SUBSIDY_BILL_LINE_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_BILL_LINE_NULL_ERROR.getMsg());
        }
        String formula = sf.getFormula();
        Set<String> paramIdSet = new HashSet<>();
        StringBuilder param = new StringBuilder();
        boolean parseParam = false;
        handleFormula(formula, paramIdSet, param, parseParam);

        List<SubsidyParam> spList = subsidyParamDao
                .getSubsidyParamListById(new ArrayList<>(paramIdSet));
        Set<String> paramNameSet = new HashSet<>();
        for (SubsidyParam sp : spList) {
            paramNameSet.add(sp.getParamName());
        }

        if (paramNameSet.contains("小时价格")) {
            paramNameSet.add("实际小时价格");
        }
        List<String> titleList = new ArrayList<>();
        titleList.add("id");
        for (String title : CommonConstants.SUBSIDY_BILL_TITLE) {
            if (CommonConstants.SUBSIDY_BILL_REQUIRED_TITLE.contains(title)
                    || paramNameSet.contains(title)) {
                titleList.add(title);
            }
        }

        String airline = fli.getAirlineCode();
        String flightLine = fli.getFlightLine();
        String applicableModels = sf.getApplicableModels();
        String airportCode = fli.getAirportCode();

        List<Object[]> resList = new ArrayList<>();

        billList.forEach(sfb -> {
            Object[] obj = new Object[titleList.size()];
            String date = DateUtils.format(((SubsidyFormulaBill) sfb).getStartDate()) + "至"
                    + DateUtils.format(((SubsidyFormulaBill) sfb).getEndDate());
            Map<String, String> paramMap = new HashMap<>();


            String[] flArr = flightLine.split("-");
            String reFlighrLine = "";
            for (int i = 0; i < flArr.length; i++) {
                reFlighrLine = reFlighrLine + flArr[flArr.length - 1 - i];
                if (i != flArr.length - 1) {
                    reFlighrLine = reFlighrLine + "-";
                }
            }
            // 根据航司、航线、日期、机场获取班次
            List<String> fiIdList =
                    flightInfoDao.getFlightIdByLineTime(((SubsidyFormulaBill) sfb).getStartDate(),
                            ((SubsidyFormulaBill) sfb).getEndDate(), airline, flightLine,
                            reFlighrLine, airportCode);

            // 获取账单结算金额
            BigDecimal settleResult = ((SubsidyFormulaBill) sfb).getSettleResult();
            // 获取保底收入
            BigDecimal guaranteedIncome =
                    ((SubsidyFormulaBill) sfb).getGuaranteedIncome() == null ? BigDecimal.ZERO
                            : ((SubsidyFormulaBill) sfb).getGuaranteedIncome();
            String formulaBillid = ((SubsidyFormulaBill) sfb).getId();
            obj[0] = formulaBillid;
            for (int i = 1; i < titleList.size(); i++) {
                if ("日期".equals(titleList.get(i))) {
                    obj[i] = date;
                } else if ("航司".equals(titleList.get(i))) {
                    obj[i] = airline;
                } else if ("航线".equals(titleList.get(i))) {
                    obj[i] = flightLine;
                } else if ("机型".equals(titleList.get(i))) {
                    obj[i] = applicableModels;
                } else if ("班次".equals(titleList.get(i))) {
                    obj[i] = fiIdList.size();
                } else if ("实际小时价格".equals(titleList.get(i))) {
                    obj[i] = Double.parseDouble(paramMap.getOrDefault("小时价格", "0"))
                            + Double.parseDouble(paramMap.getOrDefault("燃油联动价", "0"))
                            + Double.parseDouble(paramMap.getOrDefault("人数联动价", "0"));
                } else if ("保底收入".equals(titleList.get(i))) {
                    obj[i] = guaranteedIncome;
                } else if ("航司盈亏".equals(titleList.get(i))) {
                    obj[i] = BigDecimal.ZERO.subtract(settleResult);
                } else if ("付款金额".equals(titleList.get(i))) {
                    obj[i] = settleResult;
                } else if ("轮挡时间".equals(titleList.get(i))) {
                    SubsidyParam sp = subsidyParamDao.getSubsidyParamByName("轮挡时间", airportCode);
                    String sumValue;
                    sumValue = subsidyBillParamJoinDao
                            .getDistinctByParamIdAndBillId(formulaBillid, sp.getId());
                    obj[i] = sumValue == null ? "0.0" : (sumValue);
                    paramMap.put(sp.getParamName(), obj[i].toString());
                } else {
                    String paramName = titleList.get(i);
                    // 根据paramName参数信息
                    SubsidyParam sp = subsidyParamDao.getSubsidyParamByName(paramName, airportCode);
                    String sumValue;
                    if ((sumValue = subsidyBillParamJoinDao
                            .getSumByParamIdAndBillId(formulaBillid, sp.getId())) != null) {
                        obj[i] = Double.parseDouble(sumValue);
                        if (CommonConstants.SUBSIDY_AVERAGE_LIST.contains(sp.getParamName())) {
                            obj[i] = BigDecimal.valueOf((Double) obj[i]).divide(BigDecimal.valueOf(fiIdList.size()));
                        }
                    } else {
                        obj[i] = 0.0;
                    }
                    paramMap.put(sp.getParamName(), obj[i].toString());
                }
            }

            resList.add(obj);
        });
        Object[] resObj = new Object[2];
        resObj[0] = titleList;
        resObj[1] = resList;
        return resObj;
    }

    @Override
    public Object[] pageSubsidyDetailBill(String formulaBillId, PageParam pageParam) {
        return getSubsidyDetailBillData(formulaBillId,pageParam);
    }


    @Override
    public Object[] countPageSubsidyDetailBill(String formulaBillId) {
        Object[] res = getSubsidyDetailBillData(formulaBillId, null);
        List<String> titleList = (List<String>) res[0];
        List<Object[]> dataList = ((List<Object[]>) res[1]);
        Object[] resCount = new Object[titleList.size()];
        Map<String, BigDecimal> averageMap = new HashMap<>();
        for (Object[] objects : dataList) {
            boolean start = false;
            for (int i = 0; i < objects.length; i++) {
                if (start) {
                    if ("regError".equals(titleList.get(i))) {
                        start = false;
                        continue;
                    }
                    if (("轮挡时间".equals(titleList.get(i)))) {
                        BigDecimal averageCount = averageMap.getOrDefault("轮挡时间", BigDecimal.ZERO);
                        averageCount = averageCount.add(BigDecimal.valueOf((double) objects[i]));
                        resCount[i] = averageCount.divide(BigDecimal.valueOf((dataList.size())));
                        averageMap.put("轮挡时间", averageCount);
                    } else {
                        resCount[i] = BigDecimal.valueOf((double) objects[i]).add(
                                resCount[i] == null ? BigDecimal.ZERO : (BigDecimal) resCount[i]);
                    }
                } else if ("班次".equals(titleList.get(i))) {
                    start = true;
                    resCount[i] = (int) objects[i] + (resCount[i] == null ? 0 : (int) resCount[i]);

                }
            }
        }
        res[1] = resCount;
        return res;
    }

    private Object[] getSubsidyDetailBillData(String formulaBillId, PageParam pageParam) {
        // 查询传入公式账单id对应补贴公式信息
        List<SubsidyFormula> sfList = getSubsidyFormulas(formulaBillId);
        SubsidyFormula sf = sfList.get(0);
        String formula = sf.getFormula();
        Set<String> paramIdSet = new HashSet<>();
        StringBuilder param = new StringBuilder();
        boolean parseParam = false;
        handleFormula(formula, paramIdSet, param, parseParam);
        List<SubsidyParam> spList = subsidyParamDao.getSubsidyParamListById(new ArrayList<>(paramIdSet));
        List<String> titleList = getTitleList(spList);
        inspectSubsidyFormulaBill(formulaBillId, BusinessMessageEnum.SUBSIDY_BILL_DETAIL_ERROR);
        Set<String> amSet = getAmSet(sf);
        List<Object[]> billObj = subsidyBillDao.getSubsidyBillByFormulaBillId(formulaBillId);
        List<Object[]> dataList;
        Pager<Object[]> pagerList = null;
        if (pageParam != null) {
            // 将查出来的航线数据按照分页需求分页
            pagerList = new Pager<>(pageParam.getPage(), pageParam.getLimit(), billObj);
            dataList = pagerList.getList();
        } else {
            dataList = billObj;
        }
        List<Object[]> resList = getResList(sf, dataList, titleList, amSet);
        Object[] res = new Object[2];
        res[0] = titleList;
        if (pagerList != null) {
            pagerList.setList(resList);
            res[1] = pagerList;
        } else {
            res[1] = resList;
        }
        return res;
    }

    private @NotNull List<Object[]> getResList(SubsidyFormula sf, List<Object[]> dataList, List<String> titleList, Set<String> amSet) {
        List<Object[]> resList = new ArrayList<>();
        String airportCode = sf.getAirportCode();
        for (Object[] objs : dataList) {
            Object[] resObj = getResObj(sf, titleList, amSet, objs, airportCode);
            resList.add(resObj);
        }
        return resList;
    }

    private @NotNull Object[] getResObj(SubsidyFormula sf, List<String> titleList, Set<String> amSet, Object[] objs, String airportCode) {
        Object[] resObj = new Object[titleList.size()];
        // 设置id
        String billId = (String) objs[0];
        resObj[0] = billId;
        // 机型异常标识
        boolean regErrorFlag = false;
        // 航班付款金额
        double settleResult = Double.parseDouble((String) objs[7]);
        // 航班付款金额
        double guaranteedIncome = Double.parseDouble(objs[10] == null ? "0.0" : (String) objs[10]);
        // 参数暂存map
        Map<String, String> paramMap = new HashMap<>();
        resObj[4] = objs[4];
        for (int i = 1; i < titleList.size(); i++) {
            if ("日期".equals(titleList.get(i))) {
                resObj[i] = objs[1];
            } else if ("航班号".equals(titleList.get(i))) {
                resObj[i] = objs[2];
            } else if ("航线".equals(titleList.get(i))) {
                resObj[i] = objs[3];
            } else if ("航段".equals(titleList.get(i))) {
                resObj[i] = objs[4];
            } else if ("机型".equals(titleList.get(i))) {
                regErrorFlag = isRegErrorFlag(sf, amSet, objs, resObj, regErrorFlag, i);
            } else if ("regError".equals(titleList.get(i))) {
                resObj[i] = regErrorFlag;
            } else if ("班次".equals(titleList.get(i))) {
                resObj[i] = 1;
            } else if ("实际小时价格".equals(titleList.get(i))) {
                resObj[i] = Double.parseDouble(paramMap.getOrDefault("小时价格", "0"))
                        + Double.parseDouble(paramMap.getOrDefault("燃油联动价", "0"))
                        + Double.parseDouble(paramMap.getOrDefault("人数联动价", "0"));
            } else if ("保底收入".equals(titleList.get(i))) {
                resObj[i] = guaranteedIncome;
            } else if ("航司盈亏".equals(titleList.get(i))) {
                resObj[i] = -settleResult;
            } else if ("付款金额".equals(titleList.get(i))) {
                resObj[i] = settleResult;
            } else {
                // 根据paramName参数信息
                SubsidyParam sp = subsidyParamDao.getSubsidyParamByName(titleList.get(i), airportCode);
                paramMap.put(sp.getParamName(), resObj[i].toString());
                resObj[i] =  getaDouble(billId, sp);
            }
        }
        return resObj;
    }

    private double getaDouble(String billId, SubsidyParam sp) {
        SubsidyBillParamJoin sbpj = subsidyBillParamJoinDao.getSubsidyBillParamJoinByParamIdAndBillId(billId, sp.getId());
        return (sbpj == null || sbpj.getParamValue() == null) ? 0.0 : Double.parseDouble(sbpj.getParamValue());
    }

    private static @NotNull Set<String> getAmSet(SubsidyFormula sf) {
        Set<String> amSet = new HashSet<>();
        // 解析机型
        if (!"*".equals(sf.getApplicableModels())) {
            String[] applicableModels = sf.getApplicableModels().split(",");
            amSet = Arrays.stream(applicableModels).collect(Collectors.toSet());
        }
        return amSet;
    }

    private SubsidyFormulaBill inspectSubsidyFormulaBill(String formulaBillId, BusinessMessageEnum subsidyBillDetailError) {
        SubsidyFormulaBill sfb = subsidyFormulaBillDao.getSubsidyFormulaBillById(formulaBillId);
        if (sfb == null) {
            // 抛出异常
            throw new GenericException(subsidyBillDetailError.getCode(), subsidyBillDetailError.getMsg());
        }
        return sfb;
    }

    private @NotNull List<SubsidyFormula> getSubsidyFormulas(String formulaBillId) {
        List<SubsidyFormula> sfList = subsidyFormulaDao.getFormulaByformulaBillId(formulaBillId);
        if (sfList.isEmpty()) {
            // 抛出异常
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_BILL_FORMULA_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_BILL_FORMULA_NULL_ERROR.getMsg());
        }
        return sfList;
    }

    @NotNull
    private static List<String> getTitleList(List<SubsidyParam> spList) {
        Set<String> paramNameSet = new HashSet<>();
        for (SubsidyParam sp : spList) {
            paramNameSet.add(sp.getParamName());
        }

        if (paramNameSet.contains("小时价格")) {
            paramNameSet.add("实际小时价格");
        }
        // 设置表头字段
        List<String> titleList = new ArrayList<>();
        titleList.add("id");
        for (String title : CommonConstants.SUBSIDY_BILL_DETAIL_TITLE) {
            if (CommonConstants.SUBSIDY_BILL_DETAIL_REQUIRED_TITLE.contains(title) || paramNameSet.contains(title)) {
                titleList.add(title);
            }
        }
        return titleList;
    }

    private boolean isRegErrorFlag(SubsidyFormula sf, Set<String> amSet, Object[] objs, Object[] resObj, boolean regErrorFlag, int i) {
        String regNo = (String) objs[5];
        String flightModel;
        // 根据机号查机型
        List<AircraftInfo> aiList = aircraftDao.findByRegNo(regNo);
        if (aiList.isEmpty()) {
            flightModel = (String) objs[6];
        } else {
            flightModel = aiList.get(0).getAirplaneModel();
        }
        resObj[i] = flightModel;
        if ((!"*".equals(sf.getApplicableModels())) && !amSet.contains(flightModel)) {
            regErrorFlag = true;
        }
        return regErrorFlag;
    }

    private static void handleFormula(String formula, Set<String> paramIdSet, StringBuilder param, boolean parseParam) {
        for (char c : formula.toCharArray()) {
            if (parseParam) {
                if (c == ']') {
                    parseParam = false;
                    String paramId = param.toString();
                    paramIdSet.add(paramId);
                    param.setLength(0);
                } else {
                    param.append(c);
                }
            } else {
                if (c == '[') {
                    parseParam = true;
                }
            }
        }
    }

    @Override
    @Transactional
    public boolean deleteBill(String formulaBillIds, LoginUserDetails user) {
        if (Strings.isBlank(formulaBillIds)) {
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FORMULA_ID_NULL.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_ID_NULL.getMsg());
        }
        String[] arrId = formulaBillIds.split(",");
        for (String formulaBillId : arrId) {
            SubsidyFormulaBill sfb = inspectSubsidyFormulaBill(formulaBillId, BusinessMessageEnum.SUBSIDY_BILL_DETAIL_ID_NULL);
            // 逻辑删除公式账单
            sfb.setInvalid("0");
            sfb.setModifiedBy(user.getUsername());
            sfb.setModifiedTime(new Date());
            subsidyFormulaBillDao.save(sfb);

            // 逻辑删除航班账单
            subsidyBillDao.deleteBillByFormulaBillId(formulaBillId, user.getUsername(), new Date());

            // 更新航线预算
            FlightLineInfo fli =
                    flightLineInfoDao.getFlightLineInfoByFormulaId(sfb.getSubsidyFormulaId());
            fli.setUsed(fli.getUsed().subtract(sfb.getSettleResult()));
            fli.setModifiedBy(user.getUsername());
            fli.setModifiedTime(new Date());
            flightLineInfoDao.saveAndFlush(fli);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean updateDetailBill(SubsidyDetailBillUpdateForm form, LoginUserDetails user) {
        String detailBillId = form.getId();
        FlightInfo fi = flightInfoDao.getFlightInfoByBillDetailId(detailBillId);
        if (fi == null) {
            // 抛错
            throw new GenericException(BusinessMessageEnum.SUBSIDY_BILL_DETAIL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_BILL_DETAIL_ERROR.getMsg());
        }
        List<SubsidyDetailBillUpdateParamForm> paramList = form.getParamList();
        for (SubsidyDetailBillUpdateParamForm sdbuf : paramList) {
            if ("付款金额".equals(sdbuf.getName())) {
                updateDetailBillBizOne(sdbuf, detailBillId, fi);
            } else {
                updateDetailBillBizTwo(user, sdbuf, fi, detailBillId);
            }
        }
        return true;
    }

    private void updateDetailBillBizTwo(LoginUserDetails user, SubsidyDetailBillUpdateParamForm sdbuf, FlightInfo fi, String detailBillId) {
        //获取参数信息
        SubsidyParam subsidyParam = subsidyParamDao.getSubsidyParamByName(sdbuf.getName(), fi.getAirportCode());
        if (subsidyParam == null) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FLIGHT_PARAM_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_FLIGHT_PARAM_NULL_ERROR.getMsg());
        }
        //校验修改参数值
        String value = sdbuf.getValue();

        // 校验数值格式
        if ("1".equals(subsidyParam.getNumType())) {
            if (!isPositiveInteger(value)) {
                // 抛出参数类型为正整数，但录入实际参数值不匹配
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR.getMsg());
            }
            value = "" + Integer.parseInt(value);
        } else if ("2".equals(subsidyParam.getNumType())) {
            if (!isInteger(value)) {
                // 抛出参数类型为整数，但录入实际参数值不匹配
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_INTEGER_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_INTEGER_ERROR.getMsg());
            }
            value = "" + Integer.parseInt(value);
        } else if ("3".equals(subsidyParam.getNumType())) {
            if (isDouble(value)) {
                // 抛出参数类型为浮点数，但录入实际参数值不匹配
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getMsg());
            }
            value = "" + Double.parseDouble(value);
        }
        //获取账单关联参数
        SubsidyBillParamJoin sbpj = subsidyBillParamJoinDao
                .getSubsidyBillParamJoinByParamIdAndBillId(detailBillId, subsidyParam.getId());
        if (sbpj == null) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_BILL_UPDATE_PARAM_NULL.getCode(),
                    BusinessMessageEnum.SUBSIDY_BILL_UPDATE_PARAM_NULL.getMsg());
        }
        sbpj.setParamValue(value);
        sbpj.setModifiedBy(user.getUsername());
        sbpj.setModifiedTime(new Date());
        subsidyBillParamJoinDao.save(sbpj);
    }

    private void updateDetailBillBizOne(SubsidyDetailBillUpdateParamForm sdbuf, String detailBillId, FlightInfo fi) {
        SubsidyBill sb = subsidyBillDao.getById(detailBillId);
        //获取结算金额旧值
        BigDecimal oldValue = BigDecimal.valueOf(Double.parseDouble(sb.getSettleResult()));
        //获取保底收入旧值
        BigDecimal guaranteedIncomeresOldValue = BigDecimal.valueOf(Double.parseDouble(sb.getGuaranteedIncome()));
        if (isDouble(sdbuf.getValue())) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_BILL_UPDATE_DOUBLE_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_BILL_UPDATE_DOUBLE_ERROR.getMsg());
        }
        BigDecimal newValue = BigDecimal.valueOf(Double.parseDouble(sdbuf.getValue()));
        sb.setSettleResult(sdbuf.getValue());

        // 计算保底收入
        SubsidyFlightParamJoin kpsr = subsidyFlightParamJoinDao
                .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "客票收入");
        BigDecimal kpsrValue = BigDecimal.ZERO;
        if (kpsr != null && !Strings.isBlank(kpsr.getParamValue())) {
            kpsrValue = BigDecimal.valueOf(Double.parseDouble(kpsr.getParamValue()));
        }
        SubsidyFlightParamJoin ryfjf = subsidyFlightParamJoinDao
                .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "燃油附加费");
        BigDecimal ryfjfValue = BigDecimal.ZERO;
        if (ryfjf != null && !Strings.isBlank(ryfjf.getParamValue())) {
            ryfjfValue = BigDecimal.valueOf(Double.parseDouble(ryfjf.getParamValue()));
        }
        BigDecimal flightGuaranteedIncome =
                ryfjfValue.add(kpsrValue).add(newValue);
        sb.setGuaranteedIncome("" + flightGuaranteedIncome);

        sb.setManualModified("1");
        subsidyBillDao.save(sb);

        // 计算出变化金额
        BigDecimal change = newValue.subtract(oldValue);
        BigDecimal guaranteedIncomeresAmount = flightGuaranteedIncome.subtract(guaranteedIncomeresOldValue);


        SubsidyFormulaBill sfb =
                subsidyFormulaBillDao.getSubsidyFormulaBillById(sb.getFormulaBillId());
        // 修改汇总账单金额
        sfb.setSettleResult(sfb.getSettleResult().add(change));
        // 修改汇总账单保底收入
        sfb.setGuaranteedIncome(sfb.getGuaranteedIncome().add(guaranteedIncomeresAmount));
        subsidyFormulaBillDao.save(sfb);

        // 修改航司预算金额
        SubsidyFormula sf = subsidyFormulaDao.getFormulaById(sfb.getSubsidyFormulaId());
        FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoById(sf.getFlightLineId());
        fli.setBudget(fli.getBudget().subtract(change));
        fli.setUsed(fli.getUsed().add(change));
        flightLineInfoDao.save(fli);
    }

    @Override
    public void exportSubsidyFomrlaBill(SubsidyFormulaBillExportForm form, HttpServletResponse res) {
        List<XSSFWorkbook> wbList = new ArrayList<>();
        List<String> nameList = new ArrayList<>();
        for (String formulaBillId : form.getFormulaBillId()) {
            // 获取航线id下所有公式账单列表
            SubsidyFormulaBill sfbTemp =
                    subsidyFormulaBillDao.getSubsidyFormulaBillById(formulaBillId);
            List<SubsidyFormulaBill> sfbList = Collections.singletonList(sfbTemp);
            SubsidyFormulaBillForm sbform = new SubsidyFormulaBillForm();
            sbform.setStartDate(sfbTemp.getStartDate());
            sbform.setEndDate(sfbTemp.getEndDate());
            sbform.setSubsidyFormulaId(sfbTemp.getSubsidyFormulaId());
            Object[] resObj = getSubsidyFormulaBill(sbform, sfbList);
            SubsidyFormula sf = subsidyFormulaDao.getFormulaById(sfbTemp.getSubsidyFormulaId());
            FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoById(sf.getFlightLineId());
            String airline = fli.getAirlineShortName();
            Object[] exportObj = new Object[5];
            exportObj[0] = resObj[0];
            exportObj[1] = resObj[1];
            exportObj[2] = airline + "-" + fli.getFlightLineCn().replace("-", "=") + "-航线补贴对账支付表";
            exportObj[3] = "执行航空：" + airline;
            exportObj[4] = getResCount((List<String>) resObj[0], (List) resObj[1]);

            // 详情账单导出数据
            Object[] exportDetail = new Object[4];
            List<Object[]> exportDetailData = new ArrayList<>();

            for (SubsidyFormulaBill sfb : sfbList) {
                Object[] getObj = getExportSubsidyDetailBillData(sfb.getId());
                // 标题
                exportDetail[0] = getObj[0];
                List<Object[]> detailBill = ((List) getObj[1]);
                exportDetailData.addAll(detailBill);
            }
            if (!exportDetailData.isEmpty()) {
                // 账单数据
                exportDetail[1] = exportDetailData;

                // 详情账单总计数据
                exportDetail[2] = getDetailCount((List) exportDetail[0], exportDetailData);
            }
            // 详情账单标题
            exportDetail[3] = fli.getFlightLineCn().replace("-", "=") + "-航线补贴对账支付明细表";

            XSSFWorkbook book = ExcelFlightUtils.exportSubsidyBill(exportObj, exportDetail);
            String fileName = airline + "-" + fli.getFlightLineCn().replace("-", "=") + "-航线补贴对账支付表"
                    + DateUtils.format(sfbTemp.getStartDate(), "yyyyMMdd") + "-"
                    + DateUtils.format(sfbTemp.getEndDate(), "yyyyMMdd") + ".xlsx";
            wbList.add(book);
            nameList.add(fileName);
        }
        try {
            if (!wbList.isEmpty()) {
                FileUtils.exportExcelToZip(wbList, nameList, "压缩文件", res);
            }
        } catch (Exception e) {
            log.error("出现业务异常", e);
        }

    }

    private Object[] getDetailCount(List<String> detailTitleList, List<Object[]> exportDetailData) {
        Object[] detailCount = new Object[detailTitleList.size()];
        for (Object[] detailData : exportDetailData) {
            for (int i = 0; i < detailCount.length; i++) {
                switch (detailTitleList.get(i)) {
                    case "班次":
                        detailCount[i] = (int) detailData[i]
                                + (detailCount[i] == null ? 0 : (int) detailCount[i]);
                        break;
                    case "保底收入":
                    case "客票收入":
                    case "航司盈亏":
                    case "付款金额":
                        detailCount[i] = BigDecimal.valueOf((double) detailData[i])
                                .add(detailCount[i] == null ? BigDecimal.ZERO : (BigDecimal) detailCount[i]);
                        break;
                    default:
                        detailCount[i] = "";
                }
            }
        }
        return detailCount;
    }

    private Object[] getResCount(List<String> titleList, List<Object[]> dataList) {
        Object[] resCount = new Object[titleList.size()];
        for (Object[] objects : dataList) {
            for (int i = 0; i < objects.length; i++) {
                switch (titleList.get(i)) {
                    case "班次":
                        resCount[i] = (int) objects[i] + (resCount[i] == null ? 0 : (int) resCount[i]);
                        break;
                    case "保底收入":
                    case "客票收入":
                    case "航司盈亏":
                    case "付款金额":
                        resCount[i] = (BigDecimal.valueOf(Double.parseDouble("" + objects[i])))
                                .add(resCount[i] == null ? BigDecimal.ZERO : (BigDecimal) resCount[i]);
                        break;
                    default:
                        resCount[i] = "";
                }
            }
        }
        return resCount;
    }

    private Object[] getExportSubsidyDetailBillData(String formulaBillId) {
        // 查询传入公式账单id对应补贴公式信息

        List<SubsidyFormula> sfList = getSubsidyFormulas(formulaBillId);
        SubsidyFormula sf = sfList.get(0);

        // 查询公式轮挡时间
        SubsidyFormulaParamJoin ldsj = subsidyFormulaParamJoinDao
                .getSubsidyFormulaParamJoinByFormulaIdAndParamName(sf.getId(), "轮挡时间");

        String airportCode = sf.getAirportCode();
        String formula = sf.getFormula();
        Set<String> paramIdSet = new HashSet<>();
        StringBuilder param = new StringBuilder();
        boolean parseParam = false;
        handleFormula(formula, paramIdSet, param, parseParam);

        List<SubsidyParam> spList = subsidyParamDao
                .getSubsidyParamListById(new ArrayList<>(paramIdSet));
        Set<String> paramNameSet = new HashSet<>();
        for (SubsidyParam sp : spList) {
            paramNameSet.add(sp.getParamName());
        }

        if (paramNameSet.contains("小时价格")) {
            paramNameSet.add("实际小时价格");
        }
        // 设置表头字段
        List<String> titleList = new ArrayList<>();
        titleList.add("id");
        for (String title : CommonConstants.SUBSIDY_BILL_DETAIL_EXPORT_TITLE) {
            if (CommonConstants.SUBSIDY_BILL_DETAIL_EXPORT_REQUIRED_TITLE.contains(title)
                    || paramNameSet.contains(title)) {
                titleList.add(title);
            }
        }


        inspectSubsidyFormulaBill(formulaBillId, BusinessMessageEnum.SUBSIDY_BILL_DETAIL_ERROR);

        Set<String> amSet = getAmSet(sf);

        List<Object[]> billObj = subsidyBillDao.getSubsidyBillByFormulaBillId(formulaBillId);
        List<Object[]> dataList = billObj;

        List<Object[]> resList = new ArrayList<>();
        for (Object[] objs : dataList) {
            Object[] resObj = new Object[titleList.size()];
            // 获取航班id
            String flightId = (String) objs[9];
            // 设置账单id
            String billId = (String) objs[0];
            resObj[0] = billId;
            // 机型异常标识
            boolean regErrorFlag = false;
            // 航班付款金额
            double settleResult = Double.parseDouble((String) objs[7]);
            // 保底收入
            double guaranteedIncome = Double.parseDouble((String) objs[10]);
            // 参数暂存map
            Map<String, String> paramMap = new HashMap<>();
            resObj[4] = objs[4];
            for (int i = 1; i < titleList.size(); i++) {
                if ("日期".equals(titleList.get(i))) {
                    resObj[i] = objs[1];
                } else if ("航班号".equals(titleList.get(i))) {
                    resObj[i] = objs[2];
                } else if ("航线".equals(titleList.get(i))) {
                    resObj[i] = objs[3];
                } else if ("航段".equals(titleList.get(i))) {
                    resObj[i] = objs[4];
                } else if ("机型".equals(titleList.get(i))) {
                    regErrorFlag = isRegErrorFlag(sf, amSet, objs, resObj, regErrorFlag, i);
                } else if ("班次".equals(titleList.get(i))) {
                    resObj[i] = 1;
                } else if ("实际小时价格".equals(titleList.get(i))) {
                    resObj[i] = Double.parseDouble(paramMap.getOrDefault("小时价格", "0"))
                            + Double.parseDouble(paramMap.getOrDefault("燃油联动价", "0"))
                            + Double.parseDouble(paramMap.getOrDefault("人数联动价", "0"));
                } else if ("保底收入".equals(titleList.get(i))) {
                    resObj[i] = guaranteedIncome;
                } else if ("航司盈亏".equals(titleList.get(i))) {
                    resObj[i] = -settleResult;
                } else if ("付款金额".equals(titleList.get(i))) {
                    resObj[i] = settleResult;
                } else if ("备注".equals(titleList.get(i))) {
                    StringBuilder sb = new StringBuilder();
                    // 机型不符
                    if (regErrorFlag) {
                        sb.append("机型不符");
                    }
                    // 轮挡时间跟规则不符
                    if (titleList.contains("轮挡时间")) {
                        SubsidyFlightParamJoin ldsjFlight = subsidyFlightParamJoinDao
                                .getSubsidyFlightParamJoinByFlightIdAndParamName(flightId, "轮挡时间");
                        if (ldsj != null && ldsjFlight != null
                                && !Double.valueOf(ldsjFlight.getParamValue())
                                .equals(Double.valueOf(ldsj.getParamValue()))) {
                            if (sb.length() != 0) {
                                sb.append(",");
                            }
                            sb.append("轮挡时间与规则不符");
                        }
                    }
                    // 免票张数
                    if (titleList.contains("客票收入")) {
                        SubsidyFlightParamJoin mpzsFlight = subsidyFlightParamJoinDao
                                .getSubsidyFlightParamJoinByFlightIdAndParamName(flightId, "免票张数");
                        if (mpzsFlight != null
                                && !Double.valueOf(mpzsFlight.getParamValue()).equals(0.0)) {
                            if (sb.length() != 0) {
                                sb.append(",");
                            }
                            sb.append("免票张数").append(Double.valueOf(mpzsFlight.getParamValue()).intValue());
                        }
                    }
                    // 账单数据是否手动修改
                    if ('1' == ((char) objs[8])) {
                        if (sb.length() != 0) {
                            sb.append(",");
                        }
                        sb.append("账单数据有手动修改");
                    }

                    resObj[i] = sb.toString();
                } else {
                    String paramName = titleList.get(i);
                    // 根据paramName参数信息
                    SubsidyParam sp = subsidyParamDao.getSubsidyParamByName(paramName, airportCode);
                    if ("1".equals(sp.getParamBelong())) {
                        // 获取公式参数设置的值
                        SubsidyFormulaParamJoin sfpj = subsidyFormulaParamJoinDao
                                .getSubsidyFormulaParamJoinByFormulaIdAndParamId(sf.getId(),
                                        sp.getId());
                        resObj[i] = sfpj.getParamValue() == null ? 0.0
                                : Double.parseDouble(sfpj.getParamValue());
                        paramMap.put(sp.getParamName(), resObj[i].toString());
                    } else if ("2".equals(sp.getParamBelong()) || "3".equals(sp.getParamBelong())) {
                        // 获取航班参数的求和值
                        Double value = subsidyFlightParamJoinDao
                                .getValueByFlightIdAndParamId(flightId, sp.getId());
                        resObj[i] = value == null ? 0.0 : value;
                        paramMap.put(sp.getParamName(), resObj[i].toString());
                    } else {
                        // 抛错
                        throw new GenericException(
                                BusinessMessageEnum.SUBSIDY_BILL_DETAIL_ERROR.getCode(),
                                BusinessMessageEnum.SUBSIDY_BILL_DETAIL_ERROR.getMsg());
                    }

                }
            }
            resList.add(resObj);
        }

        Object[] res = new Object[2];
        res[0] = titleList;
        res[1] = resList;

        return res;
    }


    // 判断正整数（int）
    private boolean isPositiveInteger(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[+]?[1-9]\\d*$");
        return pattern.matcher(str).matches();
    }

    // 判断整数（int）
    private boolean isInteger(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-+]?\\d*$");
        return pattern.matcher(str).matches();
    }


    // 判断浮点数（double和float）
    private boolean isDouble(String str) {
        if (null == str || str.isEmpty()) {
            return true;
        }
        if (isInteger(str)) {
            return false;
        }
        // Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
        Pattern pattern = Pattern.compile("^[-+]?\\d*[.]\\d+$"); // 之前这里正则表达式错误，现更正
        return !pattern.matcher(str).matches();
    }

}
