package com.swcares.aiot.service;

import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.param.PageParam;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;

public interface LogService {
    /*
     * Title: saveLogForUpdate<br>
     * Author: 李龙<br>
     * Description: (修改操作日志记录)<br>
     * Date:  17:13 <br>
     * @param object1
     * @param object2
     * @param user
     * @param airportCode
     * return: void
     */
    void addLogForUpdate(Object object1, Object object2, LoginUserDetails user, String content, String airportCode);
    /*
     * Title: addLogForSave<br>
     * Author: 李龙<br>
     * Description: (新增操作日志记录)<br>
     * Date:  9:30 <br>
     * @param object
     * @param user
     * @param airportCode
     * return: void
     */
    void addLogForSave(Object object, LoginUserDetails user, String content, String airportCode);
    /*
     * Title: addLogForDelete<br>
     * Author: 李龙<br>
     * Description: (删除操作日志记录)<br>
     * Date:  10:29 <br>
     * @param object
     * @param User
     * @param content
     * @param airportCode
     * return: void
     */
    void addLogForDelete(Object object, LoginUserDetails user, String content, String airportCode);
    /*
     * Title: addLogForExport<br>
     * Author: 李龙<br>
     * Description: (上传操作日志)<br>
     * Date:  9:29 <br>
     * @param User
     * @param content
     * @param airportCode
     * return: void
     */
    void addLogForImport(LoginUserDetails user, String content, String airportCode);
    /*
     * Title: addLogForExport<br>
     * Author: 李龙<br>
     * Description: (下载操作日志)<br>
     * Date:  16:00 <br>
     * @param user
     * @param object
     * @param content
     * @param airportCode
     * return: void
     */
    void addLogForExport(LoginUserDetails user, Object object, String content, String airportCode, String formName);

    /*
     * Title: pageLogByCondition<br>
     * Author: 李龙<br>
     * Description: (根据条件分页查询日志信息)<br>
     * Date:  15:04 <br>
     * @param airportCode
     * @param operatorId
     * @param startTime
     * @param endTime
     * @param pageParam
     * return: ResultBuilder
     */
    ResultBuilder pageLogByCondition(String airportCode, String operatorId, Date startTime, Date endTime, PageParam pageParam);
    /*
     * Title: exportLog<br>
     * Author: 李龙<br>
     * Description: (根据条件导出日志数据)<br>
     * Date:  16:55 <br>
     * @param airportCode
     * @param operatorId
     * @param startTime
     * @param endTime
     * @param response
     * return: void
     */
    void exportLog(String airportCode, String operatorId, Date startTime, Date endTime, HttpServletResponse response);

}
