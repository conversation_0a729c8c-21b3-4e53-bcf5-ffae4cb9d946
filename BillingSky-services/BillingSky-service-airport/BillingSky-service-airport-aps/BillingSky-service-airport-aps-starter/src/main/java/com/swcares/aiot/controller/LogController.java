package com.swcares.aiot.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.core.model.entity.Log;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.LogService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * ClassName：LogController <br>
 * Description：日志操作控制层<br>
 * Copyright © 2020-10-12 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-12-18 14:45<br>
 * @version v1.0 <br>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/log")
@Api(value = "log", tags = {"日志操作接口"})
public class LogController {
    @Resource
    private LogService logService;

    @ApiOperation(value = "根据操作人账号、操作开始日期、操作结束日期、机场三字码查询日志信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = Log.class)})
    @GetMapping(value = "/pageLogByCondition")
    public Object pageLogByCondition(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                                     @RequestParam @ApiParam(name = "operatorId", value = "操作人ID") String operatorId,
                                     @RequestParam @ApiParam(name = "startTime", value = "操作开始日期") String startTime,
                                     @RequestParam @ApiParam(name = "endTime", value = "操作结束日期") String endTime,
                                     @ApiParam(name = "pageParam", value = "分页信息") @Validated PageParam pageParam) {
        if (CharSequenceUtil.isBlank(operatorId)) {
            operatorId = null;
        }
        return logService.pageLogByCondition(airportCode,
                operatorId,
                FormatUtils.parseStringToDateTime(startTime),
                FormatUtils.parseStringToDateTime(endTime),
                pageParam);
    }

    @ApiOperation(value = "根据操作人账号、操作开始日期、操作结束日期、机场三字码导出日志信息")
    @GetMapping(value = "/exportLog")
    public void exportLog(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                          @RequestParam @ApiParam(name = "operatorId", value = "操作人ID") String operatorId,
                          @RequestParam @ApiParam(name = "startTime", value = "操作开始日期") String startTime,
                          @RequestParam @ApiParam(name = "endTime", value = "操作结束日期") String endTime,
                          HttpServletResponse response) {

        if (CharSequenceUtil.isBlank(operatorId)) {
            operatorId = null;
        }
        logService.exportLog(airportCode,
                operatorId,
                FormatUtils.parseStringToDateTime(startTime),
                FormatUtils.parseStringToDateTime(endTime),
                response);
    }

}
