package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.core.common.enums.FlightDateTypeEnum;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.entity.AirlineBillNew;
import com.swcares.aiot.core.model.vo.AirlineBillMissVo;
import com.swcares.aiot.mapper.AirlineBillMapper;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.CmpCalcSaveAirlineBills
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/27 14:04
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcSaveAirlineBills", name = "组件-对账通机场端-计算模块-生成保存航司账单")
public class CmpCalcSaveAirlineBills extends NodeComponent {

    @Resource
    private AirlineBillMapper airlineBillMapper;

    @Override
    public void process()  {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        ReCalcDto dto = ctxCalc.getDto();
        String airportCode = dto.getAirportCode();
        if(StringUtils.isBlank(airportCode)){
            log.error("机场三字码为空，生成航司账单失败");
            return;
        }
        insertAirlineBill(airlineBillMapper.newCalclistMissAirlineBillByDate(airportCode), FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue(), airportCode);
        insertAirlineBill(airlineBillMapper.newCalclistMissAirlineBillByTime(airportCode), FlightDateTypeEnum.TAKE_OFF_DATE_TYPE.getValue(), airportCode);
    }

    private void insertAirlineBill(List<AirlineBillMissVo> airlineBillMissVoList, Integer dateType, String airportCode) {
        if (CollUtil.isEmpty(airlineBillMissVoList)) {
            log.info("dateType为:{} 的航司账单没有缺失，无需新增！", dateType);
            return;
        }
        for (AirlineBillMissVo airlineBillMissVo : airlineBillMissVoList) {
            AirlineBillNew airlineBillNew = new AirlineBillNew();
            airlineBillNew.setSettleCode(airlineBillMissVo.getSettleCode());
            airlineBillNew.setSettleMonth(airlineBillMissVo.getFlightDate());
            airlineBillNew.setDateType(dateType);
            airlineBillNew.setAirportCode(airportCode);
            airlineBillMapper.insert(airlineBillNew);
        }
    }
}
