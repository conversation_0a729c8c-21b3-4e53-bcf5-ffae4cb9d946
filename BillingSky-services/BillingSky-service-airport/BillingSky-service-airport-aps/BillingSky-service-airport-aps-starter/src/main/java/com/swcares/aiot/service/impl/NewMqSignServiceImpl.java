package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.client.remote.IIndicatorBizRemoteClient;
import com.swcares.aiot.core.common.enums.InvalidEnum;
import com.swcares.aiot.core.common.enums.VariableStatusEnum;
import com.swcares.aiot.core.entity.BillBusDataItem;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TServiceRecord;
import com.swcares.aiot.core.entity.TServiceRecordConfirm;
import com.swcares.aiot.core.enums.EnumIndicatorDataFormat;
import com.swcares.aiot.core.mapstruct.MsServiceRecord;
import com.swcares.aiot.core.model.vo.VariableGuaranteeVo;
import com.swcares.aiot.core.service.ITFlightInfoService;
import com.swcares.aiot.core.service.ITServiceRecordConfirmService;
import com.swcares.aiot.core.service.ITServiceRecordService;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;
import com.swcares.aiot.mapper.VariableGuaranteeMapper;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.aiot.service.NewMqSignService;
import com.swcares.baseframe.common.base.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.service.impl.NewMqSignServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/28 14:21
 * @version v1.0
 */
@Service
@Slf4j
public class NewMqSignServiceImpl implements NewMqSignService {

    public static final String SIGN_IMPORT_NEW = "电子签单自动推送-new";

    @Resource
    private ITServiceRecordService serviceRecordService;
    @Resource
    private ITServiceRecordConfirmService confirmService;
    @Resource
    private ITFlightInfoService flightInfoService;
    @Resource
    private IIndicatorBizRemoteClient iIndicatorBizRemoteClient;
    @Resource
    private VariableGuaranteeMapper variableGuaranteeMapper;
    @Resource
    private MsServiceRecord msServiceRecord;


    @Override
    public void saveServiceRecord(List<SignBusDataSettlementVO> vo) {
        saveServiceRecordByCondition(vo, null, null);
    }

    @Override
    public void saveServiceRecordByCondition(List<SignBusDataSettlementVO> voList, String syncflightNos, Long syncItemId) {
        if (voList == null || voList.isEmpty()) {
            log.error("收到电子签单数据为空");
            return;
        }
        voList.forEach(vo -> {
            //获取签单下的所有航班
            List<FlightInfoMb> flightInfoMbList = getFlightInfoMbList(vo);
            if (flightInfoMbList.isEmpty()) {
                log.error("签单数据未获取航班信息！");
                return;
            }
            //获取计算引擎所有指标项map
            Map<String, IndAllIndicatorRetrVo> indicatorRetrVoMap = getIndictorDict();
            //获取签单下签单项数据
            List<BillBusDataItem> billBusDataItemList = vo.getList();
            if (billBusDataItemList == null || billBusDataItemList.isEmpty()) {
                log.error("收到电子签单 签单项数据为空");
                return;
            }
            billBusDataItemList.forEach(billBusDataItem -> {
                //判断手动同步指定指标项数据
                if (syncItemId != null && !syncItemId.equals(billBusDataItem.getItemId())) {
                    log.info("手动同步签单签单消息，业务保障项id为{}，同步业务保障项id为空，或与同步对象签单项id不一致,无需同步：{}", syncItemId, billBusDataItem);
                    return;
                }
                //解析签单项数据
                parseItemData(syncflightNos, vo, billBusDataItem, indicatorRetrVoMap, flightInfoMbList);
            });
        });
    }

    private void parseItemData(String syncflightNos, SignBusDataSettlementVO vo, BillBusDataItem billBusDataItem, Map<String, IndAllIndicatorRetrVo> indicatorRetrVoMap, List<FlightInfoMb> flightInfoMbList) {
        //电子签单 签单项id
        Long itemId = billBusDataItem.getItemId();
        //结算字段匹配信息
        VariableGuaranteeVo variableGuaranteeVo = variableGuaranteeMapper.selectMatchingRelationship(vo.getAirportCode(), null, String.valueOf(itemId));
        if (variableGuaranteeVo == null || StringUtils.isBlank(variableGuaranteeVo.getVariableId())) {
            log.error("{}签单项未建立字典匹配！", itemId);
            return;
        }
        //签单项对应指标项数据
        IndAllIndicatorRetrVo indAllIndicatorRetrVo = indicatorRetrVoMap.get(variableGuaranteeVo.getVariableId());
        if (indAllIndicatorRetrVo == null) {
            log.error("{}签单项未建立字典匹配！", itemId);
            return;
        }
        //匹配规则起降归集
        String landFlag = variableGuaranteeVo.getLandFlag();
        //根据起降归集获取签单项下需生成数据的航班
        List<FlightInfoMb> itemFlightInfoMbList = getItemFlightInfoMbs(flightInfoMbList, landFlag);
        //遍历航班，生成航班下的业务保障数据
        itemFlightInfoMbList.forEach(flightInfoMb -> {
            //判断手工同步时指定的航班数据
            if (CharSequenceUtil.isNotEmpty(syncflightNos) && !syncflightNos.contains(flightInfoMb.getFlightNo())) {
                return;
            }
            // 组装servicerecord
            TServiceRecord serviceRecord = getTServiceRecord(vo, billBusDataItem, flightInfoMb, vo.getAirportCode(), indAllIndicatorRetrVo, variableGuaranteeVo);
            if ((serviceRecord.getUsedNumber() == null || serviceRecord.getUsedNumber().compareTo(BigDecimal.ZERO) == 0)
                    && StringUtils.isBlank(serviceRecord.getSelectorOption())) {
                log.info("FlightInfo = {} ，billBusDataItem={}, 同步数据为null或为0，不进行保存", flightInfoMb, billBusDataItem);
                return;
            }
            //保存业务保障数据/备份数据
            saveServiceRecord(flightInfoMb, indAllIndicatorRetrVo, serviceRecord, billBusDataItem);
        });
    }

    private void saveServiceRecord(FlightInfoMb flightInfoMb, IndAllIndicatorRetrVo indAllIndicatorRetrVo, TServiceRecord serviceRecord
            , BillBusDataItem billBusDataItem) {
        //判断是否确认业务保障数据，已确认则同步到备份表
        if (VariableStatusEnum.CONFIRMED.getValue().equals(flightInfoMb.getVariableStatus())
                || VariableStatusEnum.MODIFIED.getValue().equals(flightInfoMb.getVariableStatus())) {
            //删除备份表
            confirmService.lambdaUpdate().eq(TServiceRecordConfirm::getFlightId, flightInfoMb.getId())
                    .eq(TServiceRecordConfirm::getServiceCode, indAllIndicatorRetrVo.getCode())
                    .eq(TServiceRecordConfirm::getInvalid, InvalidEnum.NORMAL.getValue())
                    .set(TServiceRecordConfirm::getInvalid, InvalidEnum.DELETED.getValue()).update();
            //将业务数据表数据更新/删除标志位改为是
            serviceRecordService.lambdaUpdate().eq(TServiceRecord::getFlightId, flightInfoMb.getId())
                    .eq(TServiceRecord::getServiceCode, indAllIndicatorRetrVo.getCode())
                    .eq(TServiceRecord::getInvalid, InvalidEnum.NORMAL.getValue())
                    .set(TServiceRecord::getSignDeleteOrUpdate, "1").update();
            //判断是否需要生成多条廊桥数据，并组装成List
            List<TServiceRecord> serviceRecordList = generateBirdgeServiceRecord(billBusDataItem, serviceRecord);
            //将业务保障数据转成备份数据
            List<TServiceRecordConfirm> tServiceRecordConfirmList = msServiceRecord.srToConfirm(serviceRecordList);
            confirmService.saveBatch(tServiceRecordConfirmList);
        } else {
            //删除业务数据表
            serviceRecordService.lambdaUpdate().eq(TServiceRecord::getFlightId, flightInfoMb.getId())
                    .eq(TServiceRecord::getServiceCode, indAllIndicatorRetrVo.getCode())
                    .eq(TServiceRecord::getInvalid, InvalidEnum.NORMAL.getValue())
                    .set(TServiceRecord::getInvalid, InvalidEnum.DELETED.getValue()).update();
            //判断是否需要生成多条廊桥数据，并组装成List
            List<TServiceRecord> serviceRecordList = generateBirdgeServiceRecord(billBusDataItem, serviceRecord);
            //保存业务保障数据
            serviceRecordService.saveBatch(serviceRecordList);

        }
    }

    private List<TServiceRecord> generateBirdgeServiceRecord(BillBusDataItem billBusDataItem, TServiceRecord serviceRecord) {
        List<TServiceRecord> saveServiceRecordList = new ArrayList<>();
        if (billBusDataItem.getCorridorBridgeType() != null) {
            if ("double_bridge".equals(billBusDataItem.getCorridorBridgeType())) {
                TServiceRecord newSr = msServiceRecord.copyServiceRecord(serviceRecord);
                newSr.setBillBusDataItemId(newSr.getBillBusDataItemId() + "_1");
                saveServiceRecordList.add(newSr);
            } else if ("three_bridge".equals(billBusDataItem.getCorridorBridgeType())) {
                TServiceRecord newSr = msServiceRecord.copyServiceRecord(serviceRecord);
                newSr.setBillBusDataItemId(newSr.getBillBusDataItemId() + "_1");
                TServiceRecord newSr2 = msServiceRecord.copyServiceRecord(serviceRecord);
                newSr2.setBillBusDataItemId(newSr2.getBillBusDataItemId() + "_2");
                saveServiceRecordList.add(newSr);
                saveServiceRecordList.add(newSr2);
            }
        }
        return saveServiceRecordList;
    }

    private TServiceRecord getTServiceRecord(SignBusDataSettlementVO vo, BillBusDataItem billBusDataItem, FlightInfoMb flightInfoMb, String airportCode, IndAllIndicatorRetrVo indAllIndicatorRetrVo, VariableGuaranteeVo variableGuaranteeVo) {
        TServiceRecord serviceRecord = new TServiceRecord();
        serviceRecord.setAirportCode(airportCode);
        serviceRecord.setServiceName(indAllIndicatorRetrVo.getName());
        serviceRecord.setServiceCode(indAllIndicatorRetrVo.getCode());
        serviceRecord.setInvalid(InvalidEnum.NORMAL.getValue());
        serviceRecord.setFlightId(flightInfoMb.getId());
        if (isInStayTime(billBusDataItem, flightInfoMb)) {
            serviceRecord.setInScope("0");
        }
        //设置时间、数值、选择器
        setTimeAndNumAndSelect(billBusDataItem, variableGuaranteeVo, indAllIndicatorRetrVo, serviceRecord);
        serviceRecord.setCreateBy(SIGN_IMPORT_NEW);
        serviceRecord.setCreateTime(LocalDateTime.now());
        serviceRecord.setModifiedBy(SIGN_IMPORT_NEW);
        serviceRecord.setModifiedTime(LocalDateTime.now());
        serviceRecord.setSignPdfUrl(vo.getSignPdfUrl());
        serviceRecord.setBillBusDataId(vo.getId() == null ? null : vo.getId().toString());
        serviceRecord.setBillBusDataItemId(billBusDataItem.getId().toString());
        return serviceRecord;
    }

    private static void setTimeAndNumAndSelect(BillBusDataItem billBusDataItem, VariableGuaranteeVo variableGuaranteeVo, IndAllIndicatorRetrVo indAllIndicatorRetrVo, TServiceRecord serviceRecord) {
        switch (variableGuaranteeVo.getConversionRules()) {
            case "0":
                //0为无规则
                setRule0(billBusDataItem, indAllIndicatorRetrVo, serviceRecord);
                break;
            case "1":
                //1为“有”统计计算为1次，“无”统计计算为0次
                setRule1(billBusDataItem, serviceRecord);
                break;
            case "2":
                //2为60分钟内计为1次，每30分钟数值加0.5，每超过不足30分钟数值加0.5
                setRule2(billBusDataItem, serviceRecord);
                break;
            default:
                //3为统计计算为1次"
                serviceRecord.setUsedNumber(BigDecimal.ONE);
        }
    }

    private static void setRule0(BillBusDataItem billBusDataItem, IndAllIndicatorRetrVo indAllIndicatorRetrVo, TServiceRecord serviceRecord) {
        //时间范围类型
        if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_RANGE.getCode().equals(indAllIndicatorRetrVo.getDataFmt())) {
            LocalDateTime startTime = billBusDataItem.getStartTime();
            LocalDateTime endTime = billBusDataItem.getEndTime();
            if (startTime == null || endTime == null || startTime.isAfter(endTime)) {
                return;
            }
            serviceRecord.setStartTime(startTime);
            serviceRecord.setEndTime(endTime);
            serviceRecord.setUsedNumber(BigDecimal.valueOf(calculateHourDifference(startTime, endTime)));
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_NUMBER.getCode().equals(indAllIndicatorRetrVo.getDataFmt())) {
            serviceRecord.setUsedNumber(BigDecimal.valueOf(billBusDataItem.getTimes()));
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_SELECTOR.getCode().equals(indAllIndicatorRetrVo.getDataFmt())) {
            serviceRecord.setSelectorOption(String.valueOf(billBusDataItem.getTimes()));
            //选择器字典，暂未推送
            serviceRecord.setSelectorDict(billBusDataItem.getFlightSelectorItem());
        }
    }

    private static void setRule1(BillBusDataItem billBusDataItem, TServiceRecord serviceRecord) {
        Double userNumber = Double.valueOf(billBusDataItem.getHaveOrNot());
        if (userNumber.equals(0.0)) {
            return;
        }
        serviceRecord.setUsedNumber(BigDecimal.valueOf(userNumber));
    }

    private static void setRule2(BillBusDataItem billBusDataItem, TServiceRecord serviceRecord) {
        if (billBusDataItem.getStartTime() == null || billBusDataItem.getEndTime() == null
                || billBusDataItem.getStartTime().isAfter(billBusDataItem.getEndTime())) {
            return;
        }
        Duration duration = Duration.between(billBusDataItem.getStartTime(), billBusDataItem.getEndTime());
        long minutes = duration.toMinutes();
        long usdTime = (minutes / 30);
        if (minutes > usdTime * 30) {
            usdTime++;
        }
        serviceRecord.setUsedNumber(BigDecimal.valueOf((double) usdTime / 2));
    }


    private boolean isInStayTime(BillBusDataItem bi, FlightInfoMb fi) {
        if (fi == null || bi.getStartTime() == null || bi.getEndTime() == null) {
            return false;
        }
        LocalDateTime startTime = bi.getStartTime();
        LocalDateTime endTime = bi.getEndTime();

        if (fi.getStayStartTime() != null && fi.getStayEndTime() != null) {
            return startTime.isBefore(fi.getStayStartTime()) || endTime.isAfter(fi.getStayEndTime());
        } else if ("A".equals(fi.getFlightFlag())) {
            //降落航班，签单开始时间大于停场开始时间
            return fi.getStayStartTime() != null && startTime.isAfter(fi.getStayStartTime());
        } else if ("D".equals(fi.getFlightFlag())) {
            //起飞航班，签单结束时间小于停场结束时间
            return fi.getStayEndTime() != null && endTime.isBefore(fi.getStayEndTime());
        }
        return false;
    }


    private static List<FlightInfoMb> getItemFlightInfoMbs(List<FlightInfoMb> flightInfoMbList, String landFlag) {
        List<FlightInfoMb> itemFlightInfoMbList = new ArrayList<>();
        if (flightInfoMbList.size() > 1) {
            if ("B".equals(landFlag)) {
                itemFlightInfoMbList.addAll(flightInfoMbList);
            } else {
                for (FlightInfoMb flightInfoMb : flightInfoMbList) {
                    if (landFlag.equals(flightInfoMb.getFlightFlag())) {
                        itemFlightInfoMbList.add(flightInfoMb);
                    }
                }
            }
        } else {
            itemFlightInfoMbList.addAll(flightInfoMbList);
        }
        return itemFlightInfoMbList;
    }

    private List<FlightInfoMb> getFlightInfoMbList(SignBusDataSettlementVO vo) {
        List<FlightInfoMb> flightInfoMbList = new ArrayList<>();
        FlightInfoMb arriveFlight = getFlightInfoByFlightId(vo.getArriveFlightId());
        if (arriveFlight != null) {
            flightInfoMbList.add(arriveFlight);
        }
        FlightInfoMb takeoffFlight = getFlightInfoByFlightId(vo.getTakeOffFlightId());
        if (takeoffFlight != null) {
            flightInfoMbList.add(takeoffFlight);
        }
        return flightInfoMbList;
    }


    private FlightInfoMb getFlightInfoByFlightId(Long flightId) {
        if (flightId != null) {
            return flightInfoService.lambdaQuery().eq(FlightInfoMb::getId, flightId)
                    .eq(FlightInfoMb::getInvalid, InvalidEnum.NORMAL.getValue()).one();
        }
        return null;
    }

    private Map<String, IndAllIndicatorRetrVo> getIndictorDict() {
        Map<String, IndAllIndicatorRetrVo> indicatorDictMap = new HashMap<>();
        BaseResult<List<IndAllIndicatorRetrVo>> indicatorDicRes = iIndicatorBizRemoteClient.list();
        if (indicatorDicRes != null && indicatorDicRes.getCode() == 200 && CollUtil.isNotEmpty(indicatorDicRes.getData())) {
            List<IndAllIndicatorRetrVo> indicatorDicList = indicatorDicRes.getData();
            for (IndAllIndicatorRetrVo indAllIndicatorRetrVo : indicatorDicList) {
                if (Integer.valueOf(1).equals(indAllIndicatorRetrVo.getSort())) {
                    indicatorDictMap.put(indAllIndicatorRetrVo.getId(), indAllIndicatorRetrVo);
                }
            }
        }
        return indicatorDictMap;
    }

    public static double calculateHourDifference(LocalDateTime start, LocalDateTime end) {
        // 计算时间差
        Duration duration = Duration.between(start, end);
        // 向上取整到两位小数
        BigDecimal bd = BigDecimal.valueOf(duration.getSeconds()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
}
