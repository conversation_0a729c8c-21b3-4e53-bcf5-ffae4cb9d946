package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.controller.BaseController;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.form.DeleteForm;
import com.swcares.aiot.core.form.NoticePageForm;
import com.swcares.aiot.core.model.entity.CargoFreeAirline;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.ReCalcService;
import com.swcares.aiot.service.SettlementStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * ClassName：com.swcares.modules.settlement.controller.ResettlementController
 * Description：
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2021/10/13 9:41
 * @version v1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/reSettlement")
@Api(value = "ResettlementController", tags = {"重新结算操作接口"})
public class ResettlementController extends BaseController {

    @Resource
    private ReCalcService reCalcService;
    @Resource
    private SettlementStatusService settlementStatusService;

    /**
     * Title: isBilling<br>
     * Author: 刘志恒<br>
     * Description: 是否正在结算<br>
     * Date:  2021/10/13 9:35 <br>
     *
     * @Param: null
     */
    @GetMapping("/isBilling")
    @ApiOperation(value = "是否正在结算")
    ResultBuilder isBilling(@ApiParam(name = "airportCode", value = "机场三字码", required = true)
                            @NotBlank(message = "airportCode不能为空") @RequestParam String airportCode) {
        return new ResultBuilder.Builder().data(reCalcService.isBilling(airportCode)).builder();
    }

    /**
     * Title: getSettlementStatusList<br>
     * Author: 刘志恒<br>
     * Description: 获取最近十条结算操作状态列表<br>
     * Date:  2021/10/13 9:35 <br>
     */
    @GetMapping("/settlementStatusList")
    @ApiOperation(value = "获取最近十条结算操作状态列表")
    ResultBuilder getSettlementStatusList(@ApiParam(name = "airportCode", value = "机场三字码", required = true)
                                          @NotBlank(message = "airportCode不能为空") @RequestParam String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return new ResultBuilder.Builder().data(settlementStatusService.getSettlementStatusList(airportCode, user)).builder();
    }

    /**
     * Title: getSettlementStatusList<br>
     * Author: 刘志恒<br>
     * Description: 分页查询结算操作状态列表<br>
     * Date:  2021/10/13 9:35 <br>
     */
    @GetMapping("/pageSettlementStatusList")
    @ApiOperation(value = "分页查询结算操作状态列表")
    ResultBuilder pageSettlementStatusList(@Validated NoticePageForm form,
                                           @Validated PageParam pageParam) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return new ResultBuilder.Builder().data(settlementStatusService.pageSettlementStatusList(form, user, pageParam)).builder();
    }

    /**
     * Title: removeSettlementStatusByIds<br>
     * Author: 刘志恒<br>
     * Description: 批量删除结算记录<br>
     * Date:  2022/11/03 16:35 <br>
     */
    @DeleteMapping("/removeSettlementStatusByIds")
    @ApiOperation(value = "批量删除结算记录")
    public ResultBuilder<Boolean> removeSettlementStatusByIds(@RequestBody @Validated DeleteForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return new ResultBuilder.Builder<Boolean>().data(settlementStatusService.removeSettlementStatusById(form.getIds(), user)).builder();
    }

    @GetMapping("/getUnreadSettlementStatus")
    @ApiOperation(value = "获取结算记录未读数")
    @ApiParam(name = "airportCode", value = "机场三字码", required = true)
    public ResultBuilder<Integer> getUnreadSettlementStatus(
            @NotBlank(message = "airportCode不能为空") @RequestParam String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return new ResultBuilder.Builder<Integer>().data(settlementStatusService.getUnreadSettlementStatus(airportCode, user)).builder();
    }


    @PostMapping("/addCargoNotFreeAirline")
    @ApiOperation(value = "添加货邮为零则免除货邮费航司")
    public ResultBuilder<Boolean> addCargoNotFreeAirline(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true)
                                                         @NotBlank(message = "airportCode不能为空") String airportCode,
                                                         @RequestParam @ApiParam(name = "airlineCodes", value = "航司二字码", required = true)
                                                         String airlineCodes) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return new ResultBuilder.Builder<Boolean>().data(reCalcService.addCargoNotFreeAirline(airportCode, airlineCodes, user)).builder();
    }

    @GetMapping("/getCargoNotFreeAirline")
    @ApiOperation(value = "获取货邮为零则免除货邮费航司")
    public ResultBuilder<List<CargoFreeAirline>> getCargoNotFreeAirline(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true)
                                                                        @NotBlank(message = "airportCode不能为空") String airportCode) {
        return new ResultBuilder.Builder<List<CargoFreeAirline>>().data(reCalcService.getCargoNotFreeAirline(airportCode)).builder();
    }

    @PostMapping("/deleteCargoNotFreeAirline")
    @ApiOperation(value = "删除货邮为零则免除货邮费航司")
    public ResultBuilder<Boolean> deleteCargoNotFreeAirline(@RequestParam @ApiParam(name = "ids", value = "免除货邮费航司数据id", required = true)
                                                   @NotBlank(message = "ids") String ids) {
        return new ResultBuilder.Builder<Boolean>().data(reCalcService.deleteCargoNotFreeAirline(ids)).builder();
    }
}
