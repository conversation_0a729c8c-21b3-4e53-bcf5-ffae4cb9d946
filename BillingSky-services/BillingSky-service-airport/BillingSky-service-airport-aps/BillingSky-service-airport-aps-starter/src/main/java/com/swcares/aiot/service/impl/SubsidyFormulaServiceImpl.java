package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.ObjectUtil;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.ParamForm;
import com.swcares.aiot.core.form.SubsidyFormulaSaveForm;
import com.swcares.aiot.core.form.SubsidyFormulaUpdateForm;
import com.swcares.aiot.core.form.SubsidyParamSaveForm;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.model.vo.SubsidyFormulaParamJoinVo;
import com.swcares.aiot.core.model.vo.SubsidyFormulaVo;
import com.swcares.aiot.dao.*;
import com.swcares.aiot.service.SubsidyFormulaService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

/**
 * ClassName：com.swcares.service.impl.SubsidyFormulaServiceImpl
 * Description：补贴公式Service实现类
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/2 10:16
 * @version v1.0
 */
@Service
public class SubsidyFormulaServiceImpl implements SubsidyFormulaService {
    private static final String RIGHT = "right";
    private static final String NUMBER = "number";
    @Resource
    private SubsidyFormulaDao subsidyFormulaDao;
    @Resource
    private SubsidyFormulaParamJoinDao subsidyFormulaParamJoinDao;
    @Resource
    private SubsidyFlightParamJoinDao subsidyFlightParamJoinDao;
    @Resource
    private SubsidyParamDao subsidyParamDao;
    @Resource
    private AirportThreeCharCodeDao airportThreeCharCodeDao;
    @Resource
    private FlightLineInfoDao flightLineInfoDao;
    @Resource
    private FlightInfoDao flightInfoDao;

    @Override
    public List<SubsidyFormulaVo> getFormulas(String flightLineId, String airportCode) {
        List<SubsidyFormula> list = subsidyFormulaDao.getSubsidyFormulaByFlightLineIdFlightId(flightLineId, airportCode);
        List<SubsidyFormulaVo> resList = new ArrayList<>();
        for (SubsidyFormula sf : list) {
            // 根据公式id获取关联参数信息(非航班参数)
            List<SubsidyFormulaParamJoin> sjList = subsidyFormulaParamJoinDao.getSubsidyFormulaParamJoinByFormulaId(sf.getId());
            Map<String, SubsidyFormulaParamJoin> sjMap = new HashMap<>();
            for (SubsidyFormulaParamJoin sj : sjList) {
                sjMap.put(sj.getParamId(), sj);
            }
            // 解析公式中的参数信息
            // 获取公式
            StringBuilder newFormula = getFormulaStrBuilder(sf, sjMap);

            SubsidyFormulaVo sfVo = new SubsidyFormulaVo();
            sfVo.setId(sf.getId());
            sfVo.setFormula(newFormula.toString());
            sfVo.setStartDate(sf.getStartDate());
            sfVo.setEndDate(sf.getEndDate());
            sfVo.setApplicableModels(sf.getApplicableModels());
            // 公式类型的参数需要处理，子参数名称需要加上父参数名称

            List<SubsidyFormulaParamJoinVo> resParamList = new ArrayList<>();
            for (SubsidyFormulaParamJoin sj : sjList) {
                if ("3".equals(sj.getParamType())) {
                    continue;
                }

                SubsidyFormulaParamJoinVo sjVo = new SubsidyFormulaParamJoinVo();
                BeanUtils.copyProperties(sj, sjVo);
                if ("2".equals(sj.getParamType())) {
                    setSjVo(sj, sjVo, sjMap);
                }
                resParamList.add(sjVo);

            }

            sfVo.setSjList(resParamList);
            resList.add(sfVo);
        }
        return resList;
    }

    private void setSjVo(SubsidyFormulaParamJoin sj, SubsidyFormulaParamJoinVo sjVo, Map<String, SubsidyFormulaParamJoin> sjMap) {
        List<SubsidyParam> tempSpList = subsidyParamDao.getSubsidyParamById(sj.getParamId());
        SubsidyParam tempSp = tempSpList.get(0);
        String paramFormula = tempSp.getParamFormula();
        sjVo.setParamFormula(paramFormula);

        StringBuilder childrenParam = new StringBuilder();
        boolean parseChildrenParam = false;
        String newParamFormula = paramFormula;
        for (char c : paramFormula.toCharArray()) {
            if (parseChildrenParam) {
                if (c == ']') {
                    parseChildrenParam = false;
                    String paramId = childrenParam.toString();
                    if (sjMap.containsKey(paramId)) {
                        // getSubsidyFormulaParamJoinVos
                        List<SubsidyFormulaParamJoinVo> childrenList = getSubsidyFormulaParamJoinVos(sjVo, sjMap, paramId);
                        sjVo.setChildrenList(childrenList);
                    } else {
                        // 如果为航班参数则替换为中文
                        List<SubsidyParam> flightSpList = subsidyParamDao.getSubsidyParamById(paramId);
                        SubsidyParam flightSp = flightSpList.get(0);
                        newParamFormula = newParamFormula.replaceAll("\\[" + paramId + "]", flightSp.getParamName());
                    }
                    childrenParam.setLength(0);
                } else {
                    childrenParam.append(c);
                }
            } else {
                parseChildrenParam = isParseChildrenParam(c, parseChildrenParam);
            }
        }
        sjVo.setParamFormula(newParamFormula);
    }

    private static boolean isParseChildrenParam(char c, boolean parseChildrenParam) {
        if (c == '[') {
            parseChildrenParam = true;
        }
        return parseChildrenParam;
    }

    private static @NotNull List<SubsidyFormulaParamJoinVo> getSubsidyFormulaParamJoinVos(SubsidyFormulaParamJoinVo sjVo, Map<String, SubsidyFormulaParamJoin> sjMap, String paramId) {
        SubsidyFormulaParamJoin childrenSj = sjMap.get(paramId);
        SubsidyFormulaParamJoinVo childrenSjVo = new SubsidyFormulaParamJoinVo();
        BeanUtils.copyProperties(childrenSj, childrenSjVo);
        List<SubsidyFormulaParamJoinVo> childrenList = sjVo.getChildrenList();
        if (childrenList == null) {
            childrenList = new ArrayList<>();
        }
        childrenList.add(childrenSjVo);
        return childrenList;
    }

    private static @NotNull StringBuilder getFormulaStrBuilder(SubsidyFormula sf, Map<String, SubsidyFormulaParamJoin> sjMap) {
        String formula = sf.getFormula();
        StringBuilder newFormula = new StringBuilder();
        StringBuilder param = new StringBuilder();
        boolean parseParam = false;
        for (char c : formula.toCharArray()) {
            if (parseParam) {
                if (c == ']') {
                    parseParam = false;
                    String paramId = param.toString();
                    newFormula.append(sjMap.get(paramId).getParamName());
                    param.setLength(0);
                } else {
                    param.append(c);
                }
            } else {
                if (c == '[') {
                    parseParam = true;
                } else {
                    newFormula.append(c);
                }
            }
        }
        return newFormula;
    }

    /**
     * Title: saveFormula<br>
     * Author: 刘志恒<br>
     * Description: 保存公式（传入的公式，公式参数用[]包起来，航班参数用｛｝）<br>
     * Date:  2022/8/5 14:30 <br>
     *
     * @param form : null
     */
    @Transactional
    @Override
    public void saveFormula(SubsidyFormulaSaveForm form, LoginUserDetails user) {
        String flightLineId = form.getFlightLineId();
        String airportCode = form.getAirportCode();
        String formula = form.getFormula();
        String applicableModels = form.getApplicableModels();
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();

        // 判断该航司下是否已存在日期重复的公式
        if (!subsidyFormulaDao.getSubsidyFormulaByFlightLineIdFlightIdStartEndDate(startDate,
                endDate, flightLineId, "", airportCode).isEmpty()) {
            // 抛出异常日期重复
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FORMULA_SAVE_REPEAT.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_SAVE_REPEAT.getMsg());
        }
        // 检测公式格式是否合格
        if (checkFormula(formula)) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FORMULA_CHECK_FORMULA_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_CHECK_FORMULA_ERROR.getMsg());
        }

        SubsidyFormula sf = new SubsidyFormula();
        sf.setFormula(formula);
        sf.setStartDate(startDate);
        sf.setEndDate(endDate);
        sf.setApplicableModels(applicableModels);
        sf.setFlightLineId(flightLineId);
        sf.setAirportCode(airportCode);
        sf.setCreateBy(user.getUsername());
        sf.setCreateTime(new Date());
        sf.setModifiedBy(user.getUsername());
        sf.setModifiedTime(new Date());
        subsidyFormulaDao.saveAndFlush(sf);
        // 根据公式中的参数id添加公式参数数据
        StringBuilder param = new StringBuilder();
        boolean parseParam = false;
        for (char c : formula.toCharArray()) {
            if (parseParam) {
                if (c == ']') {
                    parseParam = false;
                    String paramId = param.toString();
                    newSubsidyFormulaParamJoin(paramId, sf, user);
                    param.setLength(0);
                } else {
                    param.append(c);
                }
            } else {
                parseParam = isParseChildrenParam(c, parseParam);
            }
        }
    }

    /**
     * Title: deleteFormula<br>
     * Author: 刘志恒<br>
     * Description: 删除公式<br>
     * Date:  2022/8/16 11:02 <br>
     *
     * @param id : null
     */
    @Transactional
    @Override
    public void deleteFormula(String id, LoginUserDetails user) {
        // 判断id对应公式是否存在
        SubsidyFormula sf = subsidyFormulaDao.getFormulaById(id);
        if (sf == null) {
            // 抛出id对应公式为空异常
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FORMULA_ID_NULL.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_ID_NULL.getMsg());
        }
        // 逻辑删除公式对应参数
        subsidyFormulaParamJoinDao.deleteBySubsidyFormulaId(id, user.getUsername(), new Date());
        // 删除公式对应航班的 公式航班共有type为3的参数（如轮挡时间）
        FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoByFormulaId(id);
        if (fli != null) {
            String flightLine = fli.getFlightLine();
            String[] flArr = flightLine.split("-");
            StringBuilder reFlighrLine = new StringBuilder();
            for (int i = 0; i < flArr.length; i++) {
                reFlighrLine.append(flArr[flArr.length - 1 - i]);
                if (i != flArr.length - 1) {
                    reFlighrLine.append("-");
                }
            }
            List<String> flightIds = flightInfoDao.getFlightIdByLineTime(sf.getStartDate(), sf.getEndDate(), fli.getAirlineCode(), flightLine, reFlighrLine.toString(), fli.getAirportCode());
            subsidyFlightParamJoinDao.deleteBelong3ByFlightIds(flightIds);
        }

        // 逻辑删除id对应公式
        sf.setInvalid("0");
        sf.setModifiedTime(new Date());
        sf.setModifiedBy(user.getUsername());
        subsidyFormulaDao.save(sf);


    }

    @Transactional
    @Override
    public void updateFormula(SubsidyFormulaUpdateForm form, LoginUserDetails user) {
        String formulaId = form.getFormulaId();
        String formula = form.getFormula();
        String applicableModels = form.getApplicableModels();
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        SubsidyFormula sf = getSubsidyFormula(formulaId, startDate, endDate);
        sf.setApplicableModels(applicableModels);
        sf.setStartDate(startDate);
        sf.setEndDate(endDate);
        String oldFormula = sf.getFormula();
        if (!oldFormula.equals(formula)) {
            updateFormulaBiz0(user, formula, sf, oldFormula, formulaId);
        }
    }

    private void updateFormulaBiz0(LoginUserDetails user, String formula, SubsidyFormula sf, String oldFormula, String formulaId) {
        // 校验公式格式
        if (checkFormula(formula)) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FORMULA_CHECK_FORMULA_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_CHECK_FORMULA_ERROR.getMsg());
        }
        sf.setFormula(formula);
        Set<String> removeSet = new HashSet<>();
        boolean addFlag = false;
        StringBuilder paramId = new StringBuilder();
        // 遍历老公式，放入删除map
        for (char c : oldFormula.toCharArray()) {
            if (addFlag) {
                addFlag = isAddFlag(c, addFlag, paramId, removeSet);
            } else {
                addFlag = isParseChildrenParam(c, addFlag);
            }
        }
        Set<String> addSet = new HashSet<>();
        boolean newAddFlag = false;
        StringBuilder newParamId = new StringBuilder();
        // 遍历新公式
        for (char c : formula.toCharArray()) {
            if (newAddFlag) {
                newAddFlag = isNewAddFlag(c, newAddFlag, newParamId, removeSet, addSet);
            } else {
                newAddFlag = isParseChildrenParam(c, newAddFlag);
            }
        }

        // 如果为公式参数，则将其子参数一并删除
        subsidyFormulaParamJoinDao.removeByParentParamAndFormula(new ArrayList<>(removeSet), formulaId);
        // 根据removeSet中的参数id和公式id将参数值对象删除
        subsidyFormulaParamJoinDao.removeByParamAndFormula(new ArrayList<>(removeSet), formulaId);
        processData(user, sf, removeSet, addSet);
    }

    private static boolean isNewAddFlag(char c, boolean newAddFlag, StringBuilder newParamId, Set<String> removeSet, Set<String> addSet) {
        if (c == ']') {
            newAddFlag = false;
            String newId = newParamId.toString();
            if (removeSet.contains(newId)) {
                removeSet.remove(newId);
            } else {
                addSet.add(newId);
            }
            newParamId.setLength(0);
        } else {
            newParamId.append(c);
        }
        return newAddFlag;
    }

    private static boolean isAddFlag(char c, boolean addFlag, StringBuilder paramId, Set<String> removeSet) {
        if (c == ']') {
            addFlag = false;
            String oldParamId = paramId.toString();
            removeSet.add(oldParamId);
            paramId.setLength(0);
        } else {
            paramId.append(c);
        }
        return addFlag;
    }

    private void processData(LoginUserDetails user, SubsidyFormula sf, Set<String> removeSet, Set<String> addSet) {
        // 如果删除的的公式航班都有的参数（如轮挡时间），则需要同步将航班参数设为0
        for (String removeId : new ArrayList<>(removeSet)) {
            removeSubsidyFlightParamJoin(removeId, sf);
        }

        // 新增addSet中的对象
        for (String newSfpjId : new ArrayList<>(addSet)) {
            newSubsidyFormulaParamJoin(newSfpjId, sf, user);
        }
    }

    private @NotNull SubsidyFormula getSubsidyFormula(String formulaId, Date startDate, Date endDate) {
        SubsidyFormula sf = subsidyFormulaDao.getFormulaById(formulaId);
        if (sf == null) {
            throw new GenericException(101, "公式id为空");
        }

        // 判断新传入日期是否与其他公式有交集
        if (!subsidyFormulaDao.getSubsidyFormulaByFlightLineIdFlightIdStartEndDate(startDate, endDate, sf.getFlightLineId(), formulaId, sf.getAirportCode()).isEmpty()) {
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FORMULA_SAVE_REPEAT.getCode(), BusinessMessageEnum.SUBSIDY_FORMULA_SAVE_REPEAT.getMsg());
        }
        return sf;
    }

    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 获取参数列表<br>
     * Date:  2022/8/5 14:33 <br>
     *
     * @param airportCode : null
     */
    @Override
    public List<SubsidyParam> getParamList(String airportCode) {
        List<SubsidyParam> spList = subsidyParamDao.getSubsidyParamByAirportCode(airportCode);
        List<SubsidyParam> resList = new ArrayList<>();
        for (SubsidyParam sp : spList) {
            if ("免票张数".equals(sp.getParamName()) || "旅客均值".equals(sp.getParamName()) || "3".equals(sp.getParamType())) {
                continue;
            }
            resList.add(sp);
        }
        return resList;
    }

    @Override
    public List<AirportThreeCharCode> getAirportCodeList(String selectAirportCode) {
        List<AirportThreeCharCode> airportList;
        // 判断输入字符串中是否包含中文
        if (ObjectUtil.checkStringContainChinese(selectAirportCode)) {
            airportList = airportThreeCharCodeDao.getAirportCodeByName(selectAirportCode);
        } else {
            airportList = airportThreeCharCodeDao.getAirportCodeByCode(selectAirportCode);
        }
        return airportList;
    }

    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 根据公式id，完善参数值<br>
     * Date:  2022/8/5 16:25 <br>
     *
     * @param paramList : null
     */
    @Transactional
    @Override
    public void completeParam(List<ParamForm> paramList, LoginUserDetails user) {
        for (ParamForm pf : paramList) {
            SubsidyFormulaParamJoin sfpj = subsidyFormulaParamJoinDao.getSubsidyFormulaParamJoinById(pf.getParamId());
            if (sfpj == null) {
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_COMPLETE_PARAM_ID_ERROE.getCode(),
                        BusinessMessageEnum.SUBSIDY_COMPLETE_PARAM_ID_ERROE.getMsg());
            }
            // 正整数、浮点数校验
            switch (sfpj.getNumType()) {
                case "1":
                    judgeOne(pf);
                    break;
                case "2":
                    judgeTwo(pf);
                    break;
                case "3":
                    judgeThree(pf);
                    break;
                default:
            }

            // 如果为公式和航班都有的参数（如轮挡时间）
            if (sfpj.getParamBelong() != null && "3".equals(sfpj.getParamBelong())) {
                SubsidyFormula sf = subsidyFormulaDao.getFormulaById(sfpj.getSubsidyFormulaId());
                if (sf == null) {
                    throw new GenericException(
                            BusinessMessageEnum.SUBSIDY_BILL_FORMULA_NULL_ERROR.getCode(),
                            BusinessMessageEnum.SUBSIDY_BILL_FORMULA_NULL_ERROR.getMsg());
                }
                FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoByFormulaId(sfpj.getSubsidyFormulaId());
                StringBuilder reFlighrLine = getReFlightLine(fli);
                List<String> fiList = flightInfoDao.getFlightIdByReLineTime(sf.getStartDate(),
                        sf.getEndDate(), fli.getAirlineCode(), fli.getFlightLine(), reFlighrLine.toString(),
                        fli.getAirportCode());
                subsidyFlightParamJoinDao.updateValueByFlightIds(pf.getValue(), fiList, sfpj.getParamId());
            }

            sfpj.setParamValue(pf.getValue());
            subsidyFormulaParamJoinDao.save(sfpj);
        }
    }

    private void judgeThree(ParamForm pf) {
        if (!isDouble(pf.getValue())) {
            // 抛出参数类型为浮点数，但录入实际参数值不匹配
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getMsg());
        }
    }

    private void judgeTwo(ParamForm pf) {
        if (!isInteger(pf.getValue())) {
            // 抛出参数类型为整数，但录入实际参数值不匹配
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_INTEGER_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_INTEGER_ERROR.getMsg());
        }
    }

    private void judgeOne(ParamForm pf) {
        if (!isPositiveInteger(pf.getValue())) {
            // 抛出参数类型为正整数，但录入实际参数值不匹配
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR
                            .getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR
                            .getMsg());
        }
    }

    private StringBuilder getReFlightLine(FlightLineInfo fli) {
        if (fli == null) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FORMULA_SAVE_LINE_ERROE.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_SAVE_LINE_ERROE.getMsg());
        }
        // 获取相反航线
        String[] flArr = fli.getFlightLine().split("-");
        StringBuilder reFlightLine = new StringBuilder();
        for (int i = 0; i < flArr.length; i++) {
            reFlightLine.append(flArr[flArr.length - 1 - i]);
            if (i != flArr.length - 1) {
                reFlightLine.append("-");
            }
        }
        return reFlightLine;
    }

    // 判断正整数（int）
    private boolean isPositiveInteger(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[+]?[1-9]\\d*$");
        return pattern.matcher(str).matches();
    }

    // 判断整数（int）
    private boolean isInteger(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-+]?\\d*$");
        return pattern.matcher(str).matches();
    }


    // 判断浮点数（double和float）
    private boolean isDouble(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        if (isInteger(str)) {
            return true;
        }
        Pattern pattern = Pattern.compile("^[-+]?\\d*[.]\\d+$");
        return pattern.matcher(str).matches();
    }


    /**
     * Title: newSubsidyFormulaParamJoin<br>
     * Author: 刘志恒<br>
     * Description: 新增SubsidyFormulaParamJoin对象<br>
     * Date:  2022/8/8 15:38 <br>
     *
     * @param paramId : null
     */
    public void newSubsidyFormulaParamJoin(String paramId, SubsidyFormula sf,
                                           LoginUserDetails user) {
        String formulaId = sf.getId();
        List<SubsidyParam> spList = subsidyParamDao.getSubsidyParamById(paramId);
        if (spList.isEmpty()) {
            throw new GenericException(10000, "参数id为空");
        }
        SubsidyParam sp = spList.get(0);
        SubsidyFormulaParamJoin sj = new SubsidyFormulaParamJoin();
        sj.setSubsidyFormulaId(formulaId);
        sj.setParamId(paramId);
        sj.setParamBelong(sp.getParamBelong());
        sj.setParamName(sp.getParamName());
        sj.setParamType(sp.getParamType());
        sj.setParamValue(null);
        sj.setUnit(sp.getUnit());
        sj.setNumType(sp.getNumType());

        sj.setCreateBy(user.getUsername());
        sj.setCreateTime(new Date());
        sj.setModifiedBy(user.getUsername());
        sj.setModifiedTime(new Date());

        // 如果为公式和航班都有的参数（如轮挡时间）
        if (sp.getParamBelong() != null && "3".equals(sp.getParamBelong())) {
            FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoByFormulaId(formulaId);
            StringBuilder reFlighrLine = getReFlightLine(fli);
            List<String> fiList = flightInfoDao.getFlightIdByReLineTime(sf.getStartDate(),
                    sf.getEndDate(), fli.getAirlineCode(), fli.getFlightLine(), reFlighrLine.toString(),
                    fli.getAirportCode());
            // 获取已设置航班参数的航班
            List<String> readyIdList =
                    subsidyFlightParamJoinDao.getFlightIdByFlightIdAndParamId(fiList, paramId);
            // 对已设置参数批量更新,设为0
            subsidyFlightParamJoinDao.updateValueByFlightIds("0", readyIdList, paramId);
            List<SubsidyFlightParamJoin> sfpjSaveList = new ArrayList<>();
            for (String fiId : fiList) {
                if (readyIdList.contains(fiId)) {
                    continue;
                }
                SubsidyFlightParamJoin sfpj = new SubsidyFlightParamJoin();
                sfpj.setFlightId(fiId);
                sfpj.setParamId(paramId);
                sfpj.setParamName(sp.getParamName());
                sfpj.setParamType(sp.getParamType());
                sfpj.setParamValue("0");
                sfpj.setParamBelong(sp.getParamBelong());
                sfpj.setUnit(sp.getUnit());
                sfpj.setNumType(sp.getNumType());

                sfpj.setAirportCode(fli.getAirportCode());
                sfpj.setCreateBy(user.getUsername());
                sfpj.setCreateTime(new Date());
                sfpj.setModifiedBy(user.getUsername());
                sfpj.setModifiedTime(new Date());

                sfpjSaveList.add(sfpj);
            }
            subsidyFlightParamJoinDao.saveAll(sfpjSaveList);
        }

        // 如果为公式类型参数，则填入子参数
        if (sp.getParamType() != null && sp.getParamType().equals(Constants.SubsidyEnum.PARAM_TYPE_FORMULA.getValue())) {
            // 用set来去重
            Set<String> newChildren = new HashSet<>();
            String paramFormula = sp.getParamFormula();
            StringBuilder childrenParam = new StringBuilder();
            boolean addFlag = false;
            for (char pfc : paramFormula.toCharArray()) {
                if (addFlag) {
                    if (pfc == ']') {
                        addFlag = false;
                        String childrenId = childrenParam.toString();
                        if (!newChildren.contains(childrenId)) {
                            SubsidyParam csp = subsidyParamDao.getById(childrenId);
                            // 如果子参数id不为空，且不为航班参数，则new
                            if (!csp.getParamBelong().equals(Constants.SubsidyEnum.PARAM_BELONG_FLIGHT.getValue())) {
                                SubsidyFormulaParamJoin csj = new SubsidyFormulaParamJoin();
                                csj.setSubsidyFormulaId(formulaId);
                                csj.setParamId(childrenId);
                                csj.setParamBelong(csp.getParamBelong());
                                csj.setParamName(csp.getParamName());
                                // 设为公式参数的子参数
                                csj.setParamType(Constants.SubsidyEnum.PARAM_TYPE_CHILDREN.getValue());
                                csj.setParamValue(null);
                                csj.setUnit(csp.getUnit());
                                csj.setParentParamId(paramId);
                                csj.setNumType(csp.getNumType());

                                csj.setCreateBy(user.getUsername());
                                csj.setCreateTime(new Date());
                                csj.setModifiedBy(user.getUsername());
                                csj.setModifiedTime(new Date());
                                subsidyFormulaParamJoinDao.save(csj);
                            }
                            newChildren.add(childrenId);
                        }
                        childrenParam.setLength(0);
                    } else {
                        childrenParam.append(pfc);
                    }
                } else {
                    addFlag = isParseChildrenParam(pfc, addFlag);
                }
            }
        }

        subsidyFormulaParamJoinDao.save(sj);
    }

    public void removeSubsidyFlightParamJoin(String removeId, SubsidyFormula sf) {
        List<SubsidyParam> spList = subsidyParamDao.getSubsidyParamById(removeId);
        if (spList.isEmpty()) {
            return;
        }
        SubsidyParam sp = spList.get(0);
        // 如果为公式和航班都有的参数，则将航班参数都设置为0
        if ("3".equals(sp.getParamBelong())) {
            FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoByFormulaId(sf.getId());
            StringBuilder reFlighrLine = getReFlightLine(fli);
            List<String> fiList = flightInfoDao.getFlightIdByReLineTime(sf.getStartDate(),
                    sf.getEndDate(), fli.getAirlineCode(), fli.getFlightLine(), reFlighrLine.toString(),
                    fli.getAirportCode());
            // 获取已设置航班参数的航班
            List<String> readyIdList =
                    subsidyFlightParamJoinDao.getFlightIdByFlightIdAndParamId(fiList, removeId);
            // 对已设置参数批量更新,设为0
            subsidyFlightParamJoinDao.updateValueByFlightIds("0", readyIdList, removeId);
        }
    }

    @Override
    public void insertParam(SubsidyParamSaveForm form, LoginUserDetails user) {
        SubsidyParam sp = new SubsidyParam();
        sp.setParamName(form.getParamName());
        sp.setParamBelong(form.getParamBelong());
        sp.setParamType(form.getParamType());
        sp.setUnit(form.getUnit());
        sp.setParamFormula(form.getParamFormula());
        sp.setAirportCode(form.getAirportCode());
        sp.setCreateBy(user.getUsername());
        sp.setCreateTime(new Date());
        sp.setModifiedBy(user.getUsername());
        sp.setModifiedTime(new Date());
        subsidyParamDao.save(sp);
    }


    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 校验公式格式是否合法<br>
     * Date:  2022/9/5 10:16 <br>
     */
    private static boolean checkFormula(String formula) {
        if (CharSequenceUtil.isBlank(formula)) {
            return true;
        }
        String currentChar;
        String lastChar = null;
        // 括号数
        int brackets = 0;
        for (int i = 0; i < formula.length(); i++) {
            char[] formulaArray = formula.toCharArray();
            char c = formulaArray[i];
            if (c == '[') {
                while (i < formula.length() - 1 && formulaArray[i] != ']') {
                    i++;
                }
                if (formulaArray[i] != ']') {
                    return true;
                }
                currentChar = NUMBER;

            } else if (c <= '9' && c >= '0') {
                while (i + 1 < formula.length() && formulaArray[i + 1] <= '9'
                        && formulaArray[i + 1] >= '0') {
                    i++;
                }
                currentChar = NUMBER;
            } else if (isCalculatingSigns(c)) {
                currentChar = "sign";
            } else if (c == '(') {
                brackets++;
                currentChar = "left";
            } else if (c == ')') {
                if (brackets == 0) {
                    return true;
                }
                brackets--;
                currentChar = RIGHT;
            } else if (c == ' ') {
                continue;
            } else {
                return true;
            }

            // 若不是以数字开头，则返回false
            if (lastChar == null) {
                if ((!NUMBER.equals(currentChar)) && !"left".equals(currentChar)) {
                    return true;
                }
            } else {
                // 当前一位为数字，后一位必须为运算符号或者右括号，否则但会false
                if (NUMBER.equals(lastChar) && !("sign".equals(currentChar) || RIGHT.equals(currentChar))) {
                    return true;
                }
                // 当前一位为运算符，后一位必须为数字或者左括号
                if ("sign".equals(lastChar) && !(NUMBER.equals(currentChar) || "left".equals(currentChar))) {
                    return true;
                }
                // 当前一位为左括号，后一位必须数字
                if ("left".equals(lastChar) && !NUMBER.equals(currentChar)) {
                    return true;
                }
                // 当前一位为右括号，后一位必须运算符号
                if (RIGHT.equals(lastChar) && !"sign".equals(currentChar)) {
                    return true;
                }
            }
            lastChar = currentChar;
        }
        // 如果左右括号数量不一致，或者不是以数字或右括号结束，则返回false
        return brackets != 0 || ((!NUMBER.equals(lastChar)) && !RIGHT.equals(lastChar));
    }

    // 是否为运算符号
    private static boolean isCalculatingSigns(char c) {
        return c == '+' || c == '-' || c == '*' || c == '/';
    }

}
