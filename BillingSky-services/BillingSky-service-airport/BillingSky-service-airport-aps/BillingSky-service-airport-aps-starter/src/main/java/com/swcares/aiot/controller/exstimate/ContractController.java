package com.swcares.aiot.controller.exstimate;

import com.swcares.aiot.core.model.dto.ContractConfirmDto;
import com.swcares.aiot.core.model.dto.ErContractPageDto;
import com.swcares.aiot.core.model.vo.ErContractPageVo;
import com.swcares.aiot.core.model.vo.ErContractVo;
import com.swcares.aiot.service.ContractService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.exstimate.ContractController
 * Description：合约管理页面controller
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/27 10:31
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/contract")
@Api(value = "ContractController", tags = {"合约管理接口"})
@ApiVersion("对账通-机场端-离港返还-api")
@Slf4j
public class ContractController {
    @Resource
    private ContractService contractService;

    @PostMapping("/page")
    @ApiOperation(value = "合约分页查询")
    public PagedResult<List<ErContractPageVo>> page(@RequestBody @Validated ErContractPageDto erContractPageDto) {
        return contractService.page(erContractPageDto);
    }


    @GetMapping("/getById/{id}")
    @ApiOperation(value = "查询合约")
    public BaseResult<ErContractVo> getById(@PathVariable("id") String id) {
        return contractService.getById(id);
    }



    @PostMapping("/confirm")
    @ApiOperation(value = "确认合约")
    public BaseResult<Boolean> confirm(@RequestBody @Validated ContractConfirmDto contractConfirmDto) {
        return contractService.confirm(contractConfirmDto);
    }

}
