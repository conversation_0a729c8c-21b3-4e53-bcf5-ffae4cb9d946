package com.swcares.aiot.service;

import com.swcares.aiot.core.model.dto.ContractConfirmDto;
import com.swcares.aiot.core.model.dto.ErContractPageDto;
import com.swcares.aiot.core.model.vo.ErContractPageVo;
import com.swcares.aiot.core.model.vo.ErContractVo;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.ContractService
 * Description：离港返还合约管理service
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/28 11:26
 * @version v1.0
 */
public interface ContractService {

    /**
     * Title：page
     * Description：合约分页接口
     * author：liuzhiheng
     * date：2025/2/27 10:32
     * @param erContractPageDto:
     * return: BaseResult<Page<ErContractPageVo>>
     */
    PagedResult<List<ErContractPageVo>> page(ErContractPageDto erContractPageDto);

    /**
     * Title：getById
     * Description：根据Id查询合约
     * author：liuzhiheng
     * date：2025/2/27 10:43
     * @param id:  主键Id
     * return: com.swcares.aiot.core.entity.ErContractVo
     */
    BaseResult<ErContractVo> getById(String id);

    /**
     * Title：confirm
     * Description：确认合约
     * author：刘志恒
     * date：2025/2/27 10:44
     * @param contractConfirmDto:  主键Id
     * return: BaseResult<Boolean>
     */
    BaseResult<Boolean> confirm( ContractConfirmDto contractConfirmDto);
}
