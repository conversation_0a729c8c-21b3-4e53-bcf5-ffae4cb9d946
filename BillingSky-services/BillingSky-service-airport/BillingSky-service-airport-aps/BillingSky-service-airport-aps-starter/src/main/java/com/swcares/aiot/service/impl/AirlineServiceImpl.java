package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.AirlineForm;
import com.swcares.aiot.core.model.entity.AirlineInfoAps;
import com.swcares.aiot.core.model.entity.FeeInfo;
import com.swcares.aiot.core.model.vo.AirlineVo;
import com.swcares.aiot.core.model.vo.UnmaintainedAirlineVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.AirlineDao;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.service.AirlineService;
import com.swcares.aiot.service.LogService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ClassName：AirlineServiceImpl <br>
 * Description：(AirlineService的实现类)<br>
 * Copyright © 2020-9-29 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-9-29 14:26<br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class AirlineServiceImpl implements AirlineService {
    private static final String ERROR_MSG = "出现业务异常";
    public static final String EXPIRED_VALUE = "1";
    public static final String NOT_EXPIRED_VALUE = "0";
    @Resource
    private AirlineDao airlineDao;
    @Resource
    private FeeDao feeDao;
    @Resource
    private LogService logService;

    @Override
    @Transactional
    public ResultBuilder<Object> saveAirlineInfo(AirlineForm airlineForm, LoginUserDetails user, String urlName) {
        AirlineInfoAps airportInfo;
        if (airlineDao.findAirlineInfoByAirlineShortNameAndAirportCode(airlineForm.getAirportCode(), airlineForm.getAirlineShortName()) != null) {
            throw new GenericException(BusinessMessageEnum.SAVE_AIRLINE_ERROR.getCode(), BusinessMessageEnum.SAVE_AIRLINE_ERROR.getMsg());
        } else {
            airportInfo = new AirlineInfoAps();
            try {
                BeanUtils.copyProperties(airportInfo, airlineForm);
            } catch (IllegalAccessException | InvocationTargetException e) {
                throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
            }
            airportInfo.setCreateTime(new Date());
            airportInfo.setCreateBy(user.getUsername());
        }
        airlineDao.save(airportInfo);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_SAVE.getValue();
        airlineForm.setAirlineId(null);
        logService.addLogForSave(airlineForm, user, content, airportInfo.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional
    public ResultBuilder<Object> delAirlineById(String id, LoginUserDetails user, String urlName) {
        List<FeeInfo> feeInfos = feeDao.listFeeRuleByAirlineId(id, null);
        AirlineForm airlineForm = new AirlineForm();
        AirlineInfoAps airlineInfo = airlineDao.findByAirlineId(id);
        try {
            BeanUtils.copyProperties(airlineForm, airlineInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(ERROR_MSG, e);
        }
        if (!feeInfos.isEmpty()) {
            log.error(BusinessMessageEnum.DELETE_AIRLINE_ERROR.getMsg());
            throw new GenericException(BusinessMessageEnum.DELETE_AIRLINE_ERROR.getCode(), BusinessMessageEnum.DELETE_AIRLINE_ERROR.getMsg());
        }
        feeDao.saveAll(feeInfos);
        airlineDao.deleteByAirlineId(id, user.getUsername(), new Date());

        //增加日志
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_DELETE.getValue();
        airlineForm.setAirlineId(null);
        logService.addLogForDelete(airlineForm, user, content, airlineForm.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<List<AirlineInfoAps>> listAirlineByAirlineCode(String airlineCode, String airportCode) {
        return new ResultBuilder.Builder<List<AirlineInfoAps>>().data(airlineDao.listAirlineByAirlineCode(airlineCode, airportCode)).builder();
    }

    @Override
    @Transactional
    public ResultBuilder<Object> updateAirlineInfoById(AirlineForm airlineForm, LoginUserDetails user, String urlName) {
        AirlineInfoAps airlineInfo = airlineDao.findByAirlineId(airlineForm.getAirlineId());
        AirlineForm airlineForm1 = new AirlineForm();
        try {
            BeanUtils.copyProperties(airlineForm1, airlineInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(ERROR_MSG, e);
        }
        airlineInfo.setAirlineName(airlineForm.getAirlineName());
        airlineInfo.setAirlineShortName(airlineForm.getAirlineShortName());
        airlineInfo.setSettleCode(airlineForm.getSettleCode());
        airlineInfo.setAirlineCode(airlineForm.getAirlineCode());
        airlineInfo.setModifiedBy(user.getUsername());
        airlineInfo.setModifiedTime(new Date());
        airlineDao.save(airlineInfo);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_UPDATE.getValue();
        airlineForm.setAirlineId(null);
        logService.addLogForUpdate(airlineForm1, airlineForm, user, content, airlineInfo.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<AirlineInfoAps> getAirlineById(String id) {
        return new ResultBuilder.Builder<AirlineInfoAps>().data(airlineDao.findByAirlineId(id)).builder();
    }

    @Override
    public ResultBuilder<Object> importAirlineInfo(MultipartFile file) {
        List<Object[]> list = FileUtils.importExcel(file);
        List<AirlineInfoAps> airlineInfos = new ArrayList<>();
        try {
            for (Object[] objects : list) {
                AirlineInfoAps airlineInfo = new AirlineInfoAps();
                airlineInfo.setAirlineShortName((String) objects[0]);
                airlineInfo.setAirlineCode((String) objects[2]);
                airlineInfo.setSettleCode((String) objects[1]);
                airlineInfos.add(airlineInfo);
            }
            airlineDao.saveAll(airlineInfos);
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
            throw new GenericException(BusinessMessageEnum.DATA_IMPORT_ERROR.getCode(), BusinessMessageEnum.DATA_IMPORT_ERROR.getMsg());
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<Page<AirlineVo>> pageAirlineInfoByCondition(String airportCode, String airlineCode, String airlineShortName,
                                                                     String settleCode, String isExpired, PageParam page) {
        if (CharSequenceUtil.isBlank(airportCode)) {
            airlineCode = null;
        }
        if (CharSequenceUtil.isBlank(airlineCode)) {
            airlineCode = null;
        }
        if (CharSequenceUtil.isBlank(airlineShortName)) {
            airlineShortName = null;
        }
        if (CharSequenceUtil.isBlank(settleCode)) {
            settleCode = null;
        }
        if (CharSequenceUtil.isBlank(isExpired)) {
            isExpired = null;
        }
        Pageable pageable = PageRequest.of(page.getPage() - 1, page.getLimit());
        Page<AirlineVo> airlineVos = airlineDao.pageAirlineInfoByCondition(airportCode, airlineCode, airlineShortName, settleCode, isExpired, pageable);
        for (AirlineVo airlineVo : airlineVos) {
            pageAirlineInfoByConditionBiz0(airportCode, airlineVo);
        }
        return new ResultBuilder.Builder<Page<AirlineVo>>().data(airlineVos).builder();
    }

    private void pageAirlineInfoByConditionBiz0(String airportCode, AirlineVo airlineVo) {
        List<FeeInfo> feeInfos = feeDao.listFeeRuleByAirlineId(airlineVo.getAirlineId(), airportCode);
        airlineVo.setIsExpired(NOT_EXPIRED_VALUE);
        if (!feeInfos.isEmpty()) {
            airlineVo.setHaveFee(1);
            for (FeeInfo feeInfo : feeInfos) {
                if (feeInfo.getIsExpired().equals(EXPIRED_VALUE)) {
                    airlineVo.setIsExpired(EXPIRED_VALUE);
                    break;
                }
            }
        } else {
            airlineVo.setHaveFee(0);
        }
    }

    @Override
    public ResultBuilder<List<AirlineInfoAps>> getAirlineInfoByShortName(String airlineShortName) {
        return new ResultBuilder.Builder<List<AirlineInfoAps>>().data(airlineDao.getAirlineInfoByShortName(airlineShortName)).builder();
    }

    @Override
    public ResultBuilder<List<UnmaintainedAirlineVo>> listNotStoredAirlineInfoByAirport(String airportCode) {
        return new ResultBuilder.Builder<List<UnmaintainedAirlineVo>>().data(airlineDao.listNotStoredAirlineInfoByAirport(airportCode)).builder();
    }

    @Override
    public ResultBuilder<List<AirlineVo>> listMaintainedAirlineCodeByAirport(String airportCode) {
        return new ResultBuilder.Builder<List<AirlineVo>>().data(airlineDao.listMaintainedAirlineCodeByAirport(airportCode)).builder();
    }

    @Override
    public ResultBuilder<List<AirlineInfoAps>> listMaintainedAirlineByAirport(String airportCode) {
        return new ResultBuilder.Builder<List<AirlineInfoAps>>().data(airlineDao.listMaintainedAirlineByAirport(airportCode)).builder();
    }

    @Override
    public ResultBuilder<List<String>> listAirlineCodeByAirportCode(String airportCode) {
        return new ResultBuilder.Builder<List<String>>().data(airlineDao.listAirlineCodeByAirportCode(airportCode)).builder();
    }

    @Override
    public ResultBuilder<List<String>> listByAirlineShortName(String airlineShortName) {
        return new ResultBuilder.Builder<List<String>>().data(airlineDao.listByAirlineShortName(airlineShortName)).builder();
    }

    @Override
    public ResultBuilder<List<String>> listSettleCodeByAirportCode(String airportCode) {
        return new ResultBuilder.Builder<List<String>>().data(airlineDao.listSettleCodeByAirportCode(airportCode)).builder();
    }

    @Override
    public ResultBuilder<Object> findInvalidAirline() {
        String now = DateUtils.format(new Date());
        String startDate = DateUtils.getEfficientDate();
        //获取航班无航司列表
        List<String> efficientAirlineShortName = airlineDao.findEfficientAirline(startDate, now);
        //获取航班有航司无公式列表
        List<String> invalidAirlineShortName = airlineDao.findInvalidAirline(startDate, now);
        List[] resList = {efficientAirlineShortName, invalidAirlineShortName};
        return new ResultBuilder.Builder<>().data(resList).builder();
    }

    @Override
    public ResultBuilder<List<AirlineInfoAps>> listAllValidAirline(String airportCode) {
        return new ResultBuilder.Builder<List<AirlineInfoAps>>().data(airlineDao.listAllValidAirline(airportCode)).builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> changeAirlineSpecialCmbFee(List<String> ids, String airportCode) {
        //先全部置为空 在重新插入本次新增的全量
        airlineDao.updateByAirport(airportCode);
        if (ObjectUtils.isNotEmpty(ids)) {
            ids.forEach(e -> airlineDao.updateByAirlineId(e, airportCode));
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<List<String>> listExpiredAirlineShortName() {
        List<String> shortNameList = airlineDao.listExpiredAirlineShortName();
        return new ResultBuilder.Builder<List<String>>().data(shortNameList).builder();
    }
}
