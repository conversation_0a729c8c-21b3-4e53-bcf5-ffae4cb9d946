package com.swcares.aiot.service;

import com.swcares.aiot.core.form.VariableGuaranteeForm;
import com.swcares.aiot.core.form.VariableGuaranteeUpdateForm;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;

/**
 * ClassName：com.swcares.service.AssureMqService
 * Description：节点保障对接mq
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/4/24 10:59
 * @version v1.0
 */
public interface AssureService {

    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 模糊搜索获取特车设备信息<br>
     * Date:  2022/4/24 11:05 <br>
     */
    Object getVariableId(String variableName, String airportCode);

    /**
     * Title: getMatchingRelationship<br>
     * Author: 刘志恒<br>
     * Description: 获取关联关系<br>
     * Date:  2022/5/5 14:18 <br>
     */
    Object getMatchingRelationship(PageParam pageParam, String variableId, String airportCode);

    /**
     * Title: addMatchingRelationship<br>
     * Author: 刘志恒<br>
     * Description: 添加关联关系<br>
     * Date:  2022/5/5 15:47 <br>
     */
    Object addMatchingRelationship(VariableGuaranteeForm form, LoginUserDetails user);

    /**
     * Title: updateMatchingRelationship<br>
     * Author: 刘志恒<br>
     * Description: 修改关联关系<br>
     * Date:  2022/5/5 15:47 <br>
     */
    Object updateMatchingRelationship(VariableGuaranteeUpdateForm form, LoginUserDetails user);

    /**
     * Title: deletedMatchingRelationship<br>
     * Author: 刘志恒<br>
     * Description: 删除关联关系<br>
     * Date:  2022/5/7 15:11 <br>
     */
    Object deletedMatchingRelationship(String id, LoginUserDetails user);

    /**
     * Title: getBillItemList<br>
     * Author: 刘志恒<br>
     * Description: 获取所有保障系统的特车设备列表<br>
     * Date:  2022/5/6 16:50 <br>
     */
    Object getBillItemList(String airportCode);


    void importItemAll();

    void importSignBusDataSettlement(String synStartDate, String synEndDate, String flightNos, String serviceRecordId, String airportCode);
}
