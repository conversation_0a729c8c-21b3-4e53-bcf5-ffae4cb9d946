package com.swcares.aiot.service.impl;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.dao.OperateRecordDao;
import com.swcares.aiot.core.model.entity.OperateRecord;
import com.swcares.aiot.service.OperateRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/*
 *
 * ClassName：LogServiceImpl <br>
 * Description：<br>
 * Copyright © 2020-10-12 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021-4-14 13:47<br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class OperateRecordServiceImpl implements OperateRecordService {
    @Resource
    private OperateRecordDao operateRecordDao;
    @Override
    public ResultBuilder addOperateRecord(OperateRecord operateRecord) {
        operateRecordDao.save(operateRecord);
        return new ResultBuilder.Builder().builder();
    }

    @Override
    public ResultBuilder listOperateRecordByOperateObjectAndOperateId(String operateObject, String operateId) {
        return new ResultBuilder.Builder().data(operateRecordDao.listOperateRecordByOperateObjectAndOperateId(operateObject,operateId)).builder();
    }
}
