package com.swcares.aiot.core.mapstruct;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TAircraftInfo;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.entity.TFlightBillHistory;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.entity.FlightBillHistory;
import com.swcares.aiot.core.model.vo.AircraftSnapShotVo;
import com.swcares.aiot.core.model.vo.FlightInfoSnapShotVo;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MsReCalcProcessBiz {
    @Mapping(target = "operation", source = "code")
    @Mapping(target = "flightBillId", ignore = true)
    @Mapping(target = "id", ignore = true)
    FlightBillHistory saveToHistory(FlightBill flightBill, String code);

    // 添加默认方法实现列表转换
    default List<FlightBillHistory> saveToHistory(List<FlightBill> flightBills, String code, LoginUserDetails userDetails) {
        if (flightBills == null) {
            return new ArrayList<>();
        }
        List<FlightBillHistory> histories = new ArrayList<>(flightBills.size());
        for (FlightBill flightBill : flightBills) {
            FlightBillHistory flightBillHistory = saveToHistory(flightBill, code);
            flightBillHistory.setFlightBillId(flightBill.getId());
            flightBillHistory.setId(null);
            flightBillHistory.setCreateBy(userDetails.getUsername());
            flightBillHistory.setCreateTime(new Date());
            histories.add(flightBillHistory);
        }
        return histories;
    }


    @Mapping(target = "operation", source = "code")
    @Mapping(target = "flightBillId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "modifiedTime", ignore = true)
    @Mapping(target = "modifiedBy", ignore = true)
    TFlightBillHistory saveToTHistory(TFlightBill flightBill, String code);
    // 添加默认方法实现列表转换
    default TFlightBillHistory saveToHistoryEntity(TFlightBill flightBill, String code, LoginUserDetails userDetails) {
        TFlightBillHistory flightBillHistory = saveToTHistory(flightBill, code);
        flightBillHistory.setId(Long.toString(getMsgId()));
        flightBillHistory.setFlightBillId(flightBill.getId());
        flightBillHistory.setCreateBy(userDetails.getUsername());
        flightBillHistory.setCreateTime(LocalDateTime.now());
        flightBillHistory.setModifiedTime(LocalDateTime.now());
        flightBillHistory.setModifiedBy(userDetails.getUsername());
        return flightBillHistory;
    }


    static long getMsgId() {
        return IdUtil.getSnowflake(RandomUtil.randomLong(1, 31), RandomUtil.randomLong(1, 31)).nextId();
    }


    FlightInfoSnapShotVo getFlightInfoSnapShotVo(FlightInfoMb flightInfoMb);

    AircraftSnapShotVo getAircraftSnapShotVo(TAircraftInfo aircraftInfo);
}
