package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aiot.core.entity.TVariableGuarantee;
import com.swcares.aiot.core.model.vo.VariableGuaranteeVo;
import org.apache.ibatis.annotations.Param;

/**
 * ClassName：com.swcares.aiot.mapper.VariableGuaranteeMapper
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/21 10:40
 * @version v1.0
 */
public interface VariableGuaranteeMapper extends BaseMapper<TVariableGuarantee> {

    VariableGuaranteeVo selectMatchingRelationship(@Param("airportCode") String airportCode,
                                                   @Param("variableId")String variableId,
                                                   @Param("itemId")String itemId);

    VariableGuaranteeVo selectMatchingRelationshipDataCenter(@Param("airportCode") String airportCode,
                                                   @Param("variableId")String variableId);


}
