package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.form.ParamForm;
import com.swcares.aiot.core.form.SubsidyFormulaSaveForm;
import com.swcares.aiot.core.form.SubsidyFormulaUpdateForm;
import com.swcares.aiot.core.form.SubsidyParamSaveForm;
import com.swcares.aiot.core.model.entity.AirportThreeCharCode;
import com.swcares.aiot.core.model.entity.SubsidyFormula;
import com.swcares.aiot.core.model.entity.SubsidyParam;
import com.swcares.aiot.core.model.vo.SubsidyFormulaVo;
import com.swcares.aiot.service.SubsidyFormulaService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.controller.SubsidyFormulaController
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/8 16:09
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/subsidyFormula")
@Api(value = "SubsidyFormulaController", tags = {"航线补贴公式接口"})
@Slf4j
public class SubsidyFormulaController {
    @Resource
    private SubsidyFormulaService subsidyFormulaService;

    @ApiOperation(value = "获取航线下的公式列表 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = SubsidyFormula.class)})
    @GetMapping("/getFormulaList")
    public ResultBuilder<List<SubsidyFormulaVo>> getFormulaList(String flightLineId, String airportCode) {

        List<SubsidyFormulaVo> list = subsidyFormulaService.getFormulas(flightLineId, airportCode);
        return new ResultBuilder.Builder<List<SubsidyFormulaVo>>().data(list).builder();
    }

    @ApiOperation(value = "保存公式 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/saveFormula")
    public ResultBuilder<Object> saveFormula(@RequestBody @Validated SubsidyFormulaSaveForm form) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFormulaService.saveFormula(form, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "删除公式 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @DeleteMapping("/deleteFormula")
    public ResultBuilder<Object> deleteFormula(String id) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFormulaService.deleteFormula(id, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "更新公式 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/updateFormula")
    public ResultBuilder<Object> updateFormula(@RequestBody @Validated SubsidyFormulaUpdateForm form) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFormulaService.updateFormula(form, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "新增参数数据 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/insertParam")
    public ResultBuilder<Object> insertParam(@RequestBody SubsidyParamSaveForm form) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFormulaService.insertParam(form, user);
        return new ResultBuilder.Builder<>().builder();
    }


    @ApiOperation(value = "完善公式参数 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/completeParam")
    public ResultBuilder<Object> completeParam(@RequestBody List<ParamForm> paramList) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFormulaService.completeParam(paramList, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "获取机场下所有公式参数的列表 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/getParamList")
    public ResultBuilder<List<SubsidyParam>> getParamList(String airportCode) {
        List<SubsidyParam> list = subsidyFormulaService.getParamList(airportCode);
        return new ResultBuilder.Builder<List<SubsidyParam>>().data(list).builder();
    }

    @ApiOperation(value = "获取三字码信息 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/getAirportCodeList")
    public ResultBuilder<List<AirportThreeCharCode>> getAirportCodeList(String selectAirportCode) {
        List<AirportThreeCharCode> list = subsidyFormulaService.getAirportCodeList(selectAirportCode);
        return new ResultBuilder.Builder<List<AirportThreeCharCode>>().data(list).builder();
    }
}
