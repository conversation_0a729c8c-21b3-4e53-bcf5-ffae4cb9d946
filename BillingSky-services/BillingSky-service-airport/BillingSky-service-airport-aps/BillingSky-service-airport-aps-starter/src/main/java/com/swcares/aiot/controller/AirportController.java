package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.AirportForm;
import com.swcares.aiot.core.model.entity.AirportInfoAps;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.core.model.vo.AirlinePercentVo;
import com.swcares.aiot.core.model.vo.AirportBillCountVo;
import com.swcares.aiot.core.model.vo.AirportBillVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.AirportService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/*
 * 
 * ClassName：AirportController <br>
 * Description：机场接口类<br>
 * Copyright © 2020-5-14 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020-5-14 15:48<br>
 * @version v1.0 <br>
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/airport")
@Api(value = "AirportController", tags = {"机场操作接口"})
@Slf4j
public class AirportController {
    @Resource
    private AirportService airportService;
    /*
     * Title: pageAirportInfo<br>
     * Author: 李龙<br>
     * Description: 查询集团下所有的机场昨日运行信息<br>
     * Date:  13:34 <br>
     * @param pageParam
     * return: java.lang.Object
     */
    @ApiOperation(value = "查询所有机场昨日运行信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirportBillCountVo.class)})
    @GetMapping(value = "/pageAirportInfo")
    public Object pageAirportInfo(@ApiParam(name = "pageParam", value = "分页信息") @Validated PageParam pageParam) {
        return airportService.pageAirportInfo(pageParam);
    }

    /*
     * Title: saveAirport<br>
     * Author: 李龙<br>
     * Description: 新增集团下管理的机场<br>
     * Date:  13:35 <br>
     * @param airportForm
     * return: java.lang.Object
     */
    @ApiOperation(value = "新增机场")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/saveAirport")
    public Object saveAirport(@RequestParam @ApiParam(name = "urlName",value = "操作页面")String urlName,
                              @RequestBody @ApiParam(name = "airport", value = "机场信息") @Valid AirportForm airportForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airportService.saveAirport(airportForm, user,urlName);
    }

    /*
     * Title: delAirportById<br>
     * Author: 李龙<br>
     * Description: 根据机场id删除机场信息<br>
     * Date:  13:36 <br>
     * @param airportId
     * return: java.lang.Object
     */
    @ApiOperation(value = "删除机场信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @DeleteMapping(value = "/delAirportById")
    public Object delAirportById(@RequestParam @ApiParam(name = "urlName",value = "操作页面")String urlName,
                                 @RequestParam @ApiParam(name = "airportId", value = "机场id") String airportId) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airportService.delAirportById(airportId,user,urlName);
    }

    /*
     * Title: pageAirportDetailsBill<br>
     * Author: 李龙<br>
     * Description: 根据条件分页查询机场的运行数据（未输入条件则查全部）<br>
     * Date:  13:37 <br>
     * @param airportShortName
     * @param startDate
     * @param endDate
     * @param airlineCode
     * @param pageParam
     * return: java.lang.Object
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "分页查询机场的运行数据")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirportBillVo.class)})
    @GetMapping(value = "/pageAirportDetailsBill")
    public Object pageAirportDetailsBill(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                                         @ApiParam(name = "pageParam", value = "分页信息") @Validated PageParam pageParam,
                                         String startDate, String endDate, String airlineCode) {
        return airportService.pageAirportDetailsBill(airportCode, airlineCode, startDate, endDate, pageParam);
    }

    /*
     * Title: getTotalAirportDetailsBill<br>
     * Author: 李龙<br>
     * Description: 获取时间段内总的机场运行数据<br>
     * Date:  11:20 <br>
     * @param airportShortName
     * @param startDate
     * @param endDate
     * @param airlineCode
     * return: java.lang.Object
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query")

    })
    @ApiOperation(value = "获取时间段内总的机场运行数据")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirportBillCountVo.class)})
    @GetMapping(value = "/getTotalAirportDetailsBill")
    public Object getTotalAirportDetailsBill(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true) String airportCode,
                                             String startDate, String endDate, String airlineCode) {

        return airportService.getTotalAirportDetailsBill(airportCode, startDate, endDate, airlineCode);
    }

    /*
     * Title: exportAirportOperationData<br>
     * Author: 李龙<br>
     * Description: 导出根据查询条件查出的机场运行数据<br>
     * Date:  13:37 <br>
     * @param airportShortName
     * @param startDate
     * @param endDate
     * @param airlineCode
     * return: java.lang.Object
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query")

    })
    @ApiOperation(value = "机场运行数据导出")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/exportAirportOperationData")
    public void exportAirportOperationData(@RequestParam @ApiParam(name = "urlName",value = "页面名称")String urlName,
                                           @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                                             String startDate,String endDate,String airlineCode,HttpServletResponse response) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        airportService.exportAirportOperationData(airportCode,startDate,endDate,airlineCode,response,user,urlName);
    }

    /*
     * Title: getAirportById<br>
     * Author: 李龙<br>
     * Description: 根据机场id，查看机场信息,用于机场信息的查看<br>
     * Date:  17:06 <br>
     * @param airportId
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据机场id，查看机场信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirportInfoAps.class)})
    @GetMapping(value = "/getAirportById")
    public Object getAirportById(@RequestParam @ApiParam(name = "airportId", value = "机场ID") String airportId) {
        return airportService.getAirportById(airportId);
    }

    /*
     * Title: getAirlinePercent<br>
     * Author: 李龙<br>
     * Description: 获取航司在机场数据的占比<br>
     * Date:  16:19 <br>
     * @param airportCode
     * @param startDate
     * @param endDate
     * @param airlineCode
     * return: java.lang.Object
     */
    @ApiOperation(value = "获取航司在机场数据的占比")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query")
    })
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirlinePercentVo.class)})
    @GetMapping(value = "/getAirlinePercent")
    public Object getAirlinePercent(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                                    String startDate, String endDate,String airlineCode) {
        return airportService.getAirlinePercent(airportCode, startDate, endDate,airlineCode);
    }
    /*
     * Title: listFlightInfoByFlightTime<br>
     * Author: 李龙<br>
     * Description: 获取日期下的航班信息<br>
     * Date:  13:59 <br>
     * @param airportCode
     * @param detailsDate
     * @param airlineCode
     * return: java.lang.Object
     */
    @ApiOperation(value = "获取日期下的航班信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "detailsDate", value = "日期", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query"),
    })
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FlightInfo.class)})
    @GetMapping(value = "/listFlightInfoByFlightTime")
    public Object listFlightInfoByFlightTime(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true) String airportCode,
                                             String detailsDate,String airlineCode){
        return airportService.listFlightInfoByFlightTime(airportCode,detailsDate,airlineCode);
    }
    /*
     * Title: updateAirport<br>
     * Author: 李龙<br>
     * Description: 修改机场信息<br>
     * Date:  9:51 <br>
     * @param airportForm
     * return: java.lang.Object
     */
    @ApiOperation(value = "修改机场信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PutMapping("/updateAirport")
    public Object updateAirport(@RequestParam @ApiParam(name = "urlName",value = "操作页面") String urlName,
                                @RequestBody @ApiParam(name = "airportForm",value = "机场信息",required = true)@Validated({Update.class}) AirportForm airportForm){
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airportService.updateAirport(airportForm,user,urlName);
    }
    /*
     * Title: getAirportByAirportCode<br>
     * Author: 李龙<br>
     * Description: 根据机场三字码查询机场信息<br>
     * Date:  15:45 <br>
     * @param airportCode
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据机场三字码查询机场信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AirportInfoAps.class)})
    @GetMapping(value = "/getAirportByAirportCode")
    public Object getAirportByAirportCode(@RequestParam @ApiParam(name = "airportCode",value = "机场三字码",required = true)String airportCode){
        return airportService.getAirportByAirportCode(airportCode);
    }
}