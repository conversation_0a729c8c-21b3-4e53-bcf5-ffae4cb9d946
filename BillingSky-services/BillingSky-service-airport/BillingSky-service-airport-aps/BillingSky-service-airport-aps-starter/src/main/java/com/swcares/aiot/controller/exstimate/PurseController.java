package com.swcares.aiot.controller.exstimate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.model.dto.ErPurseDto;
import com.swcares.aiot.core.model.dto.ErPursePageDto;
import com.swcares.aiot.core.model.vo.ErPursePageVo;
import com.swcares.aiot.core.model.vo.ErPursePeVo;
import com.swcares.aiot.core.model.vo.ErPurseVo;
import com.swcares.aiot.service.PurseService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.exstimate.PurseController
 * Description：钱包管理页面controller
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/27 10:30
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/purse")
@Api(value = "PurseController", tags = {"钱包管理接口"})
@ApiVersion("对账通-机场端-离港返还-api")
@Slf4j
public class PurseController {

    @Resource
    private PurseService purseService;

    @PostMapping("/page")
    @ApiOperation(value = "钱包分页查询")
    public BaseResult<Page<ErPursePageVo>> page(@RequestBody @Validated ErPursePageDto erPursePageDto) {
        return purseService.page(erPursePageDto);
    }

    @PostMapping("/addErPurse")
    @ApiOperation(value = "新增钱包")
    public BaseResult<Boolean> addErPurse(@RequestBody @Validated ErPurseDto erPurseDto) {
        return purseService.addErPurse(erPurseDto);
    }

    @PutMapping("/updateErPurse")
    @ApiOperation(value = "编辑钱包")
    public BaseResult<Boolean> updateErPurse(@RequestBody @Validated ErPurseDto erPurseDto) {
        return purseService.updateErPurse(erPurseDto);
    }

    @GetMapping("/getById/{id}")
    @ApiOperation(value = "查询钱包")
    public BaseResult<ErPurseVo> getById(@PathVariable("id") Long id) {
        return purseService.getById(id);
    }

    @GetMapping("/getErPursePeList")
    @ApiOperation(value = "查询收付款方钱包信息")
    public BaseResult<List<ErPursePeVo>> getErPursePeList() {
        return purseService.getErPursePeList();
    }
}
