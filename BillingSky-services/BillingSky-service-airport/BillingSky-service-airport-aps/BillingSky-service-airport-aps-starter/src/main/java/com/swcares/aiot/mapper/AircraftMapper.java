package com.swcares.aiot.mapper;

import com.swcares.aiot.core.entity.AircraftAirlineInfoVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.mapper.AircraftMapper
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/2 10:19
 * @version v1.0
 */
public interface AircraftMapper {

    List<AircraftAirlineInfoVo> listAirlineInfoByRegno(@Param("regNo") String regNo,
                                                       @Param("effectiveDate") LocalDate effectiveDate);

    List<AircraftAirlineInfoVo> listAirlineInfoByAirlineShortName(@Param("airlineShortName") String airlineShortName);
}
