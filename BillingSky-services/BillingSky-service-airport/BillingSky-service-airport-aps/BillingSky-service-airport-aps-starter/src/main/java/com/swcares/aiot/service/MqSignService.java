package com.swcares.aiot.service;

import com.swcares.aiot.core.model.vo.*;
import com.swcares.aiot.core.model.entity.SignDelete;
import com.swcares.aiot.core.vo.SettlementItemVo;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;

import java.util.List;

/**
 * ClassName：com.swcares.service.impl.MqSignService
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/6/23 16:51
 * @version v1.0
 */
public interface MqSignService {
    void saveServiceRecord(List<SignBusDataSettlementVO> vo);
    void saveServiceRecordByCondition(List<SignBusDataSettlementVO> voList, String syncflightNos,
                                             Long syncItemId);
    void itemUpdate(SettlementItemVo ssiv);
    void itemInsert(SettlementItemVo ssi);
    void itemDelete(SignDelete sd);

    void saveBusData(BusDataSendVo vo);

    void saveBusItem(BusItemSendVo itemSendVo);

    void deleteBusItem(BusItemDeleteSendVo itemSendVo);

}
