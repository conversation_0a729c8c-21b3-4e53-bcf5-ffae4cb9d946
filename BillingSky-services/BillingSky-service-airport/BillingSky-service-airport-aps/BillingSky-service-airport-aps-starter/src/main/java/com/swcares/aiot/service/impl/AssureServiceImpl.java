package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.swcares.aiot.client.IBillBusDataClient;
import com.swcares.aiot.client.IBillItemClient;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.entity.TAirportSignItem;
import com.swcares.baseframe.common.enums.DeletedEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.VariableGuaranteeForm;
import com.swcares.aiot.core.form.VariableGuaranteeUpdateForm;
import com.swcares.aiot.core.model.entity.BillItem;
import com.swcares.aiot.core.model.entity.RulesVariableRecord;
import com.swcares.aiot.core.model.entity.VariableGuarantee;
import com.swcares.aiot.core.model.vo.VariableGuaranteeVo;
import com.swcares.aiot.core.model.vo.VariableIdVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.core.service.ITAirportSignItemService;
import com.swcares.aiot.core.vo.SettlementItemVo;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;
import com.swcares.aiot.dao.BillItemDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.aiot.dao.VariableGuaranteeDao;
import com.swcares.aiot.dao.VariableRecordDao;
import com.swcares.aiot.service.AssureService;
import com.swcares.aiot.service.MqSignService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.service.impl.AssureMqServiceImpl
 * Description：节点保障系统对接mqservice
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/4/24 11:38
 * @version v1.0
 */
@Service
@Slf4j
public class AssureServiceImpl implements AssureService {
    @Resource
    private BillItemDao billItemDao;
    @Resource
    private VariableGuaranteeDao variableGuaranteeDao;
    @Resource
    private VariableRecordDao variableRecordDao;
    @Resource
    private IBillItemClient iBillItemClient;
    @Resource
    private IBillBusDataClient iBillBusDataClient;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private MqSignService mqSignService;
    @Resource
    private IConfConfigBizClient iConfConfigBizClient;
    @Resource
    private ITAirportSignItemService itAirportSignItemService;

    @Override
    public Object getVariableId(String variableName, String airportCode) {
        List<RulesVariableRecord> list =
                variableRecordDao.getVariableRecordByFuzzySearch(variableName, airportCode);
        List<VariableIdVo> resList = new ArrayList<>();
        List<String> useTimeList = variableRecordDao.getUseTimeCodeList();
        List<String> useNumberList = variableRecordDao.getUseNumberCodeList();
        for (RulesVariableRecord rvr : list) {
            String variable = rvr.getVariable();
            if ((!useTimeList.contains(variable))
                    && (!useNumberList.contains(variable))) {
                continue;
            }
            VariableIdVo vi = new VariableIdVo();
            vi.setVariableId(rvr.getId());
            vi.setVariableName(rvr.getVariableName());
            resList.add(vi);
        }
        return new ResultBuilder.Builder<List<VariableIdVo>>().data(resList).builder();
    }

    @Override
    public Object getMatchingRelationship(PageParam pageParam, String variableId,
                                          String airportCode) {
        String org = getSignDataOrg(airportCode);
        List<VariableGuaranteeVo> resList = new ArrayList<>();
        if("DATA_CENTER".equals(org)) {
            //查询数据中心匹配关系
            dataCenterQuery(variableId,airportCode,resList);
        }else if("THIRD_PARTY".equals(org)) {
            //查询第三方匹配关系
            thirdPartyQuery(variableId, airportCode, resList);
        }else{
            //查询电子签单匹配关系
            signQuery(variableId, airportCode, resList);
        }
        Pager<VariableGuaranteeVo> pagerList = new Pager<>(pageParam.getPage(), pageParam.getLimit(), resList);
        return new ResultBuilder.Builder<Pager<VariableGuaranteeVo>>().data(pagerList).builder();
    }

    private String getSignDataOrg(String airportCode) {
        String org = "SIGN";
        try {
            BaseResult<JSONObject> jsonObjectBaseResult = iConfConfigBizClient.getConfig("ELECTRONIC_SIGNING_DICTIONARY_TABLE");
            if (ObjectUtil.isEmpty(jsonObjectBaseResult) || ObjectUtil.isEmpty(jsonObjectBaseResult.getData())) {
                log.error("{}机场未配置签单数据来源 ！ jsonObjectBaseResult = {}", airportCode, jsonObjectBaseResult);
            }

            Map<String, String> collectionSourceMap =
                    jsonObjectBaseResult.getData().toJavaObject(new TypeReference<Map<String, String>>() {
                    });
            String orgTemp = collectionSourceMap.get(airportCode);
            if (StringUtils.isNotBlank(orgTemp)) {
                org = orgTemp;
            }
        }catch (Exception e){
            log.error("获取配置中心签单来源异常，e:{0}",e);
        }
        return org;
    }

    private void signQuery(String variableId, String airportCode, List<VariableGuaranteeVo> resList) {
        List<Object[]> list =
                variableGuaranteeDao.getVariableGuaranteeList(variableId, airportCode);

        for (Object[] obj : list) {
            String variable = (String) obj[12];
            List<String> useTimeList = variableRecordDao.getUseTimeCodeList();
            List<String> useNumberList = variableRecordDao.getUseNumberCodeList();
            if ((!useTimeList.contains(variable))
                    && (!useNumberList.contains(variable)) && !"O".equalsIgnoreCase((String) obj[13])) {
                continue;
            }
            VariableGuaranteeVo vgo = new VariableGuaranteeVo();

            vgo.setVariableName((String) obj[0]);
            vgo.setDataFormat((String) obj[1]);
            vgo.setId((String) obj[2]);
            vgo.setVariableId((String) obj[3]);
            vgo.setItemId(obj[4] == null ? null : obj[4].toString());
            vgo.setLandFlag((String) obj[5]);
            vgo.setDataUpdate((String) obj[6]);
            vgo.setConversionRules((String) obj[7]);
            vgo.setRemark((String) obj[8]);

            vgo.setInvalidDate(DateUtils.format((Date) obj[9], DateUtils.FLIGHT_TIME_PATTERN));
            vgo.setAirportCode((String) obj[10]);
            vgo.setItemName((String) obj[11]);
            vgo.setType((String) obj[14]);
            resList.add(vgo);
        }
    }

    private void dataCenterQuery(String variableId, String airportCode, List<VariableGuaranteeVo> resList) {
        List<Object[]> list =
                variableGuaranteeDao.getDataCenterVariableGuaranteeList(variableId, airportCode);

        for (Object[] obj : list) {
            String variable = (String) obj[12];
            List<String> useTimeList = variableRecordDao.getUseTimeCodeList();
            List<String> useNumberList = variableRecordDao.getUseNumberCodeList();
            if ((!useTimeList.contains(variable))
                    && (!useNumberList.contains(variable)) && !"O".equalsIgnoreCase((String) obj[13])) {
                continue;
            }
            VariableGuaranteeVo vgo = new VariableGuaranteeVo();

            vgo.setVariableName((String) obj[0]);
            vgo.setDataFormat((String) obj[1]);
            vgo.setId((String) obj[2]);
            vgo.setVariableId((String) obj[3]);
            vgo.setItemId(obj[4] == null ? null : obj[4].toString());
            vgo.setLandFlag((String) obj[5]);
            vgo.setDataUpdate((String) obj[6]);
            vgo.setConversionRules((String) obj[7]);
            vgo.setRemark((String) obj[8]);

            vgo.setInvalidDate(DateUtils.format((Date) obj[9], DateUtils.FLIGHT_TIME_PATTERN));
            vgo.setAirportCode((String) obj[10]);
            vgo.setItemName((String) obj[11]);
            vgo.setType((String) obj[14]);
            resList.add(vgo);
        }
    }

    private void thirdPartyQuery(String variableId, String airportCode, List<VariableGuaranteeVo> resList) {
        List<Object[]> thirdPartylist =
                variableGuaranteeDao.getThirdPartyVariableGuaranteeList(variableId, airportCode);
        for (Object[] obj : thirdPartylist) {
            String variable = (String) obj[12];
            List<String> useTimeList = variableRecordDao.getUseTimeCodeList();
            List<String> useNumberList = variableRecordDao.getUseNumberCodeList();
            if ((!useTimeList.contains(variable))
                    && (!useNumberList.contains(variable))) {
                continue;
            }
            VariableGuaranteeVo vgo = new VariableGuaranteeVo();
            vgo.setVariableName((String) obj[0]);
            vgo.setDataFormat((String) obj[1]);
            vgo.setId((String) obj[2]);
            vgo.setVariableId((String) obj[3]);
            vgo.setLandFlag((String) obj[5]);
            vgo.setDataUpdate((String) obj[6]);
            vgo.setConversionRules((String) obj[7]);
            vgo.setRemark((String) obj[8]);
            vgo.setInvalidDate(DateUtils.format((Date) obj[9], DateUtils.FLIGHT_TIME_PATTERN));
            vgo.setAirportCode((String) obj[10]);
            vgo.setItemName((String) obj[11]);
            vgo.setType((String) obj[14]);
            resList.add(vgo);
        }
    }

    @Override
    public Object addMatchingRelationship(VariableGuaranteeForm form, LoginUserDetails user) {

        String org = getSignDataOrg(form.getAirportCode());
        String type;
        if("SIGN".equals(org)) {
            type="1";
        }else {
            type="3";
        }

        String variableId = form.getVariableId();
        if (CharSequenceUtil.isEmpty(variableId)) {
            throw new GenericException(BusinessMessageEnum.ASSURE_SAVE_ERROR_1.getCode(), BusinessMessageEnum.ASSURE_SAVE_ERROR_1.getMsg());
        } else {
            variableRecordDao.getById(variableId);
        }
        List<VariableGuarantee> vgList;
        if("SIGN".equals(org)) {
            vgList = variableGuaranteeDao.getVariableGuaranteeByVariableId(variableId, type);
        }else{
            vgList = variableGuaranteeDao.getAirportVariableGuaranteeByVariableId(variableId, type);
        }
        if (!vgList.isEmpty()) {
            throw new GenericException(BusinessMessageEnum.ADD_MATCHING_RELATIONSHIP_ERROR.getCode(), BusinessMessageEnum.ADD_MATCHING_RELATIONSHIP_ERROR.getMsg());
        }
        if (CharSequenceUtil.isEmpty(form.getItemId())) {
            throw new GenericException(BusinessMessageEnum.ASSURE_SAVE_ERROR_2.getCode(), BusinessMessageEnum.ASSURE_SAVE_ERROR_2.getMsg());
        }


        if (CharSequenceUtil.isEmpty(form.getLandFlag())) {
            throw new GenericException(BusinessMessageEnum.ASSURE_SAVE_ERROR_3.getCode(), BusinessMessageEnum.ASSURE_SAVE_ERROR_3.getMsg());
        }
        if (CharSequenceUtil.isEmpty(form.getDataUpdate())) {
            throw new GenericException(BusinessMessageEnum.ASSURE_SAVE_ERROR_4.getCode(), BusinessMessageEnum.ASSURE_SAVE_ERROR_4.getMsg());
        }
        if (CharSequenceUtil.isEmpty(form.getConversionRules())) {
            throw new GenericException(BusinessMessageEnum.ASSURE_SAVE_ERROR_5.getCode(), BusinessMessageEnum.ASSURE_SAVE_ERROR_5.getMsg());
        }
        if (CharSequenceUtil.isEmpty(form.getAirportCode())) {
            throw new GenericException(BusinessMessageEnum.ASSURE_SAVE_ERROR_7.getCode(), BusinessMessageEnum.ASSURE_SAVE_ERROR_7.getMsg());
        }
        VariableGuarantee vg = new VariableGuarantee();
        copyPropertiesIgnoreNull(form, vg);
        Date date = new Date();
        vg.setInvalidDate(new Date());
        vg.setModifiedTime(date);
        vg.setCreateTime(date);
        vg.setModifiedBy(user.getUsername());
        vg.setCreateBy(user.getUsername());
        vg.setInvalid("1");
        vg.setType(type);
        variableGuaranteeDao.save(vg);
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public Object updateMatchingRelationship(VariableGuaranteeUpdateForm form,
                                             LoginUserDetails user) {
        VariableGuarantee vg;
        if (CharSequenceUtil.isEmpty(form.getId())
                || (vg = variableGuaranteeDao.getVariableGuaranteeById(form.getId())) == null) {
            throw new GenericException(BusinessMessageEnum.ASSURE_UPDATE_ERROR_1.getCode(),
                    BusinessMessageEnum.ASSURE_UPDATE_ERROR_1.getMsg());
        }
        copyPropertiesIgnoreNull(form, vg);
        vg.setInvalidDate(new Date());
        vg.setModifiedBy(user.getUsername());
        vg.setModifiedTime(new Date());
        variableGuaranteeDao.save(vg);
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public Object deletedMatchingRelationship(String id, LoginUserDetails user) {
        VariableGuarantee vg = variableGuaranteeDao.getVariableGuaranteeById(id);
        vg.setInvalid("0");
        vg.setModifiedBy(user.getUsername());
        vg.setModifiedTime(new Date());
        variableGuaranteeDao.save(vg);
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public Object getBillItemList(String airportCode) {
        String org = getSignDataOrg(airportCode);
        if("SIGN".equals(org)) {
            List<BillItem> list = billItemDao.getBillItemList(airportCode);
            return new ResultBuilder.Builder<>().data(list).builder();
        }else {
            List<TAirportSignItem> list =itAirportSignItemService.lambdaQuery().eq(TAirportSignItem::getAirportCode,airportCode)
                    .eq(TAirportSignItem::getDeleted,Boolean.FALSE).list();
            return new ResultBuilder.Builder<>().data(list).builder();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importItemAll() {
        List<SettlementItemVo> list = iBillItemClient.getItemAll(TenantHolder.getTenant());
        if (list.isEmpty()) {
            throw new GenericException(BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getCode(), BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getMsg());
        }
        List<BillItem> oldBiAll = billItemDao.getBillItemAll();
        Map<String, BillItem> map = oldBiAll.stream().collect(Collectors.toMap(BillItem::getId, Function.identity()));
        // 字段保存list
        List<BillItem> saveList = new ArrayList<>();
        // 匹配关系删除list
        List<String> vgDeleteList = new ArrayList<>();
        for (SettlementItemVo ssiv : list) {
            if (ObjectUtil.isEmpty(ssiv) || ObjectUtil.isEmpty(ssiv.getTenantId())) {
                throw new GenericException(BusinessMessageEnum.SIGN_ENTITY_TENANT_ID_NULL.getCode(), BusinessMessageEnum.SIGN_ENTITY_TENANT_ID_NULL.getMsg());
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
            ssiv.setAirportCode(airportCode);
            BillItem oldBi;
            if (map.containsKey(String.valueOf(ssiv.getId()))) {
                oldBi = map.get(String.valueOf(ssiv.getId()));
                map.remove(String.valueOf(ssiv.getId()));
            } else {
                oldBi = new BillItem();
            }
            if ("1".equals(String.valueOf(ssiv.getDeleted().getValue()))
                    && oldBi.getDeleted() != null && "0".equals(oldBi.getDeleted())) {
                // 删除匹配关系
                vgDeleteList.add(oldBi.getId());

            }
            copyPropertiesIgnoreNull(ssiv, oldBi);
            // 设置id
            oldBi.setId(String.valueOf(ssiv.getId()));
            // 设置删除字段
            oldBi.setDeleted(String.valueOf(ssiv.getDeleted().getValue()));

            if (oldBi.getCreatedTime() == null) {
                oldBi.setCreatedBy("电子签单自动导入-sign");
                oldBi.setCreatedTime(new Date());
            }
            oldBi.setUpdatedBy("电子签单自动导入-sign");
            oldBi.setUpdatedTime(new Date());
            saveList.add(oldBi);

        }
        // 将map中剩下的item设置为删除，并删除匹配关系
        for (Map.Entry<String, BillItem> entry : map.entrySet()) {
            BillItem it = entry.getValue();
            it.setDeleted(String.valueOf(DeletedEnum.DELETED.getValue()));
            saveList.add(it);
            vgDeleteList.add(it.getId());
        }
        // 删除匹配关系
        variableGuaranteeDao.deleteAllByItemIdList(vgDeleteList);
        // 保存新的item
        billItemDao.saveAll(saveList);

        log.info("导入字段信息：");
        log.info(saveList.toString());

    }

    @Transactional
    @Override
    public void importSignBusDataSettlement(String synStartDate, String synEndDate,
                                            String syncflightNos, String serviceRecordId, String airportCode) {
        // 判断日期格式是否正确
        if ((!DateUtils.isValidDate(synStartDate)) || !DateUtils.isValidDate(synEndDate)) {
            throw new GenericException(BusinessMessageEnum.DATE_FORMAT_ERROR.getCode(),
                    BusinessMessageEnum.DATE_FORMAT_ERROR.getMsg());
        }
        //如果当前输入航班不为空，判断输入航班是否存在
        if (CharSequenceUtil.isNotEmpty(syncflightNos)) {
            List<String> flightNoList = Arrays.asList(syncflightNos.replace(CharSequenceUtil.SPACE, CharSequenceUtil.EMPTY).split(StrPool.COMMA));
            if (flightInfoDao.countFlightNo(flightNoList, airportCode, synStartDate, synEndDate) == 0) {
                throw new GenericException(BusinessMessageEnum.FLIGHT_NOT_FOUND_ERROR.getCode(), BusinessMessageEnum.FLIGHT_NOT_FOUND_ERROR.getMsg());
            }
        }
        // 判断当前serviceRecordId是否建立关联
        List<Object[]> vgList =
                variableGuaranteeDao.getVariableGuaranteeList(serviceRecordId, airportCode);
        if (vgList.isEmpty() || vgList.get(0)[4] == null) {
            throw new GenericException(BusinessMessageEnum.VARIABLE_GUARANTEE_ERROR.getCode(),
                    BusinessMessageEnum.VARIABLE_GUARANTEE_ERROR.getMsg());
        }
        if (vgList.size() > 1) {
            throw new GenericException(
                    BusinessMessageEnum.VARIABLE_GUARANTEE_TOO_MUCH_ERROR.getCode(),
                    BusinessMessageEnum.VARIABLE_GUARANTEE_TOO_MUCH_ERROR.getMsg());
        }
        Long itemId = Long.valueOf((vgList.get(0)[4]).toString());
        synStartDate += " 00:00:00";
        synEndDate += " 23:59:59";
        List<SignBusDataSettlementVO> list;
        try {
            list = iBillBusDataClient.selectSignItemByFlightDate(synStartDate, synEndDate, TenantHolder.getTenant(), itemId);
        } catch (Exception e) {
            log.error("连接签单系统异常", e);
            throw new GenericException(BusinessMessageEnum.SIGN_CONNECT_ERROR.getCode(), BusinessMessageEnum.SIGN_CONNECT_ERROR.getMsg());
        }
        if (list == null || list.isEmpty()) {
            throw new GenericException(BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getCode(), BusinessMessageEnum.SIGN_LIST_NULL_ERROR.getMsg());
        }

        mqSignService.saveServiceRecordByCondition(list, syncflightNos, itemId);
    }


    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

}
