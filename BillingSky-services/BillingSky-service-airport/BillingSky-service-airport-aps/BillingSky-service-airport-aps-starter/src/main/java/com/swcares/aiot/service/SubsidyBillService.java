package com.swcares.aiot.service;

import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.form.SubsidyDetailBillUpdateForm;
import com.swcares.aiot.core.form.SubsidyFormulaBillCountForm;
import com.swcares.aiot.core.form.SubsidyFormulaBillExportForm;
import com.swcares.aiot.core.form.SubsidyFormulaBillForm;
import com.swcares.aiot.core.param.PageParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.service.SubsidyBillService
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/23 10:32
 * @version v1.0
 */
public interface SubsidyBillService {

    List getFormulaNums(SubsidyFormulaBillCountForm form);

    Object[] pageSubsidyFormulaBill(SubsidyFormulaBillForm form, PageParam pageParam);

    Object[] pageSubsidyDetailBill(String formulaBillId, PageParam pageParam);

    Object[] countPageSubsidyDetailBill(String formulaBillId);

    boolean deleteBill(String formulaBillId, LoginUserDetails user);

    boolean updateDetailBill(SubsidyDetailBillUpdateForm form, LoginUserDetails user);

    void exportSubsidyFomrlaBill(SubsidyFormulaBillExportForm form, HttpServletResponse res);
}
