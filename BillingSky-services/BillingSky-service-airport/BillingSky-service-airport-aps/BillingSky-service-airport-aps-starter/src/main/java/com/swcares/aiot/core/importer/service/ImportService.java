package com.swcares.aiot.core.importer.service;

import com.swcares.aiot.core.importer.entity.ApsFlightInfo;
import com.swcares.aiot.core.importer.entity.BaseFlightInfo;
import com.swcares.aiot.core.importer.entity.FlightInfoSegment;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.baseframe.common.security.LoginUserDetails;

import java.util.List;

/**
 * ClassName：com.swcares.importer.service.ImportService
 * Description：导入春秋航空航班数据service
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/2/17 10:38
 * @version v1.0
 */
public interface ImportService {


    void cacheFlightData(BaseFlightInfo bfi, ApsFlightInfo fi, String flightInfoFlag, String airportCode, String flightFlag, ApsFlightInfo origin, Integer dataStatus);

    void cacheFlightCargoTravelerData(ApsFlightInfo fi, ApsFlightInfo origin, boolean traveller, Integer dataStatus);

    void cacheSplitFlightCargoTravelerData(FlightInfoSegment fis, ApsFlightInfo afi);

    void syncFlightData(List<FlightInfo> allById);
}
