package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.AirlineForm;
import com.swcares.aiot.core.model.entity.AirlineInfoAps;
import com.swcares.aiot.core.model.vo.AirlineVo;
import com.swcares.aiot.core.model.vo.UnmaintainedAirlineVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * ClassName：com.swcares.modules.settlement.service.airline.AirlineService <br>
 * Description：航司操作service<br>
 * Copyright © 2020-5-25 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-5-25 16:21<br>
 * @version v1.0 <br>
 */
public interface AirlineService {
    /**
     * Title: saveAirlineInfo<br>
     * Author: 李龙<br>
     * Description: 新增航司<br>
     * Date:  16:57 <br>
     *
     * @param airlineForm :
     * @param user        :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> saveAirlineInfo(AirlineForm airlineForm, LoginUserDetails user, String urlName);

    /**
     * Title: <br>
     * Author: 李龙<br>
     * Description: 根据id，删除航司<br>
     * Date:  16:57 <br>
     *
     * @return :
     */
    ResultBuilder<Object> delAirlineById(String id, LoginUserDetails user, String urlName);

    /**
     * Title: <br>
     * Author: 李龙<br>
     * Description: 修改航司<br>
     * Date:  16:59 <br>
     *
     * @return :
     */
    ResultBuilder<Object> updateAirlineInfoById(AirlineForm airlineForm, LoginUserDetails user, String urlName);

    /**
     * Title: <br>
     * Author: 李龙<br>
     * Description: 根据id查询航司信息<br>
     * Date:  17:02 <br>
     *
     * @return :
     */
    ResultBuilder<AirlineInfoAps> getAirlineById(String id);

    /**
     * Title: <br>
     * Author: 李龙<br>
     * Description: 航司动态分页查询<br>
     * Date:  11:05 <br>
     *
     * @return :
     */
    ResultBuilder<Page<AirlineVo>> pageAirlineInfoByCondition(String airportShortName, String airlineCode, String airlineShortName, String settleCode,String isExpired, PageParam page);

    /**
     * Title: <br>
     * Author: 李龙<br>
     * Description: 根据航司简称查询航司<br>
     * Date:  14:16 <br>
     *
     * @return :
     */
    ResultBuilder<List<AirlineInfoAps>> getAirlineInfoByShortName(String airlineShortName);

    /**
     * Title: listNotStoredAirlineInfoByAirport<br>
     * Author: 李龙<br>
     * Description: 查询未维护的航司<br>
     * Date:  14:48 <br>
     *
     * @return : ResultBuilder
     */
    ResultBuilder<List<UnmaintainedAirlineVo>> listNotStoredAirlineInfoByAirport(String airportCode);

    /**
     * Title: listMaintainedAirlineByairport<br>
     * Author: 李龙<br>
     * Description: 查询已维护的航司<br>
     * Date:  9:48 <br>
     *
     * @param airportShortName :
     * @return : ResultBuilder
     */
    ResultBuilder<List<AirlineInfoAps>> listMaintainedAirlineByAirport(String airportShortName);

    /**
     * Title: listAirlineByAirlineCode<br>
     * Author: 李龙<br>
     * Description: 根据航司二字码查询航司<br>
     * Date:  17:21 <br>
     *
     * @return : ResultBuilder
     */
    ResultBuilder<List<AirlineInfoAps>> listAirlineByAirlineCode(String airlineCode, String airportCode);

    /**
     * Title: importAirlineInfo<br>
     * Author: 李龙<br>
     * Description: 导入航司基础数据<br>
     * Date:  13:44 <br>
     *
     * @param file :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> importAirlineInfo(MultipartFile file);

    /**
     * Title: listMaintainedAirlineCodeByAirport<br>
     * Author: 李龙<br>
     * Description: (查询已维护航司的航司代码（不重复）)<br>
     * Date:  14:48 <br>
     *
     * @param airportCode :
     * @return : ResultBuilder
     */
    ResultBuilder<List<AirlineVo>> listMaintainedAirlineCodeByAirport(String airportCode);

    /**
     * 根据机场三字码查询该机场已维护的航司二字码
     *
     * @param airportCode :
     * @return :
     */
    ResultBuilder<List<String>> listAirlineCodeByAirportCode(String airportCode);

    /**
     * 航空公司简称模糊搜索
     *
     * @param airlineShortName :
     * @return :
     */
    ResultBuilder<List<String>> listByAirlineShortName(String airlineShortName);

    /**
     * 根据机场三字码查询该机场已维护的航司结算代码
     *
     * @param airportCode :
     * @return :
     */
    ResultBuilder<List<String>> listSettleCodeByAirportCode(String airportCode);

    /**
     * Title: 根据当前日期，查询近一个月有效航班数据对应的机号对应的航司，是否配置有效费用规则<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/1/10 16:44 <br>
     */
    ResultBuilder<Object> findInvalidAirline();

    /**
     * Title：listAllValidAirline <br>
     * Description： <br>
     *
     * @return : null
     * author ：lihuanyu <br>
     * date ：2022/10/18 15:37 <br>
     */
    ResultBuilder<List<AirlineInfoAps>> listAllValidAirline(String airportCode);

    /**
     * Title：changeAirlineSpecialCmbFee <br>
     * Description：修改特殊货邮服务费 <br>
     *
     * @return : null
     * author ：lihuanyu <br>
     * date ：2022/10/18 15:37 <br>
     */
    ResultBuilder<Object> changeAirlineSpecialCmbFee(List<String> ids, String airportCode);

    /**
     * Title: listExpiredAirlineShortName
     * Description: 获取有过期费用的航司
     * @return : ResultBuilder
     * author: liuzhiheng
     * Date: 2024-10-10 09:26:48
     */
    ResultBuilder<List<String>> listExpiredAirlineShortName();
}
