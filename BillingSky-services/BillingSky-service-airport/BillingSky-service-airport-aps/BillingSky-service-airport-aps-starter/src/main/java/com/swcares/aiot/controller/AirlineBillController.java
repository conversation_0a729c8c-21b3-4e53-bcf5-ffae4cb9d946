package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.AirlineBillForm;
import com.swcares.aiot.core.model.dto.AirlineBillExportDto;
import com.swcares.aiot.core.model.dto.AirlineBillPageDto;
import com.swcares.aiot.core.model.dto.AirlineBillTaxRateDto;
import com.swcares.aiot.core.model.vo.AirlineBillCountVo;
import com.swcares.aiot.core.model.vo.AirlineBillCountVoNew;
import com.swcares.aiot.core.model.vo.AirlineBillPageVo;
import com.swcares.aiot.core.model.vo.AirlineBillVo;
import com.swcares.aiot.service.AirlineBillNewService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.RestBaseController;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.example.mydemo3.controller.settleBill.AirlineBillController <br>
 * Description：(航司账单接口)<br>
 * Copyright © 2020/5/18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/5/18 10:40<br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@RequestMapping("/api/airlineBill")
@Api(value = "AirlineBillController", tags = {"航司账单接口"})
public class AirlineBillController extends RestBaseController {

    @Resource
    private AirlineBillNewService airlineBillService;

    @PostMapping("/pageAirlineBillInfoByCondition")
    @ApiOperation(value = "航司账单动态条件分页查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = AirlineBillVo.class)})
    public PagedResult<List<AirlineBillPageVo>> pageAirlineBillInfoByCondition(@RequestBody @Validated AirlineBillPageDto dto) {
        return ok(airlineBillService.pageAirlineBillInfoByCondition(dto));
    }

    @PostMapping("/udpateAirlineBillTaxRate")
    @ApiOperation(value = "航司账单税率数据更新")
    public Object udpateAirlineBillTaxRate(@RequestBody @Validated AirlineBillTaxRateDto airlineBillTaxRateDto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airlineBillService.updateAirlineBillTaxRate(airlineBillTaxRateDto, user);
    }

    /**
     * Title: updateAirlineBillInfo<br>
     * Author: 叶咏秋<br>
     * Description: (航司账单调整金额和拒付金额数据更新)<br>
     * Date:  13:48 <br>
     *
     * @param airlineBillForm return: java.lang.Object
     */
    @PutMapping("/updateAirlineBillInfo")
    @ApiOperation(value = "航司账单调整金额和拒付金额数据更新")
    public Object updateAirlineBillInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                        @ApiParam(name = "airlineBillForm", value = "航司账单信息更新条件表单")
                                        @RequestBody @Validated({Update.class}) AirlineBillForm airlineBillForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return airlineBillService.updateAirlineBillInfo(airlineBillForm, user, urlName);
    }

    /**
     * Title: countTotal<br>
     * Author: 叶咏秋<br>
     * Description: (航司账单金额总计)<br>
     * Date:  13:56 <br>
     *
     * @param airlineBillDto return: java.lang.Object
     */
    @PostMapping("/countTotal")
    @ApiOperation(value = "航司账单金额总计")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功", response = AirlineBillCountVo.class)})
    public BaseResult<AirlineBillCountVoNew> countTotal(@RequestBody @Validated AirlineBillPageDto airlineBillDto) {
        return ok(airlineBillService.countTotal(airlineBillDto));
    }

    @PostMapping(value = "/exportAirlineBillInfo")
    @ApiOperation(value = "导出航司账单")
    public void exportAirlineBillInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                      @RequestBody @Validated AirlineBillExportDto airlineBillExportDto,
                                      HttpServletResponse response) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        airlineBillService.exportAirlineBillInfo(airlineBillExportDto, response, user, urlName);
    }
}
