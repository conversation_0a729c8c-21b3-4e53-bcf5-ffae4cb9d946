package com.swcares.aiot.core.annotation;

import java.lang.annotation.*;

/*
 *
 * ClassName：OperateLog <br>
 * Description：<br>
 * Copyright © 2020-10-12 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021-4-14 13:20<br>
 * @version v1.0 <br>
 */
@Documented
@Retention(value = RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface OperateLog {

    //操作类型
    String operateType() default "";

    //操作对象
    String operateObject() default "";

    //需要用到的mapper
    String mapper() default "";
}
