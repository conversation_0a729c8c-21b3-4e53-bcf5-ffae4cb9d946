package com.swcares.aiot.service;

import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.PassengerInfoForm;
import com.swcares.aiot.core.form.PassengerInfoSearchForm;
import com.swcares.aiot.core.param.PageParam;

import javax.servlet.http.HttpServletResponse;

/**
 * ClassName：PassengerInfoService <br>
 * Description：(旅客信息业务逻辑层接口)<br>
 * Copyright © 2020/6/3 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020/6/3 14:21<br>
 * @version v1.0 <br>
 */
public interface PassengerInfoService {
    /**
     * Title: pagePassengerInfoByFlightId<br>
     * Author: 叶咏秋<br>
     * Description: (通过航班id分页查询该航班所有旅客信息)<br>
     * Date:  14:15 <br>
     * @param pageParam
     * @param flightId
     * return: ResultBuilder
     */
    ResultBuilder pagePassengerInfoByFlightId(PageParam pageParam, String flightId);

    /**
     * Title: pagePassengerInfoByCondition<br>
     * Author: 叶咏秋<br>
     * Description: (动态条件分页查询指定航班的旅客信息)<br>
     * Date:  17:45 <br>
     * @param pageParam
     * return: ResultBuilder
     */
    ResultBuilder pagePassengerInfoByCondition(PageParam pageParam, PassengerInfoSearchForm passengerInfoSearchForm);

    /**
     * Title: savePassengerInfo<br>
     * Author: 叶咏秋<br>
     * Description: (新增旅客信息)<br>
     * Date:  18:19 <br>
     * @param passengerInfoForm
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder savePassengerInfo(PassengerInfoForm passengerInfoForm, LoginUserDetails user, String urlName);

    /**
     * Title: getPassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (通过旅客id查询旅客信息)<br>
     * Date:  9:40 <br>
     * @param pid
     * return: ResultBuilder
     */
    ResultBuilder getPassengerInfoById(String pid);

    /**
     * Title: updatePassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (更新旅客信息)<br>
     * Date:  18:35 <br>
     * @param passengerInfoForm
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder updatePassengerInfoById(PassengerInfoForm passengerInfoForm, LoginUserDetails user, String urlName);

    /**
     * Title: delPassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (批量删除旅客信息,即数据有效状态设置为0)<br>
     * Date:  9:15 <br>
     * @param ids
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder delPassengerInfoById(String ids, LoginUserDetails user, String urlName);

    /**
     * Title: exportPassengerInfoBatch<br>
     * Author: 叶咏秋<br>
     * Description: (批量导出旅客信息)<br>
     * Date:  11:25 <br>
     * @param passengerInfoSearchForm
     * @param response
     * return: void
     */
    void exportPassengerInfoBatch(PassengerInfoSearchForm passengerInfoSearchForm, HttpServletResponse response, String urlName,LoginUserDetails user);
}
