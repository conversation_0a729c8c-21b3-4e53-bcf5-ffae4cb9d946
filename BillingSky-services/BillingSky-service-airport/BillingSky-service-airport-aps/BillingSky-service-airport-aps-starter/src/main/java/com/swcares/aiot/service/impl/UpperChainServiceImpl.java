package com.swcares.aiot.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.swcares.aiot.client.IUpperChainClient;
import com.swcares.aiot.core.common.enums.FlightDateTypeEnum;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.entity.TFlightBillHistory;
import com.swcares.aiot.core.form.SubmitForm;
import com.swcares.aiot.core.model.dto.FlightBillChainDto;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.model.vo.FlightBillChainVo;
import com.swcares.aiot.core.model.vo.TFlightBillVO;
import com.swcares.aiot.core.service.ITFlightBillService;
import com.swcares.aiot.service.FlightBillHistoryService;
import com.swcares.aiot.service.UpperChainService;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * Description
 * date 2024/9/29
 */
@Slf4j
@Service
public class UpperChainServiceImpl implements UpperChainService {

    @Resource
    private ITFlightBillService itFlightBillService;

    @Resource
    private FlightBillHistoryService flightBillHistoryService;

    private static final LocalTime LOCAL_TIME = LocalTime.of(23, 59, 59);

    @Resource
    private IUpperChainClient iUpperChainClient;

    @Override
    public List<TFlightBill> getTFlightBill(SubmitForm form) {
        List<String> airlineCodeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(form.getAirlineCode())) {
            airlineCodeList = Arrays.asList(form.getAirlineCode().split(","));
        }
        LambdaQueryWrapper<TFlightBill> queryWrapper = new QueryWrapper<TFlightBill>().lambda()
                .in(CollUtil.isNotEmpty(airlineCodeList), TFlightBill::getSettleCode, airlineCodeList)
                .eq(TFlightBill::getSubmit, 0)
                .eq(TFlightBill::getInvalid, 1)
                .eq(StringUtils.isNotEmpty(form.getAirportCode()), TFlightBill::getAirportCode, form.getAirportCode())
                .eq(StringUtils.isNotEmpty(form.getFeeCode()), TFlightBill::getFeeCode, form.getFeeCode());
        if (FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue().equals(form.getDateType())) {
            queryWrapper.between(TFlightBill::getFlightDate, form.getStartDate(), form.getEndDate());
        } else {
            queryWrapper.between(TFlightBill::getFlightTime,
                    DateUtils.dateToLdt(form.getStartDate()),
                    LocalDateTime.of(DateUtils.dateToLd(form.getEndDate()), LOCAL_TIME));
        }
        return itFlightBillService.list(queryWrapper);
    }

    @Override
    @Transactional
    public void upperChain(List<TFlightBill> tFlightBillList) {
        if (CollUtil.isNotEmpty(tFlightBillList)) {
            Long tenantId = TenantHolder.getTenant();
            SecurityContext context = SecurityContextHolder.getContext();
            ThreadUtil.execute(() -> {
                try {
                    SecurityContextHolder.setContext(context);
                    TenantHolder.setTenant(tenantId);
                    log.info("机场端租户ID：{}", tenantId);
                    List<String> ids = tFlightBillList.stream().map(TFlightBill::getId).collect(Collectors.toList());
                    List<TFlightBillVO> tFlightBillVOList = tFlightBillList.stream().map(k -> {
                        TFlightBillVO tFlightBillVO = new TFlightBillVO();
                        BeanUtil.copyProperties(k, tFlightBillVO);
                        return tFlightBillVO;
                    }).collect(Collectors.toList());
                    // 获取历史数据
                    List<TFlightBillHistory> tFlightBillHistoryByFlightBillIds = flightBillHistoryService.getTFlightBillHistoryByFlightBillIds(ids, BillOperation.SUBMIT.getCode());
                    Map<String, List<TFlightBillHistory>> tFlightBillHistoryIdMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(tFlightBillHistoryByFlightBillIds)) {
                        tFlightBillHistoryIdMap = tFlightBillHistoryByFlightBillIds.stream().collect(Collectors.groupingBy(TFlightBillHistory::getFlightBillId));
                    }
                    Map<String, List<TFlightBillHistory>> finalTflightBillHistoryIdsMap = tFlightBillHistoryIdMap;
                    List<FlightBillChainDto> flightBillChainDtoList = tFlightBillVOList.stream().map(k -> {
                        FlightBillChainDto flightBillChainDto = new FlightBillChainDto();
                        flightBillChainDto.setBusinessDataId(k.getId());
                        if (finalTflightBillHistoryIdsMap.containsKey(k.getId())) {
                            flightBillChainDto.setBusinessDataHistoryId(finalTflightBillHistoryIdsMap.get(k.getId()).get(0).getId());
                        }
                        flightBillChainDto.setBusinessData(JSON.toJSONString(k));
                        return flightBillChainDto;
                    }).collect(Collectors.toList());
                    upperChainByParamet(flightBillChainDtoList);
                } catch (Exception e) {
                    log.error("机场端上链过程异常：{}", e.getMessage(), e);
                } finally {
                    TenantHolder.clear();
                }
            });
        }
    }

    private void upperChainByParamet(List<FlightBillChainDto> flightBillChainDtoList) {
        if (CollectionUtils.isNotEmpty(flightBillChainDtoList)) {
            try {
                // 上链
                List<FlightBillChainVo> flightBillChainVos = iUpperChainClient.upperChainByParamet(flightBillChainDtoList);
                List<TFlightBill> tFlightBills = flightBillChainVos.stream().map(k -> {
                    TFlightBill tFlightBill = new TFlightBill();
                    tFlightBill.setId(k.getBusinessDataId());
                    tFlightBill.setChainId(k.getChainId());
                    tFlightBill.setTransactionId(k.getTransactionId());
                    return tFlightBill;
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tFlightBills)) {
                    itFlightBillService.updateBatchById(tFlightBills);
                }
            } catch (Exception e) {
                log.error("机场端上链异常：{}", e.getMessage(), e);
            }
        }
    }

}
