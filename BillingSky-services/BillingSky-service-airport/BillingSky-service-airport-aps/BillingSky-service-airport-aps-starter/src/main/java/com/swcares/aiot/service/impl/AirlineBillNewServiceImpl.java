package com.swcares.aiot.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.client.IConfDictMgtBizClient;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.enums.FlightDateTypeEnum;
import com.swcares.aiot.core.common.util.CsvUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.AirlineBillForm;
import com.swcares.aiot.core.model.dto.AirlineBillExportDto;
import com.swcares.aiot.core.model.dto.AirlineBillPageDto;
import com.swcares.aiot.core.model.dto.AirlineBillTaxRateDto;
import com.swcares.aiot.core.model.entity.AirlineBillNew;
import com.swcares.aiot.core.model.vo.*;
import com.swcares.aiot.mapper.AirlineBillMapper;
import com.swcares.aiot.model.vo.ConfDictCacheVo;
import com.swcares.aiot.service.AirlineBillNewService;
import com.swcares.aiot.service.LogService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.worm.hutool.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.AirlineBillNewServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/25 15:05
 * @version v1.0
 */
@Service
@Slf4j
public class AirlineBillNewServiceImpl implements AirlineBillNewService {

    @Resource
    private AirlineBillMapper airlineBillMapper;
    @Resource
    private LogService logService;
    @Resource
    private IConfDictMgtBizClient confDictMgtBizClient;


    @Override
    public void checkAndNewAirlineBill(String airportCode) {
        //航班日期的航司账单
        insertAirlineBill(airlineBillMapper.listMissAirlineBillByFlightDate(airportCode), FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue(), airportCode);
        //起降日期的航司账单
        insertAirlineBill(airlineBillMapper.listMissAirlineBillByFlightTime(airportCode), FlightDateTypeEnum.TAKE_OFF_DATE_TYPE.getValue(), airportCode);
    }

    @Async
    @Override
    public void checkAndNewAirlineBill(Date startDate, Date endDate, String airportCode, Long tenantId) {
        TenantHolder.setTenant(tenantId);
        //航班日期的航司账单
        insertAirlineBill(airlineBillMapper.listMissAirlineBillByFlightInfoAndDate(airportCode,startDate,endDate), FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue(), airportCode);
        //起降日期的航司账单
        insertAirlineBill(airlineBillMapper.listMissAirlineBillByFlightInfoAndTime(airportCode,startDate,endDate), FlightDateTypeEnum.TAKE_OFF_DATE_TYPE.getValue(), airportCode);
        TenantHolder.clear();
    }

    private void insertAirlineBill(List<AirlineBillMissVo> airlineBillMissVoList, Integer dateType, String airportCode) {
        if (airlineBillMissVoList.isEmpty()) {
            log.info("新增dateType为:{} 的航司数据为空！", dateType);
            return;
        }
        for (AirlineBillMissVo airlineBillMissVo : airlineBillMissVoList) {
            AirlineBillNew airlineBillNew = new AirlineBillNew();
            airlineBillNew.setSettleCode(airlineBillMissVo.getSettleCode());
            airlineBillNew.setSettleMonth(airlineBillMissVo.getFlightDate());
            airlineBillNew.setDateType(dateType);
            airlineBillNew.setAirportCode(airportCode);
            airlineBillMapper.insert(airlineBillNew);
        }
    }

    private Integer getDateType(LocalDateTime flightDate){
        Integer dateType;
        if (ObjectUtil.isNotEmpty(flightDate)) {
            dateType = FlightDateTypeEnum.FLIGHT_DATE_TYPE.getValue();
        } else {
            dateType = FlightDateTypeEnum.TAKE_OFF_DATE_TYPE.getValue();
        }
        return dateType;
    }

    private String getSettleMonth(LocalDateTime flightDate,LocalDateTime flightTime){
        if(Objects.isNull(flightDate) && Objects.isNull(flightTime)){
            throw new BusinessException(CommonErrors.INPUT_INVALIDATE_ERROR);
        }
        String settleMonth;
        if (ObjectUtil.isNotEmpty(flightDate)) {
            settleMonth = LocalDateTimeUtil.format(flightDate, DatePattern.NORM_MONTH_FORMATTER);
        } else {
            settleMonth = LocalDateTimeUtil.format(flightTime, DatePattern.NORM_MONTH_FORMATTER);
        }
        return settleMonth;
    }

    @Override
    public IPage<AirlineBillPageVo> pageAirlineBillInfoByCondition(AirlineBillPageDto dto) {
        Map<String, String> settleCodeAirlineAbbrMap = getSettleCodeAirlineAbbrMap();
        // 拿到日期类型
        Integer dateType = getDateType(dto.getFlightDate());
        String settleMonth = getSettleMonth(dto.getFlightDate(), dto.getFlightTime());
        IPage<AirlineBillPageVo> pageList = airlineBillMapper.pageAirlineBillInfoByCondition(dto.getAirportCode(),
                settleMonth, dto.getSettleCodeList(), dateType, dto.createPage());
        pageList.getRecords().forEach(airlineBillPageVo -> {
            airlineBillPageVo.setAirlineAbbr(settleCodeAirlineAbbrMap.getOrDefault(airlineBillPageVo.getSettleCode(),
                    "未知航空公司"));
            BigDecimal taxRate = new BigDecimal(airlineBillPageVo.getTaxRate());
            BigDecimal taxDivisor = new BigDecimal("100").add(taxRate);
            BigDecimal taxFee = airlineBillPageVo.getTotalSettleAmount()
                    .multiply(taxRate)
                    .divide(taxDivisor, 2, RoundingMode.HALF_UP);
            airlineBillPageVo.setTaxFee(taxFee);
            airlineBillPageVo.setRealTotalSettleAmount(airlineBillPageVo.getTotalSettleAmount().subtract(taxFee));
        });
        return pageList;
    }

    private Map<String, String> getSettleCodeAirlineAbbrMap() {
        BaseResult<List<ConfDictCacheVo>> apsSettleCodeAirlineAbbr = confDictMgtBizClient.getDict(
                "APS_SETTLE_CODE_AIRLINE_ABBR", TenantHolder.getTenant().toString());
        if(apsSettleCodeAirlineAbbr.getCode() != BaseResult.OK_CODE || CollectionUtils.isEmpty(apsSettleCodeAirlineAbbr.getData())){
            throw new BusinessException(ExceptionCodes.AIRLINE_DICT_CONFIG_MISS);
        }
        return apsSettleCodeAirlineAbbr.getData().stream().collect(Collectors.toMap(ConfDictCacheVo::getDictKey,
                ConfDictCacheVo::getDictValue));
    }

    @Override
    public ResultBuilder<Object> updateAirlineBillTaxRate(AirlineBillTaxRateDto airlineBillTaxRateDto, LoginUserDetails user) {

        AirlineBillNew airlineBill = airlineBillMapper.selectById(airlineBillTaxRateDto.getAirlineBillId());
        if (airlineBill == null) {
            throw new GenericException(BusinessMessageEnum.DATA_NOT_EXIST.getCode(),
                    BusinessMessageEnum.DATA_NOT_EXIST.getMsg());
        }
        if (airlineBillTaxRateDto.getTaxRate()!=null) {
            airlineBill.setTaxRate(airlineBillTaxRateDto.getTaxRate());
        } else {
            throw new GenericException(BusinessMessageEnum.DATA_Flag_ERROR.getCode(),
                    BusinessMessageEnum.DATA_Flag_ERROR.getMsg());
        }
        airlineBill.setUpdatedBy(user.getUsername());
        airlineBill.setUpdatedTime(LocalDateTime.now());
        airlineBillMapper.updateById(airlineBill);
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<Object> updateAirlineBillInfo(AirlineBillForm airlineBillForm, LoginUserDetails user, String urlName) {
        /*A:调整金额,R:拒付金额*/
        String amountFlag = airlineBillForm.getAmountFlag();
        BigDecimal amountData = airlineBillForm.getAmountData();
        /*更新前，先查询数据是否存在，即数据是否有效*/
        AirlineBillNew airlineBill = airlineBillMapper.selectById(airlineBillForm.getId());
        if (airlineBill == null) {
            throw new GenericException(BusinessMessageEnum.DATA_NOT_EXIST.getCode(),
                    BusinessMessageEnum.DATA_NOT_EXIST.getMsg());
        }
        if (amountFlag.equals(Constants.AirlineBillEnum.ADJUST_AMOUNT_FLAG.getValue())) {
            airlineBill.setAdjustAmount(amountData);
        } else if (amountFlag.equals(Constants.AirlineBillEnum.REFUSE_AMOUNT_FLAG.getValue())) {
            airlineBill.setRefuseAmount(amountData);
        } else {
            throw new GenericException(BusinessMessageEnum.DATA_Flag_ERROR.getCode(),
                    BusinessMessageEnum.DATA_Flag_ERROR.getMsg());
        }
        airlineBill.setUpdatedBy(user.getUsername());
        airlineBill.setUpdatedTime(LocalDateTime.now());
        airlineBillMapper.updateById(airlineBill);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() +
                urlName +
                Constants.LogEnum.LOG_MEG_SAVE.getValue();
        airlineBillForm.setId(null);
        logService.addLogForSave(airlineBillForm, user, content, airlineBill.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public AirlineBillCountVoNew countTotal(AirlineBillPageDto dto) {
        // 拿到日期类型
        Integer dateType=getDateType(dto.getFlightDate());
        String settleMonth=getSettleMonth(dto.getFlightDate(),dto.getFlightTime());
        List<AirlineBillCountVoNew> airlineBillCountVoList = airlineBillMapper.countTotalAmount(
                dto.getAirportCode(),
                settleMonth, dto.getSettleCodeList(), dateType);
        return getAirlineBillCountVoList(airlineBillCountVoList);
    }

    private AirlineBillCountVoNew getAirlineBillCountVoList(List<AirlineBillCountVoNew> airlineBillCountVoList) {
        AirlineBillCountVoNew tmp = new AirlineBillCountVoNew();
        for (AirlineBillCountVoNew each : airlineBillCountVoList) {
            BigDecimal taxRate = each.getTaxRate();
            BigDecimal taxDivisor = new BigDecimal("100").add(taxRate);
            tmp.setSettleAmountTotal(tmp.getSettleAmountTotal().add(each.getSettleAmountTotal()));
            tmp.setAdjustAmountTotal(tmp.getAdjustAmountTotal().add(each.getAdjustAmountTotal()));
            tmp.setRefuseAmountTotal(tmp.getRefuseAmountTotal().add(each.getRefuseAmountTotal()));
            tmp.setActualAmountTotal(tmp.getActualAmountTotal().add(each.getActualAmountTotal()));
            tmp.setTotalSettleAmountTotal(tmp.getTotalSettleAmountTotal().add(each.getTotalSettleAmountTotal()));
            BigDecimal taxFee = each.getTotalSettleAmountTotal()
                    .multiply(taxRate)
                    .divide(taxDivisor, 2, RoundingMode.HALF_UP);
            tmp.setTaxFee(tmp.getTaxFee().add(taxFee));
            tmp.setRealTotalSettleAmount(tmp.getRealTotalSettleAmount()
                    .add(each.getTotalSettleAmountTotal().subtract(taxFee)));
        }
        return tmp;
    }

    @Override
    public void exportAirlineBillInfo(AirlineBillExportDto dto, HttpServletResponse response, LoginUserDetails user, String urlName) {
        String settleMonth = getSettleMonth(dto.getFlightTime(), dto.getFlightDate());
        //设置文件名
        String fileName = FormatUtils.formatDateToDay(new Date()) + "_APT_A_" + dto.getAirportCode()
                + "_TLJ_" + settleMonth;
        //设置 header
        FileUtils.setCsvResponseHeader(fileName, response);
        // 拿到日期类型
        Integer dateType = getDateType(dto.getFlightDate());
        String queryMonth = getSettleMonth(dto.getFlightDate(), dto.getFlightTime());
        //查出符合条件的所有数据
        List<AirlineBillPageVo> resultList = airlineBillMapper.listAirlineBillInfoByCondition( dto.getAirportCode(),
                queryMonth, dto.getSettleCodeList(), dateType);
        List<AirlineBillVoNew> result = dealResult(resultList);
        //数据转换
        List<AirlineBillCsvVo> airlineBillCsvVoList = getAirlineBillCsvVoList(result, dto);
        // 表头
        String[] head = {
                //结算月份
                Constants.AirlineBillEnum.SETTLE_MONTH.getValue(),
                //结算机场
                Constants.AirlineBillEnum.SETTLE_AIRPORT.getValue(),
                //结算人
                Constants.AirlineBillEnum.SETTLE_PERSON.getValue(),
                //结算人电话
                Constants.AirlineBillEnum.SETTLE_PERSON_NUMBER.getValue(),
                //付款航空公司代码
                Constants.AirlineBillEnum.AIRLINE_SETTLE_CODE.getValue(),
                //付款航空公司简称
                Constants.AirlineBillEnum.AIRLINE_ABBR.getValue(),
                //结算金额
                Constants.AirlineBillEnum.SETTLE_AMOUNT.getValue(),
                //调整金额
                Constants.AirlineBillEnum.ADJUST_AMOUNT.getValue(),
                //拒付金额
                Constants.AirlineBillEnum.REFUSE_AMOUNT.getValue(),
                //税率
                Constants.AirlineBillEnum.TAX_RATE.getValue(),
                //实际结算金额
                Constants.AirlineBillEnum.ACTUAL_SETTLE_AMOUNT.getValue(),
                //总结算金额（含税）
                Constants.AirlineBillEnum.TOTAL_SETTLE_AMOUNT.getValue(),
                //税额
                Constants.AirlineBillEnum.TAX_FEE.getValue(),
                //总结算金额（不含税）
                Constants.AirlineBillEnum.REAL_TOTAL_SETTLE_AMOUNT.getValue()

        };
        //字段名
        List<String> fieldNameList = new ArrayList<>();
        Field[] fields = AirlineBillCsvVo.class.getDeclaredFields();
        for (Field field : fields) {
            fieldNameList.add(field.getName());
        }
        String[] fieldArray = fieldNameList.toArray(new String[0]);
        try {
            ServletOutputStream csvResult = response.getOutputStream();
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + URLUtil.encode(fileName, StringUtil.UTF8) + ".csv");
            List<Object[]> objects = CsvUtils.getData(airlineBillCsvVoList, fieldArray, AirlineBillCsvVo.class);
            CsvUtils.simpleExport(true, "\n", head, objects, fileName, csvResult);
        } catch (IOException e) {
            throw new GenericException(BusinessMessageEnum.IO_ERROR.getCode(), BusinessMessageEnum.IO_ERROR.getMsg());
        }
        String content = Constants.LogEnum.LOG_MEG_1.getValue()
                + urlName +
                Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, dto, content, dto.getAirportCode(), "航司账单");
    }

    private List<AirlineBillCsvVo> getAirlineBillCsvVoList(List<AirlineBillVoNew> result,AirlineBillExportDto airlineBillExportDto){
        List<AirlineBillCsvVo> airlineBillCsvVoList = new ArrayList<>();
        Map<String, String> settleCodeAirlineAbbrMap = getSettleCodeAirlineAbbrMap();
        for (AirlineBillVoNew airlineBillVo : result) {
            AirlineBillCsvVo data = new AirlineBillCsvVo();
            data.setSettleMonth(airlineBillVo.getSettleMonth());
            data.setSettleAirport(airlineBillVo.getAirportCode());
            data.setAirlineSettleCode(airlineBillVo.getSettleCode());
            data.setAirlineAbbr(settleCodeAirlineAbbrMap.getOrDefault(airlineBillVo.getSettleCode(), "未知航空公司"));
            data.setSettleAmount(airlineBillVo.getSettleAmount());
            data.setAdjustAmount(airlineBillVo.getAdjustAmount());
            data.setRefuseAmount(airlineBillVo.getRefuseAmount());
            data.setTaxRate(airlineBillVo.getTaxRate());
            data.setActualSettleAmount(airlineBillVo.getActualAmount());
            data.setTotalSettleAmount(airlineBillVo.getTotalSettleAmount());
            data.setTaxFee(airlineBillVo.getTaxFee());
            data.setRealTotalSettleAmount(airlineBillVo.getRealTotalSettleAmount());
            data.setSettlePerson(airlineBillExportDto.getUserName());
            if (airlineBillExportDto.getPhoneNumber() != null) {
                data.setSettlePersonNumber("\t" + airlineBillExportDto.getPhoneNumber() + "\t");
            }
            airlineBillCsvVoList.add(data);
        }
        return airlineBillCsvVoList;
    }

    private List<AirlineBillVoNew> dealResult(List<AirlineBillPageVo> resultList){
        List<AirlineBillVoNew> airlineBillVoNewList = new ArrayList<>();
        resultList.forEach(airlineBillPageVo -> {
            BigDecimal taxRate = new BigDecimal(airlineBillPageVo.getTaxRate());
            BigDecimal taxDivisor = new BigDecimal("100").add(taxRate);
            BigDecimal taxFee = airlineBillPageVo.getTotalSettleAmount()
                    .multiply(taxRate)
                    .divide(taxDivisor, 2, RoundingMode.HALF_UP);
            airlineBillPageVo.setTaxFee(taxFee);
            airlineBillPageVo.setRealTotalSettleAmount(airlineBillPageVo.getTotalSettleAmount().subtract(taxFee));
            AirlineBillVoNew airlineBillVoNew = new AirlineBillVoNew();
            BeanUtils.copyProperties(airlineBillPageVo, airlineBillVoNew);
            airlineBillVoNew.setSettleMonth("=\""+airlineBillPageVo.getSettleMonth()+"\"");
            airlineBillVoNewList.add(airlineBillVoNew);
        });
        return airlineBillVoNewList;
    }
}
