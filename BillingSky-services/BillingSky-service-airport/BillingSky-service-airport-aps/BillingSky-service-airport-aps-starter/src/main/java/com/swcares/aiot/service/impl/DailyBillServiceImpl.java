package com.swcares.aiot.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.swcares.aiot.client.IErDailyBillBizClient;
import com.swcares.aiot.core.model.dto.ErAutoConfirmDto;
import com.swcares.aiot.core.model.dto.ErBillFulfillAndConfirmDto;
import com.swcares.aiot.core.model.dto.ErDailyBillPageDto;
import com.swcares.aiot.core.model.vo.ErBillFulfillAndConfirmVo;
import com.swcares.aiot.core.model.vo.ErDailyBillAccVo;
import com.swcares.aiot.core.model.vo.ErDailyBillDownloadVo;
import com.swcares.aiot.core.model.vo.ErDailyBillPageVo;
import com.swcares.aiot.service.DailyBillService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.worm.hutool.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.impl.DailyBillServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/3/26 10:00
 * @version v1.0
 */
@Slf4j
@Service
public class DailyBillServiceImpl implements DailyBillService {

    @Resource
    private IErDailyBillBizClient iErDailyBillBizClient;


    @Override
    public PagedResult<List<ErDailyBillPageVo>> pageErDailyBill(ErDailyBillPageDto erDailyBillPageDto) {
        PagedResult<List<ErDailyBillPageVo>> result = iErDailyBillBizClient.pageErDailyBill(erDailyBillPageDto);
        if (result.getCode() == 200 && result.getData() != null) {
            result.getData().forEach(erDailyBillPageVo -> {
                erDailyBillPageVo.setBoardingCnt(null);
                erDailyBillPageVo.setPayFlightCnt(null);
                erDailyBillPageVo.setPayCheckinCnt(null);
                erDailyBillPageVo.setPayBoardingCnt(null);
            });
        }
        return result;
    }

    @Override
    public BaseResult<ErDailyBillAccVo> accErDailyBill(ErDailyBillPageDto erDailyBillPageDto) {
        BaseResult<ErDailyBillAccVo> result = iErDailyBillBizClient.accErDailyBill(erDailyBillPageDto);
        if (result.getCode() == 200 && result.getData() != null) {
            ErDailyBillAccVo erDailyBillAccVo = result.getData();
            erDailyBillAccVo.setBoardingCnt(null);
            erDailyBillAccVo.setPayFlightCnt(null);
            erDailyBillAccVo.setPayCheckinCnt(null);
            erDailyBillAccVo.setPayBoardingCnt(null);
            result.setData(erDailyBillAccVo);
        }

        return result;
    }

    @Override
    public BaseResult<ErBillFulfillAndConfirmVo> confirmPay(ErBillFulfillAndConfirmDto erBillFulfillAndConfirmDto) {
        return iErDailyBillBizClient.confirmPay(erBillFulfillAndConfirmDto);
    }

    @Override
    public BaseResult<Boolean> switchDailyAutoConfirm(ErAutoConfirmDto erAutoConfirmDto){
        return iErDailyBillBizClient.switchDailyAutoConfirm(erAutoConfirmDto);
    }

    @Override
    public BaseResult<Boolean> dailyAutoConfirmQuery(String airportCode){
        return iErDailyBillBizClient.dailyAutoConfirmQuery(airportCode);
    }

    @Override
    public void downloadErDailyBill(ErDailyBillPageDto erDailyBillPageDto, HttpServletResponse response) throws IOException {
        List<ErDailyBillDownloadVo> erDailyBillDownloadVos = iErDailyBillBizClient.downloadErDailyBillData(erDailyBillPageDto);
        String yyyyMMdd = LocalDateTimeUtil.format(LocalDate.now(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        log.info("日期：{}", yyyyMMdd);
        // 设置响应头
        String fileName = URLEncoder.encode(CharSequenceUtil.format("每日预估账单-{}.xlsx", yyyyMMdd), CharsetUtil.UTF_8);
        response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + fileName);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        // 使用 EasyExcel 写入数据
        EasyExcelFactory.write(response.getOutputStream(), ErDailyBillDownloadVo.class).sheet("每日预估账单").doWrite(erDailyBillDownloadVos);
    }
}
