package com.swcares.aiot.service;

import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.dto.ReCalcErrorDto;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.model.entity.SettlementStatus;
import com.swcares.aiot.core.form.NoticePageForm;
import com.swcares.aiot.core.form.ReCalcForm;
import com.swcares.aiot.core.form.SubsidyFlightForm;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.core.model.vo.SettlementStatusVo;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;

/**
 * ClassName：com.swcares.modules.settlement.service.SettlementStatusService
 * Description：
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2021/10/13 10:43
 * @version v1.0
 */
public interface SettlementStatusService {
    /**
     * Title: getSettlementStatusList<br>
     * Author: liuzhiheng<br>
     * Description: 获取最新十条结算操作的状态<br>
     * Date:  16:30 <br>
     * return: List
     */
    List<SettlementStatusVo> getSettlementStatusList(String airportCode, LoginUserDetails user);
    /**
     * Title: pageSettlementStatusList<br>
     * Author: liuzhiheng<br>
     * Description: 分页查询结算操作的状态<br>
     * Date:  14:30 <br>
     * return: List
     */
    Pager pageSettlementStatusList(NoticePageForm form, LoginUserDetails user, PageParam pageParam);

    /**
     * Title: insertSettlementStatus<br>
     * Author: liuzhiheng<br>
     * Description: 插入新的结算操作<br>
     * Date:  16:30 <br>
     */
    String insertSettlementStatus(ReCalcForm form,LoginUserDetails user, Workbook errorWb, String errorFileName);

    /**
     * Title: changeStatus<br>
     * Author: liuzhiheng<br>
     * Description: 插入新的结算操作<br>
     * Date:  16:30 <br>
     */
    void changeStatus(String id,String status);

    /**
     * Title: removeSettlementStatusByFlightId<br>
     * Author: 刘志恒<br>
     * Description: 根据航班id删除结算操作记录<br>
     * Date:  2022/10/31 14:29 <br>

     */
    Integer removeSettlementStatusByFlightId(String flightId,String formulaType);

    /**
     * Title: cancelSettlementStatusByFlightId<br>
     * Author: 刘志恒<br>
     * Description: 根据航班id和公式类型，取消结算<br>
     * Date:  2022/11/17 11:21 <br>

     */
    Integer cancelSettlementStatusByFlightId(String flightId,String formulaType);


    Boolean removeSettlementStatusById(String ids,LoginUserDetails user);
    /**
     * Title: getSettlementIdListByFlightId<br>
     * Author: 刘志恒<br>
     * Description: 根据航班id获取结算记录id<br>
     * Date:  2022/10/31 15:53 <br>

     */
    List<String> getSettlementIdListByFlightId(List<String> flightIdList,String formulaType);


    /**
     * Title: findById<br>
     * Author: liuzhiheng<br>
     * Description: 通过id获取SettlementStatus对象<br>
     * Date:  16:30 <br>
     */
    SettlementStatus findById(String id);


    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 航线补贴结算通知创建<br>
     * Date:  2022/8/23 10:21 <br>

     */
    SettlementStatus subsidySettlement(SubsidyFlightForm form, LoginUserDetails user);

    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 航线补贴结算成功<br>
     * Date:  2022/8/23 10:22 <br>

     */
    void subsidySettlementSuccess(SettlementStatus ss);

    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 航线补贴结算失败<br>
     * Date:  2022/8/23 10:22 <br>

     */
    void subsidySettlementFail(SettlementStatus ss);

    /**
     * Title: getUnreadSettlementStatus<br>
     * Author: 刘志恒<br>
     * Description: 获取当前用户未读结算记录<br>
     * Date:  2022/11/2 10:53 <br>

     */
    Integer getUnreadSettlementStatus(String airportCode,LoginUserDetails user);


    /**
     * Title: newSettlementStatus<br>
     * Author: liuzhiheng<br>
     * Description: 插入新的结算操作<br>
     * Date:  16:30 <br>
     */
    SettlementStatus newSettlementStatus(ReCalcDto dto, LoginUserDetails user);

    boolean updateById(SettlementStatus settlementStatus);

    /**
     * Title: uploadErrorFile<br>
     * Author: liuzhiheng<br>
     * Description: 上传错误文件<br>
     * Date:  16:30 <br>
     */
    void uploadErrorFile(SettlementStatus settlementStatus,
                         String errorFileName,List<ReCalcErrorDto>  errorDtoList);
}
