package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.enums.FileBusinessTypeEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.NoticePageForm;
import com.swcares.aiot.core.form.ReCalcForm;
import com.swcares.aiot.core.form.SubsidyFlightForm;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.dto.ReCalcErrorDto;
import com.swcares.aiot.core.model.entity.FlightLineInfo;
import com.swcares.aiot.core.model.entity.SettlementStatus;
import com.swcares.aiot.core.model.entity.UserRead;
import com.swcares.aiot.core.model.vo.SettlementStatusVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.dao.FlightLineInfoDao;
import com.swcares.aiot.dao.SettlementStatusDao;
import com.swcares.aiot.dao.UserReadDao;
import com.swcares.aiot.service.IFileAttachmentBizService;
import com.swcares.aiot.service.SettlementStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * ClassName：com.swcares.modules.settlement.service.impl.SettlementStatusServiceImpl
 * Description：
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2021/10/13 10:44
 * @version v1.0
 */

@Service
@Slf4j
public class SettlementStatusServiceImpl implements SettlementStatusService {
    //minio上传bucket名
    public static final String UPLOAD_BUCKET_NAME = "aps-fail-settlement";

    @Resource
    private IFileAttachmentBizService fileAttachmentBizService;
    @Resource
    private SettlementStatusDao settlementStatusDao;
    @Resource
    private FlightLineInfoDao flightLineInfoDao;
    @Resource
    private UserReadDao userReadDao;
    @Resource
    private FeeDao feeDao;

    @Override
    public List<SettlementStatusVo> getSettlementStatusList(String airportCode, LoginUserDetails user) {
        List<Object[]> objects = settlementStatusDao.getSettlementStatusList(airportCode, "" + user.getId(), null, null);
        if (!objects.isEmpty() && objects.size() > 10) {
            objects = objects.subList(0, 10);
        }
        List<SettlementStatusVo> ssVoList = new ArrayList<>();
        List<UserRead> urList = new ArrayList<>();
        getSettlementStatusVoList(objects, ssVoList, urList, user);
        userReadDao.saveAll(urList);

        return ssVoList;
    }

    @Override
    public Pager pageSettlementStatusList(NoticePageForm form, LoginUserDetails user, PageParam pageParam) {

        List<Object[]> objects = settlementStatusDao.getSettlementStatusList(form.getAirportCode(),
                "" + user.getId(), form.getStartDate(), form.getEndDate());
        Pager pagerList = new Pager<>(pageParam.getPage(), pageParam.getLimit(), objects);
        List<SettlementStatusVo> ssVoList = new ArrayList<>();
        List<UserRead> urList = new ArrayList<>();
        getSettlementStatusVoList(pagerList.getList(), ssVoList, urList, user);
        userReadDao.saveAll(urList);
        pagerList.setList(ssVoList);
        return pagerList;
    }

    @Override
    public String insertSettlementStatus(ReCalcForm form, LoginUserDetails user, Workbook errorWb,
                                         String errorFileName) {
        if (Strings.isNotBlank(errorFileName) && !errorFileName.contains(".")) {
            errorFileName = errorFileName + ".xls";
        }
        SettlementStatus settlementStatus = new SettlementStatus();
        // 保存结算操作状态
        settlementStatus.setFromDate(form.getStartDate());
        settlementStatus.setToDate(form.getEndDate());
        String flightNos = form.getFlightNo();
        if (Strings.isNotBlank(flightNos) && flightNos.length() > 255) {
            flightNos = flightNos.substring(0, 249) + "等";
        }
        settlementStatus.setFlightNos(flightNos);
        settlementStatus.setAirportCode(form.getAirportCode());
        if (form.isSuccessHide()) {
            settlementStatus.setInvalid("0");
        }
        if (Strings.isNotBlank(form.getFormulaType())) {
            settlementStatus.setFormulaType(form.getFormulaType());
        }
        String flightId = form.getFlightId();
        if (Strings.isNotBlank(flightId) && flightId.length() > 255) {
            flightId = flightId.substring(0, 249) + "等";
        }
        settlementStatus.setFlightId(flightId);
        if (Strings.isNotBlank(form.getFeeCodes())) {
            settlementStatus.setFeeName(feeDao.findFeeNameByFeeCodes(form.getFeeCodes()));
        }
        settlementStatus.setType("1");


        // 将上传错误数据文件上传至minio
        if (errorWb != null && !Strings.isBlank(errorFileName)) {
            try {
                Long fileKey = fileAttachmentBizService.uploadExcel(FileBusinessTypeEnum.SETTLEMENT_FAIL_RECORD, errorFileName, errorWb);
                // 将minio存储的失败信息文件地址保存到数据库
                settlementStatus.setFailFile(fileKey.toString());
            } catch (Exception e) {
                log.error("出现业务异常", e);
                settlementStatus.setFailFile("错误数据文件生成失败！");
            }
        }
        settlementStatus.setCreateBy(user.getUsername());
        settlementStatus.setCreateTime(new Date());
        settlementStatusDao.save(settlementStatus);
        settlementStatusDao.flush();
        return settlementStatus.getId();
    }


    @Override
    public void changeStatus(String id, String status) {
        log.info("finally:changeStatus: id: {},status :{}", id, status);
        SettlementStatus ss = findById(id);
        log.info("finally:ss: {}",ss);
        //如果是已取消的结算记录（目前只有取消确认可取消等待中结算），则不修改状态
        if ("-2".equals(ss.getStatus())) {
            return;
        }
        //如果是数据确认时失败了，需要将invalid设为1
        if (Strings.isNotBlank(ss.getFlightId()) && "-1".equals(status)) {
            ss.setInvalid("1");
        }
        ss.setStatus(status);
        log.info("finally:update status: {}",ss);
        settlementStatusDao.save(ss);
        log.info("finally:update status: {}",ss);
    }

    @Transactional
    @Override
    public Integer removeSettlementStatusByFlightId(String flightId, String formulaType) {
        return settlementStatusDao.removeSettlementStatusByFlightId(flightId, formulaType);
    }

    @Transactional
    @Override
    public Integer cancelSettlementStatusByFlightId(String flightId, String formulaType) {
        return settlementStatusDao.cancelSettlementStatusByFlightId(flightId, formulaType);
    }

    @Transactional
    @Override
    public Boolean removeSettlementStatusById(String ids, LoginUserDetails user) {
        String[] idArr = ids.split(",");
        List<SettlementStatus> removeList = new ArrayList<>();
        for (String id : idArr) {
            SettlementStatus ss = settlementStatusDao.getById(id);
            if ("0".equals(ss.getInvalid())) {
                throw new GenericException(BusinessMessageEnum.SETTLEMENT_STATUS_REMOVED.getCode(),
                        BusinessMessageEnum.SETTLEMENT_STATUS_REMOVED.getMsg());
            }
            if ("0".equals(ss.getStatus()) || "2".equals(ss.getStatus())) {
                throw new GenericException(BusinessMessageEnum.SETTLEMENT_STATUS_STATUS_ERROR.getCode(),
                        BusinessMessageEnum.SETTLEMENT_STATUS_STATUS_ERROR.getMsg());
            }
            ss.setInvalid("0");
            removeList.add(ss);
        }
        settlementStatusDao.saveAll(removeList);
        return true;
    }

    @Override
    public List<String> getSettlementIdListByFlightId(List<String> flightIdList, String formulaType) {
        return settlementStatusDao.getSettlementIdListByFlightId(flightIdList, formulaType);
    }

    @Override
    public SettlementStatus findById(String id) {
        settlementStatusDao.flush();
        return settlementStatusDao.getSettlementStatusById(id);
    }

    @Override
    public SettlementStatus subsidySettlement(SubsidyFlightForm form, LoginUserDetails user) {
        SettlementStatus ss = new SettlementStatus();
        ss.setType("2");
        ss.setAirportCode(form.getAirportCode());
        ss.setStatus("0");

        ss.setFromDate(form.getStartDate());
        ss.setToDate(form.getEndDate());

        FlightLineInfo fli = flightLineInfoDao.getFlightLineInfoById(form.getFlightLine());
        ss.setFlightLine(fli.getFlightLineCn());
        ss.setAirlineCode(fli.getAirlineShortName());

        ss.setCreateTime(new Date());
        ss.setCreateBy(user.getUsername());

        settlementStatusDao.save(ss);

        return ss;
    }

    @Override
    public void subsidySettlementSuccess(SettlementStatus ss) {
        ss.setStatus("1");
        settlementStatusDao.save(ss);
    }


    @Override
    public void subsidySettlementFail(SettlementStatus ss) {
        ss.setStatus("-1");
        settlementStatusDao.save(ss);
    }

    @Override
    public Integer getUnreadSettlementStatus(String airportCode, LoginUserDetails user) {
        return settlementStatusDao.getUnreadSettlementStatus(airportCode, "" + user.getId());
    }

    @Override
    public SettlementStatus newSettlementStatus(ReCalcDto dto, LoginUserDetails user) {
        SettlementStatus settlementStatus = new SettlementStatus();
        // 保存结算操作状态
        settlementStatus.setFromDate(dto.getStartDate());
        settlementStatus.setToDate(dto.getEndDate());
        String flightNos = dto.getFlightNo();
        if (Strings.isNotBlank(flightNos) && flightNos.length() > 255) {
            flightNos = flightNos.substring(0, 249) + "......";
        }
        settlementStatus.setFlightNos(flightNos);
        settlementStatus.setAirportCode(dto.getAirportCode());
        if (dto.isSuccessHide()) {
            settlementStatus.setInvalid("0");
        }

        List<String> flightIdList = dto.getFlightId();
        if(flightIdList!=null && !flightIdList.isEmpty()){
            StringBuilder flightIdStr=new StringBuilder();
            for(String flightId : flightIdList){
                if(flightIdStr.length()+flightId.length()>240){
                    flightIdStr.deleteCharAt(flightIdStr.length()-1);
                    flightIdStr.append(".....");
                    break;
                }
                flightIdStr.append(flightId);
                flightIdStr.append(",");
            }
            settlementStatus.setFlightId(flightIdStr.toString());
        }
        if (CollUtil.isNotEmpty(dto.getFeeCodes())) {
            settlementStatus.setFeeName(feeDao.findFeeNameByFeeCodes(String.join(",", dto.getFeeCodes())));
        }
        settlementStatus.setType("1");
        settlementStatus.setCreateBy(user.getUsername());
        settlementStatus.setCreateTime(new Date());
        settlementStatusDao.save(settlementStatus);
        settlementStatusDao.flush();
        return settlementStatus;
    }

    @Override
    public boolean updateById(SettlementStatus settlementStatus) {
        settlementStatusDao.save(settlementStatus);
        return Boolean.TRUE;
    }

    private void getSettlementStatusVoList(List<Object[]> objects, List<SettlementStatusVo> ssVoList,
                                           List<UserRead> urList, LoginUserDetails user) {
        for (Object[] objs : objects) {
            SettlementStatusVo ssv = new SettlementStatusVo();
            ssv.setId(objs[0] == null ? null : "" + objs[0]);
            ssv.setFlightNos(objs[1] == null ? null : "" + objs[1]);
            ssv.setFromDate(objs[2] == null ? null : (Date) objs[2]);
            ssv.setToDate(objs[3] == null ? null : (Date) objs[3]);
            ssv.setStatus(objs[4] == null ? null : "" + objs[4]);
            ssv.setType(objs[5] == null ? null : "" + objs[5]);
            ssv.setAirlineCode(objs[6] == null ? null : "" + objs[6]);
            ssv.setFlightLine(objs[7] == null ? null : "" + objs[7]);
            if ("1".equals(ssv.getStatus()) && objs[8] != null && !Strings.isBlank("" + objs[8]) && !"错误数据文件生成失败！".equals(objs[8])) {
                ssv.setFailFile(objs[8].toString());
            }

            ssv.setCreateBy(objs[9] == null ? null : "" + objs[9]);
            ssv.setCreateTime(objs[10] == null ? null : (Date) objs[10]);
            ssv.setAirportCode(objs[11] == null ? null : "" + objs[11]);
            ssv.setRead(objs[12] == null ? null : "" + objs[12]);
            ssv.setFeeName(objs[13] == null ? null : "" + objs[13]);
            if ("0".equals(ssv.getRead())) {
                UserRead ur = new UserRead();
                ur.setSettlementUploadId(ssv.getId());
                ur.setType("1");
                ur.setUserId("" + user.getId());
                urList.add(ur);
            }
            ssVoList.add(ssv);
        }
    }


    @Override
    public void uploadErrorFile(SettlementStatus settlementStatus,
                                         String errorFileName,List<ReCalcErrorDto>  errorDtoList) {
        // 将上传错误数据文件上传至minio
        if (CollUtil.isNotEmpty(errorDtoList) && !Strings.isBlank(errorFileName)) {
            try {
                Long fileKey = fileAttachmentBizService.uploadExcel(FileBusinessTypeEnum.SETTLEMENT_FAIL_RECORD,errorDtoList, errorFileName, ReCalcErrorDto.class);
                // 将minio存储的失败信息文件地址保存到数据库
                settlementStatus.setFailFile(fileKey.toString());
            } catch (Exception e) {
                log.error("出现业务异常", e);
                settlementStatus.setFailFile("错误数据文件生成失败！");
            }
        }
    }
}
