package com.swcares.aiot.core.importer.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.*;
import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * ClassName：com.swcares.importer.entity.ApsFlightInfo
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/8/31 11:10
 * @version v1.0
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_flight_info_segment")
@ApiModel(value = "Entity Of t_flight_info_segment")
public class FlightInfoSegment {
    /**
     * ID
     */

    @Id
    @GeneratedValue(generator="system-uuid",strategy = GenerationType.IDENTITY)
    @GenericGenerator(name="system-uuid", strategy = "uuid")
    @ApiModelProperty(name = "id",value = "ID")
    private String id;
    /**
     * 航班日期
     */
    @ApiModelProperty(value = "航班日期", name = "flightDate", required = false)
    private Date flightDate;
    /**
     * 航班号
     */
    @ApiModelProperty(value = "航班号", name = "flightNo", required = false)
    private String flightNo;
    /**
     * 起降标识
     */
    @ApiModelProperty(value = "起降标识", name = "flightFlag", required = false)
    private String flightFlag;
    /**
     * 航段
     */
    @ApiModelProperty(value = "航段", name = "flightSegment", required = false)
    private String flightSegment;

    @ApiModelProperty(value = "是否为航线中相邻的航段（1为是，0为否）")
    private Integer isNear;
    /**
     * 机场三字码
     */
    @ApiModelProperty(value = "机场三字码", name = "airportCode", required = false)
    private String airportCode;
    /**
     * 航班id
     */
    @ApiModelProperty(value = "航班id", name = "baseFlightId", required = false)
    private String baseFlightId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy", required = false)
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime", required = false)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "modifiedBy", required = false)
    private String modifiedBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifiedTime", required = false)
    private Date modifiedTime;
    /**
     * 持卡旅客人数
     */
    @ApiModelProperty(value = "持卡旅客人数", name = "cardHolderNumber", required = false)
    private Integer cardHolderNumber=0;
    /**
     * 持卡旅客随行人数
     */
    @ApiModelProperty(value = "持卡旅客随行人数", name = "accompanyingCardHolderNumber", required = false)
    private Integer accompanyingCardHolderNumber=0;
    /**
     * 重要旅客人数
     */
    @ApiModelProperty(value = "重要旅客人数", name = "importantNumber", required = false)
    private Integer importantNumber=0;
    /**
     * 重要旅客随行人数
     */
    @ApiModelProperty(value = "重要旅客随行人数", name = "accompanyingImportantNumber", required = false)
    private Integer accompanyingImportantNumber=0;
    /**
     * 持外交护照人数
     */
    @ApiModelProperty(value = "持外交护照人数", name = "diplomaticPassportNumber", required = false)
    private Integer diplomaticPassportNumber=0;
    /**
     * 商务舱人数
     */
    @ApiModelProperty(value = "商务舱人数", name = "clubClassNumber", required = false)
    private Integer clubClassNumber=0;
    /**
     * 头等舱人数
     */
    @ApiModelProperty(value = "头等舱人数", name = "firstClassNumber", required = false)
    private Integer firstClassNumber=0;
    /**
     * 经济舱人数
     */
    @ApiModelProperty(value = "经济舱人数", name = "economyClassNumber", required = false)
    private Integer economyClassNumber=0;
    /**
     * 成人数
     */
    @ApiModelProperty(value = "成人数", name = "adultNumber", required = false)
    private Integer adultNumber=0;
    /**
     * 儿童数
     */
    @ApiModelProperty(value = "儿童数", name = "childNumber", required = false)
    private Integer childNumber=0;
    /**
     * 婴儿数
     */
    @ApiModelProperty(value = "婴儿数", name = "infantNumber", required = false)
    private Integer infantNumber=0;
    /**
     * 过站成人数
     */
    @ApiModelProperty(value = "过站成人数", name = "transitAdultNumber", required = false)
    private Integer transitAdultNumber=0;
    /**
     * 过站儿童数
     */
    @ApiModelProperty(value = "过站儿童数", name = "transitChildNumber", required = false)
    private Integer transitChildNumber=0;
    /**
     * 过站婴儿数
     */
    @ApiModelProperty(value = "过站婴儿数", name = "transitInfantNumber", required = false)
    private Integer transitInfantNumber=0;
    /**
     * 进出港人数
     */
    @ApiModelProperty(value = "进出港人数", name = "psgNumber", required = false)
    private Integer psgNumber=0;
    /**
     * 客坐率
     */
    @ApiModelProperty(value = "客坐率", name = "plf", required = false)
    private Double plf=0.0;
    /**
     * 货物重量
     */
    @ApiModelProperty(value = "货物重量", name = "cargo", required = false)
    private Double cargo=0.0;
    /**
     * 邮件重量
     */
    @ApiModelProperty(value = "邮件重量", name = "mail", required = false)
    private Double mail=0.0;
    /**
     * 行李重量
     */
    @ApiModelProperty(value = "行李重量", name = "bag", required = false)
    private Double bag=0.0;
    /**
     * 行李件数
     */
    @ApiModelProperty(value = "行李件数", name = "bagNumber", required = false)
    private Double bagNumber=0.0;
    /**
     * 过站货物重量
     */
    @ApiModelProperty(value = "过站货物重量", name = "transitCargo", required = false)
    private Double transitCargo=0.0;
    /**
     * 过站邮件重量
     */
    @ApiModelProperty(value = "过站邮件重量", name = "transitMail", required = false)
    private Double transitMail=0.0;
    /**
     * 过站行李重量
     */
    @ApiModelProperty(value = "过站行李重量", name = "transitBag", required = false)
    private Double transitBag=0.0;
    /**
     * 过站行李件数
     */
    @ApiModelProperty(value = "过站行李件数", name = "transitBagNum", required = false)
    private Integer transitBagNum=0;
    /**
     * 重量单位
     */
    @ApiModelProperty(value = "重量单位", name = "weightUints", required = false)
    private String weightUints="KG";
    /**
     * 数据有效 有效:1|无效:0
     */
    @ApiModelProperty(value = "数据有效 有效:1|无效:0", name = "invalid", required = false)
    private String invalid="1";

}
