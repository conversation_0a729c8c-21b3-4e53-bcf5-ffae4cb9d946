package com.swcares.aiot.service;

import com.swcares.aiot.core.entity.TFlightBillHistorySnapshot;
import com.swcares.aiot.core.form.FlightBillForm;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.dto.FlightFeeBillDownloadDto;
import com.swcares.baseframe.common.security.LoginUserDetails;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.NewReCalcService
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/8 11:07
 * @version v1.0
 */
public interface IFlightFeeBillBizService {


    /**
     * Title: exportFlightBillInfo<br>
     * Author: 叶咏秋<br>
     * Description: (导出航班明细账单)<br>
     * Date:  16:56 <br>
     *
     * @param dto :
     * @param response       :
     */
    void download(FlightFeeBillDownloadDto dto, HttpServletResponse response);

    /**
     * 获取航班明细账单快照
     * @param billHistoryId 航班明细账单id
     * @return 航班明细账单快照
     */
    List<TFlightBillHistorySnapshot> getSnapshot(String billHistoryId);
}
