package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.annotation.OperateLog;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.entity.ExcelData;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.EasyExcelUtil;
import com.swcares.aiot.core.common.util.ExcelAircraftUtils;
import com.swcares.aiot.core.common.util.ExcelBridgeUtils;
import com.swcares.aiot.core.common.util.ExcelFlightLineUtils;
import com.swcares.aiot.core.common.util.ExcelFlightUtils;
import com.swcares.aiot.core.common.util.ExcelUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.core.common.util.RegexUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.ActualFlightTime;
import com.swcares.aiot.core.form.AirportBillSearchForm;
import com.swcares.aiot.core.form.ExportForm;
import com.swcares.aiot.core.form.FlightBusinessInfoConfirmForm;
import com.swcares.aiot.core.form.FlightInfoConfirmForm;
import com.swcares.aiot.core.form.FlightInfoExportForm;
import com.swcares.aiot.core.form.FlightInfoForm;
import com.swcares.aiot.core.form.FlightInfoSearchForm;
import com.swcares.aiot.core.form.FlightPlanForm;
import com.swcares.aiot.core.form.FlightUpdatePassengerForm;
import com.swcares.aiot.core.form.ReCalcForm;
import com.swcares.aiot.core.form.ServiceRecordForm;
import com.swcares.aiot.core.form.ServiceSecordBatchForm;
import com.swcares.aiot.core.form.UsedTimeAndNumberForm;
import com.swcares.aiot.core.importer.service.ImportService;
import com.swcares.aiot.core.model.entity.AircraftInfo;
import com.swcares.aiot.core.model.entity.AircraftRecord;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.core.model.entity.FlightLineRecord;
import com.swcares.aiot.core.model.entity.FlightPlanInfo;
import com.swcares.aiot.core.model.entity.FlightTerminal;
import com.swcares.aiot.core.model.entity.OperateRecord;
import com.swcares.aiot.core.model.entity.RulesVariableRecord;
import com.swcares.aiot.core.model.entity.ServiceRecord;
import com.swcares.aiot.core.model.entity.ServiceRecordConfirm;
import com.swcares.aiot.core.model.vo.ACCAExcelVo;
import com.swcares.aiot.core.model.vo.BridgeDataExcelParserVo;
import com.swcares.aiot.core.model.vo.FlightBusinessDataExcelParserVo;
import com.swcares.aiot.core.model.vo.FlightBusinessDataExcelVO;
import com.swcares.aiot.core.model.vo.FlightDataExcelParserVo;
import com.swcares.aiot.core.model.vo.FlightInfoCountVo;
import com.swcares.aiot.core.model.vo.FlightInfoDataVO;
import com.swcares.aiot.core.model.vo.FlightInfoVoNew;
import com.swcares.aiot.core.model.vo.FlightServiceVo;
import com.swcares.aiot.core.model.vo.ListServiceRecordByFlightIdsVo;
import com.swcares.aiot.core.model.vo.SafeguardTypeVo;
import com.swcares.aiot.core.model.vo.ServiceRecordVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.AircraftDao;
import com.swcares.aiot.dao.AircraftRecordDao;
import com.swcares.aiot.dao.AircraftRecordRepository;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.dao.FlightBillDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.aiot.dao.FlightInfoRepository;
import com.swcares.aiot.dao.FlightLineRecordDao;
import com.swcares.aiot.dao.FlightLineRecordRepository;
import com.swcares.aiot.dao.FlightPlanDao;
import com.swcares.aiot.dao.FlightTerminalDao;
import com.swcares.aiot.dao.OperateRecordDao;
import com.swcares.aiot.dao.ServiceRecordConfirmDao;
import com.swcares.aiot.dao.ServiceRecordDao;
import com.swcares.aiot.dao.VariableRecordDao;
import com.swcares.aiot.mapper.FlightInfoBizMapper;
import com.swcares.aiot.service.FlightDataMsgService;
import com.swcares.aiot.service.FlightInfoService;
import com.swcares.aiot.service.FormulaService;
import com.swcares.aiot.service.LogService;
import com.swcares.aiot.service.ReCalcService;
import com.swcares.aiot.service.UploadStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.beans.PropertyDescriptor;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ClassName：FlightInfoServiceImpl <br>
 * Description：(航班数据采集业务层)<br>
 * Copyright © 2020/6/18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/18 9:31<br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlightInfoServiceImpl implements FlightInfoService {

    public static final String  UPDATE_DATA="updateData";
    public static final String  OVERDUE_DATA="overdueData";
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private FlightBillDao flightBillDao;
    @Resource
    private FlightInfoRepository flightInfoRepository;
    @Resource
    private ServiceRecordDao serviceRecordDao;
    @Resource
    private ServiceRecordConfirmDao serviceRecordConfirmDao;
    @Resource
    private AircraftDao aircraftDao;
    @Resource
    private VariableRecordDao variableRecordDao;
    @Resource
    private FlightLineRecordDao flightLineRecordDao;
    @Resource
    private AircraftRecordDao aircraftRecordDao;
    @Resource
    private AircraftRecordRepository aircraftRecordRepository;
    @Resource
    private FlightLineRecordRepository flightLineRecordRepository;
    @Resource
    private FlightPlanDao flightPlanDao;
    @Resource
    private LogService logService;
    @Resource
    private FlightTerminalDao flightTerminalDao;
    @Resource
    private OperateRecordDao operateRecordDao;
    @Resource
    private ReCalcService reCalcService;
    @Resource
    private UploadStatusService uploadStatusService;
    @Resource
    private FeeDao feeDao;
    @Resource
    private ImportService importService;
    @Resource
    private FlightDataMsgService flightDataMsgService;
    @Resource
    private FlightInfoBizMapper flightInfoBizMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private FormulaService formulaService;

    private static final ReentrantLock NON_FAIR_LOCK = new ReentrantLock();
    private static final ReentrantLock NON_FAIR_LOCK_2 = new ReentrantLock();
    private static final ReentrantLock FAIR_LOCK = new ReentrantLock(true);
    private static final ReentrantLock FAIR_LOCK_2 = new ReentrantLock(true);
    private static final ReentrantLock STAY_TIME_LOCK = new ReentrantLock(true);
    private static final ReentrantLock FLIGHT_BUSINESS_LOCK = new ReentrantLock();

    public static final String MSG_ERROR = "出现业务异常";

    private static final String CONFIRM_CODE = "111111111111111111";

    /**
     * 结束日期
     * 当前日期day减1
     */
    private Date getEndDate() {
        return DateUtils.addDateDays(new Date(), -1);
    }

    /**
     * 开始日期
     * END_DATE day减60
     */
    private Date getStartDate() {
        return DateUtils.addDateDays(getEndDate(), -60);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bridgeTemplate(String ids, HttpServletResponse response) {
        Set<String> idset = new HashSet<>();
        if (CharSequenceUtil.isEmpty(ids)) {
            throw new GenericException(BusinessMessageEnum.BRIDGE_DOWN_ERROR.getCode(), BusinessMessageEnum.BRIDGE_DOWN_ERROR.getMsg());
        }
        String[] arrStr = ids.split(",");

        Collections.addAll(idset, arrStr);
        /* 查询航班数据 */
        List<FlightInfo> flightDateList = flightInfoDao.queryFlightDateByIds(idset);
        if (CollectionUtils.isEmpty(flightDateList)) {
            throw new GenericException(BusinessMessageEnum.BRIDGE_DOWN_ERROR.getCode(), BusinessMessageEnum.BRIDGE_DOWN_ERROR.getMsg());
        }

        FlightInfo fi = new FlightInfo();
        fi.setAirportCode("BZX");
        fi.setFlightDate(new Date());
        fi.setFlightNo("CA1111");
        fi.setRegNo("B1111");
        fi.setFlightSegment("CTU-BZX");
        fi.setFlightFlag("A");
        fi.setFlightTime(new Date());
        flightDateList.add(fi);
        XSSFWorkbook workbook = ExcelBridgeUtils.exportData(flightDateList);
        // 设置样板数据
        ExcelBridgeUtils.convertDataToRow2(flightDateList.size(), workbook);
        String fileName = CommonConstants.BRIDGE_TEMPLATE_FILE_NAME;
        FileUtils.exportToExcel(workbook, fileName, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HashMap<String, Object> importFlightDataBatch(MultipartFile file,
                                                         String airportCodeParam, String urlName, LoginUserDetails user) {
        // 验证上次文件合法性
        ExcelUtils.checkSuffix(file);
        HashMap<String, Object> map = new HashMap<>();
        // 接收解析数据
        List<FlightDataExcelParserVo> excelDataVOS =
                ExcelFlightUtils.readExcel(file, airportCodeParam);
        // 临时存放重复数据
        List<FlightDataExcelParserVo> resultRepeatData = new ArrayList<>();
        // 临时存放超期数据
        List<FlightDataExcelParserVo> resultOverdueData = new ArrayList<>();
        // 临时存放更新数据
        List<FlightDataExcelParserVo> resultData = new ArrayList<>();

        if (CollectionUtils.isEmpty(excelDataVOS)) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(),
                    BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }
        for (FlightDataExcelParserVo excelDataVO : excelDataVOS) {
            if (Objects.nonNull(excelDataVO)) {
                FlightDataExcelParserVo date;
                try {
                    // 查询已存在航班使用数据（返回前端，确认是否覆盖） 插入前先查询是否存在该条数据
                    date = getFlightData2(excelDataVO);
                    if (Objects.isNull(date) && DateUtils.isEffectiveDate(
                            excelDataVO.getFlightDate(), getStartDate(), getEndDate())) {
                        // 新增表数据
                        flightDataConvert(excelDataVO, user);
                        // 更新数据返回
                        resultData.add(excelDataVO);
                    } else if (Objects.isNull(date)
                            && !DateUtils.isEffectiveDate(excelDataVO.getFlightDate(),
                            getStartDate(), getEndDate())) {
                        // 添加过期数据
                        resultOverdueData.add(excelDataVO);
                    } else {
                        // 添加重复数据（返回前端，做覆盖处理）
                        resultRepeatData.add(excelDataVO);
                    }
                } catch (Exception e) {
                    log.error(" 导入失败 ", e);
                    throw new GenericException(
                            BusinessMessageEnum.FLIGHT_DATA_IMPORT_ERROR.getCode(),
                            BusinessMessageEnum.FLIGHT_DATA_IMPORT_ERROR.getMsg());
                }
            }
        }
        map.put(UPDATE_DATA, resultData.size());
        map.put(OVERDUE_DATA, resultOverdueData.size());
        map.put("repeatData", resultRepeatData.size());
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_IMPORT.getValue() + file.getOriginalFilename()
                + Constants.LogEnum.LOG_MEG_2.getValue();
        logService.addLogForImport(user, content, airportCodeParam);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HashMap<String, Object> importBridgeDataBatch(MultipartFile file,
                                                         String airportCodeParam, String urlName, LoginUserDetails user) {
        // 判断上传文件
        String ext = ExcelUtils.getFileName(file);

        // 验证导入文件合法性
        if (ExcelUtils.isAllow(ext.substring(1))) {
            throw new GenericException(BusinessMessageEnum.BRIDGE_IMPORT_SUFFIX_ERROR.getCode(),
                    BusinessMessageEnum.BRIDGE_IMPORT_SUFFIX_ERROR.getMsg());
        }

        // 读取上传航班数据
        List<BridgeDataExcelParserVo> excelDataVOS =
                ExcelBridgeUtils.readExcel(file, airportCodeParam);
        HashMap<String, Object> map = new HashMap<>();
        // 临时存放重复数据
        List<BridgeDataExcelParserVo> resultRepeatData = new ArrayList<>();
        // 临时存放超期数据
        List<BridgeDataExcelParserVo> resultOverdueData = new ArrayList<>();
        // 临时存放更新数据
        List<BridgeDataExcelParserVo> resultData = new ArrayList<>();
        if (CollectionUtils.isEmpty(excelDataVOS)) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(),
                    BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }

        for (BridgeDataExcelParserVo excelDataVO : excelDataVOS) {
            if (Objects.nonNull(excelDataVO)) {
                BridgeDataExcelParserVo date;
                try {
                    // 查询必要条件参数填充
                    date = getBridgeDataExcelParserVo(excelDataVO);
                    if (Objects.isNull(date) && DateUtils.isEffectiveDate(
                            excelDataVO.getFlightDate(), getStartDate(), getEndDate())) {
                        dataConvert(excelDataVO, user);
                        resultData.add(excelDataVO);
                    } else if (Objects.isNull(date)
                            && !DateUtils.isEffectiveDate(excelDataVO.getFlightDate(),
                            getStartDate(), getEndDate())) {
                        resultOverdueData.add(excelDataVO);
                    } else {
                        resultRepeatData.add(excelDataVO);
                    }
                } catch (Exception e) {
                    throw new GenericException(
                            BusinessMessageEnum.BRIDGE_DATA_IMPORT_ERROR.getCode(),
                            BusinessMessageEnum.BRIDGE_DATA_IMPORT_ERROR.getMsg());
                }
            }
        }
        map.put(UPDATE_DATA, resultData.size());
        map.put(OVERDUE_DATA, resultOverdueData.size());
        map.put("repeatData", resultRepeatData.size());
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_IMPORT.getValue() + file.getOriginalFilename()
                + Constants.LogEnum.LOG_MEG_2.getValue();
        logService.addLogForImport(user, content, airportCodeParam);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HashMap<String, Object> importBridgeRepeatDataBatch(MultipartFile file,
                                                               String airportCodeParam, String urlName, LoginUserDetails user) {
        // 判断上传文件
        String ext = ExcelUtils.getFileName(file);
        // 验证导入文件合法性
        if (ExcelUtils.isAllow(ext.substring(1))) {
            throw new GenericException(BusinessMessageEnum.BRIDGE_IMPORT_SUFFIX_ERROR.getCode(),
                    BusinessMessageEnum.BRIDGE_IMPORT_SUFFIX_ERROR.getMsg());
        }
        HashMap<String, Object> map = new HashMap<>();
        // 读取上传航班数据
        List<BridgeDataExcelParserVo> excelDataVOS =
                ExcelBridgeUtils.readExcel(file, airportCodeParam);
        // 临时存放超期数据
        List<BridgeDataExcelParserVo> resultOverdueData = new ArrayList<>();
        // 临时存放更新数据
        List<BridgeDataExcelParserVo> resultData = new ArrayList<>();

        if (CollectionUtils.isEmpty(excelDataVOS)) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(),
                    BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }
        for (BridgeDataExcelParserVo excelDataVO : excelDataVOS) {
            if (Objects.nonNull(excelDataVO)) {
                BridgeDataExcelParserVo date;
                try {
                    // 查询必要条件参数填充
                    date = getBridgeDataExcelParserVo(excelDataVO);
                    if (Objects.nonNull(date) && DateUtils.isEffectiveDate(
                            excelDataVO.getFlightDate(), getStartDate(), getEndDate())) {
                        FlightInfo flightInfo = flightDataConvert(date, user);
                        // 根据开始结束时间，计算服务使用时长
                        excelDataVO.setServiceTime(DateUtils.calculateLengthOfTime("CBT",
                                excelDataVO.getStartTime(), excelDataVO.getEndTime()));
                        serviceRecordDataConvert(excelDataVO, flightInfo, user);
                        resultData.add(excelDataVO);
                    } else {
                        resultOverdueData.add(excelDataVO);
                    }
                } catch (Exception e) {
                    throw new GenericException(
                            BusinessMessageEnum.BRIDGE_DATA_IMPORT_ERROR.getCode(),
                            BusinessMessageEnum.BRIDGE_DATA_IMPORT_ERROR.getMsg());
                }
            }
        }
        map.put(UPDATE_DATA, resultData.size());
        map.put(OVERDUE_DATA, resultOverdueData.size());
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_IMPORT.getValue() + file.getOriginalFilename()
                + Constants.LogEnum.LOG_MEG_2.getValue();
        logService.addLogForImport(user, content, airportCodeParam);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HashMap<String, Object> importFlightRepeatDataBatch(MultipartFile file,
                                                               String airportParam, String urlName, LoginUserDetails user) {
        ExcelUtils.checkSuffix(file);
        List<FlightDataExcelParserVo> excelDataVOS = ExcelFlightUtils.readExcel(file, airportParam);

        HashMap<String, Object> map = new HashMap<>();
        // 临时存放更新数据
        List<FlightDataExcelParserVo> resultData = new ArrayList<>();
        // 临时存放超期数据
        List<FlightDataExcelParserVo> resultOverdueData = new ArrayList<>();

        if (CollectionUtils.isEmpty(excelDataVOS)) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(),
                    BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }

        for (FlightDataExcelParserVo excelDataVO : excelDataVOS) {
            if (Objects.nonNull(excelDataVO)) {
                FlightDataExcelParserVo date;
                try {
                    // 获取查询参数
                    date = getFlightData(excelDataVO);
                    if (Objects.nonNull(date) && DateUtils.isEffectiveDate(
                            excelDataVO.getFlightDate(), getStartDate(), getEndDate())) {
                        flightDataConvert(date, user);
                        resultData.add(excelDataVO);
                    } else {
                        resultOverdueData.add(excelDataVO);
                    }
                } catch (Exception e) {
                    throw new GenericException(
                            BusinessMessageEnum.FLIGHT_DATA_IMPORT_ERROR.getCode(),
                            BusinessMessageEnum.FLIGHT_DATA_IMPORT_ERROR.getMsg());
                }
            }
        }
        map.put(UPDATE_DATA, resultData.size());
        map.put(OVERDUE_DATA, resultOverdueData.size());
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_IMPORT.getValue() + file.getOriginalFilename()
                + Constants.LogEnum.LOG_MEG_2.getValue();
        logService.addLogForImport(user, content, airportParam);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportBridgeDataBatch(ExportForm form, HttpServletResponse res, String urlName, LoginUserDetails user) {
        if (Objects.isNull(form)) {
            throw new GenericException(BusinessMessageEnum.QUERY_DATA_IS_EMPTY.getCode(), BusinessMessageEnum.QUERY_DATA_IS_EMPTY.getMsg());
        }
        List<BridgeDataExcelParserVo> bridgeDataBatch = flightInfoRepository.getBridgeDataBatch(
                form.getStartDate(), form.getEndDate(), form.getAirlineCode(), form.getFlightNo(),
                form.getFlightFlag(), form.getAirportCode());
        XSSFWorkbook workbook = ExcelBridgeUtils.exportBridgeData(bridgeDataBatch);
        String fileName = CommonConstants.BRIDGE_FILE_NAME;
        FileUtils.exportToExcel(workbook, fileName, res);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, form, content, form.getAirportCode(), "廊桥");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportFlightDataBatch(ExportForm form, HttpServletResponse res, String urlName, LoginUserDetails user) {
        if (null == form) {
            throw new GenericException(BusinessMessageEnum.QUERY_DATA_IS_EMPTY.getCode(), BusinessMessageEnum.QUERY_DATA_IS_EMPTY.getMsg());
        }
        //如果航班日期和起降日期都是空的则默认起降日期当前前一个月的
        if ((form.getStartDate() == null || form.getEndDate() == null) && (form.getFlightTimeStartDate() == null || form.getFlightTimeEndDate() == null)) {
            form.setFlightTimeEndDate(new Date());
            Calendar calendar = CalendarUtil.calendar(DateUtil.beginOfDay(new Date()));
            calendar.add(Calendar.MONTH, -1);
            form.setFlightTimeStartDate(calendar.getTime());
        }

        List<Object[]> objectList = flightInfoDao.getFlightDataBatch(form.getStartDate(), form.getEndDate(),
                form.getFlightTimeStartDate(), form.getFlightTimeEndDate(),
                form.getAirlineCode(), form.getFlightNo(), form.getFlightFlag(),
                form.getAirportCode());
        List<Object[]> flightLineList = flightInfoDao.getFlightLineCount(form.getStartDate(), form.getEndDate(), form.getFlightTimeStartDate(), form.getFlightTimeEndDate());
        Map<String, Integer> flightLineMap = new HashMap<>();
        for (Object[] objs : flightLineList) {
            if (CharSequenceUtil.isNotBlank((String) objs[0])) {
                flightLineMap.put((String) objs[0], Integer.parseInt(objs[1] + ""));
            }
        }

        List<FlightDataExcelParserVo> flightDataBatch = getFlightDataBatch(objectList, flightLineMap);
        try {
            // 导出DBF文件名
            String fileName = CommonConstants.FLIGHT_FILE_NAME;
            //获取指定表头
            String[][] excelHeadYebpProductSystem = ExcelFlightUtils.getExcelHeadYbpProductSystemTitleByNumber();
            //导出DBF
            FileUtils.exportDbf(res, flightDataBatch, FlightDataExcelParserVo.class, fileName, excelHeadYebpProductSystem);
        } catch (Exception e) {
            log.error("导出失败！", e);
        }

        // 操作日志
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, form, content, form.getAirportCode(), "航班");
    }

    private List<FlightDataExcelParserVo> getFlightDataBatch(List<Object[]> objectList, Map<String, Integer> flightLineMap) {
        List<FlightDataExcelParserVo> flightDataBatch = new ArrayList<>();
        for (Object[] obj : objectList) {
            String airportCode = obj[2] == null ? "" : String.valueOf(obj[2]);
            String flightLine = obj[7] == null ? "" : String.valueOf(obj[7]);
            if (CharSequenceUtil.isNotBlank(airportCode)
                    && CharSequenceUtil.isNotBlank(flightLine)
                    && flightLine.split("-").length >= 3
                    && checkAirline(airportCode,flightLine,String.valueOf(obj[9]))) {
                List<Object[]> splitList = flightInfoDao.getFlightDataBatch((String) obj[30]);
                for (Object[] splitObj : splitList) {
                    flightDataBatch.add(getFlightDataExcelParserVo(splitObj));
                }
            } else {
                flightDataBatch.add(getFlightDataExcelParserVo(obj));
            }
        }
        excelSetFlag(flightDataBatch, flightLineMap);
        return flightDataBatch;
    }

    private boolean checkAirline(String airportCode,String flightLine,String flightFlag){
        String[] split = flightLine.split("-");
        for(int i=0;i<split.length;i++){
            if(split[i].equals(airportCode)
                    &&((flightFlag.equals("A") && i>=1)||(flightFlag.equals("D") && i<=split.length-2))){
                return true;
            }
        }
        return false;
    }

    private void excelSetFlag(List<FlightDataExcelParserVo> flightDataBatch, Map<String, Integer> flightLineMap) {
        for (FlightDataExcelParserVo dataBatch : flightDataBatch) {
            String flightLine = dataBatch.getFlightLine();
            int flightData = flightLineMap.getOrDefault(flightLine, 0);
            if (flightData > 0) {
                dataBatch.setAirportFlag("FALSE");
            } else {
                dataBatch.setAirportFlag("TRUE");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFlightLineDataBatch(MultipartFile file, String param, String urlName, LoginUserDetails user) {
        if (CharSequenceUtil.isEmpty(param)) {
            throw new GenericException(BusinessMessageEnum.QUERY_DATA_IS_EMPTY.getCode(),
                    BusinessMessageEnum.QUERY_DATA_IS_EMPTY.getMsg());
        }
        /*
         * 创建锁
         * true:公平锁，所有线程安装先后队列顺序获取锁资源
         */
        // 读取上传航线数据
        List<FlightLineRecord> excelDataVOS = ExcelFlightLineUtils.readExcel(file, param);
        if (CollectionUtils.isEmpty(excelDataVOS)) {
            throw new GenericException(BusinessMessageEnum.FLIGHT_LINE_DATA_IS_EMPTY.getCode(),
                    BusinessMessageEnum.FLIGHT_LINE_DATA_IS_EMPTY.getMsg());
        }
        try {
            FAIR_LOCK.lock();
            // 清空数据库
            flightLineRecordDao.truncateFlightLineRecord();
            flightLineRecordDao.flush();
            flightLineRecordRepository.saveAll(excelDataVOS);
        } catch (Exception e) {
            throw new GenericException(BusinessMessageEnum.DATABASE_ERROR.getCode(),
                    BusinessMessageEnum.DATABASE_ERROR.getMsg());
        } finally {
            FAIR_LOCK.unlock();
        }
    }

    @Override
    @Transactional
    public void importAircraftDataBatch(MultipartFile file) {

        // 读取上传机号数据
        List<AircraftRecord> excelDataVOS = ExcelAircraftUtils.readExcel(file);
        if (CollectionUtils.isEmpty(excelDataVOS)) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(),
                    BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }
        try {
            FAIR_LOCK_2.lock();
            // 清空数据库
            aircraftRecordDao.truncateAircraftRecord();
            aircraftRecordDao.flush();
            aircraftRecordRepository.saveAll(excelDataVOS);
        } catch (Exception e) {
            throw new GenericException(BusinessMessageEnum.DATABASE_ERROR.getCode(),
                    BusinessMessageEnum.DATABASE_ERROR.getMsg());
        } finally {
            FAIR_LOCK_2.unlock();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<FlightPlanInfo>> saveFlightPlanInfo(List<FlightPlanForm> flightPlanForms,
                                                                  LoginUserDetails user, String airportCode, String urlName) {
        this.checkFlightPlan(flightPlanForms);
        List<FlightPlanForm> listWithoutDuplicates =
                flightPlanForms.stream().distinct().collect(Collectors.toList());
        int i = flightPlanForms.size() - listWithoutDuplicates.size();
        List<FlightPlanInfo> flightPlanInfos1 = new ArrayList<>();
        for (FlightPlanForm listWithoutDuplicate : listWithoutDuplicates) {
            FlightPlanInfo flightPlanInfo = new FlightPlanInfo();
            flightPlanInfo.setFlightDate(listWithoutDuplicate.getFlightDate());
            flightPlanInfo.setFlightNo(listWithoutDuplicate.getFlightNo());
            flightPlanInfo.setFlightFlag(listWithoutDuplicate.getFlightFlag());
            flightPlanInfo.setCreateBy(user.getUsername());
            flightPlanInfo.setCreateTime(new Date());
            flightPlanInfo.setAirportCode(airportCode);
            if (flightPlanDao.getFlightPlanInfoByFlightDateAndFlightNoAndFlightFlag(
                    flightPlanInfo.getFlightDate(), flightPlanInfo.getFlightNo(), airportCode,
                    flightPlanInfo.getFlightFlag()) != null) {
                flightPlanInfos1.add(flightPlanInfo);
            } else {
                flightPlanDao.save(flightPlanInfo);
                String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                        + Constants.LogEnum.LOG_MEG_SAVE.getValue();
                FlightPlanForm flightPlanForm = new FlightPlanForm();
                try {
                    BeanUtils.copyProperties(flightPlanInfo, flightPlanForm);
                } catch (Exception e) {
                    log.error("copyProperties fail！ flightPlanInfo = {}", flightPlanInfo, e);
                }
                logService.addLogForSave(flightPlanForm, user, content, airportCode);
            }
        }
        int k = listWithoutDuplicates.size() - flightPlanInfos1.size();
        if (flightPlanInfos1.isEmpty() && i == 0) {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>()
                    .data(flightPlanInfos1).msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue()
                            + k + Constants.FlightPlanEnum.SAVE_MSG_2.getValue())
                    .builder();
        } else if (!flightPlanInfos1.isEmpty() && i != 0) {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>().data(flightPlanInfos1)
                    .msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue() + k
                            + Constants.FlightPlanEnum.SAVE_MSG_2.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue() + i
                            + Constants.FlightPlanEnum.SAVE_MSG_4.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue()
                            + flightPlanInfos1.size()
                            + Constants.FlightPlanEnum.SAVE_MSG_5.getValue())
                    .builder();
        } else if (flightPlanInfos1.isEmpty()) {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>().data(flightPlanInfos1)
                    .msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue() + k
                            + Constants.FlightPlanEnum.SAVE_MSG_2.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue() + i
                            + Constants.FlightPlanEnum.SAVE_MSG_4.getValue())
                    .builder();
        } else {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>().data(flightPlanInfos1)
                    .msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue() + k
                            + Constants.FlightPlanEnum.SAVE_MSG_2.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue()
                            + flightPlanInfos1.size()
                            + Constants.FlightPlanEnum.SAVE_MSG_5.getValue())
                    .builder();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Page<FlightPlanInfo>> pageFlightPlanInfoByCondition(String flightDate, String flightNo,
                                                                             String aiportCode, PageParam pageParam) {
        Pageable pageable = PageRequest.of(pageParam.getPage() - 1, pageParam.getLimit());
        return new ResultBuilder.Builder<Page<FlightPlanInfo>>().data(flightPlanDao.pageFlightPlanInfoByCondition(flightDate, flightNo, aiportCode, pageable)).builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> delFlightPlanInfo(String id, LoginUserDetails user, String urlName) {
        FlightPlanInfo flightPlanInfo = flightPlanDao.getById(id);
        flightPlanDao.deleteByFlightPlanId(id, user.getUsername(), DateUtils.format(new Date()));
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_DELETE.getValue();
        FlightPlanForm flightPlanForm = new FlightPlanForm();
        try {
            BeanUtils.copyProperties(flightPlanInfo, flightPlanForm);
        } catch (Exception e) {
            log.error("copyProperties fail ! flightPlanInfo = {}", flightPlanInfo, e);
        }
        logService.addLogForDelete(flightPlanForm, user, content, flightPlanInfo.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void flightTemplate(HttpServletResponse response) {
        XSSFWorkbook workbook = ExcelFlightUtils.exportTemplate();
        String fileName = CommonConstants.FLIGHT_TEMPLATE_FILE_NAME;
        FileUtils.exportToExcel(workbook, fileName, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<FlightPlanInfo>> importFlightPlanInfo(MultipartFile file, LoginUserDetails user,
                                                                    String airportCode, String urlName) {
        List<Object[]> list = FileUtils.importExcel(file);
        List<FlightPlanInfo> flightPlanInfos = new ArrayList<>();
        List<FlightPlanForm> flightPlanForms = new ArrayList<>();
        for (Object[] objects : list) {
            FlightPlanInfo flightPlanInfo = new FlightPlanInfo();
            FlightPlanForm flightPlanForm = new FlightPlanForm();
            if ("null".equals(String.valueOf(objects[0]))) {
                continue;
            }
            Date date = new Date((long) (Double.parseDouble(String.valueOf(objects[0]))));
            flightPlanInfo.setFlightDate(DateUtils.format(date));
            flightPlanInfo.setFlightNo((String) objects[1]);
            flightPlanInfo.setFlightFlag((String) objects[2]);
            try {
                BeanUtils.copyProperties(flightPlanInfo, flightPlanForm);
            } catch (Exception e) {
                log.error("BeanUtils.copyProperties fail！flightPlanInfo = {}", flightPlanInfo, e);
            }
            flightPlanInfos.add(flightPlanInfo);
            flightPlanForms.add(flightPlanForm);
        }
        this.checkFlightPlan(flightPlanForms);
        List<FlightPlanInfo> listWithoutDuplicates =
                flightPlanInfos.stream().distinct().collect(Collectors.toList());
        // 上传文件中重复数据条数
        int i = flightPlanInfos.size() - listWithoutDuplicates.size();
        List<FlightPlanInfo> flightPlanInfos1 = new ArrayList<>();
        for (FlightPlanInfo listWithoutDuplicate : listWithoutDuplicates) {
            FlightPlanInfo flightPlanInfo = new FlightPlanInfo();
            flightPlanInfo.setFlightDate(listWithoutDuplicate.getFlightDate());
            flightPlanInfo.setFlightNo(listWithoutDuplicate.getFlightNo());
            flightPlanInfo.setFlightFlag(listWithoutDuplicate.getFlightFlag());
            flightPlanInfo.setAirportCode(airportCode);
            flightPlanInfo.setCreateBy(user.getUsername());
            flightPlanInfo.setCreateTime(new Date());
            if (flightPlanDao.getFlightPlanInfoByFlightDateAndFlightNoAndFlightFlag(
                    flightPlanInfo.getFlightDate(), flightPlanInfo.getFlightNo(), airportCode,
                    flightPlanInfo.getFlightFlag()) != null) {
                flightPlanInfos1.add(flightPlanInfo);
            } else {
                flightPlanDao.save(flightPlanInfo);
            }
        }
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_IMPORT.getValue() + file.getOriginalFilename()
                + Constants.LogEnum.LOG_MEG_2.getValue();
        logService.addLogForImport(user, content, airportCode);
        // 成功上传数据条数
        int k = listWithoutDuplicates.size() - flightPlanInfos1.size();
        if (flightPlanInfos1.isEmpty() && i == 0) {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>().data(flightPlanInfos1)
                    .msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue() + k + Constants.FlightPlanEnum.SAVE_MSG_2.getValue())
                    .builder();
        } else if (!flightPlanInfos1.isEmpty() && i != 0) {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>().data(flightPlanInfos1)
                    .msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue() + k
                            + Constants.FlightPlanEnum.SAVE_MSG_2.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue() + i
                            + Constants.FlightPlanEnum.SAVE_MSG_4.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue()
                            + flightPlanInfos1.size()
                            + Constants.FlightPlanEnum.SAVE_MSG_5.getValue())
                    .builder();
        } else if (flightPlanInfos1.isEmpty()) {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>().data(flightPlanInfos1)
                    .msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue() + k
                            + Constants.FlightPlanEnum.SAVE_MSG_2.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue() + i
                            + Constants.FlightPlanEnum.SAVE_MSG_4.getValue())
                    .builder();
        } else {
            return new ResultBuilder.Builder<List<FlightPlanInfo>>().data(flightPlanInfos1)
                    .msg(Constants.FlightPlanEnum.SAVE_MSG_1.getValue() + k
                            + Constants.FlightPlanEnum.SAVE_MSG_2.getValue()
                            + Constants.FlightPlanEnum.SAVE_MSG_3.getValue()
                            + flightPlanInfos1.size()
                            + Constants.FlightPlanEnum.SAVE_MSG_5.getValue())
                    .builder();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> coverFlightPlanInfo(List<FlightPlanForm> flightPlanForms,
                                                     String airportCode) {
        for (FlightPlanForm flightPlanForm : flightPlanForms) {
            FlightPlanInfo flightPlanInfo1 =
                    flightPlanDao.getFlightPlanInfoByFlightDateAndFlightNoAndFlightFlag(
                            flightPlanForm.getFlightDate(), flightPlanForm.getFlightNo(),
                            airportCode, flightPlanForm.getFlightFlag());
            flightPlanDao.save(flightPlanInfo1);
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportFlightPlanTemplate(HttpServletResponse response) {
        // 生成xlsx的Excel
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 生成Sheet表，写入第一行的列头
        ExcelUtils.buildDataSheet(workbook, CommonConstants.FLIGHT_PLAN_FILE_HEADER);
        String fileName = CommonConstants.FLIGHT_PLAN_FILE_NAME;
        FileUtils.exportToExcel(workbook, fileName, response);
    }

    /*
     * Title: checkFlightPlan<br> Author: 李龙<br> Description: (校验航班计划)<br> Date: 11:11 <br>
     *
     * @param list return: int
     */
    private void checkFlightPlan(List<FlightPlanForm> flightPlanForms) {
        // 航班号不符合规范
        int y = 0;
        // 航班日期小于T-1
        int x = 0;
        String pattern = "^[A-Za-z0-9][A-Za-z]\\d{1,2}[A-Za-z0-9]?[A-Za-z0-9]{1,2}$";
        Date now = new Date();
        for (FlightPlanForm flightPlanForm : flightPlanForms) {
            String date = flightPlanForm.getFlightDate();
            Date date1 = new Date();
            try {
                date1 = new SimpleDateFormat("yyyy-MM-dd").parse(date);
            } catch (Exception e) {
                log.error(MSG_ERROR, e);
            }
            String flightNo = flightPlanForm.getFlightNo();
            if (date1.getTime() < DateUtils.addDateDays(now, -1).getTime()) {
                x++;
                continue;
            }
            if (!Pattern.matches(pattern, flightNo) || flightNo.length() > 7
                    || flightNo.length() < 5) {
                y++;
            }
        }
        if (x != 0) {
            throw new GenericException(BusinessMessageEnum.DATA__FLIGHT_PLAN_ERROR_1.getCode(),
                    BusinessMessageEnum.DATA__FLIGHT_PLAN_ERROR_1.getMsg() + x
                            + BusinessMessageEnum.DATE__FLIGHT_PLAN_ERROR_2.getMsg()
                            + DateUtils.format(DateUtils.addDateDays(now, -1))
                            + BusinessMessageEnum.DATE__FLIGHT_PLAN_ERROR_3.getMsg());
        } else if (y != 0) {
            throw new GenericException(BusinessMessageEnum.FLIGHT_NO_FORMATE_ERROR_1.getCode(),
                    BusinessMessageEnum.FLIGHT_NO_FORMATE_ERROR_1.getMsg() + y
                            + BusinessMessageEnum.FLIGHT_NO_FORMATE_ERROR_2.getMsg());
        }

    }

    /**
     * Title: getFlightData<br>
     * Author: wangxiong<br>
     * Description: 通过条件查询航班使用数据 <br>
     * Date:  16:54 <br>
     *
     * @param excelDataVO 航班使用拓展Vo
     * @return : FlightDataExcelParserVo 拓展Vo
     */
    private FlightDataExcelParserVo getFlightData(FlightDataExcelParserVo excelDataVO) {
        // 获取查询参数
        FlightDataExcelParserVo date;
        String airlineCode = excelDataVO.getAirlineCode();
        String airportCode = excelDataVO.getAirportCode();
        // 拼接航司与航班号组成数据库存储值
        String flightNo = airlineCode.concat(excelDataVO.getFlightNo());
        String flightModel = excelDataVO.getFlightModel();
        String flightSegment = excelDataVO.getFlightSegment();
        String flightSegmentType = excelDataVO.getFlightSegmentType();
        Date flightTime = excelDataVO.getFlightTime();
        String regNo = excelDataVO.getRegNo();
        String flightLine = excelDataVO.getFlightLine();
        String flightLineType = excelDataVO.getFlightLineType();
        String flightFlag = excelDataVO.getFlightFlag();
        Integer adultNumber = excelDataVO.getAdultNumber();
        Integer childNumber = excelDataVO.getChildNumber();
        Integer infantNumber = excelDataVO.getInfantNumber();
        Double bag = excelDataVO.getBag();
        Integer bagNumber = excelDataVO.getBagNumber();
        Double cargo = excelDataVO.getCargo();
        Double mail = excelDataVO.getMail();
        date = flightInfoDao.getFlightData(airlineCode, airportCode, flightNo, flightModel,
                flightSegment, flightSegmentType, flightTime, regNo, flightLine, flightLineType,
                flightFlag, adultNumber, childNumber, infantNumber, bag, bagNumber, cargo, mail);
        return date;
    }

    /**
     * Title: getFlightData2<br>
     * Author: 刘志恒<br>
     * Description: 通过航班号、机场、航班他日期、起降标识获取唯一航班<br>
     * Date:  2022/2/7 9:37 <br>
     *
     * @param excelDataVO :
     */
    private FlightDataExcelParserVo getFlightData2(FlightDataExcelParserVo excelDataVO) {
        FlightDataExcelParserVo date;
        String airlineCode = excelDataVO.getAirlineCode();
        String airportCode = excelDataVO.getAirportCode();
        // 拼接航司与航班号组成数据库存储值
        String flightNo = airlineCode.concat(excelDataVO.getFlightNo());
        String flightFlag = excelDataVO.getFlightFlag();
        Date flightDate = excelDataVO.getFlightDate();
        date = flightInfoDao.getFlightData(airportCode, flightNo, flightDate, flightFlag);
        return date;
    }

    /**
     * Title: getBridgeDataExcelParserVo<br>
     * Author: Man<br>
     * Description: 查询数据库信息，并返回excel拓展实体对象数据 <br>
     * Date:  16:03 <br>
     *
     * @param excelDataVO excel拓展数据
     * @return : BridgeDataExcelParserVo
     */
    private BridgeDataExcelParserVo getBridgeDataExcelParserVo(
            BridgeDataExcelParserVo excelDataVO) {
        BridgeDataExcelParserVo date;
        String airportCode = excelDataVO.getAirportCode();
        Date flightDate = excelDataVO.getFlightDate();
        String flightNo = excelDataVO.getFlightNo();
        String regNo = excelDataVO.getRegNo();
        String flightSegment = excelDataVO.getFlightSegment();
        String flightFlag = excelDataVO.getFlightFlag();
        Date flightTime = excelDataVO.getFlightTime();
        date = flightInfoDao.getFlightData(airportCode, flightDate, flightNo, regNo, flightSegment,
                flightFlag, flightTime);
        return date;
    }

    /**
     * Title: flightDataConvert<br>
     * Author: Man<br>
     * Description: 拓展Vo实体返回对象转数据库映射表对象并保存 <br>
     * Date:  16:00 <br>
     *
     * @param excelDataVO 拓展实体
     */
    private void flightDataConvert(FlightDataExcelParserVo excelDataVO, LoginUserDetails user) {
        // 新增一条航班记录
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setId(excelDataVO.getId());
        flightInfo.setFlightDate(excelDataVO.getFlightDate());
        flightInfo.setAirlineCode(excelDataVO.getAirlineCode());
        flightInfo.setAirportCode(excelDataVO.getAirportCode());
        // 航班号验证
        if (RegexUtils.checkBridgeFlightNo(excelDataVO.getFlightNo())) {
            flightInfo.setFlightNo(excelDataVO.getFlightNo());
        } else {
            flightInfo.setFlightNo(excelDataVO.getAirlineCode().concat(excelDataVO.getFlightNo()));
        }
        flightInfo.setFlightModel(excelDataVO.getFlightModel());
        flightInfo.setTaskFlag(excelDataVO.getFlightType());
        flightInfo.setFlightLine(excelDataVO.getFlightLine());
        flightInfo.setFlightLineType(excelDataVO.getFlightLineType());
        flightInfo.setFlightSegment(excelDataVO.getFlightSegment());
        flightInfo.setFlightSegmentType(excelDataVO.getFlightSegmentType());
        flightInfo.setFlightFlag(excelDataVO.getFlightFlag());
        flightInfo.setFlightTime(excelDataVO.getFlightTime());
        flightInfo.setAdultNumber(excelDataVO.getAdultNumber());
        flightInfo.setChildNumber(excelDataVO.getChildNumber());
        flightInfo.setInfantNumber(excelDataVO.getInfantNumber());
        flightInfo.setTransitAdultNumber(excelDataVO.getTransitAdultNumber());
        flightInfo.setTransitChildNumber(excelDataVO.getTransitChildNumber());
        flightInfo.setTransitInfantNumber(excelDataVO.getTransitInfantNumber());
        flightInfo.setCargo(excelDataVO.getCargo());
        flightInfo.setMail(excelDataVO.getMail());
        flightInfo.setBag(excelDataVO.getBag());
        flightInfo.setBagNumber(excelDataVO.getBagNumber());
        flightInfo.setRegNo(excelDataVO.getRegNo());
        flightInfo.setDiplomaticPassportNumber(excelDataVO.getDiplomaticPassportNumber());
        flightInfo.setCreateTime(new Date());
        // 设置为手动录入起降时间
        flightInfo.setIsModifyFlightTime(1);
        flightInfo.setCreateBy(user.getUsername());
        // 计算客座率
        int seatNum = excelDataVO.getMaxSeat();
        int peopleNum = excelDataVO.getAdultNumber() + excelDataVO.getChildNumber()
                + excelDataVO.getTransitAdultNumber() + excelDataVO.getTransitChildNumber();
        flightInfo.setPsgNumber(peopleNum);
        if (seatNum != 0) {
            flightInfo.setPlf((double) 100 * (peopleNum) / seatNum);
        }
        flightInfoDao.save(flightInfo);
        flightInfoDao.flush();
        // 判断是否关联从签单同步过来的无flightId服务记录
        syncServiceRecord(flightInfo);
    }

    /**
     * Title: serviceRecordDataConvert<br>
     * Author: Man<br>
     * Description: 拓展Vo实体返回对象转数据库映射表对象并保存 <br>
     * Date:  15:15 <br>
     *
     * @param excelDataVO 拓展实体
     * @param flightInfo  数据库映射实体
     */
    private void serviceRecordDataConvert(BridgeDataExcelParserVo excelDataVO,
                                          FlightInfo flightInfo, LoginUserDetails user) {
        // 新增一条客桥使用信息记录
        ServiceRecord serviceRecord = new ServiceRecord();
        serviceRecord.setId(excelDataVO.getSId());
        serviceRecord.setFlightId(flightInfo.getId());
        serviceRecord.setCreateTime(new Date());
        serviceRecord.setCreateBy(user.getUsername());
        serviceRecord.setAirportCode(excelDataVO.getAirportCode());
        serviceRecord.setStartTime(excelDataVO.getStartTime());
        serviceRecord.setEndTime(excelDataVO.getEndTime());
        serviceRecord.setServiceNumber(excelDataVO.getServiceNumber());
        serviceRecord.setSeat(excelDataVO.getAirportSeat());
        serviceRecord
                .setServiceName(Constants.Indicators.BRIDGE_DATA_IMPORT_SERVICE_NAME.getValue());
        serviceRecord.setUsedNumber(excelDataVO.getServiceTime());
        serviceRecord
                .setServiceCode(Constants.Indicators.BRIDGE_DATA_IMPORT_SERVICE_CODE.getValue());
        serviceRecordDao.save(serviceRecord);
        serviceRecordDao.flush();
    }

    /**
     * Title: flightDataConvert<br>
     * Author: Man<br>
     * Description: 拓展Vo实体返回对象转数据库映射表对象并保存 <br>
     * Date:  15:15 <br>
     *
     * @param excelDataVO 拓展实体
     * @return : FlightInfo 数据库实体
     */
    private FlightInfo flightDataConvert(BridgeDataExcelParserVo excelDataVO,
                                         LoginUserDetails user) {
        // 新增一条航班记录
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setId(excelDataVO.getId());
        flightInfo.setAirportCode(excelDataVO.getAirportCode());
        flightInfo.setFlightDate(excelDataVO.getFlightDate());
        flightInfo.setAirlineCode(excelDataVO.getFlightNo().substring(0, 2));
        flightInfo.setFlightNo(excelDataVO.getFlightNo());
        flightInfo.setFlightSegment(excelDataVO.getFlightSegment());
        flightInfo.setFlightFlag(excelDataVO.getFlightFlag());
        if (excelDataVO.getFlightFlag().equals(Constants.Indicators.FLIGHT_FLAG_A.getValue())) {
            flightInfo.setFlightFee(Constants.Indicators.GENERIC_CONFIRM_FLAG.getValue());
        } else if (excelDataVO.getFlightFlag().equals(Constants.Indicators.FLIGHT_FLAG_D.getValue())) {
            flightInfo.setFlightFee(Constants.Indicators.GENERIC_NEGATIVE_FLAG.getValue());
        }
        flightInfo.setFromAirportCode(flightInfo.getFlightSegment().substring(0, 3));
        flightInfo.setToAirportCode(flightInfo.getFlightSegment().substring(4, 7));
        if (flightInfo.getFlightSegment().substring(0, 3)
                .equals(Constants.Indicators.AIRPOET_CODE_FLAG.getValue())) {
            flightInfo.setFlightType(Constants.Indicators.FLIGHT_FLAG_D.getValue());
        } else {
            flightInfo.setFlightType(Constants.Indicators.FLIGHT_FLAG_A.getValue());
        }

        if (flightInfo.getFlightSegment().substring(0, 3)
                .equals(Constants.Indicators.AIRPOET_CODE_FLAG.getValue())) {
            flightInfo.setFlightSegmentType(Constants.Indicators.FLIGHT_FLAG_D.getValue());
        } else {
            flightInfo.setFlightSegmentType(Constants.Indicators.FLIGHT_FLAG_A.getValue());
        }
        flightInfo.setFlightTime(excelDataVO.getFlightTime());
        flightInfo.setRegNo(excelDataVO.getRegNo());
        flightInfo.setCreateTime(new Date());
        flightInfo.setCreateBy(user.getUsername());
        flightInfoDao.save(flightInfo);
        flightInfoDao.flush();

        // 判断是否关联从签单同步过来的无flightId服务记录
        syncServiceRecord(flightInfo);
        return flightInfo;
    }


    /**
     * Title: dataConvert<br>
     * Author: Man<br>
     * Description: 拓展Vo实体返回对象转数据库映射表对象并保存 <br>
     * Date:  11:31 <br>
     *
     * @param excelDataVO excel拓展实体
     */
    private void dataConvert(BridgeDataExcelParserVo excelDataVO, LoginUserDetails user) {
        // 新增一条航班记录
        FlightInfo flightInfo = flightDataConvert(excelDataVO, user);

        // 新增一条客桥使用信息记录
        serviceRecordDataConvert(excelDataVO, flightInfo, user);
    }

    /**
     * Title: pageFlightInfoByCondition<br>
     * Author: 叶咏秋<br>
     * Description: (航班信息动态条件分页查询)<br>
     * Date:  10:02 <br>
     *
     * @param pageParam            :
     * @param flightInfoSearchForm return: ResultBuilder
     */
    @Override
    public ResultBuilder<IPage<FlightInfoVoNew>> pageFlightInfoByCondition(PageParam pageParam, FlightInfoSearchForm flightInfoSearchForm) {
        this.setFlightTimeDefaultRange(flightInfoSearchForm);
        IPage<FlightInfoVoNew> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageParam.getPage(), pageParam.getLimit());
        IPage<FlightInfoVoNew> objectList = flightInfoBizMapper.pageFlightInfoByCondition(flightInfoSearchForm, page);
        // 转换成自定义实体类
        for (FlightInfoVoNew o : objectList.getRecords()) {
            StringBuilder msg = new StringBuilder("系统校验");
            if (StringUtils.isNotBlank(o.getConfirmCode())) {
                for (int i = 0; i < 18; i++) {
                    if (o.getConfirmCode().substring(i, i++).equals(Constants.CONFIR_EXP_FLAG)) {
                        msg.append(Constants.CONFIRMSG[i]);
                    }
                }
                msg.append("可能存在误差，请确认");
                if (!CONFIRM_CODE.equals(o.getConfirmCode())) {
                    o.setConfirmMsg(msg.toString().replaceFirst("、", "："));
                }
            }
        }
        return new ResultBuilder.Builder<IPage<FlightInfoVoNew>>().data(objectList).builder();
    }

    /**
     * Title: getByServiceCode <br>
     * Description: 根据服务code查询航班信息 <br>
     * author 曾华川  <br>
     * date 2022/10/17 9:26<br>
     *
     * @return com.swcares.common.ResultBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Pager<FlightServiceVo>> getByServiceCode(PageParam pageParam, String serviceCode,
                                                                  FlightInfoSearchForm flightInfoSearchForm) {
        List<Object[]> objects = flightInfoDao.getFlightInfoByServiceCode(serviceCode,flightInfoSearchForm);
        // 转换成自定义实体类
        List<FlightServiceVo> pageList = new ArrayList<>();
        for (Object[] o : objects) {
            handleFlightServiceVo(pageList, o);
        }
        // 自定义分页对象
        Pager<FlightServiceVo> pagerList = new Pager<>(pageParam.getPage(), pageParam.getLimit(), pageList);
        return new ResultBuilder.Builder<Pager<FlightServiceVo>>().data(pagerList).builder();
    }

    private static void handleFlightServiceVo(List<FlightServiceVo> pageList, Object[] o) {
        FlightServiceVo flightInfoVo = new FlightServiceVo();
        flightInfoVo.setId(o[0] == null ? null : o[0].toString());
        flightInfoVo.setFlightNo(o[1] == null ? null : o[1].toString());
        flightInfoVo.setFlightDate(o[2] == null ? null : (Date) o[2]);
        flightInfoVo.setFlightTime(o[3] == null ? null : (Date) o[3]);
        flightInfoVo.setFlightFlag(o[4] == null ? null : o[4].toString());
        flightInfoVo.setStartTime(o[5] == null ? null : (Date) o[5]);
        flightInfoVo.setEndTime(o[6] == null ? null : (Date) o[6]);
        flightInfoVo.setUsedNumber(o[7] == null ? null : ((BigDecimal) o[7]).doubleValue());
        flightInfoVo.setInScope(o[8] == null ? "1" : "" + o[8]);
        pageList.add(flightInfoVo);
    }

    /**
     * Title: getChangeNum<br>
     * Author: 刘志恒<br>
     * Description: 获取确认后修改航班条数<br>
     * Date:  2023/10/27 16:41 <br>
     *
     * @param flightInfoSearchForm ：对象
     */
    @Override
    public ResultBuilder<Integer> getChangeNum(FlightInfoSearchForm flightInfoSearchForm) {
        String airlineCode = flightInfoSearchForm.getAirlineCode();
        String regNo = flightInfoSearchForm.getRegNo();
        String flightNo = flightInfoSearchForm.getFlightNo();
        String flightFlag = flightInfoSearchForm.getFlightFlag();
        String dataStatus = String.valueOf(flightInfoSearchForm.getDataStatus());
        String dataLost = flightInfoSearchForm.getDataLost();
        String variableStatus = String.valueOf(flightInfoSearchForm.getVariableStatus());
        // 如果某些参数为空则返回null
        if (CharSequenceUtil.isBlank(airlineCode)) {
            airlineCode = null;
        }
        if (CharSequenceUtil.isBlank(regNo)) {
            regNo = null;
        }
        if (CharSequenceUtil.isBlank(flightNo)) {
            flightNo = null;
        }
        if (CharSequenceUtil.isBlank(flightFlag)) {
            flightFlag = null;
        }
        if (CharSequenceUtil.isBlank(dataStatus)) {
            dataStatus = null;
        }
        if (CharSequenceUtil.isBlank(variableStatus)) {
            variableStatus = null;
        }
        this.setFlightTimeDefaultRange(flightInfoSearchForm);

        return new ResultBuilder.Builder<Integer>().data(flightInfoDao.getFlightVariableChangeNumByCondition(flightInfoSearchForm.getAirportCode(),
                flightInfoSearchForm.getStartDate(), flightInfoSearchForm.getEndDate(),
                flightInfoSearchForm.getFlightTimeStartDate(), flightInfoSearchForm.getFlightTimeEndDate(),
                airlineCode, regNo, flightNo, flightFlag, dataStatus, dataLost, variableStatus)).builder();
    }

    /**
     * Title: countTotal<br>
     * Author: 叶咏秋<br>
     * Description: (航班信息数据总计)<br>
     * Date:  10:08 <br>
     *
     * @param flightInfoSearchForm return: ResultBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<FlightInfoCountVo>> countTotal(FlightInfoSearchForm flightInfoSearchForm) {
        String airlineCode = flightInfoSearchForm.getAirlineCode();
        String flightNo = flightInfoSearchForm.getFlightNo();
        String flightFlag = flightInfoSearchForm.getFlightFlag();
        String regNo = flightInfoSearchForm.getRegNo();
        String dataStatus = flightInfoSearchForm.getDataStatus() == null ? null : String.valueOf(flightInfoSearchForm.getDataStatus());
        String dataLost = flightInfoSearchForm.getDataLost();
        String variableStatus = flightInfoSearchForm.getVariableStatus() == null ? null : String.valueOf(flightInfoSearchForm.getVariableStatus());

        // 如果某些参数为空则返回null
        airlineCode = getAirlineCode(airlineCode);
        regNo = getRegNo(regNo);
        flightNo = getFlightNo(flightNo);
        flightFlag = getFlightFlag(flightFlag);
        dataStatus = getDataStatus(dataStatus);
        dataLost = getDataLost(dataLost);
        variableStatus = getString(variableStatus);

        this.setFlightTimeDefaultRange(flightInfoSearchForm);

        List<Object[]> resultList = flightInfoDao.countTotal(flightInfoSearchForm.getAirportCode(),
                flightInfoSearchForm.getStartDate(), flightInfoSearchForm.getEndDate(),
                flightInfoSearchForm.getFlightTimeStartDate(), flightInfoSearchForm.getFlightTimeEndDate(),
                airlineCode, regNo, flightNo, flightFlag, dataStatus, dataLost, variableStatus);
        // 转换成自定义实体类
        List<FlightInfoCountVo> flightInfoCountVoList = new ArrayList<>();
        for (Object[] o : resultList) {
            FlightInfoCountVo flightInfoCountVo = new FlightInfoCountVo();
            handleFlightInfoCountVo1(o, flightInfoCountVo);
            handleFlightInfoCountVo2(o, flightInfoCountVo);
            handleFlightInfoCountVo3(o, flightInfoCountVo);
            handleFlightInfoCountVo4(o, flightInfoCountVo);
            handleFlightInfoCountVo5(o, flightInfoCountVo);
            handleFlightInfoCountVo6(o, flightInfoCountVo);
            handleFlightInfoCountVo7(o, flightInfoCountVo);
            handleFlightInfoCountVo8(o, flightInfoCountVo);
            handleFlightInfoCountVo9(o, flightInfoCountVo);
            flightInfoCountVoList.add(flightInfoCountVo);
        }
        return new ResultBuilder.Builder<List<FlightInfoCountVo>>().data(flightInfoCountVoList).builder();
    }

    private void handleFlightInfoCountVo9(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo
                .setCadfTotal(o[40] == null ? null : ((BigDecimal) o[40]).doubleValue());
        flightInfoCountVo
                .setPsgTotal(o[41] == null ? null : ((BigDecimal) o[41]).doubleValue());
    }

    private void handleFlightInfoCountVo8(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo
                .setCbatTotal(o[35] == null ? null : ((BigDecimal) o[35]).doubleValue());
        flightInfoCountVo
                .setCbetTotal(o[36] == null ? null : ((BigDecimal) o[36]).doubleValue());
        flightInfoCountVo
                .setCbanTotal(o[37] == null ? null : ((BigDecimal) o[37]).doubleValue());
        flightInfoCountVo
                .setCbenTotal(o[38] == null ? null : ((BigDecimal) o[38]).doubleValue());
        flightInfoCountVo
                .setCopTotal(o[39] == null ? null : ((BigDecimal) o[39]).doubleValue());
    }

    private void handleFlightInfoCountVo7(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo
                .setCdufTotal(o[30] == null ? null : ((BigDecimal) o[30]).doubleValue());
        flightInfoCountVo
                .setCaufTotal(o[31] == null ? null : ((BigDecimal) o[31]).doubleValue());
        flightInfoCountVo
                .setCtufTotal(o[32] == null ? null : ((BigDecimal) o[32]).doubleValue());
        flightInfoCountVo
                .setCwufTotal(o[33] == null ? null : ((BigDecimal) o[33]).doubleValue());
        flightInfoCountVo
                .setCeufTotal(o[34] == null ? null : ((BigDecimal) o[34]).doubleValue());
    }

    private void handleFlightInfoCountVo6(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo.setCmTotal(o[25] == null ? null : ((BigDecimal) o[25]).doubleValue());
        flightInfoCountVo
                .setCmufTotal(o[26] == null ? null : ((BigDecimal) o[26]).doubleValue());
        flightInfoCountVo
                .setCiufTotal(o[27] == null ? null : ((BigDecimal) o[27]).doubleValue());
        flightInfoCountVo
                .setCpufTotal(o[28] == null ? null : ((BigDecimal) o[28]).doubleValue());
        flightInfoCountVo
                .setCsufTotal(o[29] == null ? null : ((BigDecimal) o[29]).doubleValue());
    }

    private void handleFlightInfoCountVo5(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo
                .setCetTotal(o[20] == null ? null : ((BigDecimal) o[20]).doubleValue());
        flightInfoCountVo
                .setCltTotal(o[21] == null ? null : ((BigDecimal) o[21]).doubleValue());
        flightInfoCountVo
                .setCpsfTotal(o[22] == null ? null : ((BigDecimal) o[22]).doubleValue());
        flightInfoCountVo
                .setCcsfTotal(o[23] == null ? null : ((BigDecimal) o[23]).doubleValue());
        flightInfoCountVo
                .setCdsfTotal(o[24] == null ? null : ((BigDecimal) o[24]).doubleValue());
    }

    private void handleFlightInfoCountVo4(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo
                .setBagTotal(o[15] == null ? null : ((BigDecimal) o[15]).doubleValue());
        flightInfoCountVo
                .setMailTotal(o[16] == null ? null : ((BigDecimal) o[16]).doubleValue());
        flightInfoCountVo
                .setCargoTotal(o[17] == null ? null : ((BigDecimal) o[17]).doubleValue());
        flightInfoCountVo
                .setCbtTotal(o[18] == null ? null : ((BigDecimal) o[18]).doubleValue());
        flightInfoCountVo
                .setCwnTotal(o[19] == null ? null : ((BigDecimal) o[19]).doubleValue());
    }

    private void handleFlightInfoCountVo3(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo.setCardHolderNumberTotal(
                o[10] == null ? null : ((BigDecimal) o[10]).doubleValue());
        flightInfoCountVo.setAccompanyingCardHolderNumberTotal(
                o[11] == null ? null : ((BigDecimal) o[11]).doubleValue());
        flightInfoCountVo.setImportantNumberTotal(
                o[12] == null ? null : ((BigDecimal) o[12]).doubleValue());
        flightInfoCountVo.setAccompanyingImportantNumberTotal(
                o[13] == null ? null : ((BigDecimal) o[13]).doubleValue());
        flightInfoCountVo
                .setBagNumberTotal(o[14] == null ? null : ((BigDecimal) o[14]).doubleValue());
    }

    private void handleFlightInfoCountVo2(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo.setEconomyClassNumberTotal(
                o[5] == null ? null : ((BigDecimal) o[5]).doubleValue());
        flightInfoCountVo.setTransitAdultNumberTotal(
                o[6] == null ? null : ((BigDecimal) o[6]).doubleValue());
        flightInfoCountVo.setTransitChildNumberTotal(
                o[7] == null ? null : ((BigDecimal) o[7]).doubleValue());
        flightInfoCountVo.setTransitInfantNumberTotal(
                o[8] == null ? null : ((BigDecimal) o[8]).doubleValue());
        flightInfoCountVo.setDiplomaticPassportNumberTotal(
                o[9] == null ? null : ((BigDecimal) o[9]).doubleValue());
    }

    private void handleFlightInfoCountVo1(Object[] o, FlightInfoCountVo flightInfoCountVo) {
        flightInfoCountVo
                .setAdultNumberTotal(o[0] == null ? null : ((BigDecimal) o[0]).doubleValue());
        flightInfoCountVo
                .setChildNumberTotal(o[1] == null ? null : ((BigDecimal) o[1]).doubleValue());
        flightInfoCountVo
                .setInfantNumberTotal(o[2] == null ? null : ((BigDecimal) o[2]).doubleValue());
        flightInfoCountVo.setFirstClassNumberTotal(
                o[3] == null ? null : ((BigDecimal) o[3]).doubleValue());
        flightInfoCountVo.setClubClassNumberTotal(
                o[4] == null ? null : ((BigDecimal) o[4]).doubleValue());
    }

    private String getAirlineCode(String airlineCode) {
        if (CharSequenceUtil.isBlank(airlineCode)) {
            airlineCode = null;
        }
        return airlineCode;
    }

    private String getRegNo(String regNo) {
        if (CharSequenceUtil.isBlank(regNo)) {
            regNo = null;
        }
        return regNo;
    }

    private String getFlightNo(String flightNo) {
        if (CharSequenceUtil.isBlank(flightNo)) {
            flightNo = null;
        }
        return flightNo;
    }

    private String getFlightFlag(String flightFlag) {
        if (CharSequenceUtil.isBlank(flightFlag)) {
            flightFlag = null;
        }
        return flightFlag;
    }

    private String getDataStatus(String dataStatus) {
        if (CharSequenceUtil.isBlank(dataStatus) || "null".equals(dataStatus)) {
            dataStatus = null;
        }
        return dataStatus;
    }

    private String getDataLost(String dataLost) {
        if (CharSequenceUtil.isBlank(dataLost) || "2".equals(dataLost)) {
            dataLost = null;
        }
        return dataLost;
    }

    private String getString(String variableStatus) {
        if (StringUtils.isBlank(variableStatus)) {
            variableStatus = null;
        }
        return variableStatus;
    }

    private void setFlightTimeDefaultRange(FlightInfoSearchForm form) {
        //如果航班日期和起降日期都是空的则默认起降日期当前前一个月的
        if ((form.getStartDate() == null || form.getEndDate() == null)
                && (form.getFlightTimeStartDate() == null || form.getFlightTimeEndDate() == null)) {
            form.setFlightTimeEndDate(new Date());
            Calendar calendar = CalendarUtil.calendar(DateUtil.beginOfDay(new Date()));
            calendar.add(Calendar.MONTH, -1);
            form.setFlightTimeStartDate(calendar.getTime());
        }
    }

    @Override
    public ResultBuilder<FlightInfoDataVO> countModifiedLostTotal(FlightInfoSearchForm flightInfoSearchForm) {
        String airlineCode = flightInfoSearchForm.getAirlineCode();
        String flightNo = flightInfoSearchForm.getFlightNo();
        String flightFlag = flightInfoSearchForm.getFlightFlag();
        String regNo = flightInfoSearchForm.getRegNo();
        String dataStatus = flightInfoSearchForm.getDataStatus() == null ? null : String.valueOf(flightInfoSearchForm.getDataStatus());
        String dataLost = flightInfoSearchForm.getDataLost();
        String variableStatus = flightInfoSearchForm.getVariableStatus() == null ? null : String.valueOf(flightInfoSearchForm.getVariableStatus());

        // 如果某些参数为空则返回null
        if (CharSequenceUtil.isBlank(airlineCode)) {
            airlineCode = null;
        }
        if (CharSequenceUtil.isBlank(regNo)) {
            regNo = null;
        }
        if (CharSequenceUtil.isBlank(flightNo)) {
            flightNo = null;
        }
        if (CharSequenceUtil.isBlank(flightFlag)) {
            flightFlag = null;
        }
        if (StringUtils.isBlank(dataStatus)) {
            dataStatus = null;
        }
        if (StringUtils.isBlank(variableStatus)) {
            variableStatus = null;
        }

        this.setFlightTimeDefaultRange(flightInfoSearchForm);

        Integer modified = flightInfoDao.countFlightInfoDataModified(flightInfoSearchForm.getAirportCode(),
                flightInfoSearchForm.getStartDate(), flightInfoSearchForm.getEndDate(),
                flightInfoSearchForm.getFlightTimeStartDate(), flightInfoSearchForm.getFlightTimeEndDate(),
                airlineCode, regNo, flightNo, flightFlag, dataLost, variableStatus);
        Integer lost = flightInfoDao.countFlightInfoDataLost(flightInfoSearchForm.getAirportCode(),
                flightInfoSearchForm.getStartDate(), flightInfoSearchForm.getEndDate(),
                flightInfoSearchForm.getFlightTimeStartDate(), flightInfoSearchForm.getFlightTimeEndDate(),
                airlineCode, regNo, flightNo, flightFlag, dataStatus, variableStatus);
        FlightInfoDataVO flightInfoDataVO = new FlightInfoDataVO();
        flightInfoDataVO.setLostTotal(lost);
        flightInfoDataVO.setModifiedTotal(modified);

        return new ResultBuilder.Builder<FlightInfoDataVO>().data(flightInfoDataVO).builder();
    }

    /**
     * Title: saveFlightInfo<br>
     * Author: 叶咏秋<br>
     * Description: (新增航班信息)<br>
     * Date:  10:50 <br>
     *
     * @param flightInfoForm :
     * @param user           return: ResultBuilder
     */
    @OperateLog(operateType = "新增", operateObject = "FlightInfo")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<FlightInfo> saveFlightInfo(FlightInfoForm flightInfoForm, LoginUserDetails user,
                                                    String urlName) {
        Integer adultNumber = flightInfoForm.getAdultNumber();
        Integer childNumber = flightInfoForm.getChildNumber();
        Integer infantNumber = flightInfoForm.getInfantNumber();
        int transitAdultNumber = flightInfoForm.getTransitAdultNumber() == null ? 0
                : flightInfoForm.getTransitAdultNumber();
        int transitChildNumber = flightInfoForm.getTransitChildNumber() == null ? 0
                : flightInfoForm.getTransitChildNumber();
        // 通过航班号+航班日期+航段,查询该航班是否已经存在
        List<FlightInfo> flightInfoList = flightInfoDao.listFlightInfoByCondition(
                flightInfoForm.getFlightNo(), flightInfoForm.getFlightDate(),
                flightInfoForm.getFlightSegment(), flightInfoForm.getAirportCode());
        if (!flightInfoList.isEmpty()) {
            // 抛出数据已存在异常
            throw new GenericException(BusinessMessageEnum.DATA_EXIST.getCode(),
                    BusinessMessageEnum.DATA_EXIST.getMsg());
        }
        // 新增不用id
        flightInfoForm.setId(null);
        FlightInfo flightInfo = new FlightInfo();
        copyProperties(flightInfoForm, flightInfo);
        // 以航线性质设置航班性质
        flightInfo.setFlightType(flightInfoForm.getFlightLineType());
        // 以航班号获取航司二字码
        flightInfo.setAirlineCode(flightInfoForm.getFlightNo().substring(0, 2));
        // 以航段设置出发航站3字码和到到达航班3字码
        flightInfo.setFromAirportCode(flightInfoForm.getFlightSegment().substring(0, 3));
        flightInfo.setToAirportCode(flightInfoForm.getFlightSegment().substring(4, 7));
        // 通过机场3字码设置采集机场3字码
        flightInfo.setDaAirportCode(flightInfoForm.getAirportCode());
        // 通过飞机注册号和航司二字码获取飞机信息
        AircraftInfo aircraftInfo =
                aircraftDao.getAircraftInfoByRegNoAndAirlineCode(flightInfoForm.getRegNo(),
                        flightInfoForm.getFlightNo().substring(0, 2), new Date());
        handleFlightInfo(adultNumber, childNumber, transitAdultNumber, transitChildNumber, flightInfo, aircraftInfo);
        // 设置进出港人数(成人数+儿童数+婴儿数)
        flightInfo.setPsgNumber(
                (adultNumber == null ? 0 : adultNumber) + (childNumber == null ? 0 : childNumber)
                        + (infantNumber == null ? 0 : infantNumber));
        // 设置采集时间
        flightInfo.setDaTime(new Date());
        flightInfo.setCreateBy(user.getUsername());
        flightInfo.setCreateTime(new Date());
        // 默认手动新建航班数据为确认后起降时间
        flightInfo.setIsModifyFlightTime(1);
        FlightInfo flight = flightInfoDao.save(flightInfo);
        // 计算停场时间
        calculateDownTime(flightInfo, user);
        // 判断是否关联从签单同步过来的无flightId服务记录
        syncServiceRecord(flight);

        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_SAVE.getValue();
        flightInfoForm.setId(null);
        logService.addLogForSave(flightInfoForm, user, content, flightInfoForm.getAirportCode());
        return new ResultBuilder.Builder<FlightInfo>().data(flight).builder();
    }

    private static void handleFlightInfo(Integer adultNumber, Integer childNumber, int transitAdultNumber, int transitChildNumber, FlightInfo flightInfo, AircraftInfo aircraftInfo) {
        if (aircraftInfo == null) {
            flightInfo.setFlightModel(null);
            flightInfo.setPlf(null);
        } else {
            // 设置机型
            flightInfo.setFlightModel(aircraftInfo.getAirplaneModel());
            // 设置客座率
            if (aircraftInfo.getMaxSeat() == null || aircraftInfo.getMaxSeat() == 0) {
                flightInfo.setPlf(null);
            } else {
                double plf =
                        FormatUtils.formatData((double) ((adultNumber == null ? 0 : adultNumber)
                                + (childNumber == null ? 0 : childNumber) + transitChildNumber
                                + transitAdultNumber) / (double) aircraftInfo.getMaxSeat());
                flightInfo.setPlf(100 * plf);
            }
        }
    }

    @OperateLog(operateObject = "FlightInfo", operateType = "确认")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> confirmFlightInfo(FlightInfoConfirmForm dto) {
        if ((dto.getStartDate() == null || dto.getEndDate() == null)
                && (dto.getFlightTimeStartDate() == null || dto.getFlightTimeEndDate() == null)) {
            dto.setFlightTimeEndDate(new Date());
            Calendar calendar = CalendarUtil.calendar(DateUtil.beginOfDay(new Date()));
            calendar.add(Calendar.MONTH, -1);
            dto.setFlightTimeStartDate(calendar.getTime());
        }
        List<FlightInfo> allById = flightInfoDao.findToBeConfirmedFlightInfo(dto.getAirportCode(), dto.getStartDate()
                , dto.getEndDate(), dto.getFlightTimeStartDate(), dto.getFlightTimeEndDate(), dto.getAirlineCode(),
                dto.getRegNo(), dto.getFlightNo(), dto.getFlightFlag(), dto.getDataStatus(), dto.getDataLost(),
                dto.getIdList(), dto.getVariableStatus());
        for (FlightInfo flightInfo : allById) {
            if (StringUtils.isBlank(flightInfo.getRegNo())) {
                throw new GenericException(BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getCode(),
                        BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getMsg() + "，航班号：" + flightInfo.getFlightNo());
            }
            flightInfo.setDataStatus(1);
            flightInfo.setConfirmCode(CONFIRM_CODE);
        }
        //数据库更新
        flightInfoDao.saveAllAndFlush(allById);
        importService.syncFlightData(allById);
        return allById.stream().map(FlightInfo::getId).collect(Collectors.toList());
    }


    @Override
    public void confirmReSettlement(String ids, String airportCode, String formulaType,
                                    LoginUserDetails user) {
        Date startDate = null;
        Date endDate = null;
        Set<String> flightNos = new HashSet<>();
        List<String> idList = Arrays.stream(ids.split(",")).collect(Collectors.toList());
        List<FlightInfo> allById = flightInfoDao.findAllById(idList);
        for (FlightInfo flightInfo : allById) {
            Date flightDate;
            if ((flightDate = flightInfo.getFlightDate()) != null) {
                if (startDate == null || flightDate.before(startDate)) {
                    startDate = flightDate;
                }
                if (endDate == null || flightDate.after(endDate)) {
                    endDate = flightDate;
                }
            }
            if (CharSequenceUtil.isNotBlank(flightInfo.getFlightNo())) {
                flightNos.add(flightInfo.getFlightNo());
            }
        }
        if (CharSequenceUtil.isNotBlank(ids)) {
            ReCalcForm form = new ReCalcForm();
            form.setAirportCode(airportCode);
            confirmReSettlementBizOne(flightNos, form);
            form.setFlightId(ids);
            form.setStartDate(startDate);
            form.setEndDate(endDate);
            form.setSuccessHide(true);
            form.setFormulaType(formulaType);
            form.setDateType(1);
            reCalcService.execute(form, user, TenantHolder.getTenant());
        }
    }

    private static void confirmReSettlementBizOne(Set<String> flightNos, ReCalcForm form) {
        StringBuilder fns = new StringBuilder();
        for (String fn : flightNos) {
            fns.append(fn).append(",");
        }
        if (CharSequenceUtil.isNotBlank(fns.toString())) {
            form.setFlightNo(fns.substring(0, fns.length() - 1));
        }
    }

    @OperateLog(operateObject = "FlightInfo", operateType = "取消确认")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<FlightInfo>> cancelConfirmFlightInfo(String flightIds, LoginUserDetails user) {
        String[] listId = flightIds.split(",");
        List<FlightInfo> result = new ArrayList<>();
        for (String flightId : listId) {
            FlightInfo flightInfo = flightInfoDao.getFlightInfoById(flightId);
            flightInfo.setDataStatus(3);
            FlightInfo flight = flightInfoDao.save(flightInfo);
            result.add(flight);

            // 取消还在等待中的结算
            reCalcService.cancelReCalcByFlight(flightInfo.getAirportCode(), flightId);
            // 删除已结算出涉及航班引用公式费用
            List<String> feeCodeList = feeDao.getFeeCodeList(flightId, "2,4");
            if (!CollectionUtils.isEmpty(feeCodeList)) {
                // 删除费用信息
                flightBillDao.updateUnpushedFlightBusinessBill(feeCodeList, flightId, user.getUsername(), new Date());
            }
        }

        return new ResultBuilder.Builder<List<FlightInfo>>().data(result).builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> confirmFlightBusinessInfo(FlightBusinessInfoConfirmForm dto) {
        if ((dto.getStartDate() == null || dto.getEndDate() == null) && (dto.getFlightTimeStartDate() == null || dto.getFlightTimeEndDate() == null)) {
            dto.setFlightTimeEndDate(new Date());
            Calendar calendar = CalendarUtil.calendar(DateUtil.beginOfDay(new Date()));
            calendar.add(Calendar.MONTH, -1);
            dto.setFlightTimeStartDate(calendar.getTime());
        }

        List<FlightInfo> listFlightInfo = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        List<ServiceRecord> saveServiceList = new ArrayList<>();
        List<FlightInfo> list = flightInfoDao.query2BeConfirmedBusinessInfoFlight(dto.getAirportCode(), dto.getStartDate()
                , dto.getEndDate(), dto.getFlightTimeStartDate(), dto.getFlightTimeEndDate(), dto.getAirlineCode(), dto.getRegNo(),
                dto.getFlightNo(), dto.getFlightFlag(), dto.getDataStatus(), dto.getDataLost(), dto.getIdList(),
                dto.getVariableStatus());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        for (FlightInfo flightInfo : list) {
            RLock rLock = redissonClient.getLock(buildTenantKey(flightInfo.getId()));
            boolean lockFlag;
            try {
                lockFlag = rLock.tryLock(30, TimeUnit.SECONDS);
            } catch (Exception e) {
                Thread.currentThread().interrupt();
                throw new GenericException(BusinessMessageEnum.LOCK_ERROR.getCode(), BusinessMessageEnum.LOCK_ERROR.getMsg() + flightInfo.getFlightNo());
            }
            if (!lockFlag) {
                throw new GenericException(BusinessMessageEnum.DATA_IS_CONFIRMED.getCode(), BusinessMessageEnum.DATA_IS_CONFIRMED.getMsg() + flightInfo.getFlightNo());
            }
            try {
                flightInfo = flightInfoDao.getFlightInfoById(flightInfo.getId());
                judgeFlightInfo(flightInfo);
                flightInfo.setVariableStatus(1);
                flightInfo.setConfirmCode(CONFIRM_CODE);
                FlightInfo flight = flightInfoDao.saveAndFlush(flightInfo);
                listFlightInfo.add(flight);
                idList.add(flight.getId());
                //需要从备份表同步到主表的数据
                List<ServiceRecordConfirm> srcList = serviceRecordConfirmDao.listServiceRecordByFlightId(flight.getId());
                for (ServiceRecordConfirm src : srcList) {
                    ServiceRecord sr = new ServiceRecord();
                    copyProperties(src, sr);
                    sr.setId(null);
                    saveServiceList.add(sr);
                }
            } finally {
                rLock.unlock();
            }
        }
        //从备份表将数据同步保存到service_record表
        serviceRecordDao.saveAll(saveServiceList);
        //删除当前航班的业务保障数据中电子签单已经删除或修改的的数据库
        serviceRecordDao.deleteSignDeleteOrUpdateData(idList);
        //删除该航班备份库的数据
        serviceRecordConfirmDao.deleteServiceRecordByDlightIds(idList);
        return idList;

    }

    private static void judgeFlightInfo(FlightInfo flightInfo) {
        if (flightInfo.getVariableStatus().equals(1)) {
            throw new GenericException(BusinessMessageEnum.DATA_IS_CONFIRMED.getCode(), BusinessMessageEnum.DATA_IS_CONFIRMED.getMsg() + flightInfo.getFlightNo());
        }
        if (StringUtils.isBlank(flightInfo.getRegNo())) {
            throw new GenericException(BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getCode(), BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getMsg() + flightInfo.getFlightNo());
        }
    }

    public String buildTenantKey(String key) {
        return TenantHolder.getTenant() + ":" + key;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<String> cancelConfirmFlightBusinessInfo(String flightIds, LoginUserDetails user) {
        String[] flightIdArr = flightIds.split(",");
        for (String flightId : flightIdArr) {
            FlightInfo flightInfo = flightInfoDao.getFlightInfoById(flightId);
            flightInfo.setVariableStatus(3);
            List<String> feeCodeList = feeDao.getFeeCodeList(flightId, "3,4");
            if (!CollectionUtils.isEmpty(feeCodeList)) {
                // 删除费用信息
                flightBillDao.updateUnpushedFlightBusinessBill(feeCodeList, flightId, user.getUsername(), new Date());
            }
            reCalcService.cancelReCalcByVariable(flightInfo.getAirportCode(), flightId);
            flightInfoDao.save(flightInfo);

            //取消确认业务保障数据时，将已确认时同步的业务保障数据，再次同步
            String flightInfoFlag = DateUtils.format(flightInfo.getFlightDate()) + "_" + flightInfo.getFlightNo()
                    + "_" + flightInfo.getFlightFlag() + "_" + flightInfo.getAirportCode();
            flightDataMsgService.getServiceClassMsg(flightInfoFlag, flightInfo.getAirportCode());
        }
        return new ResultBuilder.Builder<String>().data(flightIds).builder();
    }

    /**
     * Title: getFlightInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (通过id查看航班信息)<br>
     * Date:  12:17 <br>
     *
     * @param id return: ResultBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<FlightInfo> getFlightInfoById(String id) {
        FlightInfo flightInfo = flightInfoDao.getFlightInfoById(id);
        return new ResultBuilder.Builder<FlightInfo>().data(flightInfo).builder();
    }

    @OperateLog(operateType = "修改", operateObject = "FlightInfo", mapper = "flightInfoDao")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<FlightInfo> updateFlightInfoById(FlightInfoForm flightInfoForm, LoginUserDetails user,
                                                          String urlName) {
        Integer adultNumber = flightInfoForm.getAdultNumber();
        Integer childNumber = flightInfoForm.getChildNumber();
        Integer infantNumber = flightInfoForm.getInfantNumber();
        int transitAdultNumber = flightInfoForm.getTransitAdultNumber() == null ? 0
                : flightInfoForm.getTransitAdultNumber();
        int transitChildNumber = flightInfoForm.getTransitChildNumber() == null ? 0
                : flightInfoForm.getTransitChildNumber();
        // 更新前查看航班数据是否存在
        FlightInfo flightInfo = flightInfoDao.getFlightInfoById(flightInfoForm.getId());
        FlightInfoForm flightInfoForm1 = new FlightInfoForm();
        try {
            BeanUtils.copyProperties(flightInfo, flightInfoForm1);
        } catch (Exception e) {
            log.error("copyProperties fail ! flightInfo = {}", flightInfo, e);
        }
        // 存在则更新
        if (flightInfo == null) {
            // 数据不存在抛出异常
            throw new GenericException(BusinessMessageEnum.DATA_NOT_EXIST.getCode(),
                    BusinessMessageEnum.DATA_NOT_EXIST.getMsg());
        }
        // 将flightInfoForm复制到flightInfo
        copyPropertiesIgnoreNull(flightInfoForm, flightInfo);

        // 1.以航线性质设置航班性质
        flightInfo.setFlightType(flightInfoForm.getFlightLineType());
        if (flightInfo.getFlightSegmentType() == null) {
            flightInfo.setFlightSegmentType(flightInfo.getFlightType());
        }
        // 2.以航班号获取航司二字码
        flightInfo.setAirlineCode(flightInfoForm.getFlightNo().substring(0, 2));
        // 3.通过机场3字码设置采集机场3字码
        flightInfo.setDaAirportCode(flightInfoForm.getAirportCode());
        // 4.以航段设置出发航站3字码和到到达航班3字码
        flightInfo.setFromAirportCode(flightInfoForm.getFlightSegment().substring(0, 3));
        flightInfo.setToAirportCode(flightInfoForm.getFlightSegment().substring(4, 7));
        // 5.设置进出港人数（成人+儿童+婴儿数）
        flightInfo.setPsgNumber(
                (adultNumber == null ? 0 : adultNumber) + (childNumber == null ? 0 : childNumber)
                        + (infantNumber == null ? 0 : infantNumber));
        // 通过飞机注册号和航司二字码获取飞机信息
        AircraftInfo aircraftInfo =
                aircraftDao.getAircraftInfoByRegNoAndAirlineCode(flightInfoForm.getRegNo(),
                        flightInfoForm.getFlightNo().substring(0, 2), new Date());
        // 6.设置机型和客座率
        handleFlightInfo(adultNumber, childNumber, transitAdultNumber, transitChildNumber, flightInfo, aircraftInfo);
        flightInfo.setModifiedBy(user.getUsername());
        flightInfo.setModifiedTime(new Date());
        flightInfo.setConfirmCode(CONFIRM_CODE);
        // 判断起降时间是否修改,或修改起降标识，若修改则重新计算停场时间
        if (flightInfoForm1.getFlightTime() == null
                || (!flightInfoForm1.getFlightTime().equals(flightInfoForm.getFlightTime()))
                || (!flightInfoForm1.getFlightFlag().equals(flightInfoForm.getFlightFlag()))) {
            // 设置手动修改起降时间标志
            flightInfo.setIsModifyFlightTime(1);
            calculateDownTime(flightInfo, user);
        }

        FlightInfo flight = flightInfoDao.save(flightInfo);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_UPDATE.getValue();
        flightInfoForm1.setId(null);
        logService.addLogForUpdate(flightInfoForm1, flightInfoForm, user, content,
                flightInfoForm.getAirportCode());
        return new ResultBuilder.Builder<FlightInfo>().data(flight).builder();
    }


    /**
     * Title: delFlightInfoBatchById<br>
     * Author: 叶咏秋<br>
     * Description: (批量删除航班信息)<br>
     * Date:  9:54 <br>
     *
     * @param ids  :
     * @param user return: ResultBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> delFlightInfoBatchById(String ids, LoginUserDetails user, String urlName) {
        // 主键拆分
        String[] id = ids.split(",");
        List<String> listId = new ArrayList<>();
        Collections.addAll(listId, id);
        List<FlightInfo> reCalculateDownTimeList = new ArrayList<>();
        try {
            for (String idd : listId) {
                FlightInfo flightInfo = flightInfoDao.getFlightInfoById(idd);
                String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_DELETE.getValue();
                FlightInfoForm flightInfoForm = new FlightInfoForm();
                BeanUtils.copyProperties(flightInfo, flightInfoForm);
                flightInfoForm.setId(null);
                logService.addLogForDelete(flightInfoForm, user, content, flightInfoForm.getAirportCode());
                // 清空当前航班的停场时间，以及之前关联起降时间的航班的停场时间

                FlightInfo oldFlight = updateOldDownTime(flightInfo, user);
                if (oldFlight != null) {
                    reCalculateDownTimeList.add(oldFlight);
                }
            }
        } catch (Exception e) {
            log.error(" reCalculateDownTimeList fail ! listId = {}", listId, e);
        }
        Date newDate = new Date();
        // 查出flightbill表中本航班对应的费用总和
        //  在航司账单中减去相应的费用
        // 删除航班的同时，也要删除该航班的相关费用的结算数据
        flightBillDao.updateFlightBillByIds(listId, user.getUsername(), newDate);
        flightInfoDao.updateFlightInfoByIds(listId, user.getUsername(), newDate);
        // 删除航班的同时，也要删除该航班的相关特车/设备数据
        serviceRecordDao.deleteServiceRecordByFlightIds(listId);

        for (FlightInfo fi : reCalculateDownTimeList) {
            calculateDownTime(fi, user);
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<ServiceRecord>> listServiceRecordByCondition(String flightId, String airportCode,
                                                                           String indexDataFlag) {
        // 如果传的是小写
        indexDataFlag = indexDataFlag.toUpperCase();
        List<ServiceRecord> serviceRecord =
                serviceRecordDao.listServiceRecordByCondition(flightId, airportCode, indexDataFlag);
        return new ResultBuilder.Builder<List<ServiceRecord>>().data(serviceRecord).builder();
    }

    /**
     * Title: listDeviceVariableRecord<br>
     * Author: 叶咏秋<br>
     * Description: (查询航班使用的所有特车/设备的定义信息)<br>
     * Date:  15:34 <br>
     * return: ResultBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<RulesVariableRecord>> listDeviceVariableRecord() {
        List<RulesVariableRecord> rulesVariableRecords = variableRecordDao.listDeviceVariableRecordLikeCAndS();
        return new ResultBuilder.Builder<List<RulesVariableRecord>>().data(rulesVariableRecords).builder();
    }


    /**
     * Title: listDeviceUsedDataByFlightId<br>
     * Author: 叶咏秋<br>
     * Description: (查询某个航班的特车/设备数据)<br>
     * Date:  14:00 <br>
     *
     * @param flightId return: ResultBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Map<String, List<ServiceRecordVo>>> listDeviceUsedDataByFlightId(String flightId) {
        FlightInfo fi = flightInfoDao.getFlightInfoById(flightId);
        //查询航班下的特车保障数据
        List<ServiceRecord> serviceRecordList =
                serviceRecordDao.findServiceRecordByFlightId(flightId);
        //查询修改后的特车保障数据
        List<Object[]> srcList = serviceRecordConfirmDao.listCountServiceRecord(flightId);
        Map<String, List<Object[]>> srcMap = listDeviceUsedDataByFlightIdBizOne(srcList);
        List<ServiceRecordVo> voList = getServiceRecordVos(serviceRecordList, srcMap);
        //如果是待确认（没有取消确认过）则可以直接显示，否则需要处理备用数据
        if (!fi.getVariableStatus().equals(0)) {
            //再次循环，将时间变更了的保障数据匹配上
            for (ServiceRecordVo srv : voList) {
                listDeviceUsedDataByFlightIdBizThree(srv, srcMap);
            }
            //处理map中多余的保障数据，表示为新增保障数据
            for (Map.Entry<String, List<Object[]>> entry : srcMap.entrySet()) {
                List<Object[]> valList = entry.getValue();
                listDeviceUsedDataByFlightIdBiz(valList, voList);
            }
        }
        // 根据serviceCode进行分组
        Map<String, List<ServiceRecordVo>> collect = voList.stream().collect(Collectors.groupingBy(ServiceRecordVo::getServiceCode));
        return new ResultBuilder.Builder<Map<String, List<ServiceRecordVo>>>().data(collect).builder();
    }

    private static void listDeviceUsedDataByFlightIdBiz(List<Object[]> valList, List<ServiceRecordVo> voList) {
        for (Object[] obj : valList) {
            ServiceRecordVo newSrVo = new ServiceRecordVo();
            newSrVo.setServiceCode((String) obj[0]);
            newSrVo.setServiceName((String) obj[4]);
            newSrVo.setChangeStartTime(obj[1] == null ? null : (Date) obj[1]);
            newSrVo.setChangeEndTime(obj[2] == null ? null : (Date) obj[2]);
            newSrVo.setChangeUsedNumber(obj[3] == null ? null : ((BigDecimal) obj[3]).doubleValue());
            newSrVo.setSignDeleteOrUpdate("1");
            voList.add(newSrVo);
        }
    }

    private static void listDeviceUsedDataByFlightIdBizThree(ServiceRecordVo srv, Map<String, List<Object[]>> srcMap) {
        if (!"1".equals(srv.getSignDeleteOrUpdate())) {
            return;
        }

        List<Object[]> objList = srcMap.get(srv.getServiceCode());
        if (objList != null) {
            Object[] objs = objList.get(0);
            srv.setChangeStartTime(objs[1] == null ? null : (Date) objs[1]);
            srv.setChangeEndTime(objs[2] == null ? null : (Date) objs[2]);
            srv.setChangeUsedNumber(objs[3] == null ? null : ((BigDecimal) objs[3]).doubleValue());
            objList.remove(0);
            if (objList.isEmpty()) {
                srcMap.remove(srv.getServiceCode());
            } else {
                srcMap.put(srv.getServiceCode(), objList);
            }
        }
    }

    @NotNull
    private List<ServiceRecordVo> getServiceRecordVos(List<ServiceRecord> serviceRecordList, Map<String, List<Object[]>> srcMap) {
        List<ServiceRecordVo> voList = new ArrayList<>();
        //循环处理将ServiceRecord类型转化为ServiceRecordVo类型，并将没有变化的数据状态修改
        for (ServiceRecord sr : serviceRecordList) {
            ServiceRecordVo srVo = new ServiceRecordVo();
            copyProperties(sr, srVo);
            //如果是签单删除或更新
            if ("1".equals(srVo.getSignDeleteOrUpdate())) {
                List<Object[]> objList = srcMap.get(srVo.getServiceCode());
                listDeviceUsedDataByFlightIdBizTwo(objList, srVo, srcMap);
            }else if(sr.getUsedNumber()==null){
                continue;
            }
            voList.add(srVo);
        }
        return voList;
    }

    private static void listDeviceUsedDataByFlightIdBizTwo(List<Object[]> objList, ServiceRecordVo srVo, Map<String, List<Object[]>> srcMap) {
        if (CollUtil.isNotEmpty(objList)) {
            for (int i = 0; i < objList.size(); i++) {
                Object[] objs = objList.get(i);
                if (objs[1] != null && srVo.getStartTime().compareTo((Date) objs[1]) == 0
                        && objs[2] != null && srVo.getEndTime().compareTo((Date) objs[2]) == 0) {
                    objList.remove(i);
                    //在修改表找到一样的serviceCode和开始结束时间，则将状态改为没有删除或修改
                    srVo.setSignDeleteOrUpdate("0");
                    if (objList.isEmpty()) {
                        srcMap.remove(srVo.getServiceCode());
                    } else {
                        srcMap.put(srVo.getServiceCode(), objList);
                    }
                    break;
                }
            }
        }
    }

    @NotNull
    private static Map<String, List<Object[]>> listDeviceUsedDataByFlightIdBizOne(List<Object[]> srcList) {
        Map<String, List<Object[]>> srcMap = new HashMap<>();
        for (Object[] objs : srcList) {
            String key = (String) objs[0];
            List<Object[]> objList = srcMap.getOrDefault(key, new ArrayList<>());
            objList.add(objs);
            srcMap.put(key, objList);
        }
        return srcMap;
    }

    @Override
    public Map<String, List<ServiceRecord>> listDeviceUsedDataByFlightIdList(List<String> flightIdList) {
        //查询航班下的特车保障数据
        List<ServiceRecord> serviceRecordList = serviceRecordDao.listServiceRecordByFlightIdList(flightIdList);
        // 根据serviceCode进行分组
        return serviceRecordList.stream()
                .collect(Collectors.groupingBy(ServiceRecord::getFlightId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<ListServiceRecordByFlightIdsVo>> listDeviceUsedDataByFlightIds(String flightIds, String serviceCode) {
        String[] id = flightIds.split(",");
        List<String> listId = new ArrayList<>();
        Collections.addAll(listId, id);
        List<ServiceRecord> serviceRecordList =
                serviceRecordDao.listServiceRecordByFlightIdsAndServiceCode(listId, serviceCode);
        Map<String, ListServiceRecordByFlightIdsVo> resMap = new HashMap<>();
        for (ServiceRecord sr : serviceRecordList) {
            ListServiceRecordByFlightIdsVo srVo = resMap.getOrDefault(sr.getFlightId(),
                    new ListServiceRecordByFlightIdsVo(sr.getFlightId()));
            List<ServiceRecord> srList = srVo.getServiceRecordList();
            if (srList == null) {
                srList = new ArrayList<>();
            }
            srList.add(sr);
            srVo.setServiceRecordList(srList);
            resMap.put(sr.getFlightId(), srVo);
        }

        List<ListServiceRecordByFlightIdsVo> res = new ArrayList<>(resMap.values());
        return new ResultBuilder.Builder<List<ListServiceRecordByFlightIdsVo>>().data(res).builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<OperateRecord>> listOperateByFlightIdAndObject(String operateId, String obj) {
        return new ResultBuilder.Builder<List<OperateRecord>>()
                .data(operateRecordDao.listOperateRecordByOperateObjectAndOperateId(obj, operateId))
                .builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportBridgeServiceRecord(FlightInfoSearchForm searchForm, HttpServletResponse response, LoginUserDetails user, String urlName) {
        List<String[]> listBridgeService = new ArrayList<>();
        // 桥载电源使用次数
        List<Object[]> condBridges = flightInfoDao.listBridgeServiceByCondition(
                searchForm.getAirportCode(), searchForm.getAirlineCode(), searchForm.getFlightNo(),
                searchForm.getFlightFlag(), searchForm.getStartDate(), searchForm.getEndDate(),
                Constants.bridgeServiceRecordEnum.BRIDGE_POWER_NUM_CODE.getValue());
        List<Object[]> list2 = flightInfoDao.listBridgeServiceByCondition(
                searchForm.getAirportCode(), searchForm.getAirlineCode(), searchForm.getFlightNo(),
                searchForm.getFlightFlag(), searchForm.getStartDate(), searchForm.getEndDate(),
                Constants.bridgeServiceRecordEnum.BRIDGE_POWER_TIME_CODE.getValue());
        condBridges.addAll(list2);
        for (Object[] condBridge : condBridges) {
            String[] bridgeService = {
                    // 发生机场
                    getEmptyString(condBridge[0]),
                    // 航班日期
                    condBridge[1] == null ? "" : DateUtils.format((Date) condBridge[1]),
                    // 航班号
                    getEmptyString(condBridge[2]),
                    // 机号
                    getEmptyString(condBridge[3]),
                    // 航段
                    getEmptyString(condBridge[4]),
                    // 起降标识
                    getEmptyString(condBridge[5]),
                    // 桥载电源
                    getEmptyString(condBridge[6]),
                    // 桥载电源开始时间
                    condBridge[7] == null ? "" : DateUtils.format((Date) condBridge[7]),
                    // 桥载电源结束时间
                    condBridge[8] == null ? "" : DateUtils.format((Date) condBridge[8])};
            listBridgeService.add(bridgeService);
        }
        List<Object[]> bridges = flightInfoDao.listBridgeServiceByCondition(
                searchForm.getAirportCode(), searchForm.getAirlineCode(), searchForm.getFlightNo(),
                searchForm.getFlightFlag(), searchForm.getStartDate(), searchForm.getEndDate(),
                Constants.bridgeServiceRecordEnum.BRIDGE_VRV_NUM_CODE.getValue());
        List<Object[]> list4 = flightInfoDao.listBridgeServiceByCondition(
                searchForm.getAirportCode(), searchForm.getAirlineCode(), searchForm.getFlightNo(),
                searchForm.getFlightFlag(), searchForm.getStartDate(), searchForm.getEndDate(),
                Constants.bridgeServiceRecordEnum.BRIDGE_VRV_TIME_CODE.getValue());
        bridges.addAll(list4);
        for (Object[] bridge : bridges) {
            String[] bridgeService = {
                    // 发生机场
                    getEmptyString(bridge[0]),
                    // 航班日期
                    bridge[1] == null ? "" : DateUtils.format((Date) bridge[1]),
                    // 航班号
                    getEmptyString(bridge[2]),
                    // 机号
                    getEmptyString(bridge[3]),
                    // 航段
                    getEmptyString(bridge[4]),
                    // 起降标识
                    getEmptyString(bridge[5]), "", "", "",
                    // 桥载空调
                    getEmptyString(bridge[6]),
                    // 桥载空调开始时间
                    bridge[7] == null ? "" : DateUtils.format((Date) bridge[7]),
                    // 桥载空调结束时间
                    bridge[8] == null ? "" : DateUtils.format((Date) bridge[8])};
            listBridgeService.add(bridgeService);
        }
        ExcelData data = new ExcelData();
        data.setFileName("桥载数据导出-" + DateUtils.format(searchForm.getStartDate()) + "_"
                + DateUtils.format(searchForm.getEndDate()) + ".xlsx");
        String[] head = {Constants.bridgeServiceRecordEnum.AIRPORT_CODE.getValue(),
                Constants.bridgeServiceRecordEnum.FLIGHT_DATE.getValue(),
                Constants.bridgeServiceRecordEnum.FLIGHT_NO.getValue(),
                Constants.bridgeServiceRecordEnum.REG_NO.getValue(),
                Constants.bridgeServiceRecordEnum.FLIGHT_SEGMENT.getValue(),
                Constants.bridgeServiceRecordEnum.LAND_FLAG.getValue(),
                Constants.bridgeServiceRecordEnum.BRIDGE_POWER.getValue(),
                Constants.bridgeServiceRecordEnum.BRIDGE_POWER_START.getValue(),
                Constants.bridgeServiceRecordEnum.BRIDGE_POWER_END.getValue(),
                Constants.bridgeServiceRecordEnum.BRIDGE_VRV.getValue(),
                Constants.bridgeServiceRecordEnum.BRIDGE_VRV_START.getValue(),
                Constants.bridgeServiceRecordEnum.BRIDGE_VRV_END.getValue()};
        data.setHead(head);
        data.setData(listBridgeService);
        FileUtils.exportExcel(response, data);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, searchForm, content, searchForm.getAirportCode(), "桥载");
    }

    private static String getEmptyString(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    /**
     * Title: saveDeviceUsedData<br>
     * Author: 叶咏秋<br>
     * Description: (上传某个航班的特车/设备数据)<br>
     * Date:  16:14 <br>
     *
     * @param serviceRecordForm :
     * @param user              return: ResultBuilder
     */
    @OperateLog(operateType = "新增", operateObject = "ServiceRecord")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<String>> saveDeviceUsedData(ServiceRecordForm serviceRecordForm, LoginUserDetails user, String urlName) {
        // 航班号和机场三字码
        String flightId = serviceRecordForm.getFlightId();
        String airportCode = serviceRecordForm.getAirportCode();
        List<UsedTimeAndNumberForm> usedTimeAndNumberFormList = serviceRecordForm.getUsedTimeAndNumberFormList();
        List<String> ids = new ArrayList<>();
        try {
            NON_FAIR_LOCK.lock();
            // 先删除数据
            serviceRecordDao.deleteServiceRecordByFlightId(flightId);
            serviceRecordDao.flush();
            // 装插入数据的list
            List<ServiceRecord> serviceRecordList = new ArrayList<>();
            List<String> useTimeList = variableRecordDao.getUseTimeCodeList();
            List<String> useNumberList = variableRecordDao.getUseNumberCodeList();
            List<String> altList = variableRecordDao.getServiceCodeByUnit("A");
            List<String[]> serviceNameAndCodeList = variableRecordDao.getServiceNameAndCodeList();
            Map<String, String> serviceCodeMap = getServiceCodeMap(serviceNameAndCodeList);
            for (UsedTimeAndNumberForm usedTimeAndNumberForm : usedTimeAndNumberFormList) {
                // 服务代码
                String serviceCode = usedTimeAndNumberForm.getServiceCode().toUpperCase();// 转成大写,数据库存的是大写
                // 开始时间
                Date startTime =
                        FormatUtils.parseStringToDateTime(usedTimeAndNumberForm.getStartTime());
                // 结束时间
                Date endTime = FormatUtils.parseStringToDateTime(usedTimeAndNumberForm.getEndTime());
                // 使用次数
                Double usedNumber = FormatUtils.parseStringToDouble(usedTimeAndNumberForm.getUsedNumber());
                ServiceRecord serviceRecord = getServiceRecordOne(flightId, airportCode, serviceCode, user, serviceCodeMap);
                /*
                作为真实数据插入的情况有2种：
                 *   startTime 和 endTime不为 null, usedNumber为 null;   插入使用时长数据的情况
                 *   startTime 和 endTime为 null, usedNumber不为 null.  插入使用次数或人工时数据的情况
                 */
                if (isaBoolean(startTime, endTime, usedNumber)) {
                    // 插入使用时长数据的情况
                    // 计算时长
                    usedNumber = DateUtils.calculateLengthOfTime(serviceRecord.getServiceCode(), startTime, endTime);

                    if (useTimeList.contains(serviceRecord.getServiceCode())) {
                        // 客桥、客梯车仅降落航班才有，需屏蔽起飞航班
                        FlightInfo flightInfo = flightInfoDao.getFlightInfoById(flightId);
                        if (check(serviceRecord.getServiceCode(), flightInfo, startTime, endTime, serviceCodeMap)) {
                            continue;
                        }

                        setInfo(serviceRecord, startTime, endTime, usedNumber);

                        // 添加到集合
                        serviceRecordList.add(serviceRecord);
                    }
                } else if (isaBooleanTwo(startTime, endTime, usedNumber)) {
                    // 插入使用次数或人工时数据的情况
                    saveDeviceUsedDataBiz0(useNumberList, serviceCode, altList, usedNumber, serviceRecord, serviceRecordList);
                } else {
                    throw new GenericException(BusinessMessageEnum.SERVICE_RECORD_TIME_AND_USED_NUMBER_ERROR.getCode(), BusinessMessageEnum.SERVICE_RECORD_TIME_AND_USED_NUMBER_ERROR.getMsg());
                }
                String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_SAVE.getValue();
                logService.addLogForSave(usedTimeAndNumberForm, user, content, airportCode);
            }
            serviceRecordDao.saveAll(serviceRecordList);
            ids.add(flightId);
            //删除备份表中的数据
            serviceRecordConfirmDao.deleteServiceRecordByDlightId(flightId);

        } catch (GenericException e) {
            throw e;
        } catch (Exception e) {
            // 抛出异常回滚事务
            throw new GenericException(BusinessMessageEnum.SAVE_VO_ERROR.getCode(), BusinessMessageEnum.SAVE_VO_ERROR.getMsg());
        } finally {
            NON_FAIR_LOCK.unlock();
        }
        return new ResultBuilder.Builder<List<String>>().data(ids).builder();
    }

    private void saveDeviceUsedDataBiz0(List<String> useNumberList, String serviceCode, List<String> altList, Double usedNumber, ServiceRecord serviceRecord, List<ServiceRecord> serviceRecordList) {
        if (isaBooleanThree(useNumberList, serviceCode, altList)) {
            usedNumber = calculateUsedNumber(serviceCode, usedNumber);
            setInfo(serviceRecord, null, null, usedNumber);
            serviceRecordList.add(serviceRecord);
        }
    }

    public ServiceRecord getServiceRecordOne(String flightId,
                                             String airportCode,
                                             String serviceCode,
                                             LoginUserDetails user,
                                             Map<String, String> serviceCodeMap) {
        ServiceRecord serviceRecord = new ServiceRecord();
        serviceRecord.setFlightId(flightId);
        serviceRecord.setAirportCode(airportCode);
        serviceRecord.setServiceCode(serviceCode);
        serviceRecord.setServiceName(serviceCodeMap.get(serviceCode));
        serviceRecord.setCreateBy(user.getUsername());
        serviceRecord.setCreateTime(new Date());
        return serviceRecord;
    }

    public void setInfo(ServiceRecord serviceRecord, Date startTime, Date endTime, Double usedNumber) {
        serviceRecord.setStartTime(startTime);
        serviceRecord.setEndTime(endTime);
        // 如果是宜宾巴中录入客梯车使用时长，则固定使用时间为一小时
        if ("CET".equals(serviceRecord.getServiceCode()) && ("BZX".equalsIgnoreCase(serviceRecord.getAirportCode())
                || "JZH".equalsIgnoreCase(serviceRecord.getAirportCode()))) {
            usedNumber = 1.00;
        }
        serviceRecord.setUsedNumber(usedNumber);
    }

    private static boolean isaBooleanThree(List<String> useNumberList, String serviceCode, List<String> altList) {
        return useNumberList.contains(serviceCode) || altList.contains(serviceCode) ;
    }

    private static boolean isaBooleanTwo(Date startTime, Date endTime, Double usedNumber) {
        return startTime == null && endTime == null && usedNumber != null;
    }

    private static boolean isaBoolean(Date startTime, Date endTime, Double usedNumber) {
        return startTime != null && endTime != null && usedNumber == null;
    }

    private boolean check(String serviceCode, FlightInfo flightInfo, Date startTime, Date endTime, Map<String, String> serviceCodeMap) {
        if (CommonConstants.excludeDepartureFlightsList.contains(serviceCode) && (flightInfo == null || "D".equalsIgnoreCase(flightInfo.getFlightFlag()))) {
            return true;
        }
        if (isInStayTime(startTime, endTime, flightInfo)) {
            throw new GenericException(BusinessMessageEnum.DATA_INVALID_TIME_NOT_SCOPE_ERROR.getCode(), serviceCodeMap.get(serviceCode) + BusinessMessageEnum.DATA_INVALID_TIME_NOT_SCOPE_ERROR.getMsg());
        }
        return false;
    }

    @NotNull
    private static Map<String, String> getServiceCodeMap(List<String[]> serviceNameAndCodeList) {
        Map<String, String> serviceCodeMap = new HashMap<>();
        for (String[] serviceNameAndCode : serviceNameAndCodeList) {
            serviceCodeMap.put(serviceNameAndCode[0], serviceNameAndCode[1]);
        }
        return serviceCodeMap;
    }

    /**
     * Title: calculateUsedNumber<br>
     * Author: 刘志恒<br>
     * Description: 处理usedNumber，部分保障数据的usedNumber需要转化为不足半个小时按半小时计，满半小时不足一小时按一小时计<br>
     * Date:  2022/11/25 15:16 <br>
     *
     * @param serviceCode ：
     * @param usedNumber  :
     */
    public double calculateUsedNumber(String serviceCode, Double usedNumber) {
        if (usedNumber.compareTo(0.0) < 0) {
            throw new GenericException(BusinessMessageEnum.SERVICE_RECORD_USED_NUMBER_ERROR.getCode(),
                    BusinessMessageEnum.SERVICE_RECORD_USED_NUMBER_ERROR.getMsg());
        }
        if (CommonConstants.HALF_HOUR_SERVICE_LIST.contains(serviceCode)) {
            BigDecimal number = BigDecimal.valueOf(usedNumber);
            return number.divide(BigDecimal.valueOf(0.5), 0, RoundingMode.UP).divide(BigDecimal.valueOf(2)).doubleValue();
        } else {
            return usedNumber;
        }
    }


    /**
     * Title: copyProperties<br>
     * Author: 叶咏秋<br>
     * Description: (Bean复制)<br>
     * Date:  17:01 <br>
     *
     * @param source :
     * @param target return: void
     */
    public void copyProperties(Object source, Object target) {
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.error("BeanUtils property copy failed,caused by:", e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
    }


    /**
     * Title: copyProperties<br>
     * Author: LIUZHIHENG<br>
     * Description: (Bean复制忽略空值)<br>
     * Date:  17:01 <br>
     *
     * @param source :
     * @param target return: void
     */
    public void copyPropertiesIgnoreNull(Object source, Object target) {
        try {
            BeanUtils.copyProperties(source, target, getNullPropertyNames(source));
        } catch (Exception e) {
            log.error("BeanUtils property copy failed,caused by:", e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
    }

    /**
     * 获取对象中的空字段，解决copyProperties时，null值覆盖原值的问题
     *
     * @param source :
     * @return :
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * Title: exportFlightACCA<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  9:46 2020/12/25 <br>
     *
     * @param res  :
     * @param form return: void
     */
    @Override
    public void exportFlightAcca(HttpServletResponse res, FlightInfoSearchForm form, String urlName, LoginUserDetails user) {
        this.setFlightTimeDefaultRange(form);
        SimpleDateFormat sd = new SimpleDateFormat("MM.dd");
        List<ACCAExcelVo> list = flightInfoBizMapper.getACCAData(form);
        String fileName = "清算中心模板数据导出-" + sd.format(form.getStartDate() == null ? form.getFlightTimeStartDate() : form.getStartDate()) + "_"
                + sd.format(form.getEndDate() == null ? form.getFlightTimeEndDate() : form.getEndDate());
        EasyExcelUtil.responseExcel(res, fileName, "sheet1", ACCAExcelVo.class, list, CollUtil.toList(new LongestMatchColumnWidthStyleStrategy()));
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, form, content, form.getAirportCode(), "清算中心");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportFlightInfoByCondition(String urlName, FlightInfoExportForm form, LoginUserDetails user, HttpServletResponse res) {
        String airportCode = form.getAirportCode();
        String airlineCode = form.getAirlineCode();
        String flightNo = form.getFlightNo();
        String flightFlag = form.getFlightFlag();
        String ids = form.getIds();
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        Set<String> idset = new HashSet<>();
        if (CharSequenceUtil.isNotBlank(ids)) {
            String[] arrStr = ids.split(",");
            Collections.addAll(idset, arrStr);
        }
        List<FlightInfo> flightInfos;
        if (!idset.isEmpty()) {
            flightInfos = flightInfoDao.queryFlightDateByIds(idset);
        } else {
            this.setFlightTimeDefaultRange(form);
            flightInfos = flightInfoDao.getFlightInfoByCondition(airportCode, startDate, endDate,
                    form.getFlightTimeStartDate(), form.getFlightTimeEndDate(),
                    airlineCode, flightNo, flightFlag);
        }
        // 导出excel文件
        XSSFWorkbook wb = new XSSFWorkbook();
        // 设置字体格式
        CellStyle cs = wb.createCellStyle();
        Font f = wb.createFont();
        f.setFontName("Arial");
        cs.setFont(f);
        Sheet sheet = wb.createSheet("FeeBill");// 创建一张表
        Row titleRow = sheet.createRow(0);// 创建第一行，起始为0

        titleRow.createCell(0).setCellValue(Constants.FlightInfoEnum.SERIAL_NO.getValue());// 第一列
        titleRow.createCell(1).setCellValue(Constants.FlightInfoEnum.DEPART_TERMINAL.getValue());
        titleRow.createCell(2).setCellValue(Constants.FlightInfoEnum.ARRIVE_TERMINAL.getValue());
        titleRow.createCell(3).setCellValue(Constants.FlightInfoEnum.FLIGHT_DATE.getValue());
        titleRow.createCell(4).setCellValue(Constants.FlightInfoEnum.CARRIER.getValue());
        titleRow.createCell(5).setCellValue(Constants.FlightInfoEnum.FLIGHT_NO.getValue());
        titleRow.createCell(6).setCellValue(Constants.FlightInfoEnum.AIRPLANE_NO.getValue());
        titleRow.createCell(7).setCellValue(Constants.FlightInfoEnum.AIRPLANE_MODEL.getValue());
        titleRow.createCell(8).setCellValue(Constants.FlightInfoEnum.AIRLINE_ATTR.getValue());
        titleRow.createCell(9)
                .setCellValue(Constants.FlightInfoEnum.TOTAL_DOMESTIC_DEPART.getValue());
        titleRow.createCell(10)
                .setCellValue(Constants.FlightInfoEnum.DOMESTIC_DEPART_INFANT.getValue());
        titleRow.createCell(11)
                .setCellValue(Constants.FlightInfoEnum.DOMESTIC_DEPART_CHILD.getValue());
        titleRow.createCell(12)
                .setCellValue(Constants.FlightInfoEnum.TOTAL_INTENATIONAL_DEPART.getValue());
        titleRow.createCell(13)
                .setCellValue(Constants.FlightInfoEnum.INTENATIONAL_DEPART_INFANT.getValue());
        titleRow.createCell(14)
                .setCellValue(Constants.FlightInfoEnum.INTENATIONAL_DEPART_CHILD.getValue());
        titleRow.createCell(15)
                .setCellValue(Constants.FlightInfoEnum.INTENATIONAL_DIPLOMACY.getValue());
        titleRow.createCell(16).setCellValue(Constants.FlightInfoEnum.REMARK.getValue());
        for (Cell c : titleRow) {
            c.setCellStyle(cs);
        }
        int cell = 1;
        for (FlightInfo flightInfo : flightInfos) {
            // 只统计出港
            if ("A".equals(flightInfo.getFlightFlag())) {
                continue;
            }
            Row row = sheet.createRow(cell);// 从第二行开始保存数据
            exportFlightInfoByConditionBizOne(flightInfo, row, cell, cs);
            cell++;
        }

        String fileName =
                FormatUtils.formatDateToDay(startDate) + "_" + FormatUtils.formatDateToDay(endDate)
                        + Constants.FlightInfoEnum.FLIGHT_EXCEL_NAME.getValue()
                        + Constants.FlightInfoEnum.FLIGHT_EXCEL_NAME_SUFFIX.getValue();
        FileUtils.exportToExcel(wb, fileName, res);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, form, content, airportCode, "航班数据");
    }

    private void exportFlightInfoByConditionBizOne(FlightInfo flightInfo, Row row, int cell, CellStyle cs) {
        // 将数据库的数据遍历出来
        row.createCell(0).setCellValue(cell);
        row.createCell(1).setCellValue(flightInfo.getFromAirportCode());
        row.createCell(2).setCellValue(flightInfo.getToAirportCode());
        row.createCell(3)
                .setCellValue(DateUtils.format(flightInfo.getFlightDate(), "yyyyMMdd"));
        row.createCell(4).setCellValue(flightInfo.getAirlineCode());
        row.createCell(5).setCellValue(flightInfo.getFlightNo());
        row.createCell(6).setCellValue(flightInfo.getRegNo());
        // 设置机型为飞机信息表中机号对应的机型
        AircraftInfo af = aircraftDao.getAircraftInfoByRegNoAndAirlineCode(
                flightInfo.getRegNo(), flightInfo.getAirlineCode(), flightInfo.getFlightDate());
        if (af != null) {
            row.createCell(7).setCellValue(af.getAirplaneModel());
        } else {
            row.createCell(7).setCellValue("");
        }
        row.createCell(8).setCellValue(flightInfo.getFlightLineType());
        // 判断国内和国际航班
        if (flightInfo.getFlightType() == null || "D".equals(flightInfo.getFlightType())) {
            row.createCell(9).setCellValue(
                    getIntegerValue(flightInfo.getPsgNumber()));
            row.createCell(10).setCellValue(
                    getIntegerValue(flightInfo.getInfantNumber()));
            row.createCell(11).setCellValue(
                    getIntegerValue(flightInfo.getChildNumber()));
            row.createCell(12).setCellValue("");
            row.createCell(13).setCellValue("");
            row.createCell(14).setCellValue("");
        } else if ("I".equals(flightInfo.getFlightType())) {
            row.createCell(9).setCellValue("");
            row.createCell(10).setCellValue("");
            row.createCell(11).setCellValue("");
            row.createCell(12).setCellValue(
                    getIntegerValue(flightInfo.getPsgNumber()));
            row.createCell(13).setCellValue(
                    getIntegerValue(flightInfo.getInfantNumber()));
            row.createCell(14).setCellValue(
                    getIntegerValue(flightInfo.getChildNumber()));
        }
        row.createCell(15).setCellValue("");
        row.createCell(16).setCellValue("");

        for (Cell c : row) {
            c.setCellStyle(cs);
        }
    }

    private Integer getIntegerValue(Integer number) {
        return number == null ? 0 : number;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importActualFlightTime(MultipartFile file, String airportCode, LoginUserDetails user, String urlName) {
        List<ActualFlightTime> actualFlightTimes = getActualFlightTimes(file);
        List<FlightInfo> result = new ArrayList<>();
        // 对比航班
        for (ActualFlightTime actualFlightTime : actualFlightTimes) {
            String fromCode = getFromCode(actualFlightTime);
            String toCode = getToCode(actualFlightTime);
            if (CharSequenceUtil.isNotBlank(fromCode) && CharSequenceUtil.isNotBlank(toCode)) {
                String flightSegment = fromCode + "-" + toCode;
                List<FlightInfo> flightInfoList = flightInfoDao.listFlightInfoByCondition2(
                        actualFlightTime.getFlightNo().substring(3),
                        actualFlightTime.getFlightDate(), flightSegment, airportCode);
                if (!CollectionUtils.isEmpty(flightInfoList)) {
                    FlightInfo flightInfo = getFlightInfo(user, actualFlightTime, flightInfoList);
                    result.add(flightInfo);
                }
            }
        }
        // 批量更新
        flightInfoDao.saveAll(result);
        // 日志记录
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_IMPORT.getValue() + file.getOriginalFilename()
                + Constants.LogEnum.LOG_MEG_2.getValue();
        logService.addLogForImport(user, content, airportCode);
    }

    private String getToCode(ActualFlightTime actualFlightTime) {
        String toCode = "";
        List<FlightTerminal> result2 = flightTerminalDao.findByFrcd(actualFlightTime.getToAirportCode());
        if (!CollectionUtils.isEmpty(result2)) {
            toCode = result2.get(0).getAirportCode();
        }
        return toCode;
    }

    private String getFromCode(ActualFlightTime actualFlightTime) {
        List<FlightTerminal> result1 = flightTerminalDao.findByFrcd(actualFlightTime.getFromAirportCode());
        // 获取航段
        String fromCode = "";
        if (!CollectionUtils.isEmpty(result1)) {
            fromCode = result1.get(0).getAirportCode();
        }
        return fromCode;
    }

    private @NotNull FlightInfo getFlightInfo(LoginUserDetails user, ActualFlightTime actualFlightTime, List<FlightInfo> flightInfoList) {
        FlightInfo flightInfo = flightInfoList.get(0);
        flightInfo.setModifiedBy(user.getUsername());
        flightInfo.setModifiedTime(new Date());

        // 修改手动修改起降时间字段
        flightInfo.setIsModifyFlightTime(1);
        if ("D".equals(flightInfo.getFlightFlag())) {
            // 起飞航班
            flightInfo.setFlightTime(actualFlightTime.getActualDepartureTime());
            // 计算停场时间
            calculateDownTime(flightInfo, user);
        } else if ("A".equals(flightInfo.getFlightFlag())) {
            // 降落航班
            flightInfo.setFlightTime(actualFlightTime.getActualLandingTime());
        }
        return flightInfo;
    }

    private @NotNull List<ActualFlightTime> getActualFlightTimes(MultipartFile file) {
        List<Object[]> excelList = FileUtils.importExcel(file);
        List<ActualFlightTime> actualFlightTimes = new ArrayList<>();
        for (Object[] objects : excelList) {
            if (objects[0] != null) {
                if (inspectObj(objects)) {
                    continue;
                }
                ActualFlightTime actualFlightTime = getActualFlightTime(objects);
                // 实际降落时间
                actualFlightTimes.add(actualFlightTime);
            }
        }
        return actualFlightTimes;
    }

    private static @NotNull ActualFlightTime getActualFlightTime(Object[] objects) {
        ActualFlightTime actualFlightTime = new ActualFlightTime();
        // 航班号
        actualFlightTime.setFlightNo(objects[0].toString().substring(1));
        // 航班日期
        actualFlightTime.setFlightDate(FormatUtils.parseStringToDate3(objects[1].toString()));
        // 起飞机场
        actualFlightTime.setFromAirportCode(objects[2].toString());
        // 降落机场
        actualFlightTime.setToAirportCode(objects[3].toString());
        // 实际起飞时间
        if (null != objects[6]) {
            actualFlightTime.setActualDepartureTime(FormatUtils.parseStringToDateTime3(objects[6].toString()));
        }
        if (objects.length > 7 && null != objects[7]) {
            actualFlightTime.setActualLandingTime(FormatUtils.parseStringToDateTime3(objects[7].toString()));
        }
        return actualFlightTime;
    }

    private boolean inspectObj(Object[] objects) {
        // 过滤公司计划取消的航班
        if (null != objects[6] && 8 == objects[6].toString().length()) {
            return true;
        }
        return objects.length > 7 && null != objects[7] && 8 == objects[7].toString().length();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> saveDeviceUsedListData(List<ServiceSecordBatchForm> serviceRecordFormList, LoginUserDetails user, String urlName) {
        try {
            NON_FAIR_LOCK_2.lock();
            // 先删除原本数据
            deleteData(serviceRecordFormList);
            serviceRecordDao.flush();
            // 装插入数据的list
            List<ServiceRecord> serviceRecordList = new ArrayList<>();
            List<String> useTimeList = variableRecordDao.getUseTimeCodeList();
            List<String> useNumberList = variableRecordDao.getUseNumberCodeList();
            List<String[]> serviceNameAndCodeList = variableRecordDao.getServiceNameAndCodeList();
            Map<String, String> serviceCodeMap = getServiceCodeMap(serviceNameAndCodeList);
            for (ServiceSecordBatchForm serviceRecordForm : serviceRecordFormList) {
                // 获取当前条的航班id
                String flightId = serviceRecordForm.getFlightId();
                // 获取当航班的机场
                String airportCode = serviceRecordForm.getAirportCode();
                // 服务代码
                String serviceCode = serviceRecordForm.getServiceCode().toUpperCase();// 转成大写,数据库存的是大写
                // 开始时间
                Date startTime = FormatUtils.parseStringToDateTime(serviceRecordForm.getStartTime());
                // 结束时间
                Date endTime = FormatUtils.parseStringToDateTime(serviceRecordForm.getEndTime());
                // 使用次数
                Double usedNumber = FormatUtils.parseStringToDouble(serviceRecordForm.getUsedNumber());
                ServiceRecord serviceRecord = getServiceRecordOne(flightId, airportCode, serviceCode, user, serviceCodeMap);
                /*
                 *作为真实数据插入的情况有2种：
                 *   startTime 和 endTime不为 null, usedNumber为 null;  插入使用时长数据的情况
                 *   startTime 和 endTime为 null, usedNumber不为 null.  插入使用次数或人工时数据的情况
                 */
                if (startTime != null && endTime != null && usedNumber == null) {
                    // 插入使用时长数据的情况
                    // 计算时长
                    usedNumber = DateUtils.calculateLengthOfTime(serviceCode, startTime, endTime);
                    serviceRecord.setStartTime(startTime);
                    serviceRecord.setEndTime(endTime);
                    if (saveDeviceUsedListDataBizOne(useTimeList, serviceCodeMap, serviceRecord, usedNumber, serviceRecordList)) {
                        continue;
                    }
                } else if (startTime == null && endTime == null && usedNumber != null) {
                    // 插入使用次数或人工时数据的情况
                    if (useNumberList.contains(serviceCode)) {
                        usedNumber = calculateUsedNumber(serviceCode, usedNumber);
                        setInfo(serviceRecord, null, null, usedNumber);
                        serviceRecordList.add(serviceRecord);
                    }
                } else {
                    throw new GenericException(BusinessMessageEnum.SERVICE_RECORD_TIME_AND_USED_NUMBER_ERROR.getCode(), BusinessMessageEnum.SERVICE_RECORD_TIME_AND_USED_NUMBER_ERROR.getMsg());
                }
                String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_SAVE.getValue();
                logService.addLogForSave(serviceRecordForm, user, content, airportCode);

            }
            serviceRecordDao.saveAll(serviceRecordList);
        } catch (GenericException e) {
            throw e;
        } catch (Exception e) {
            // 抛出异常回滚事务
            throw new GenericException(BusinessMessageEnum.SAVE_VO_ERROR.getCode(), BusinessMessageEnum.SAVE_VO_ERROR.getMsg());
        } finally {
            NON_FAIR_LOCK_2.unlock();
        }

        return new ResultBuilder.Builder<>().builder();
    }

    private void deleteData(List<ServiceSecordBatchForm> serviceRecordFormList) {
        for (ServiceSecordBatchForm entity : serviceRecordFormList) {
            serviceRecordDao.deleteServiceRecordByFlightIdAndServiceCode(entity.getFlightId(), entity.getServiceCode());
        }
    }

    private boolean saveDeviceUsedListDataBizOne(List<String> useTimeList, Map<String, String> serviceCodeMap, ServiceRecord serviceRecord, Double usedNumber, List<ServiceRecord> serviceRecordList) {
        String serviceCode = serviceRecord.getServiceCode();
        if (useTimeList.contains(serviceCode)) {
            // 客桥、客梯车仅降落航班才有，需屏蔽起飞航班
            FlightInfo flightInfo = flightInfoDao.getFlightInfoById(serviceRecord.getFlightId());
            if (CommonConstants.excludeDepartureFlightsList.contains(serviceCode) && (flightInfo == null || "D".equalsIgnoreCase(flightInfo.getFlightFlag()))) {
                return true;
            }


            if (isInStayTime(serviceRecord.getStartTime(), serviceRecord.getEndTime(), flightInfo)) {
                throw new GenericException(BusinessMessageEnum.DATA_INVALID_TIME_NOT_SCOPE_ERROR.getCode(), serviceCodeMap.get(serviceCode) + BusinessMessageEnum.DATA_INVALID_TIME_NOT_SCOPE_ERROR.getMsg());
            }

            setInfo(serviceRecord, serviceRecord.getStartTime(), serviceRecord.getEndTime(), usedNumber);
            // 添加到集合
            serviceRecordList.add(serviceRecord);
        }
        return false;
    }


    /**
     * Title: searchForFlights<br>
     * Author: 刘志恒<br>
     * Description: 模糊搜索航班号<br>
     * Date:  9:33 2021/06/18<br>
     *
     * @param flightNum :
     * @return ResultBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<List<String>> searchForFlights(String flightNum) {
        List<String> list = flightInfoDao.searchForFlights(flightNum);
        return new ResultBuilder.Builder<List<String>>().data(list).builder();
    }


    /**
     * 重新结算停场时间(包括起飞降落航班)
     */
    public void calculateDownTime(FlightInfo info, LoginUserDetails user) {
        List<FlightInfo> recalculateDownTimeList = new ArrayList<>();
        boolean changeFlag = false;

        try {
            if (info.getRegNo() != null && info.getFlightTime() != null) {
                String flightTime = DateUtils.formatDateTime(info.getFlightTime());

                // 获取该机号，在该起降时间前序/后序的十条航班数据
                List<FlightInfo> flightInfoList = getFlightInfos(info, flightTime);
                for (FlightInfo flightInfo : flightInfoList) {
                    // 跳过空值
                    if (flightInfo.getFlightTime() != null) {
                        if (!info.getFlightFlag().equals(flightInfo.getFlightFlag())) {
                            // 将现关联起的两个航班之前的关联航班停场时间置空,如果他们俩之前就是关联航班则不用置空
                            // 如果成功置空，则将置空航班放到再次计算停场时间list中
                            calculateDownTimeBizOne(info, user, flightInfo, recalculateDownTimeList);
                            // 计算停场时间
                            double time = DateUtils.hourDifference(info.getFlightTime(),
                                    flightInfo.getFlightTime());
                            Date startTime = DateUtils.earlyTime(flightInfo.getFlightTime(),
                                    info.getFlightTime());
                            Date endTime = DateUtils.laterTime(flightInfo.getFlightTime(),
                                    info.getFlightTime());
                            // 设置起飞航班停场时间
                            info.setStayStartTime(startTime);
                            info.setStayEndTime(endTime);
                            info.setStayTime(BigDecimal.valueOf(time).setScale(2, RoundingMode.DOWN));
                            info.setModifiedBy(user.getUsername());
                            info.setModifiedTime(new Date());
                            // 设置当前起飞航班的前序降落航班的停场时间
                            flightInfo.setStayStartTime(startTime);
                            flightInfo.setStayEndTime(endTime);
                            flightInfo.setStayTime(BigDecimal.valueOf(time).setScale(2, RoundingMode.DOWN));
                            flightInfo.setModifiedBy(user.getUsername());
                            flightInfo.setModifiedTime(new Date());
                            // 更新降落航班的停场时间
                            flightInfoDao.save(flightInfo);
                            flightInfoDao.flush();
                            changeFlag = true;
                            break;
                        } else {
                            throw new GenericException(
                                    "没有查询到日期为:" + DateUtils.format(info.getFlightDate()) + "的"
                                            + info.getFlightNo() + "的前序进港的航班");
                        }
                    }
                }
            } else {
                throw new GenericException("计算停场时间航班的机号为空，或是起降时间为空！！！");
            }
        } catch (GenericException ge) {
            log.error(ge.getParams());
        } catch (Exception e) {
            log.error(MSG_ERROR, e);
        } finally {
            // 如果传入航班没匹配到关联的前序或后序航班，则将本航班停场时间置为空
            // 并将本航班之前关联的前序/后续航班置空
            calculateDownTimeFinally(info, user, changeFlag, recalculateDownTimeList);
        }
    }

    private List<FlightInfo> getFlightInfos(FlightInfo info, String flightTime) {
        return "D".equals(info.getFlightFlag())
                ? flightInfoDao.getPreorderFlight(info.getRegNo(), flightTime,
                info.getAirportCode(), info.getId())
                : flightInfoDao.getPrologueFlight(info.getRegNo(), flightTime,
                info.getAirportCode(), info.getId());
    }

    private void calculateDownTimeFinally(FlightInfo info, LoginUserDetails user, boolean changeFlag, List<FlightInfo> recalculateDownTimeList) {
        if (!changeFlag) {
            // 将传入航班之前的前序起飞/后续降落航班停场时间置空；
            FlightInfo tempInfo2 = updateOldDownTime(info, user);
            if (tempInfo2 != null) {
                recalculateDownTimeList.add(tempInfo2);
            }

            info.setStayStartTime(null);
            info.setStayEndTime(null);
            info.setStayTime(new BigDecimal(0));
            info.setModifiedBy(user.getUsername());
            info.setModifiedTime(new Date());
        }
        flightInfoDao.save(info);
        flightInfoDao.flush();
        // 如果再次计算停场时间list不为空，则将list中的flightinfo重新计算停场时间
        for (FlightInfo reCalcuateInfo : recalculateDownTimeList) {
            calculateDownTime(reCalcuateInfo, user);
        }
    }

    private void calculateDownTimeBizOne(FlightInfo info, LoginUserDetails user, FlightInfo flightInfo, List<FlightInfo> recalculateDownTimeList) {
        if (info.getStayStartTime() != flightInfo.getStayStartTime()
                || info.getStayEndTime() != flightInfo.getStayEndTime()) {
            // 将新关联的前序/后序航班之前关联的航班停场时间置空
            FlightInfo tempInfo = updateOldDownTime(flightInfo, user);
            if (tempInfo != null) {
                recalculateDownTimeList.add(tempInfo);
            }

            // 将传入航班之前的前序起飞/后续降落航班停场时间置空；
            FlightInfo tempInfo2 = updateOldDownTime(info, user);
            if (tempInfo2 != null) {
                recalculateDownTimeList.add(tempInfo2);
            }

        }
    }

    /**
     * Title: updateOldDownTime<br>
     * Author: 刘志恒<br>
     * Description: 重新计算停场时间时，将之前关联航班的停场时间清零，并返回之前关联航班<br>
     *
     * @param info 置空此航班的关联航班的停场时间
     *             Date:  2021/11/4 15:47 <br>
     */
    public FlightInfo updateOldDownTime(FlightInfo info, LoginUserDetails user) {
        // 如果当前航班的
        if (info.getStayStartTime() != null && info.getStayEndTime() != null
                && info.getStayTime() != null) {
            // 根据传入航班获取其关联的起飞/降落航班
            FlightInfo flightInfo = flightInfoDao.getFlightInfoByStayTimeAndRegNo(info.getRegNo(),
                    info.getAirportCode(), info.getStayStartTime(), info.getStayEndTime(),
                    info.getId());
            if (flightInfo != null) {
                flightInfo.setStayStartTime(null);
                flightInfo.setStayEndTime(null);
                flightInfo.setStayTime(new BigDecimal(0));
                flightInfo.setModifiedBy(user.getUsername());
                flightInfo.setModifiedTime(new Date());
                flightInfoDao.save(flightInfo);
                flightInfoDao.flush();
                return flightInfo;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> updatePassengerDataBatch(List<FlightUpdatePassengerForm> flightInfoForms, LoginUserDetails user, String urlName) {

        for (FlightUpdatePassengerForm fif : flightInfoForms) {
            FlightInfo fi = flightInfoDao.getFlightInfoById(fif.getId());

            boolean update = false;
            // 设置头等舱人数
            if (fif.getFirstClassNumber() != null) {
                fi.setFirstClassNumber(fif.getFirstClassNumber());
                update = true;
            }
            // 设置重要旅客人数
            if (fif.getImportantNumber() != null) {
                fi.setImportantNumber(fif.getImportantNumber());
                update = true;
            }
            // 设置重要旅客随行人数
            if (fif.getAccompanyingImportantNumber() != null) {
                fi.setAccompanyingImportantNumber(fif.getAccompanyingImportantNumber());
                update = true;
            }
            // 设置持卡旅客人数
            if (fif.getCardHolderNumber() != null) {
                fi.setCardHolderNumber(fif.getCardHolderNumber());
                update = true;
            }
            // 设置持卡随行人数
            if (fif.getAccompanyingCardHolderNumber() != null) {
                fi.setAccompanyingCardHolderNumber(fif.getAccompanyingCardHolderNumber());
                update = true;
            }
            if (update) {
                fi.setModifiedBy(user.getUsername());
                fi.setModifiedTime(new Date());

                flightInfoDao.save(fi);
            }
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> getFlightStayTime(String airportCode, String startTime, String endTime, LoginUserDetails user, String urlName) {
        // 校验传入参数是否符合格式("yyyy-MM-dd HH:mm:ss")
        if ((DateUtils.isValidDateTime(startTime)) || (DateUtils.isValidDateTime(endTime))) {
            throw new GenericException(BusinessMessageEnum.FORM_ATTR_ERROR.getCode(), BusinessMessageEnum.FORM_ATTR_ERROR.getMsg());
        }
        // 由于选择时间较大时，计算时间较长，多线程下避免数据不安全问题。考虑线程等待时长，等的时间长的先执行

        try {
            STAY_TIME_LOCK.lock();
            // 获取当前机场所有离港航班
            List<FlightInfo> departureFlights = flightInfoDao.getDepartureFlight(airportCode, startTime, endTime);
            if (departureFlights != null && !departureFlights.isEmpty()) {
                for (FlightInfo flightInfo : departureFlights) {
                    // 将查询出来的数据已有的停场开始结束时间及时长设为初始值，重新计算
                    flightInfo.setStayTime(new BigDecimal(0));
                    flightInfo.setStayStartTime(null);
                    flightInfo.setStayEndTime(null);
                    flightInfoDao.save(flightInfo);
                    FlightInfo preFlight = null;
                    // 获取离港航班的前序进港航班
                    List<FlightInfo> preorderFlight = flightInfoDao.getPreFlightByCondition(flightInfo.getRegNo(), DateUtils.formatDateTime(flightInfo.getFlightTime()), airportCode);
                    preFlight = getFlightInfo(flightInfo, preorderFlight, preFlight);
                    // 如果为空说明未找到前序航班。当正常重新计算完成，将前序航班重新赋值并入库
                    if (preFlight != null) {
                        flightInfoDao.save(preFlight);
                        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_SAVE.getValue();
                        logService.addLogForSave(preFlight, user, content, airportCode);
                    }
                    // 如果开始结束时间被重新赋值，则将离港航班重新计算的结果存入数据库
                    getFlightStayTimeBizOne(airportCode, user, urlName, flightInfo);
                }
            }

        } catch (Exception e) {
            log.error(MSG_ERROR, e);
            if (e instanceof ParseException) {
                throw new GenericException(BusinessMessageEnum.FORMAT_ERROR.getCode(), BusinessMessageEnum.FORMAT_ERROR.getMsg());
            }
            // 抛出异常回滚事务
            throw new GenericException(BusinessMessageEnum.SAVE_VO_ERROR.getCode(), BusinessMessageEnum.SAVE_VO_ERROR.getMsg());
        } finally {
            STAY_TIME_LOCK.unlock();
        }
        return new ResultBuilder.Builder<>().builder();
    }

    private void getFlightStayTimeBizOne(String airportCode, LoginUserDetails user, String urlName, FlightInfo flightInfo) {
        if (flightInfo.getStayStartTime() != null && flightInfo.getStayEndTime() != null) {
            flightInfoDao.save(flightInfo);
            String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_SAVE.getValue();
            logService.addLogForSave(flightInfo, user, content, airportCode);
        }
    }

    private FlightInfo getFlightInfo(FlightInfo flightInfo, List<FlightInfo> preorderFlight, FlightInfo preFlight) throws ParseException {
        Date stayStartTime;
        Date stayEndTime;
        double stayTime;
        if (preorderFlight != null) {
            for (FlightInfo info : preorderFlight) {
                if (info.getFlightTime() != null && flightInfo.getFlightTime() != null && DateUtils.getTime(DateUtils.formatFlightTime(info.getFlightTime()).trim() + ":00") < DateUtils.getTime(DateUtils.formatFlightTime(flightInfo.getFlightTime()).trim() + ":00")) {
                    // 将查询出来的前序航班数据已有的停场开始结束时间及时长设为初始值，重新计算
                    preFlight = info;
                    preFlight.setStayTime(new BigDecimal(0));
                    preFlight.setStayStartTime(null);
                    preFlight.setStayEndTime(null);
                    flightInfoDao.save(preFlight);
                    // 如果查出来最近的航班离当前离港航班停机时长超过7天,放弃当前数据
                    if (DateUtils.getTime(DateUtils.formatFlightTime(flightInfo.getFlightTime()).trim() + ":00") - DateUtils.getTime(DateUtils.formatFlightTime(info.getFlightTime()).trim() + ":00") < CommonConstants.SEVEN_DAY_LONG) {
                        // 为起始结束时间及停机时长赋值
                        stayStartTime = info.getFlightTime();
                        stayEndTime = flightInfo.getFlightTime();
                        stayTime = DateUtils.hourDifference(stayStartTime, stayEndTime);
                        // 重新计算后,为离港航班的属性赋值
                        flightInfo.setStayTime(BigDecimal.valueOf(stayTime).setScale(2, RoundingMode.DOWN));
                        flightInfo.setStayStartTime(stayStartTime);
                        flightInfo.setStayEndTime(stayEndTime);
                        // 重新计算后,为前序到港航班的属性赋值
                        preFlight.setStayTime(BigDecimal.valueOf(stayTime).setScale(2, RoundingMode.DOWN));
                        preFlight.setStayStartTime(stayStartTime);
                        preFlight.setStayEndTime(stayEndTime);
                        break;
                    }
                }

            }
        }
        return preFlight;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportFlightBusinessData(FlightInfoSearchForm flightInfoSearchForm, HttpServletResponse response, LoginUserDetails user, String urlName) {
        this.setFlightTimeDefaultRange(flightInfoSearchForm);
        // 用于保存需要导出数据
        List<FlightBusinessDataExcelVO> resultList = new ArrayList<>();
        // 根据页面条件从数据库中获取数据
        List<FlightInfo> flightList = flightInfoDao.getFlightInfoByCondition(
                flightInfoSearchForm.getAirportCode(), flightInfoSearchForm.getStartDate(),
                flightInfoSearchForm.getEndDate(),
                flightInfoSearchForm.getFlightTimeStartDate(), flightInfoSearchForm.getFlightTimeEndDate(),
                flightInfoSearchForm.getAirlineCode(),
                flightInfoSearchForm.getFlightNo(), flightInfoSearchForm.getFlightFlag());
        if (CollUtil.isNotEmpty(flightList)) {
            List<String> flightIdList = flightList.stream().map(FlightInfo::getId).distinct().collect(Collectors.toList());
            Map<String, List<ServiceRecord>> flightIdServiceRecordListMap =
                    listDeviceUsedDataByFlightIdList(flightIdList);
            for (FlightInfo flightInfo : flightList) {
                List<ServiceRecord> serviceRecordList = flightIdServiceRecordListMap.get(flightInfo.getId());
                if (!CollectionUtils.isEmpty(serviceRecordList)) {
                    serviceRecordList.forEach(serviceRecord -> {
                        FlightBusinessDataExcelVO flightBusinessDataExcelVO = ObjectUtils.copyBean(flightInfo, FlightBusinessDataExcelVO.class);
                        flightBusinessDataExcelVO.setServiceName(serviceRecord.getServiceName());
                        flightBusinessDataExcelVO.setStartTime(serviceRecord.getStartTime());
                        flightBusinessDataExcelVO.setEndTime(serviceRecord.getEndTime());
                        if(serviceRecord.getUsedNumber()==null){
                            return;
                        }
                        checkSTCode(serviceRecord, flightBusinessDataExcelVO);
                        flightBusinessDataExcelVO.setAirportCode(flightInfoSearchForm.getAirportCode());

                        resultList.add(flightBusinessDataExcelVO);
                    });
                }
            }
        }

        // 导出数据的时间段
        String startTimeStr = FormatUtils.formatDateToString(flightInfoSearchForm.getStartDate() == null ? flightInfoSearchForm.getFlightTimeStartDate() : flightInfoSearchForm.getStartDate()).replace("-", "");
        String endTimeStr = FormatUtils.formatDateToString(flightInfoSearchForm.getEndDate() == null ? flightInfoSearchForm.getFlightTimeEndDate() : flightInfoSearchForm.getEndDate()).replace("-", "");
        // 表名
        String fileName = Constants.FlightBusinessInfoEnum.FLIGHT_BUSINESS_EXCEL_NAME_PREFIX
                .getValue() + startTimeStr + "_" + endTimeStr
                + Constants.FlightBusinessInfoEnum.FLIGHT_BUSINESS_EXCEL_NAME_SUFFIX.getValue();
        EasyExcelUtil.responseExcel(response, fileName, "sheet1", FlightBusinessDataExcelVO.class, resultList,
                CollUtil.toList(new LongestMatchColumnWidthStyleStrategy()));
        // 日志输出
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        AirportBillSearchForm form = new AirportBillSearchForm();
        form.setAirportCode(flightInfoSearchForm.getAirportCode());
        form.setStartDate(DateUtils.format(flightInfoSearchForm.getStartDate()));
        form.setEndDate(DateUtils.format(flightInfoSearchForm.getEndDate()));
        logService.addLogForExport(user, form, content, form.getAirportCode(), "业务保障数据");
    }

    private void checkSTCode(ServiceRecord serviceRecord, FlightBusinessDataExcelVO flightBusinessDataExcelVO) {
        if("ST".equalsIgnoreCase(serviceRecord.getServiceCode())){
            List<SafeguardTypeVo> safeguardTypeVoList = formulaService.getSafeguardType(flightBusinessDataExcelVO.getAirportCode());
            for(SafeguardTypeVo safeguardTypeVo : safeguardTypeVoList){
                if(safeguardTypeVo.getSortOrder().equals(serviceRecord.getUsedNumber().intValue())) {
                    flightBusinessDataExcelVO.setOther(safeguardTypeVo.getSelectorItemName());
                }
            }
        }else {
            flightBusinessDataExcelVO.setUsedNumber(BigDecimal.valueOf(serviceRecord.getUsedNumber()).stripTrailingZeros().toString());
            flightBusinessDataExcelVO.setUsedNumber2(flightBusinessDataExcelVO.getUsedNumber());
        }
    }

    /**
     * Title: importFlightBusinessData <br>
     * Description: 导入业务保障数据-数据格式处理 <br>
     *
     * @param file             :
     * @param airportCodeParam :
     * @return com.swcares.common.ResultBuilder
     * author 曾华川  <br>
     * date 2022-10-19 16:44<br>
     */
    public ResultBuilder<Map<String, List<FlightBusinessDataExcelParserVo>>> importFlightBusinessDataHandle(MultipartFile file, String airportCodeParam) {
        // 文件格式校验
        ExcelUtils.checkSuffix(file);
        // 读取上传的业务保障数据
        List<FlightBusinessDataExcelParserVo> excelDataParserVOList = ExcelFlightUtils.readFlightBusinessExcel(file, airportCodeParam);
        Map<String, List<FlightBusinessDataExcelParserVo>> result = new HashMap<>();
        List<FlightBusinessDataExcelParserVo> successExcelDataVOList;
        // 将读取成功的数据进行校验
        successExcelDataVOList = getSuccessList(excelDataParserVOList);
        excelDataParserVOList.removeAll(successExcelDataVOList);
        // 表格内容校验
        if (CollectionUtils.isEmpty(successExcelDataVOList)) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(), BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }
        // 系统中的24项保障服务
        List<RulesVariableRecord> variableRecordList = variableRecordDao.listDeviceVariableRecordLikeCAndS();
        // 对保障项数据的条数进行校验
        // 时间格式数据
        List<FlightBusinessDataExcelParserVo> timeFormatList = successExcelDataVOList.stream()
                .filter(excelData -> CharSequenceUtil.isNotBlank(excelData.getStartTime()) || CharSequenceUtil.isNotBlank(excelData.getEndTime()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(timeFormatList)) {
            // 去除时间类型的数据
            successExcelDataVOList.removeAll(timeFormatList);
            // 处理时间格式数据
            handleTimeList(timeFormatList);
        }
        // 数值格式数据
        List<FlightBusinessDataExcelParserVo> numberFormatList = successExcelDataVOList.stream()
                .filter(excelData -> CharSequenceUtil.isNotBlank(excelData.getNumber()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(numberFormatList)) {
            // 去除数值类型的数据
            successExcelDataVOList.removeAll(numberFormatList);
            // 处理数值类型数据
            handleNumberList(numberFormatList);
        }

        // 恢复为初次查询的list集合
        successExcelDataVOList.addAll(timeFormatList);
        successExcelDataVOList.addAll(numberFormatList);
        excelDataParserVOList.addAll(successExcelDataVOList);

        // 处理互斥保障项数据
        List<FlightBusinessDataExcelParserVo> mutexList = getSuccessList(excelDataParserVOList);
        if (!CollectionUtils.isEmpty(mutexList)) {
            excelDataParserVOList.removeAll(mutexList);
            handleMutexList(mutexList);
        }
        // list集合恢复
        excelDataParserVOList.addAll(mutexList);

        // 对数据的格式进行校验
        successExcelDataVOList = getSuccessList(excelDataParserVOList);
        excelDataParserVOList.removeAll(successExcelDataVOList);
        handleSuccessExcelDataVOList(successExcelDataVOList, variableRecordList);
        // list恢复
        excelDataParserVOList.addAll(successExcelDataVOList);

        result.put("success", getSuccessList(excelDataParserVOList));
        result.put("error", getErrorList(excelDataParserVOList));
        return new ResultBuilder.Builder<Map<String, List<FlightBusinessDataExcelParserVo>>>().data(result).builder();
    }

    private void handleSuccessExcelDataVOList(List<FlightBusinessDataExcelParserVo> successExcelDataVOList, List<RulesVariableRecord> variableRecordList) {
        for (FlightBusinessDataExcelParserVo excelParserVo : successExcelDataVOList) {
            // 判断表格的保障服务是否为系统服务
            boolean isSystemService = Boolean.FALSE;
            for (RulesVariableRecord rulesVariableRecord : variableRecordList) {
                // 成功匹配到系统中的服务
                if (excelParserVo.getServiceName().equals(rulesVariableRecord.getVariableName())) {
                    isSystemService = Boolean.TRUE;
                    FlightInfo statusFlight = getFlightInfo(excelParserVo, rulesVariableRecord);
                    if (statusFlight == null) {
                        break;
                    }
                    excelParserVo.setId(statusFlight.getId());
                }
            }
            // 保障内容不在系统服务的24项中-标*字段不符合要求
            if (!isSystemService) {
                excelParserVo.setErrorReason(BusinessMessageEnum.DATA_CONFORMANCE_ERROR.getMsg());
            }
        }
    }

    private @Nullable FlightInfo getFlightInfo(FlightBusinessDataExcelParserVo excelParserVo, RulesVariableRecord rulesVariableRecord) {
        // 服务数据同时为空
        if (isBooleanServiceDataBlank(excelParserVo)) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_NULL_ERROR.getMsg());
            return null;
        }
        // 服务数据同时存在
        if (isBooleanServiceDataNotBlank(excelParserVo)) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_FORMAT_NUMBER_ERROR.getMsg());
            return null;
        }

        // 服务的计量单位为小时
        if ("H".equals(rulesVariableRecord.getVariableUnit())) {
            if (serviceUnitHour(excelParserVo)) {
                return null;
            }
        } else if (!"O".equals(rulesVariableRecord.getVariableUnit()) && serviceUnitNumber(excelParserVo)) {
            // 计量单位为数量
            return null;
        }

        FlightInfo statusFlight = flightInfoDao.getFlightInfoByCondition(excelParserVo.getFlightNo(), excelParserVo.getFlightDate(), excelParserVo.getFlightFlag(), excelParserVo.getAirportCode());
        // 未找到该航班数据
        if (statusFlight == null) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_FLIGHT_CANNOT_BE_FOUND.getMsg());
            return null;
        }
        Integer status = statusFlight.getVariableStatus();
        // 业务保障数据已确认
        if (status != null && status == 1) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_IS_CONFIRMED.getMsg());
            return null;
        }
        // 判断保障开始结束时间与航班停场开始结束时间对比
        if (isBooleanBizStayTime(excelParserVo, statusFlight)) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_INVALID_TIME_NOT_SCOPE_ERROR.getMsg());
            return null;
        }
        return statusFlight;
    }

    private static boolean serviceUnitNumber(FlightBusinessDataExcelParserVo excelParserVo) {
        // 时间字段不为空-字段格式错误
        if (CharSequenceUtil.isNotBlank(excelParserVo.getStartTime()) || CharSequenceUtil.isNotBlank(excelParserVo.getEndTime())) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_SERVICE_CODE_ERROR.getMsg());
            return true;
        }
        // 数量字段为空-数据不全
        if (CharSequenceUtil.isBlank(excelParserVo.getNumber())) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_INCOMPLETE_ERROR.getMsg());
            return true;
        }
        return false;
    }

    private static boolean serviceUnitHour(FlightBusinessDataExcelParserVo excelParserVo) {
        // 表格数量字段不为空-字段格式错误
        if (CharSequenceUtil.isNotBlank(excelParserVo.getNumber())) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_SERVICE_CODE_ERROR.getMsg());
            return true;
        }
        // 表格时间字段显示不全-数据不全
        if (CharSequenceUtil.isBlank(excelParserVo.getStartTime()) || CharSequenceUtil.isBlank(excelParserVo.getEndTime())) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_INCOMPLETE_ERROR.getMsg());
            return true;
        }
        // 开始时间不能大于结束时间
        int compare = excelParserVo.getStartTime().compareTo(excelParserVo.getEndTime());
        if (compare > 0) {
            excelParserVo.setErrorReason(BusinessMessageEnum.DATA_INVALID_TIME_ERROR.getMsg());
            return true;
        }
        return false;
    }

    private boolean isBooleanBizStayTime(FlightBusinessDataExcelParserVo excelParserVo, FlightInfo statusFlight) {
        return CharSequenceUtil.isNotBlank(excelParserVo.getStartTime())
                && CharSequenceUtil.isNotBlank(excelParserVo.getEndTime())
                && isInStayTime(DateUtils.parseDateTime(excelParserVo.getStartTime()), DateUtils.parseDateTime(excelParserVo.getEndTime()), statusFlight);
    }

    private static boolean isBooleanServiceDataNotBlank(FlightBusinessDataExcelParserVo excelParserVo) {
        return CharSequenceUtil.isNotBlank(excelParserVo.getStartTime())
                && CharSequenceUtil.isNotBlank(excelParserVo.getEndTime())
                && CharSequenceUtil.isNotBlank(excelParserVo.getNumber())
                && CharSequenceUtil.isNotBlank(excelParserVo.getOther());
    }

    private static boolean isBooleanServiceDataBlank(FlightBusinessDataExcelParserVo excelParserVo) {
        return CharSequenceUtil.isBlank(excelParserVo.getStartTime())
                && CharSequenceUtil.isBlank(excelParserVo.getEndTime())
                && CharSequenceUtil.isBlank(excelParserVo.getNumber())
                && CharSequenceUtil.isBlank(excelParserVo.getOther());
    }

    /**
     * Title: importFlightBusinessData <br>
     * Description: 业务保障数据导入 <br>
     *
     * @param file             :
     * @param airportCodeParam :
     * @param urlName          :
     * @param user             :
     * @return com.swcares.common.ResultBuilder
     * author 曾华川  <br>
     * date 2022-10-24 10:33<br>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBuilder<Object> importFlightBusinessData(MultipartFile file, String airportCodeParam, String urlName, LoginUserDetails user) {
        String uploadId = uploadStatusService.newUpload("业务保障数据上传", airportCodeParam, user);
        List<FlightBusinessDataExcelParserVo> error;
        List<FlightBusinessDataExcelParserVo> successData;
        try {
            FLIGHT_BUSINESS_LOCK.lock();
            ResultBuilder<Map<String, List<FlightBusinessDataExcelParserVo>>> resultBuilder = this.importFlightBusinessDataHandle(file, airportCodeParam);
            uploadStatusService.startUpload(uploadId);

            // 保存航班信息
            Map<String, List<FlightBusinessDataExcelParserVo>> data = resultBuilder.getData();
            successData = data.get("success");
            Map<String, String> variableMap = getVariableMap();
            if (!CollectionUtils.isEmpty(successData)) {

                // 保存服务记录信息
                List<ServiceRecord> serviceRecordList = new ArrayList<>();
                for (FlightBusinessDataExcelParserVo successDatum : successData) {
                    ServiceRecord serviceRecord = new ServiceRecord();
                    String flightId = successDatum.getId();
                    serviceRecord.setFlightId(flightId);
                    serviceRecord.setAirportCode(successDatum.getAirportCode());
                    serviceRecord.setFlightDate(DateUtils.parseDate(successDatum.getFlightDate()));
                    serviceRecord.setFlightNo(successDatum.getFlightNo());
                    serviceRecord.setFlightFlag(successDatum.getFlightFlag());
                    String serviceCode = variableMap.get(successDatum.getServiceName());
                    serviceRecord.setServiceCode(serviceCode);
                    serviceRecord.setServiceName(successDatum.getServiceName());
                    Date startTime = DateUtils.parseDateTime1(successDatum.getStartTime());
                    Date endTime = DateUtils.parseDateTime1(successDatum.getEndTime());
                    serviceRecord.setStartTime(startTime);
                    serviceRecord.setEndTime(endTime);
                    Double usedNumber = getUsedNumber(successDatum, startTime, endTime, serviceCode);
                    if(usedNumber==null){
                        continue;
                    }
                    serviceRecord.setUsedNumber(usedNumber);
                    serviceRecord.setCreateBy(user.getUsername());
                    serviceRecord.setCreateTime(new Date());
                    serviceRecord.setModifiedBy(user.getUsername());
                    serviceRecord.setModifiedTime(new Date());
                    serviceRecordList.add(serviceRecord);

                    // 删除该航班该保障项的老数据
                    serviceRecordDao.deleteServiceRecordByFlightIdAndServiceCode(flightId, serviceCode);
                }
                serviceRecordDao.flush();
                serviceRecordDao.saveAll(serviceRecordList);
                serviceRecordDao.flush();
            }

            // 导入失败文件处理
            error = data.get("error");
            if (CollectionUtils.isEmpty(error)) {
                uploadStatusService.uploadSuccess(uploadId, null, null);
            } else {
                handleExcelParserVo(file, error, uploadId);
            }

        } catch (Exception e) {
            log.error(MSG_ERROR, e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            uploadStatusService.uploadFail(uploadId, "上传失败");
            throw e;
        } finally {
            FLIGHT_BUSINESS_LOCK.unlock();
        }
        return new ResultBuilder.Builder<>().msg(CollectionUtils.isEmpty(error) ? "上传成功！" : "上传失败！").builder();
    }

    private Double getUsedNumber(FlightBusinessDataExcelParserVo successDatum, Date startTime, Date endTime, String serviceCode) {
        Double usedNumber=null;
        if(CharSequenceUtil.isNotBlank(successDatum.getNumber())){
            usedNumber=Double.parseDouble(successDatum.getNumber());
        } else if (startTime !=null && endTime !=null) {
            usedNumber=DateUtils.calculateLengthOfTime(serviceCode, startTime, endTime);
        } else if(CharSequenceUtil.isNotBlank(successDatum.getOther())){
            String seletorItem= successDatum.getOther();
            List<SafeguardTypeVo> safeguardTypeVoList = formulaService.getSafeguardType(successDatum.getAirportCode());
            for(SafeguardTypeVo safeguardTypeVo : safeguardTypeVoList){
                if(StringUtils.equals(safeguardTypeVo.getSelectorItemName(),seletorItem)){
                    usedNumber=Double.valueOf(safeguardTypeVo.getSortOrder());
                    break;
                }
            }

        }
        return usedNumber;
    }

    private void handleExcelParserVo(MultipartFile file, List<FlightBusinessDataExcelParserVo> error, String uploadId) {
        List<Object[]> errorData = new ArrayList<>();
        for (FlightBusinessDataExcelParserVo excelParserVo : error) {
            String[] obj = new String[9];
            obj[0] = "".equals(excelParserVo.getAirportCode()) ? " " : excelParserVo.getAirportCode();
            obj[1] = "".equals(excelParserVo.getFlightDate()) ? " " : excelParserVo.getFlightDate();
            obj[2] = "".equals(excelParserVo.getFlightNo()) ? " " : excelParserVo.getFlightNo();
            obj[3] = "".equals(excelParserVo.getFlightFlag()) ? " " : excelParserVo.getFlightFlag();
            obj[4] = "".equals(excelParserVo.getServiceName()) ? " " : excelParserVo.getServiceName();
            obj[5] = "".equals(excelParserVo.getStartTime()) ? " " : excelParserVo.getStartTime();
            obj[6] = "".equals(excelParserVo.getEndTime()) ? " " : excelParserVo.getEndTime();
            obj7(excelParserVo, obj);
            obj8(excelParserVo, obj);
            errorData.add(obj);
        }
        XSSFWorkbook errorSheets = ExcelFlightUtils.exportFlightBusinessError(errorData);
        uploadStatusService.uploadSuccess(uploadId, file.getOriginalFilename(), errorSheets);
    }

    private void obj8(FlightBusinessDataExcelParserVo excelParserVo, String[] obj) {
        obj[8] = "".equals(excelParserVo.getErrorReason()) ? " " : excelParserVo.getErrorReason();
    }

    private void obj7(FlightBusinessDataExcelParserVo excelParserVo, String[] obj) {
        obj[7] = "".equals(excelParserVo.getNumber()) ? " " : excelParserVo.getNumber();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportFlightBusinessDataTemplate(HttpServletResponse response) {
        List<RulesVariableRecord> rulesVariableRecords = variableRecordDao.listDeviceVariableRecordLikeCAndS();
        List<String> variableName =
                rulesVariableRecords.stream().map(RulesVariableRecord::getVariableName).collect(Collectors.toList());
        ExcelFlightUtils.exportFlightBusinessDataTemplate(response,variableName);
    }

    /**
     * Title: getSuccessList <br>
     * Description: 获取格式正确的数据 <br>
     *
     * @param list :
     * @return java.util.List<com.swcares.vo.FlightBusinessDataExcelParserVo>
     * <AUTHOR>  <br>
     * date 2022-10-21 17:12<br>
     */
    private List<FlightBusinessDataExcelParserVo> getSuccessList(
            List<FlightBusinessDataExcelParserVo> list) {
        return list.stream().filter(excelData -> CharSequenceUtil.isBlank(excelData.getErrorReason()))
                .collect(Collectors.toList());
    }

    /**
     * Title: getSuccessList <br>
     * Description: 获取格式错误的数据 <br>
     *
     * @param list :
     * @return java.util.List<com.swcares.vo.FlightBusinessDataExcelParserVo>
     * <AUTHOR>  <br>
     * date 2022-10-21 18:45<br>
     */
    private List<FlightBusinessDataExcelParserVo> getErrorList(List<FlightBusinessDataExcelParserVo> list) {
        return list.stream().filter(excelData -> CharSequenceUtil.isNotBlank(excelData.getErrorReason())).collect(Collectors.toList());
    }

    /**
     * Title: handleTimeList <br>
     * Description: 处理时间范围类型的保障数据 <br>
     *
     * @param timeFormatList :
     * <AUTHOR>  <br>
     * date 2022-10-21 15:29<br>
     */
    private void handleTimeList(List<FlightBusinessDataExcelParserVo> timeFormatList) {
        // 根据机场code，航班号，保障项进行分组
        Map<String, Map<String, Map<String, List<FlightBusinessDataExcelParserVo>>>> map =
                timeFormatList.stream()
                        .collect(Collectors.groupingBy(
                                FlightBusinessDataExcelParserVo::getAirportCode,
                                Collectors.groupingBy(FlightBusinessDataExcelParserVo::getFlightNo,
                                        Collectors.groupingBy(
                                                FlightBusinessDataExcelParserVo::getServiceName))));
        for (Map.Entry<String, Map<String, Map<String, List<FlightBusinessDataExcelParserVo>>>> airportEntry : map
                .entrySet()) {
            for (Map.Entry<String, Map<String, List<FlightBusinessDataExcelParserVo>>> flightEntry : airportEntry
                    .getValue().entrySet()) {
                for (Map.Entry<String, List<FlightBusinessDataExcelParserVo>> serviceEntry : flightEntry
                        .getValue().entrySet()) {
                    // 获取同一航班下的保障项
                    List<FlightBusinessDataExcelParserVo> serviceList = serviceEntry.getValue();
                    // 相同时间类型保障项数据不能超过4条
                    if (serviceList.size() > 4) {
                        serviceList.forEach(dataExcelParserVo -> dataExcelParserVo.setErrorReason(BusinessMessageEnum.DATA_NUMBER_NOT_MORE_THAN_FOUR.getMsg()));
                    }
                }
            }
        }
    }

    /**
     * Title: handleNumberList <br>
     * Description: 处理数值类型的保障数据 <br>
     *
     * @param numberFormatList :
     * <AUTHOR>  <br>
     * date 2022-10-21 15:28<br>
     */
    private void handleNumberList(List<FlightBusinessDataExcelParserVo> numberFormatList) {
        // 根据机场code，航班号，保障项进行分组
        Map<String, Map<String, Map<String, List<FlightBusinessDataExcelParserVo>>>> map =
                numberFormatList.stream()
                        .collect(Collectors.groupingBy(
                                FlightBusinessDataExcelParserVo::getAirportCode,
                                Collectors.groupingBy(FlightBusinessDataExcelParserVo::getFlightNo,
                                        Collectors.groupingBy(
                                                FlightBusinessDataExcelParserVo::getServiceName))));
        for (Map.Entry<String, Map<String, Map<String, List<FlightBusinessDataExcelParserVo>>>> airportEntry : map
                .entrySet()) {
            for (Map.Entry<String, Map<String, List<FlightBusinessDataExcelParserVo>>> flightEntry : airportEntry
                    .getValue().entrySet()) {
                for (Map.Entry<String, List<FlightBusinessDataExcelParserVo>> serviceEntry : flightEntry
                        .getValue().entrySet()) {
                    // 获取同一航班下的保障项
                    List<FlightBusinessDataExcelParserVo> serviceList = serviceEntry.getValue();
                    // 相同数值类型保障项数据不能超过1条
                    if (serviceList.size() > 1) {
                        serviceList.forEach(dataExcelParserVo -> dataExcelParserVo.setErrorReason(
                                BusinessMessageEnum.DATA_NUMBER_NOT_MORE_THAN_ONE
                                        .getMsg()));
                    }
                }
            }
        }
    }

    /**
     * Title: handleMutexList <br>
     * Description: 处理互斥的保障项数据 <br>
     *
     * @param mutexList :
     * <AUTHOR>  <br>
     * date 2022-10-21 16:15<br>
     */
    private void handleMutexList(List<FlightBusinessDataExcelParserVo> mutexList) {
        // 根据机场code，航班号进行分组
        Map<String, Map<String, List<FlightBusinessDataExcelParserVo>>> map = mutexList.stream()
                .collect(Collectors.groupingBy(FlightBusinessDataExcelParserVo::getAirportCode,
                        Collectors.groupingBy(FlightBusinessDataExcelParserVo::getFlightNo)));
        for (Map.Entry<String, Map<String, List<FlightBusinessDataExcelParserVo>>> airportEntry : map
                .entrySet()) {
            for (Map.Entry<String, List<FlightBusinessDataExcelParserVo>> flightEntry : airportEntry
                    .getValue().entrySet()) {
                // 获取保障项名称的集合
                List<String> flightList = flightEntry.getValue().stream()
                        .map(FlightBusinessDataExcelParserVo::getServiceName)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(flightList)) {
                    List<String> bridgeList = CommonConstants.bridgeList;
                    List<String> ladderCarList = CommonConstants.ladderCarList;
                    bridgeList.retainAll(flightList);
                    ladderCarList.retainAll(flightList);
                    // 处理互斥的保障项
                    if (!bridgeList.isEmpty() && !ladderCarList.isEmpty()) {
                        flightEntry.getValue()
                                .forEach(excelData -> excelData.setErrorReason(
                                        BusinessMessageEnum.DATA_EXCLUSIVE_NUMBER_ERROR.getMsg()));
                    }
                }
            }
        }
    }

    /**
     * Title: sycnServiceRecord<br>
     * Author: 刘志恒<br>
     * Description: 匹配从签单系统同步过来但无flightId的特车设备数据<br>
     * Date:  2022/7/14 16:39 <br>
     */
    private void syncServiceRecord(FlightInfo flight) {
        try {
            List<ServiceRecord> srList = serviceRecordDao.getServiceRecordByDateNoFlag(flight.getFlightDate(), flight.getFlightNo(), flight.getFlightFlag());
            if (!srList.isEmpty()) {
                srList.forEach(sr -> sr.setFlightId(flight.getId()));
                serviceRecordDao.saveAll(srList);
            }
        } catch (Exception e) {
            log.error("新建航班匹配签单同步特车设备错误=========");
            log.error(MSG_ERROR, e);
        }
    }


    private boolean isInStayTime(Date startTime, Date endTime, FlightInfo fi) {
        if (fi.getStayStartTime() != null && fi.getStayEndTime() != null) {
            return startTime.before(fi.getStayStartTime()) || endTime.after(fi.getStayEndTime());
        } else if ("A".equals(fi.getFlightFlag())) {
            // 降落航班，签单开始时间大于停场开始时间
            return fi.getStayStartTime() != null && startTime.before(fi.getStayStartTime());
        } else if ("D".equals(fi.getFlightFlag())) {
            // 起飞航班，签单结束时间小于停场结束时间
            return fi.getStayEndTime() != null && endTime.after(fi.getStayEndTime());
        }
        return false;
    }

    /**
     * Title: getFlightDataExcelParserVo<br>
     * Author: 刘志恒<br>
     * Description: 处理导出数据对象<br>
     * Date:  2023/9/6 13:53 <br>
     */
    private FlightDataExcelParserVo getFlightDataExcelParserVo(Object[] obj) {
        String flightLine = obj[7] == null ? "" : String.valueOf(obj[7]);
        FlightDataExcelParserVo vo = new FlightDataExcelParserVo();
        vo.setFlightDate((Date) obj[0]);
        vo.setAirlineCode(String.valueOf(obj[1]));
        vo.setAirportCode(String.valueOf(obj[2]));
        String flightNo = String.valueOf(obj[4]);
        if (flightNo.length() > 4) {
            flightNo = flightNo.substring(2);
        }
        vo.setFlightNo(flightNo);
        vo.setFlightModel(String.valueOf(obj[5]));
        vo.setFlightType(String.valueOf(obj[6]));
        vo.setFlightLine(flightLine);
        vo.setFlightLineAlias(String.valueOf(obj[8]));
        setFlightLineType(obj, vo);
        vo.setFlightSegment(String.valueOf(obj[10]));
        setFlightSegmentType(obj, vo);
        vo.setMaxPayload((Integer) obj[12]);
        vo.setMaxSeat((Integer) obj[13]);
        vo.setQuotaPayload(0);
        vo.setQuotaSeat(0);
        vo.setAvailablePayload((Integer) obj[14]);
        vo.setAvailableSeat((Integer) obj[15]);
        vo.setFlightFlag(String.valueOf(obj[16]));
        vo.setFlightFrequency(1);
        vo.setFlightTime((Date) obj[17]);
        // 进港航班的成人儿童婴儿需要加上过站，并将过站成人儿童婴儿设为0
        setAttribute(obj, vo);
        setCargo(obj, vo);
        setMail(obj, vo);
        setBag(obj, vo);
        vo.setBagNumber((Integer) obj[27]);
        vo.setRegNo(String.valueOf(obj[28]));
        vo.setDiplomaticPassportNumber((Integer) obj[29]);
        return vo;
    }

    private static void setBag(Object[] obj, FlightDataExcelParserVo vo) {
        if (null != obj[26]) {
            vo.setBag(((BigDecimal) obj[26]).doubleValue());
        } else {
            vo.setCargo(null);
        }
    }

    private static void setMail(Object[] obj, FlightDataExcelParserVo vo) {
        if (null != obj[25]) {
            vo.setMail(((BigDecimal) obj[25]).doubleValue());
        } else {
            vo.setCargo(null);
        }
    }

    private static void setCargo(Object[] obj, FlightDataExcelParserVo vo) {
        if (null != obj[24]) {
            vo.setCargo(((BigDecimal) obj[24]).doubleValue());
        } else {
            vo.setCargo(null);
        }
    }

    private static void setAttribute(Object[] obj, FlightDataExcelParserVo vo) {
        int adultNumber = obj[18] == null ? 0 : (Integer) obj[18];
        int childNumber = obj[19] == null ? 0 : (Integer) obj[19];
        int infantNumber = obj[20] == null ? 0 : (Integer) obj[20];
        int tAdultNumber = obj[21] == null ? 0 : (Integer) obj[21];
        int tChildNumber = obj[22] == null ? 0 : (Integer) obj[22];
        int tInfantNumber = obj[23] == null ? 0 : (Integer) obj[23];
        if ("A".equals(String.valueOf(obj[16]))) {
            vo.setAdultNumber(adultNumber + tAdultNumber);
            vo.setChildNumber(childNumber + tChildNumber);
            vo.setInfantNumber(infantNumber + tInfantNumber);
            vo.setTransitAdultNumber(0);
            vo.setTransitChildNumber(0);
            vo.setTransitInfantNumber(0);
        } else {
            vo.setAdultNumber(adultNumber);
            vo.setChildNumber(childNumber);
            vo.setInfantNumber(infantNumber);
            vo.setTransitAdultNumber(tAdultNumber);
            vo.setTransitChildNumber(tChildNumber);
            vo.setTransitInfantNumber(tInfantNumber);
        }
    }

    private static void setFlightSegmentType(Object[] obj, FlightDataExcelParserVo vo) {
        if ("D".equals(String.valueOf(obj[11]))) {
            vo.setFlightSegmentType("DOM");
        } else {
            vo.setFlightSegmentType(String.valueOf(obj[11]));
        }
    }

    private static void setFlightLineType(Object[] obj, FlightDataExcelParserVo vo) {
        if ("国内".equals(String.valueOf(obj[9]))) {
            vo.setFlightLineType("DOM");
        } else {
            vo.setFlightLineType(String.valueOf(obj[9]));
        }
    }

    private Map<String,String> getVariableMap(){
        List<RulesVariableRecord> rulesVariableRecords = variableRecordDao.listDeviceVariableRecordLikeCAndS();
        Map<String,String> variableMap=new HashMap<>();
        for(RulesVariableRecord variableRecord:rulesVariableRecords){
            variableMap.put(variableRecord.getVariableName(),variableRecord.getVariable());
        }
        return variableMap;
    }
}
