package com.swcares.aiot.service.impl;

import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.URLUtil;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.CsvUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.core.entity.TFlightBillHistorySnapshot;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.entity.ServiceRecord;
import com.swcares.aiot.core.model.vo.FlightBillCsvVo;
import com.swcares.aiot.core.service.ITFlightBillHistorySnapshotService;
import com.swcares.aiot.dto.FlightFeeBillDownloadDto;
import com.swcares.aiot.mapper.FlightFeeBillBizMapper;
import com.swcares.aiot.service.IFlightFeeBillBizService;
import com.swcares.aiot.service.LogService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.worm.hutool.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.NewReCalcServiceimpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/8 11:08
 * @version v1.0
 */
@Service
@Slf4j
public class IFlightFeeBillBizServiceImpl implements IFlightFeeBillBizService {


    private static final String POWERCAR = "POWERCAR";
    private static final String INFLATION = "INFLATION";
    @Resource
    private LogService logService;
    @Resource
    private FlightFeeBillBizMapper flightFeeBillBizMapper;
    @Resource
    private ITFlightBillHistorySnapshotService itFlightBillHistorySnapshotService;

    @Override
    public void download(FlightFeeBillDownloadDto dto, HttpServletResponse response) {
        IFlightFeeBillBizServiceImpl.log.info("开始导出航班明细账单，导出查询参数为：{}", dto);
        this.setFlightTimeDefaultRange(dto);
        //将开始日期的月份作为文件名的结算月份
        String settleMonth = FormatUtils.formatDateToMonth(dto.getStartDate() == null ? dto.getFlightTimeStartDate() : dto.getStartDate());
        //设置文件名
        String fileName = FormatUtils.formatDateToDay(new Date()) + "_APT_A_" + dto.getAirportCode() + "_TLJ_" + settleMonth;
        //设置 header
        FileUtils.setCsvResponseHeader(fileName, response);
        //获取数据
        List<FlightBill> flightBillList = this.getFlightBillInfoList(dto);
        //数据转换
        List<FlightBillCsvVo> flightBillCsvVoList = new ArrayList<>();
        //匹配过的特车设备id集合，防止重复
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");


        List<String> powercarFlightIdList = flightBillList.stream()
                .filter(fb -> (IFlightFeeBillBizServiceImpl.POWERCAR.equals(fb.getServiceRecord())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        Map<String, List<ServiceRecord>> powercarSrMap = this.getListMap(dto, powercarFlightIdList);

        List<String> inflationFlightIdList = flightBillList.stream()
                .filter(fb -> (IFlightFeeBillBizServiceImpl.INFLATION.equals(fb.getServiceRecord())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        Map<String, List<ServiceRecord>> inflationSrMap = this.getMap(dto, inflationFlightIdList);

        for (FlightBill flightBill : flightBillList) {
            FlightBillCsvVo data = new FlightBillCsvVo();

            Date flightTime = flightBill.getFlightTime();
            String flightFlag = flightBill.getFlightFlag();

            data.setOriginalId(null);
            data.setAccountAirportCode(flightBill.getAirportCode());
            data.setBusinessAirportCode(flightBill.getAirportCode());
            data.setPayAirlineCode(flightBill.getSettleCode());
            data.setBusinessAirlineCode(flightBill.getSettleCode());
            data.setFlightDate(FormatUtils.formatDateToDay(dto.getDateType() == 1 ? flightBill.getFlightDate() : flightBill.getFlightTime()));
            data.setFlightNo(flightBill.getFlightNo());
            data.setRegNo(flightBill.getRegNo());
            data.setFlightModel(flightBill.getFlightModel());
            data.setFlightLine(flightBill.getFlightLine());
            this.handleDom(flightBill, data);
            data.setDaAirportCode(flightBill.getAirportCode());
            String serviceRecord = flightBill.getServiceRecord();
            data.setStartTime(null);
            data.setEndTime(null);
            data.setPricingAmount(flightBill.getPricingAmount());
            data.setUnitPrice(flightBill.getUnitPrice());
            if (flightBill.getServiceStartTime() != null) {
                data.setStartTime(sdf.format(flightBill.getServiceStartTime()));
            }
            if (flightBill.getServiceEndTime() != null) {
                data.setEndTime(sdf.format(flightBill.getServiceEndTime()));
            }
            //处理客梯车单价和位数，并判断客梯车使用时间
            this.handleCbt(flightBill, serviceRecord, data);
            //设置气源车和电源车的计价量为业务保障数据的使用量
            this.handlePowInf(flightBill, powercarSrMap, data, inflationSrMap);

            //设置国航计价量为3位，其他为2位
            this.setPricingAmount(flightBill, FlightBill::getFlightNo, FlightBill::getFeeCode
                    , FlightBill::setPricingAmount, FlightBill::getPricingAmount);

            //设置金额为两位小数
            data.setUnitPrice(data.getUnitPrice().setScale(2, RoundingMode.HALF_UP));
            data.setChargePrice(flightBill.getChargePrice().setScale(2, RoundingMode.HALF_UP));

            data.setFlightTime(FormatUtils.formatDateTimeToMinute(flightTime));
            data.setFlightFlag(flightFlag);
            final String tf = "W/Z";
            data.setTaskFlag(tf);
            data.setFlightStatus(CommonConstants.FLIGHT_STATUS_MAP.get(tf));
            data.setFeeCode(flightBill.getFeeCode());
            data.setFeeName(flightBill.getFeeName());
            data.setOriginalBillNo(null);
            data.setRefuseAmount(null);
            data.setAdjustAmount(null);
            flightBillCsvVoList.add(data);
        }
        String[] head = this.getHeaders();

        //字段名
        List<String> fieldNameList = new ArrayList<>();
        Field[] fields = FlightBillCsvVo.class.getDeclaredFields();
        for (Field field : fields) {
            fieldNameList.add(field.getName());
        }
        String[] fieldArray = fieldNameList.toArray(new String[0]);
        try {
            ServletOutputStream csvResult = response.getOutputStream();
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + URLUtil.encode(fileName, StringUtil.UTF8) + ".csv");
            List<Object[]> objects = CsvUtils.getData(flightBillCsvVoList, fieldArray, FlightBillCsvVo.class);
            for (int i = 0; i < objects.size(); i++) {
                objects.get(i)[0] = i + 2;
            }
            CsvUtils.simpleExport(true, "\n", head, objects, fileName, csvResult);

        } catch (IOException e) {
            throw new GenericException(BusinessMessageEnum.IO_ERROR.getCode(), BusinessMessageEnum.IO_ERROR.getMsg());
        }
        String content = Constants.LogEnum.LOG_MEG_1.getValue()
                + dto.getUrlName() + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        this.logService.addLogForExport(user, dto, content, dto.getAirportCode(), "航班明细账单");
    }

    @Override
    public List<TFlightBillHistorySnapshot> getSnapshot(String billHistoryId) {
        return this.itFlightBillHistorySnapshotService.lambdaQuery().eq(TFlightBillHistorySnapshot::getBillHistoryId, billHistoryId).list();
    }

    private void setFlightTimeDefaultRange(FlightFeeBillDownloadDto dto) {
        //如果航班日期和起降日期都是空的则默认起降日期当前前一个月的
        if ((dto.getStartDate() == null || dto.getEndDate() == null) && (dto.getFlightTimeStartDate() == null || dto.getFlightTimeEndDate() == null)) {
            dto.setFlightTimeEndDate(new Date());
            Calendar calendar = CalendarUtil.calendar(DateUtil.beginOfDay(new Date()));
            calendar.add(Calendar.MONTH, -1);
            dto.setFlightTimeStartDate(calendar.getTime());
        }
    }

    /**
     * 航班明细账单动态条件查询
     *
     * @param dto :
     * @return :
     */
    private List<FlightBill> getFlightBillInfoList(FlightFeeBillDownloadDto dto) {
        String airportCode = dto.getAirportCode();
        String choosedFeeInfo = dto.getChoosedFeeInfo();
        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();
        String fromAirportCode = dto.getFromAirportCode();
        String toAirportCode = dto.getToAirportCode();
        String flightNo = dto.getFlightNo();
        String settleCode = dto.getSettleCode();
        List<String> settleCodeList = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(settleCode)) {
            settleCodeList.addAll(Arrays.asList(settleCode.split(",")));
        }
        List<String> choseFeeInfos = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(choosedFeeInfo)) {
            choseFeeInfos.addAll(Arrays.asList(choosedFeeInfo.split(",")));
        }
        this.setFlightTimeDefaultRange(dto);
        /*
         * 查询list
         */
        return this.flightFeeBillBizMapper.listFlightBillInfoByCondition(
                airportCode,
                startDate,
                endDate,
                dto.getFlightTimeStartDate(),
                dto.getFlightTimeEndDate(),
                fromAirportCode,
                toAirportCode,
                flightNo,
                choseFeeInfos,
                settleCodeList,
                dto.getRegNo(),
                dto.getFlightFlag(),
                dto.getSubmit()
        );
    }

    private Map<String, List<ServiceRecord>> getListMap(FlightFeeBillDownloadDto dto, List<String> powercarFlightIdList) {
        List<ServiceRecord> cetSrList =
                this.flightFeeBillBizMapper.listServiceRecordByFlightIdAndServiceCode(
                        powercarFlightIdList,
                        dto.getAirportCode(),
                        "CPUF"
                );
        return cetSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                Collectors.mapping(t -> t, Collectors.toList())));
    }

    private Map<String, List<ServiceRecord>> getMap(FlightFeeBillDownloadDto dto, List<String> inflationFlightIdList) {
        List<ServiceRecord> inflationSrList =
                this.flightFeeBillBizMapper.listServiceRecordByFlightIdAndServiceCode(
                        inflationFlightIdList,
                        dto.getAirportCode(),
                        "CSUF"
                );
        return inflationSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                Collectors.mapping(t -> t, Collectors.toList())));
    }

    private void handleDom(FlightBill flightBill, FlightBillCsvVo data) {
        if (flightBill.getFlightLineType() != null && "D".equals(flightBill.getFlightLineType())) {
            data.setFlightLineType("DOM");
        } else {
            data.setFlightLineType(flightBill.getFlightLineType());
        }
        data.setFlightSegment(flightBill.getFlightSegment());
        if (flightBill.getFlightSegmentType() != null && "D".equals(flightBill.getFlightSegmentType())) {
            data.setFlightSegmentType("DOM");
        } else {
            data.setFlightSegmentType(flightBill.getFlightSegmentType());
        }
    }

    private void handleCbt(FlightBill flightBill, String serviceRecord, FlightBillCsvVo data) {
        //设置客桥费的使用时间
        if (("CBT".equals(serviceRecord)) || ("客桥费".equals(flightBill.getFeeName()))) {

            BigDecimal pa = flightBill.getChargePrice().divide(BigDecimal.valueOf(200), RoundingMode.UP);
            //设置客桥费时，单价与计价量问题
            data.setPricingAmount(pa);
            data.setUnitPrice(BigDecimal.valueOf(200));
        }
    }

    private void handlePowInf(FlightBill flightBill, Map<String, List<ServiceRecord>> powercarSrMap, FlightBillCsvVo data, Map<String, List<ServiceRecord>> inflationSrMap) {
        if (IFlightFeeBillBizServiceImpl.POWERCAR.equals(flightBill.getFeeCode())) {
            List<ServiceRecord> srList = powercarSrMap.get(flightBill.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                data.setPricingAmount(BigDecimal.valueOf(srList.get(0).getUsedNumber()));
                data.setUnitPrice(flightBill.getChargePrice().divide(data.getPricingAmount(), 2, RoundingMode.HALF_UP));

            }
        }
        if (IFlightFeeBillBizServiceImpl.INFLATION.equals(flightBill.getFeeCode())) {
            List<ServiceRecord> srList = inflationSrMap.get(flightBill.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                data.setPricingAmount(BigDecimal.valueOf(srList.get(0).getUsedNumber()));
                data.setUnitPrice(flightBill.getChargePrice().divide(data.getPricingAmount(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    private <T> void setPricingAmount(T object, Function<T, String> flightNoFunction
            , Function<T, String> feeCodeFunction, BiConsumer<T, BigDecimal> setter,
                                      Function<T, BigDecimal> getPricingAmountFunction) {
        //设置国航计价量为3位，其他为2位
        if (flightNoFunction.apply(object).startsWith("CA") || "SC-D".equals(feeCodeFunction.apply(object))) {
            setter.accept(object, getPricingAmountFunction.apply(object).setScale(3, RoundingMode.HALF_UP));
        } else {
            setter.accept(object, getPricingAmountFunction.apply(object).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private String[] getHeaders() {
        // 表头
        return new String[]{
                //机场原始数据ID （空）
                Constants.FlightBillEnum.ORIGINAL_ID.getValue(),
                //开账机场代码
                Constants.FlightBillEnum.ACCOUNT_AIRPORT_CODE.getValue(),
                //实际运营机场代码
                Constants.FlightBillEnum.BUSINESS_AIRPORT_CODE.getValue(),
                //付款航空公司代码
                Constants.FlightBillEnum.PAY_AIRLINE_CODE.getValue(),
                //实际运营航空公司代码
                Constants.FlightBillEnum.BUSINESS_AIRLINE_CODE.getValue(),
                //航班日期
                Constants.FlightBillEnum.FLIGHT_DATE.getValue(),
                //航班号
                Constants.FlightBillEnum.FLIGHT_NO.getValue(),
                //飞机号
                Constants.FlightBillEnum.REG_NO.getValue(),
                //机型
                Constants.FlightBillEnum.FLIGHT_MODEL.getValue(),
                //航线
                Constants.FlightBillEnum.FLIGHT_LINE.getValue(),
                //航线性质
                Constants.FlightBillEnum.FLIGHT_LINE_TYPE.getValue(),
                //航段
                Constants.FlightBillEnum.FLIGHT_SEGMENT.getValue(),
                //航段性质
                Constants.FlightBillEnum.FLIGHT_SEGMENT_TYPE.getValue(),
                //发生机场
                Constants.FlightBillEnum.DA_AIRPORT_CODE.getValue(),
                //开始时间  (空)
                Constants.FlightBillEnum.START_TIME.getValue(),
                //结束时间 (空)
                Constants.FlightBillEnum.END_TIME.getValue(),
                //起降时间
                Constants.FlightBillEnum.FLIGHT_TIME.getValue(),
                //起降标志
                Constants.FlightBillEnum.FLIGHT_FLAG.getValue(),
                //任务性质
                Constants.FlightBillEnum.TASK_FLAG.getValue(),
                //航班状态
                Constants.FlightBillEnum.FLIGHT_STATUS.getValue(),
                //费用名称
                Constants.FlightBillEnum.FEE_CODE.getValue(),
                //计价量
                Constants.FlightBillEnum.PRICING_AMOUNT.getValue(),
                //单价
                Constants.FlightBillEnum.UNIT_PRICE.getValue(),
                //金额
                Constants.FlightBillEnum.CHARGE_PRICE.getValue(),
                //备注
                Constants.FlightBillEnum.FEE_NAME.getValue(),
                //原始账单号 （空）
                Constants.FlightBillEnum.ORIGINAL_BILL_NO.getValue(),
                //拒付金额 （空）
                Constants.FlightBillEnum.REFUSE_AMOUNT.getValue(),
                //调整金额 （空）
                Constants.FlightBillEnum.ADJUST_AMOUNT.getValue()
        };
    }
}
