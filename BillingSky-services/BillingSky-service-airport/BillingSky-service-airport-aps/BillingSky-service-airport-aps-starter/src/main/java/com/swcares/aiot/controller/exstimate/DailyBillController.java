package com.swcares.aiot.controller.exstimate;

import com.swcares.aiot.core.model.dto.ErAutoConfirmDto;
import com.swcares.aiot.core.model.dto.ErBillFulfillAndConfirmDto;
import com.swcares.aiot.core.model.dto.ErDailyBillPageDto;
import com.swcares.aiot.core.model.vo.ErBillFulfillAndConfirmVo;
import com.swcares.aiot.core.model.vo.ErDailyBillAccVo;
import com.swcares.aiot.core.model.vo.ErDailyBillPageVo;
import com.swcares.aiot.service.DailyBillService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.exstimate.DailyBillController
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *e
 * <AUTHOR>
 * date 2025/3/26 09:58
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/daily/bill")
@Api(value = "DailyBillController", tags = {"每日账单接口"})
@ApiVersion("对账通-机场端-离港返还-api")
@Slf4j
public class DailyBillController {

    @Resource
    private DailyBillService dailyBillService;

    @PostMapping("/airportPageErDailyBill")
    @ApiOperation(value = "机场端-分页查询[每日账单]")
    public PagedResult<List<ErDailyBillPageVo>> airportPageErDailyBill(@RequestBody @Validated ErDailyBillPageDto erDailyBillPageDto) {
        erDailyBillPageDto.setIsAirport(Boolean.TRUE);
        return dailyBillService.pageErDailyBill(erDailyBillPageDto);
    }

    @PostMapping("/downloadErDailyBill")
    @ApiOperation(value = "查询-下载[每日账单]")
    public void downloadErDailyBill(@RequestBody @Validated ErDailyBillPageDto erDailyBillPageDto, HttpServletResponse response) throws IOException {
        erDailyBillPageDto.setIsAirport(Boolean.TRUE);
        dailyBillService.downloadErDailyBill(erDailyBillPageDto, response);
    }

    @PostMapping("/airportAccErDailyBill")
    @ApiOperation(value = "机场端-统计[每日账单]")
    public BaseResult<ErDailyBillAccVo> airportAccErDailyBill(@RequestBody @Validated ErDailyBillPageDto erDailyBillPageDto) {
        erDailyBillPageDto.setIsAirport(Boolean.TRUE);
        return dailyBillService.accErDailyBill(erDailyBillPageDto);
    }

    @PutMapping("/confirmPay")
    @ApiOperation(value = "支付-[每日账单]确认支付")
    public BaseResult<ErBillFulfillAndConfirmVo> confirmPay(@RequestBody @Validated ErBillFulfillAndConfirmDto erBillFulfillAndConfirmDto) {
        return dailyBillService.confirmPay(erBillFulfillAndConfirmDto);
    }

    @PutMapping("/switchDailyAutoConfirm")
    @ApiOperation(value = "机场端-日账单自动确认开关")
    public BaseResult<Boolean> switchDailyAutoConfirm(@RequestBody ErAutoConfirmDto erAutoConfirmDto) {
        return dailyBillService.switchDailyAutoConfirm(erAutoConfirmDto);
    }

    @GetMapping("/dailyAutoConfirmQuery")
    @ApiOperation(value = "机场端-日账单自动确认开关查询")
    public BaseResult<Boolean> dailyAutoConfirmQuery(@RequestParam("airportCode") String airportCode) {
        return dailyBillService.dailyAutoConfirmQuery(airportCode);
    }

}
