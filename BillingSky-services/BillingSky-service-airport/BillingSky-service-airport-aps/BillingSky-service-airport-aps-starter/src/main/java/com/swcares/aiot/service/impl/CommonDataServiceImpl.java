package com.swcares.aiot.service.impl;

import cn.hutool.http.HttpStatus;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.service.CommonDataService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.api.bd.BaseDataCommonRemoteService;
import com.swcares.components.bd.vo.AirlineInfoVO;
import com.swcares.components.bd.vo.AirportInfoComboVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.service.impl.CommonDataServiceImpl
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/11/22 17:23
 * @version v1.0
 */
@Service
@Slf4j
public class CommonDataServiceImpl implements CommonDataService {

    @Resource
    private BaseDataCommonRemoteService baseDataCommonRemoteService;

    @Override
    public Map<String, Map<Object, String>> getBaseDataMap() {
        BaseResult<List<AirlineInfoVO>> airlineCombo = baseDataCommonRemoteService.getAirlineCombo();
        Map<Object, String> airlineMap = new HashMap<>();
        Map<Object, String> airportMap = new HashMap<>();
        Map<String, Map<Object, String>> dataVo = new HashMap<>();
        // 把航司二字码分装成map
        if (airlineCombo.getCode() == HttpStatus.HTTP_OK) {
            List<AirlineInfoVO> data = airlineCombo.getData();
            if (ObjectUtils.isNotEmpty(data)) {
                data.forEach(e -> {
                    if (ObjectUtils.isNotEmpty(e.getAirlineAbbr())) {
                        airlineMap.put(e.getAirline2code(), e.getAirlineAbbr());
                    } else if (ObjectUtils.isNotEmpty(e.getAirlineName())) {
                        airlineMap.put(e.getAirline2code(), e.getAirlineName());
                    } else {
                        airlineMap.put(e.getAirline2code(), e.getAirlineEn());
                    }
                });
            }
        } else {
            throw new GenericException(BusinessMessageEnum.USER_CENTER_REMOTE_ERROR.getCode(),
                    BusinessMessageEnum.USER_CENTER_REMOTE_ERROR.getMsg());
        }
        dataVo.put("airline2Code", airlineMap);
        BaseResult<List<AirportInfoComboVO>> combo = baseDataCommonRemoteService.getCombo("");
        if (combo.getCode() == HttpStatus.HTTP_OK) {
            List<AirportInfoComboVO> data = combo.getData();
            if (ObjectUtils.isNotEmpty(data)) {
                data.forEach(e -> airportMap.put(e.getCode(), e.getCityName()));
            }
        } else {
            throw new GenericException(BusinessMessageEnum.USER_CENTER_REMOTE_ERROR.getCode(),
                    BusinessMessageEnum.USER_CENTER_REMOTE_ERROR.getMsg());
        }
        dataVo.put("airport3Code", airportMap);
        return dataVo;
    }
}
