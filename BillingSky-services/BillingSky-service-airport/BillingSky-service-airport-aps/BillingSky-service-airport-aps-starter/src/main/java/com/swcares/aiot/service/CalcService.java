package com.swcares.aiot.service;

import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.vo.ExpensesRetrVo;
import com.swcares.aiot.core.model.vo.ForExpensesRetrVo;
import com.swcares.baseframe.common.security.LoginUserDetails;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.CalcService
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/25 13:19
 * @version v1.0
 */
public interface CalcService {

    /**
     * Description 计算机场端航班明细账单 <br>
     * author liuzhiheng <br>
     * Date 2025/8/25 14:49
     *
     * @param dto ;
     * @param user ;
     * @param tenantId ;
     */
    void execute(ReCalcDto dto, LoginUserDetails user, Long tenantId);

    /**
     * Description 查询-调用费用项接口获取费用项 <br>
     * author liuzhiheng <br>
     * Date 2025/8/25 14:49
     *
     * @param dto :
     * @return List<ForExpensesRetrVo>
     */
    List<ForExpensesRetrVo> retrForExpenses(ExpensesRetrVo dto);
}
