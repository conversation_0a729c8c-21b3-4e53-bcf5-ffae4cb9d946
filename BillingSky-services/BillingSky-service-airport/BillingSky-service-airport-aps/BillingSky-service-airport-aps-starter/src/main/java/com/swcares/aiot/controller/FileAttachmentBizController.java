package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.file.vo.AttachmentVO;
import com.swcares.aiot.service.IFileAttachmentBizService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Title ：FileAttachmentBizController <br>
 * Package ：com.swcares.aiot.controller <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2024年 09月03日 19:29 <br>
 * @version v1.0 <br>
 */
@ApiVersion("对账通-机场端api")
@RestController
@RequestMapping("/common/file")
@Api(tags = "控制器-附件上传（发票+处理记录）")
public class FileAttachmentBizController extends BaseController {

    @Resource
    private IFileAttachmentBizService fileAttachmentBizService;

    @PostMapping(value = "upload", headers = "content-type=multipart/form-data")
    @ApiOperation(value = "文件上传", notes = "返回文件信息")
    public BaseResult<Object> uploadFile(@RequestPart("file") MultipartFile file,
                                         @RequestParam() Integer businessType) {
        FileUtils.checkFile(file);
        return ok(fileAttachmentBizService.uploadFile(file, businessType));
    }


    @PostMapping(value = "/uploadAttachment", headers = "content-type=" + MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "文件上传", notes = "返回文件信息", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResult<AttachmentVO> uploadAttachment(@RequestPart("file") MultipartFile file, @RequestParam Integer businessType) {
        FileUtils.checkFile(file);
        AttachmentVO attachmentVo = fileAttachmentBizService.uploadAttachment(file, businessType);
        return BaseResult.ok(attachmentVo);
    }

    @GetMapping("/download/{fileKey}/{tableId}")
    @ApiOperation(value = "下载文件", notes = "返回文件")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response,
                             @ApiParam("需要下载的文件的key") @PathVariable("fileKey") String fileKey,
                             @ApiParam("主键id") @PathVariable(value = "tableId") Long tableId) throws IOException {
        fileAttachmentBizService.downloadFile(fileKey, tableId, request, response);
        response.flushBuffer();
    }

    @GetMapping("/download/{fileKey}")
    @ApiOperation(value = "下载或预览文件", notes = "返回文件")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response, @ApiParam("需要下载的文件的key") @PathVariable("fileKey") String fileKey,
                             @ApiParam("附件名称") @RequestParam(value = "originalName") String originalName) throws IOException {
        fileAttachmentBizService.downloadFile(fileKey, originalName, request, response);
        response.flushBuffer();
    }
}
