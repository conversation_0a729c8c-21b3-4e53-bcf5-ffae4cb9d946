package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.model.entity.AirlineBillNew;
import com.swcares.aiot.core.model.vo.AirlineBillCountVoNew;
import com.swcares.aiot.core.model.vo.AirlineBillMissVo;
import com.swcares.aiot.core.model.vo.AirlineBillPageVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.mapper.AirlineBillMapper
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/25 13:25
 * @version v1.0
 */
public interface AirlineBillMapper extends BaseMapper<AirlineBillNew> {

    List<AirlineBillMissVo> listMissAirlineBillByFlightDate(@Param("airportCode")String airportCode);

    List<AirlineBillMissVo> listMissAirlineBillByFlightTime(@Param("airportCode")String airportCode);

    List<AirlineBillMissVo> listMissAirlineBillByFlightInfoAndDate(@Param("airportCode")String airportCode,
                                                                   @Param("startDate")Date startDate,
                                                                   @Param("endDate") Date endDate);

    List<AirlineBillMissVo> listMissAirlineBillByFlightInfoAndTime(@Param("airportCode")String airportCode,
                                                                   @Param("startDate")Date startDate,
                                                                   @Param("endDate")Date endDate);

    IPage<AirlineBillPageVo> pageAirlineBillInfoByCondition(@Param("airportCode") String airportCode,
                                                            @Param("settleMoth") String settleMoth,
                                                            @Param("settleCodeList") List<String> settleCodeList,
                                                            @Param("dateType") Integer dateType,
                                                            Page<AirlineBillPageVo> page);

    List<AirlineBillPageVo> listAirlineBillInfoByCondition(@Param("airportCode") String airportCode,
                                                           @Param("settleMoth") String settleMoth,
                                                           @Param("settleCodeList") List<String> settleCodeList,
                                                           @Param("dateType") Integer dateType);


    List<AirlineBillCountVoNew> countTotalAmount(@Param("airportCode") String airportCode,
                                                 @Param("settleMoth")String settleMoth,
                                                 @Param("settleCodeList") List<String> settleCodeList,
                                                 @Param("dateType") Integer dateType);


    List<AirlineBillMissVo> newCalclistMissAirlineBillByDate(@Param("airportCode")String airportCode);

    List<AirlineBillMissVo> newCalclistMissAirlineBillByTime(@Param("airportCode")String airportCode);
}
