package com.swcares.aiot.service;

import cn.hutool.core.lang.Assert;
import com.swcares.aiot.core.entity.BillFileAttachment;
import com.swcares.aiot.core.enums.FileBusinessTypeEnum;
import com.swcares.aiot.file.vo.AttachmentVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title ：IFileAttachmentBizService <br>
 * Package ：com.swcares.aiot.service <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : 附件处理service <br>
 *
 * <AUTHOR> <br>
 * date 2024年 09月03日 20:03 <br>
 * @version v1.0 <br>
 */
public interface IFileAttachmentBizService {

    Object uploadFile(MultipartFile file, Integer businessType);

    void associatedBusinessInfo(List<BillFileAttachment> fileList, FileBusinessTypeEnum businessType, Long tableId);
    void assFileByFileKeyList(List<String> fileKeyList, FileBusinessTypeEnum businessType, Long tableId);


    List<BillFileAttachment> queryFileList(FileBusinessTypeEnum businessType, Long tableId);

    List<BillFileAttachment> queryFileList(FileBusinessTypeEnum businessType, List<Long> tableIdList);

    List<BillFileAttachment> queryFileList(List<String> fileKeyList);

    void downloadFile(String fileKey, Long tableId, HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException;

    void downloadFile(String fileKey, String originalName, HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException;

    AttachmentVO uploadAttachment(MultipartFile file, Integer businessType);


    /**
     * Title: 上传Excel<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/12 10:56 <br>
     *
     * @param wb         excel数据
     * @param fileName   excel文件名
     * @param fileBusinessTypeEnum 文件业务类型
     */
     Long uploadExcel(FileBusinessTypeEnum fileBusinessTypeEnum, String fileName, Workbook wb);

     /**
      * Title : uploadExcelOutputStream <br>
      * Description : 上传excel文件流（从原本minIOutil中迁移过来的） <br>
      * <AUTHOR>  <br>
      * @date 2025/5/28 14:26<br>
      * @param fileBusinessTypeEnum
      * @param errorFileName
      * @param outputStream
      * @return java.lang.Long
      */
    Long uploadExcelOutputStream(FileBusinessTypeEnum fileBusinessTypeEnum, String errorFileName, ByteArrayOutputStream outputStream);


    <T> Long uploadExcel(FileBusinessTypeEnum fileBusinessTypeEnum,List<T> data, String fileName, Class<T> clazz);
}
