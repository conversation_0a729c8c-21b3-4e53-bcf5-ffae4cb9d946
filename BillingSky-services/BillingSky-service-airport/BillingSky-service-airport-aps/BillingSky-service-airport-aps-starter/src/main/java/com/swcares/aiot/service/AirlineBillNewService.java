package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.AirlineBillForm;
import com.swcares.aiot.core.model.dto.AirlineBillExportDto;
import com.swcares.aiot.core.model.dto.AirlineBillPageDto;
import com.swcares.aiot.core.model.dto.AirlineBillTaxRateDto;
import com.swcares.aiot.core.model.vo.AirlineBillCountVoNew;
import com.swcares.aiot.core.model.vo.AirlineBillPageVo;
import com.swcares.baseframe.common.security.LoginUserDetails;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * ClassName：com.swcares.aiot.service.AirlineBillNewService
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/25 15:04
 * @version v1.0
 */
public interface AirlineBillNewService {

    /**
     * Title: checkAndNewAirlineBill<br>
     * Author: liuzhiheng<br>
     * Description: 检查缺失的航司账单并进行生成<br>
     * Date:  14:17 <br>
     *
     * @param airportCode :
     */
    void checkAndNewAirlineBill(String airportCode);

    void checkAndNewAirlineBill(Date startDate,Date endDate,String airportCode,Long tenantId);

    IPage<AirlineBillPageVo> pageAirlineBillInfoByCondition(AirlineBillPageDto dto);

    ResultBuilder<Object> updateAirlineBillTaxRate(AirlineBillTaxRateDto airlineBillTaxRateDto, LoginUserDetails user);

    /**
     * Title: updateAirlineBillInfo<br>
     * Author: 叶咏秋<br>
     * Description: (航司账单调整金额和拒付金额数据更新)<br>
     * Date:  13:49 <br>
     *
     * @param airlineBillUpdateForm :
     * @param user                  :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> updateAirlineBillInfo(AirlineBillForm airlineBillUpdateForm, LoginUserDetails user, String urlName);

    /**
     * Title: countTotal<br>
     * Author: 叶咏秋<br>
     * Description: (航司账单金额总计)<br>
     * Date:  13:57 <br>
     *
     * @param dto return: ResultBuilder
     */
    AirlineBillCountVoNew countTotal(AirlineBillPageDto dto);

    /**
     * Title: exportAirlineBillInfo<br>
     * Author: 叶咏秋<br>
     * Description: (导出航司账单)<br>
     * Date:  14:17 <br>
     *
     * @param airlineBillExportDto :
     * @param response              return: void
     */
    void exportAirlineBillInfo(AirlineBillExportDto airlineBillExportDto, HttpServletResponse response, LoginUserDetails user, String urlName);
}
