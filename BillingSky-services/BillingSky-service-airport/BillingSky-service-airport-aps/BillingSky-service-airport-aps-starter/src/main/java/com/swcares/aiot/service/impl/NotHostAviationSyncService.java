package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aiot.ComplexMessage;
import com.swcares.aiot.core.entity.NotHostAviation;
import com.swcares.aiot.core.service.INotHostAviationService;
import com.swcares.aiot.synergy.annotation.DataType;
import com.swcares.aiot.synergy.service.IDataSyncService;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：NotHostAviationSyncService
 * Description：离岗返还非HOST航数据接收实现类
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/9/1 17:03
 * Version v1.0
 */
@DataType("NOT_HOST_AVIATION")
@Slf4j
@Service
public class NotHostAviationSyncService implements IDataSyncService {

    @Resource
    private INotHostAviationService iNotHostAviationService;

    @Override
    public Long getTenantId(String tenantCode) {
        return ConfigUtil.getLong("signature_config", tenantCode.toUpperCase(), GlobalConstants.TENANT_DEFAULT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncData(ComplexMessage message) {
        List<NotHostAviation> bills = JSONObject.parseArray(JSONUtil.toJsonStr(message.getPayload().getData()), NotHostAviation.class);
        log.info("收到离岗返还非HOST航信息：{}", bills);
        if (CollectionUtil.isEmpty(bills)) {
            return;
        }
        iNotHostAviationService.saveOrUpdateBatch(bills);
    }

}
