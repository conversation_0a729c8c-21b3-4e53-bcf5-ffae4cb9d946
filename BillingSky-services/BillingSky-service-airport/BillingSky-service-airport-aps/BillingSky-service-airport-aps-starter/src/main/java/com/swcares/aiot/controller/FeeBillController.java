package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.model.dto.FeeBillPagedDTO;
import com.swcares.aiot.core.model.vo.FeeBillCountVO;
import com.swcares.aiot.core.model.vo.FeeBillVO;
import com.swcares.aiot.service.FeeBillService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.RestBaseController;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ClassName：FeeBillController <br>
 * Description：(费用账单接口)<br>
 * Copyright © 2020/5/18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/5/18 13:46<br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@RequestMapping("/api/feeBill")
@Api(value = "FeeBillController", tags = {"费用账单接口"})
public class FeeBillController extends RestBaseController {

    @Resource
    private FeeBillService feeBillService;

    @PostMapping("/pageFeeBillInfoByCondition")
    @ApiOperation(value = "费用账单动态条件分页查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FeeBillVO.class)})
    public PagedResult<List<FeeBillVO>> pageFeeBillInfoByCondition(@Validated @RequestBody FeeBillPagedDTO dto) {
        return ok(feeBillService.pageFeeBillInfoByCondition(dto));
    }

    @PostMapping("/countTotal")
    @ApiOperation(value = "费用账单总计")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功！", response = FeeBillCountVO.class)
    })
    public BaseResult<FeeBillCountVO> countTotal(@Validated @RequestBody FeeBillPagedDTO feeBillForm) {
        return ok(feeBillService.countTotal(feeBillForm));
    }

    @PostMapping("/exportFeeBillInfo")
    @ApiOperation(value = "导出费用账单")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public void exportFeeBillBySettleCode(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                          @Validated @RequestBody FeeBillPagedDTO dto, HttpServletResponse response) throws IOException {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        feeBillService.exportFeeBillInfo(dto, response, user, urlName);
    }

}