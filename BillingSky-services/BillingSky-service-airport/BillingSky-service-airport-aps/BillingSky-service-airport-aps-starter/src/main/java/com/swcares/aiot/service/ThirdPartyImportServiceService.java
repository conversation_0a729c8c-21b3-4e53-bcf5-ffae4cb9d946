package com.swcares.aiot.service;

import com.swcares.aiot.core.model.dto.ThirdPartyImportFlightDto;
import com.swcares.aiot.core.model.dto.ThirdPartyRelaDto;
import com.swcares.aiot.core.model.dto.ThirdPartyServiceDeleteDto;
import com.swcares.aiot.core.model.vo.ThirdPartyServiceVo;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.ThirdPartyImportServiceService
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/23 09:50
 * @version v1.0
 */
public interface ThirdPartyImportServiceService {

    List<ThirdPartyServiceVo> getAllServiceInfo(String airportCode);

    Boolean saveRela(ThirdPartyRelaDto thirdPartyRelaDto);

    Boolean deleteRela(ThirdPartyServiceDeleteDto thirdPartyServiceDeleteDto);

    Boolean importServiceRecord(ThirdPartyImportFlightDto thirdPartyImportFlightDto);
}
