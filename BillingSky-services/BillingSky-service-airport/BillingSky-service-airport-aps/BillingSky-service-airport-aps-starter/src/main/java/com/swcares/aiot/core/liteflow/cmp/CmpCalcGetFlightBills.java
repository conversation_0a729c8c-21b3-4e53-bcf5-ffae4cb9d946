package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TAircraftInfo;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.enums.IsServiceFeeEnum;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.mapstruct.MsFlightBill;
import com.swcares.aiot.core.mapstruct.MsReCalcProcessBiz;
import com.swcares.aiot.core.model.vo.FlightBillHistorySnapShotVo;
import com.swcares.aiot.core.model.dto.ReCalcErrorDto;
import com.swcares.aiot.model.dto.ActCustomerCaluDto;
import com.swcares.aiot.model.dto.ActExpensesResultCaluDto;
import com.swcares.aiot.model.dto.ActFlightIndexCaluDto;
import com.swcares.aiot.model.dto.ActIndexCaluDto;
import com.swcares.aiot.model.vo.ActExpensesCaluVo;
import com.swcares.aiot.model.vo.ActExpensesResultCaluVo;
import com.swcares.aiot.model.vo.ValidActFormulaExpressCaluVo;
import com.swcares.aiot.service.IActuarialBizService;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.CmpCalcGetFlightBills
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/13 11:01
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcGetFlightBills", name = "组件-对账通机场端-计算模块-生成账单")
public class CmpCalcGetFlightBills extends NodeComponent {
    @Resource
    private IActuarialBizService iActuarialBizService;
    @Resource
    private MsFlightBill msFlightBill;
    @Resource
    private MsReCalcProcessBiz msReCalcProcessBiz;

    @Override
    public void process() {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        //计算账单发送参数
        ActExpensesResultCaluDto actExpensesResultCaluDto = ctxCalc.getActExpensesResultCaluDto();
        //账单对应快照map
        Map<TFlightBill, FlightBillHistorySnapShotVo> snapShotMap = new HashMap<>();
        //计算返回结果
        List<ActExpensesResultCaluVo> calculateRes=Collections.synchronizedList(new ArrayList<>() );
        //按天组装
        List<ActFlightIndexCaluDto> actFlightIndexCaluDtos = actExpensesResultCaluDto.getActFlightIndexCaluDtos();
        Map<String, List<ActFlightIndexCaluDto>> flightDateMap = actFlightIndexCaluDtos.stream().collect(Collectors.groupingBy(ActFlightIndexCaluDto::getFlightDate));
        Long tenant = TenantHolder.getTenant();
        flightDateMap.values().stream().parallel().forEach(flightIndexCaluDtoList->{
            TenantHolder.setTenant(tenant);
            ActExpensesResultCaluDto newActExpensesResultCaluDto = new ActExpensesResultCaluDto();
            newActExpensesResultCaluDto.setCustomerType(actExpensesResultCaluDto.getCustomerType());
            newActExpensesResultCaluDto.setExpensesCode(actExpensesResultCaluDto.getExpensesCode());
            newActExpensesResultCaluDto.setCustomerIataA(actExpensesResultCaluDto.getCustomerIataA());
            newActExpensesResultCaluDto.setActFlightIndexCaluDtos(flightIndexCaluDtoList);
            List<ActExpensesResultCaluVo> tempCalculateRes = iActuarialBizService.caluActExpenses(newActExpensesResultCaluDto);
            if(CollUtil.isNotEmpty(tempCalculateRes)){
                calculateRes.addAll(tempCalculateRes);
            }
            log.info("{}日期航班已计算完",flightIndexCaluDtoList.get(0).getFlightDate());
            TenantHolder.clear();
        });
        TenantHolder.setTenant(tenant);
        List<TFlightBill> flightBillList;
        if (CollUtil.isNotEmpty(calculateRes)) {
            log.info("计算引擎返回的结果数据:{}", calculateRes);
            // 根据计算引擎结果组装账单
            flightBillList = this.getFlightBills(calculateRes, ctxCalc,snapShotMap);
        } else {
            log.error("计算中心返回结果为空");
            throw new BusinessException(ExceptionCodes.CALC_RES_ERROR);
        }
        ctxCalc.setFlightBillList(flightBillList);
        ctxCalc.setSnapShotMap(snapShotMap);
    }

    /**
     * Title: getFlightBills
     * 将计算引擎计算结果组装成flightBill
     */
    private List<TFlightBill> getFlightBills(List<ActExpensesResultCaluVo> actExpensesResultCaluVos, CtxCalc ctxCalc,Map<TFlightBill, FlightBillHistorySnapShotVo> snapShotMap) {
        // 新计算出的账单结果list
        List<TFlightBill> flightBillList = new ArrayList<>();
        for (ActExpensesResultCaluVo actExpensesResultCaluVo : actExpensesResultCaluVos) {
            for (ActCustomerCaluDto actCustomerCaluDto : actExpensesResultCaluVo.getActCustomerCaluDtos()) {
                for (ActExpensesCaluVo actExpensesCaluVo : actCustomerCaluDto.getActExpensesCaluVos()) {
                    getFlightBillAndErrorList(actExpensesCaluVo, actExpensesResultCaluVo, flightBillList,ctxCalc,snapShotMap);
                }
            }
        }
        return flightBillList;
    }

    private void getFlightBillAndErrorList(ActExpensesCaluVo actExpensesCaluVo, ActExpensesResultCaluVo actExpensesResultCaluVo, List<TFlightBill> airlineFlightBills
            ,CtxCalc ctxCalc,Map<TFlightBill, FlightBillHistorySnapShotVo> snapShotMap) {
        Map<String, TAircraftInfo> flightAircraftInfoMap = ctxCalc.getFlightAircraftInfoMap();
        Map<String,ReCalcErrorDto> errorDtoMap = ctxCalc.getErrorDtoMap();
        Map<String, FlightInfoMb> flightInfoCalculateVoMap=ctxCalc.getFlightInfoMap();
        Set<String> noErrorMsgSet = ctxCalc.getNoErrorMsgSet();
        if (actExpensesCaluVo.getExpensesAmount() != null && actExpensesCaluVo.getExpensesAmount().compareTo(BigDecimal.ZERO)!=0) {
            String flightId = actExpensesResultCaluVo.getFlightId();
            FlightInfoMb flightInfoMb = flightInfoCalculateVoMap.get(flightId);
            TAircraftInfo tAircraftInfo = flightAircraftInfoMap.get(flightId);

            TFlightBill flightBill=msFlightBill.infoToFlightBill(flightInfoMb);
            flightBill.setFeeName(actExpensesCaluVo.getExpensesName());
            flightBill.setFeeCode(actExpensesCaluVo.getExpensesCode());
            // 收费金额
            flightBill.setChargePrice(actExpensesCaluVo.getExpensesAmount().setScale(2, RoundingMode.UP));
            // 航司简称
            flightBill.setAirlineShortName(tAircraftInfo.getAirlineShortName());
            flightBill.setSettleCode(tAircraftInfo.getSettleCode());
            flightBill.setFlightModel(tAircraftInfo.getAirplaneModel());
            flightBill.setSettleMonth(flightInfoMb.getFlightDate());
            flightBill.setFlightId(flightInfoMb.getId());
            //设置是否为业务保障费用
            flightBill.setIsServiceFee(isServiceFee(actExpensesCaluVo));
            ValidActFormulaExpressCaluVo validActFormulaExpressCaluVo = actExpensesCaluVo.getValidActFormulaExpressCaluVo();
            if (validActFormulaExpressCaluVo != null) {
                // 计价量
                flightBill.setPricingAmount(validActFormulaExpressCaluVo.getValuationQuantityResult());
                // 单价
                flightBill.setUnitPrice(validActFormulaExpressCaluVo.getValuationUnitPriceResult());
            }
            airlineFlightBills.add(flightBill);
            //存储快照需要的航班、飞机、公式信息
            FlightBillHistorySnapShotVo flightBillHistortSnapShotVo = new FlightBillHistorySnapShotVo();
            flightBillHistortSnapShotVo.setActExpensesCaluVo(actExpensesCaluVo);
            flightBillHistortSnapShotVo.setFlightInfoMb(msReCalcProcessBiz.getFlightInfoSnapShotVo(flightInfoMb));
            flightBillHistortSnapShotVo.setAircraftInfo(msReCalcProcessBiz.getAircraftSnapShotVo(tAircraftInfo));
            snapShotMap.put(flightBill,flightBillHistortSnapShotVo);
        } else if (CharSequenceUtil.isNotBlank(actExpensesCaluVo.getErrMsg())
                &&!noErrorMsgSet.contains(actExpensesResultCaluVo.getFlightId() + "_" + actExpensesCaluVo.getExpensesCode())) {
            String flightId = actExpensesResultCaluVo.getFlightId();
            FlightInfoMb flightInfoMb = flightInfoCalculateVoMap.get(flightId);
            ReCalcErrorDto errorObj = new ReCalcErrorDto();
            errorObj.setFlightNo(actExpensesResultCaluVo.getFlightNo());
            errorObj.setFlightFlag(flightInfoMb.getFlightFlag());
            errorObj.setFlightDate(actExpensesResultCaluVo.getFlightDate());
            errorObj.setErrorFee(actExpensesCaluVo.getExpensesName());
            errorObj.setErrorMsg(actExpensesCaluVo.getErrMsg());
            errorDtoMap.put(actExpensesResultCaluVo.getFlightId() + "_" + actExpensesCaluVo.getExpensesCode(),errorObj);
        }
    }


    private Integer isServiceFee(ActExpensesCaluVo actExpensesCaluVo){
        if(actExpensesCaluVo!=null){
            List<ActIndexCaluDto> actIndexCaluDtoList = actExpensesCaluVo.getIndexCaluDtos();
            if(actIndexCaluDtoList!=null) {
                for (ActIndexCaluDto actIndexCaluDto : actIndexCaluDtoList) {
                    if (Integer.valueOf(1).equals(actIndexCaluDto.getSort())) {
                        return IsServiceFeeEnum.IS_SERVICE_FEE.getValue();
                    }
                }
            }
        }
        return IsServiceFeeEnum.NOT_SERVICE_FEE.getValue();
    }
}
