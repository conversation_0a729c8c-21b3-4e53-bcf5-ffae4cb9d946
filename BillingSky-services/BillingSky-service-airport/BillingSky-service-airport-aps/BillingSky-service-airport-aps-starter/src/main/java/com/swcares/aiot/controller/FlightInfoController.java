package com.swcares.aiot.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.controller.BaseController;
import com.swcares.aiot.core.common.entity.JsonVO;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.ExcelUtils;
import com.swcares.aiot.core.common.util.valid.group.Insert;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.*;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.core.model.entity.FlightPlanInfo;
import com.swcares.aiot.core.model.entity.ServiceRecord;
import com.swcares.aiot.core.model.vo.FlightInfoCountVo;
import com.swcares.aiot.core.model.vo.FlightInfoDataVO;
import com.swcares.aiot.core.model.vo.FlightInfoVo;
import com.swcares.aiot.core.model.vo.ServiceRecordVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.FlightInfoService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ClassName：FlightInfoController <br>
 * Description：(航班数据采集操作接口)<br>
 * Copyright © 2020/6/18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/18 9:04<br>
 * @version v1.0 <br>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/flightInfo")
@Api(value = "FlightInfoController", tags = {"航班数据采集操作接口"})
public class FlightInfoController extends BaseController<Object> {

    @Resource
    private FlightInfoService flightInfoService;

    @GetMapping("/pageFlightInfoByCondition")
    @ApiOperation(value = "航班视图-航班信息动态条件分页查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightInfoVo.class)})
    public Object pageFlightInfoByCondition(@Validated @ApiParam(name = "pageParam", value = "分页/条件") PageParam pageParam,
                                            @Validated @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件")
                                            FlightInfoSearchForm flightInfoSearchForm) {
        return flightInfoService.pageFlightInfoByCondition(pageParam, flightInfoSearchForm);
    }

    @ApiOperation(value = "业务明细视图-根据服务类型查询航班的特车/设备使用情况")
    @GetMapping("/pageFlightInfoByServiceCode")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightInfoVo.class)})
    public Object pageFlightInfoByServiceCode(@Validated @ApiParam(name = "pageParam", value = "分页/条件") PageParam pageParam,
                                              @Validated @ApiParam(name = "serviceCode", value = "服务code", required = true) String serviceCode,
                                              @Validated @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件")
                                              FlightInfoSearchForm flightInfoSearchForm) {
        return flightInfoService.getByServiceCode(pageParam, serviceCode, flightInfoSearchForm);
    }

    @ApiOperation(value = "获取确认后修改航班条数")
    @GetMapping("/getChangeNum")
    public ResultBuilder<Integer> getChangeNum(@Validated @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件")
                                               FlightInfoSearchForm flightInfoSearchForm) {
        return flightInfoService.getChangeNum(flightInfoSearchForm);
    }

    @GetMapping("/countTotal")
    @ApiOperation(value = "航班信息数据总计")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightInfoCountVo.class)})
    public Object countTotal(@Validated @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件")
                             FlightInfoSearchForm flightInfoSearchForm) {
        return flightInfoService.countTotal(flightInfoSearchForm);
    }

    @GetMapping("/countModifiedLostTotal")
    @ApiOperation(value = "航班修改和缺失数据总计")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightInfoDataVO.class)})
    public Object countModifiedLostTotal(@Validated @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件")
                                         FlightInfoSearchForm flightInfoSearchForm) {
        return flightInfoService.countModifiedLostTotal(flightInfoSearchForm);
    }

    @PostMapping("/saveFlightInfo")
    @ApiOperation(value = "新增航班信息")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightInfo.class)})
    public Object saveFlightInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                 @RequestBody @Validated({Insert.class}) @ApiParam(name = "flightInfoForm", value = "航班信息表单")
                                 FlightInfoForm flightInfoForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.saveFlightInfo(flightInfoForm, user, urlName);
    }

    @GetMapping("/getFlightInfoById")
    @ApiOperation(value = "通过航班id查看航班信息")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightInfo.class)})
    public Object getFlightInfoById(@RequestParam @ApiParam(name = "id", value = "航班id") @NotBlank(message = "id不能为空") String id) {
        return flightInfoService.getFlightInfoById(id);
    }

    @PutMapping("/updateFlightInfoById")
    @ApiOperation(value = "通过航班id修改航班信息")
    public Object updateFlightInfoById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                       @RequestBody @Validated({Update.class}) @ApiParam(name = "flightInfoForm", value = "航班信息表单")
                                       FlightInfoForm flightInfoForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.updateFlightInfoById(flightInfoForm, user, urlName);
    }

    @DeleteMapping("/delFlightInfoBatchById")
    @ApiOperation(value = "批量删除航班信息")
    public Object delFlightInfoBatchById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                         @RequestParam @ApiParam(name = "ids", value = "批量删除的id,多个则以逗号分隔", required =
                                                 true) @NotBlank(message = "ids不能为空") String ids) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.delFlightInfoBatchById(ids, user, urlName);
    }

    @GetMapping("/listServiceRecordByCondition")
    @ApiOperation(value = "查看某个航班的某个特车/设备的所有数据")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ServiceRecord.class)})
    public Object listServiceRecordByCondition(@ApiParam(name = "flightId", value = "航班ID", required = true)
                                               @NotBlank(message = "flightId不能为空") @RequestParam String flightId,
                                               @ApiParam(name = "airportCode", value = "机场三字码", required = true)
                                               @NotBlank(message = "airportCode不能为空") @RequestParam String airportCode,
                                               @ApiParam(name = "indexDataFlag", value = "指标数据标识", required = true)
                                               @NotBlank(message = "indexDataFlag不能为空") @RequestParam String indexDataFlag) {
        return flightInfoService.listServiceRecordByCondition(flightId, airportCode, indexDataFlag);
    }

    @ApiOperation(value = "业务明细视图-查询航班使用的所有特车/设备的定义信息")
    @GetMapping("/listDeviceVariableRecord")
    public Object listDeviceVariableRecord() {
        return flightInfoService.listDeviceVariableRecord();
    }

    @ApiOperation(value = "航班视图-查询某个航班的特车/设备数据")
    @GetMapping("/listDeviceUsedDataByFlightId")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ServiceRecord.class)})
    public ResultBuilder<Map<String, List<ServiceRecordVo>>> listDeviceUsedDataByFlightId(@ApiParam(name = "flightId", value = "航班id", required = true) @RequestParam @NotBlank(message = "flightId不能为空") String flightId) {
        return flightInfoService.listDeviceUsedDataByFlightId(flightId);
    }

    @PostMapping("/saveDeviceUsedData")
    @ApiOperation(value = "上传某个航班的特车/设备数据")
    public Object saveDeviceUsedData(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                     @RequestBody @Validated @ApiParam(name = "serviceRecordForm",
                                             value = "航班相关设备使用数据导入表单", required = true) ServiceRecordForm serviceRecordForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.saveDeviceUsedData(serviceRecordForm, user, urlName);
    }

    @GetMapping("/bridgeTemplate")
    @ApiOperation(value = "廊桥数据模板下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "航班id,多个以逗号分割", dataType = "String", paramType = "query"),
    })
    public void bridgeTemplate(String ids, HttpServletResponse response) {
        flightInfoService.bridgeTemplate(ids, response);
    }


    @GetMapping("/flightTemplate")
    @ApiOperation(value = "航班采集模板下载")
    public void flightTemplate(HttpServletResponse response) {
        flightInfoService.flightTemplate(response);
    }

    @PostMapping("/importFlightDataBatch")
    @ApiOperation(value = "航班数据批量导入")
    public JsonVO<Object> importFlightDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                @RequestBody @ApiParam(name = "file", value = "导入文件") MultipartFile file,
                                                @RequestParam("airportCode") String airportCodeParam) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return super.success(JSON.toJSONString(flightInfoService.importFlightDataBatch(file, airportCodeParam, urlName, user)));
    }


    @PostMapping("/importFlightRepeatDataBatch")
    @ApiOperation(value = "航班重复数据批量导入")
    public JsonVO<Object> importFlightRepeatDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                      @RequestBody @ApiParam(name = "file", value = "导入文件") MultipartFile file,
                                                      @RequestParam("airportCode") String airportCodeParam) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return success(JSON.toJSONString(flightInfoService.importFlightRepeatDataBatch(file, airportCodeParam, urlName, user)));
    }

    @PostMapping("/importBridgeDataBatch")
    @ApiOperation(value = "客桥使用数据批量导入")
    public JsonVO<Object> importBridgeDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                @RequestBody @ApiParam(name = "file", value = "导入文件") MultipartFile file,
                                                @RequestParam("airportCode") String airportCodeParam) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return super.success(JSON.toJSONString(flightInfoService.importBridgeDataBatch(file, airportCodeParam, urlName, user)));
    }

    @PostMapping("/importBridgeRepeatDataBatch")
    @ApiOperation(value = "客桥使用重复数据批量导入")
    public JsonVO<Object> importBridgeRepeatDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                      @RequestBody @ApiParam(name = "file", value = "导入文件") MultipartFile file,
                                                      @RequestParam("airportCode") String airportCodeParam) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return super.success(JSON.toJSONString(flightInfoService.importBridgeRepeatDataBatch(file, airportCodeParam, urlName, user)));
    }

    @PostMapping("/exportBridgeDataBatch")
    @ApiOperation(value = "廊桥数据批量导出")
    public void exportBridgeDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                      HttpServletResponse res, @RequestBody ExportForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightInfoService.exportBridgeDataBatch(form, res, urlName, user);
    }

    @PostMapping("/exportFlightDataBatch")
    @ApiOperation(value = "航班使用数据批量导出")
    public void exportFlightDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                      HttpServletResponse res, @RequestBody ExportForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightInfoService.exportFlightDataBatch(form, res, urlName, user);
    }

    @PostMapping("/importFlightLineDataBatch")
    @ApiOperation(value = "航线数据批量导入")
    public JsonVO<Object> importFlightLineDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                    @RequestParam("file") MultipartFile file,
                                                    @RequestParam("airportCode") String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        log.info("上传开始：{}", new Date());
        flightInfoService.importFlightLineDataBatch(file, airportCode, urlName, user);
        log.info("上传结束：{}", new Date());
        return super.success();
    }

    @PostMapping("/importAircraftDataBatch")
    @ApiOperation(value = "机号数据批量导入")
    public JsonVO<Object> importAircraftDataBatch(@RequestParam("file") MultipartFile file) {
        log.info("机号上传开始：{}", new Date());
        flightInfoService.importAircraftDataBatch(file);
        log.info("机号上传结束：{}", new Date());
        return super.success();
    }

    @PostMapping("/importFlightPlanInfo")
    @ApiOperation(value = "航班计划导入")
    public ResultBuilder<List<FlightPlanInfo>> importFlightPlanInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                                    @RequestBody @ApiParam(name = "file", value = "导入文件") MultipartFile file,
                                                                    @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        ExcelUtils.checkSuffix(file);
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.importFlightPlanInfo(file, user, airportCode, urlName);
    }

    @PostMapping("/saveFlightPlanInfo")
    @ApiOperation(value = "手动导入航班计划")
    public ResultBuilder<List<FlightPlanInfo>> saveFlightPlanInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                                  @RequestBody @ApiParam(name = "flightPlanForms", value = "航班计划") List<FlightPlanForm> flightPlanForms,
                                                                  @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.saveFlightPlanInfo(flightPlanForms, user, airportCode, urlName);
    }

    @GetMapping("/pageFlightPlanInfoByCondition")
    @ApiOperation(value = "根据条件动态查询航班计划")
    @ApiImplicitParams({@ApiImplicitParam(name = "flightDate", value = "航班日期", dataType = "String"),
            @ApiImplicitParam(name = "flightNo", value = "航班号", dataType = "String"),
            @ApiImplicitParam(name = "airportCode", value = "机场三字码")
    })
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FlightPlanInfo.class)})
    public ResultBuilder<Page<FlightPlanInfo>> pageFlightPlanInfoByCondition(String flightDate, String flightNo, String airportCode, @ApiParam(name = "pageParam", value = "分页参数") @Validated PageParam pageParam) {
        return flightInfoService.pageFlightPlanInfoByCondition(flightDate, flightNo, airportCode, pageParam);
    }

    @DeleteMapping("/delFlightPlanInfo")
    @ApiOperation(value = "根据id删除航班计划")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    public ResultBuilder<Object> delFlightPlanInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                   @RequestParam @ApiParam(name = "id", value = "航班计划Id") String id) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.delFlightPlanInfo(id, user, urlName);
    }

    @GetMapping(value = "/exportFlightPlanTemplate")
    @ApiOperation(value = "航班计划模板下载")
    public void exportFlightPlanTemplate(HttpServletResponse response) {
        flightInfoService.exportFlightPlanTemplate(response);
    }

    @PostMapping(value = "/coverFlightPlanInfo")
    @ApiOperation(value = "覆盖原有航班计划")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    public ResultBuilder<Object> coverFlightPlanInfo(@RequestBody @ApiParam(name = "flightPlanForms", value = "航班计划") List<FlightPlanForm> flightPlanForms,
                                                     @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return flightInfoService.coverFlightPlanInfo(flightPlanForms, airportCode);
    }


    @PostMapping("/exportFlightACCA")
    @ApiOperation(value = "清算中心模版导出")
    @ApiImplicitParam(name = "urlName", value = "页面名称")
    public void exportFlightAcca(@RequestParam String urlName, HttpServletResponse res, @RequestBody FlightInfoSearchForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightInfoService.exportFlightAcca(res, form, urlName, user);
    }

    @PostMapping("/exportFlightInfoByCondition")
    @ApiOperation(value = "机场建设数据下载")
    public void exportFlightInfoByCondition(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                            @RequestBody @ApiParam(name = "form", value = "航班信息导出条件") FlightInfoExportForm form,
                                            HttpServletResponse res) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightInfoService.exportFlightInfoByCondition(urlName, form, user, res);
    }

    @ApiOperation("确认航班信息及服务记录")
    @PostMapping(value = "/confirmFlightInfo")
    public Object confirmFlightInfo(@RequestBody @Validated FlightInfoConfirmForm dto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        List<String> idList = flightInfoService.confirmFlightInfo(dto);
        //确认后重新结算
        if (CollectionUtils.isNotEmpty(idList)) {
            flightInfoService.confirmReSettlement(String.join(",", idList), dto.getAirportCode(),
                    CommonConstants.SETTLEMENT_FLIGHT_FORMULA, user);
        }

        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation("取消确认航班信息及服务记录")
    @PutMapping(value = "/cancelConfirmFlightInfo")
    public Object cancelConfirmFlightInfo(@RequestParam @ApiParam(name = "flightIds", value = "航班id") String flightIds) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.cancelConfirmFlightInfo(flightIds, user);
    }

    @ApiOperation("确认-业务保障数据")
    @PostMapping(value = "/confirmFlightBusinessInfo")
    public Object confirmFlightBusinessInfo(@RequestBody @Validated FlightBusinessInfoConfirmForm dto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        List<String> idList = flightInfoService.confirmFlightBusinessInfo(dto);
        //确认后重新结算
        if (CollUtil.isNotEmpty(idList)) {
            flightInfoService.confirmReSettlement(String.join(",", idList), dto.getAirportCode(),
                    CommonConstants.SETTLEMENT_VARIABLE_FORMULA, user);
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation("取消确认-业务保障数据")
    @PutMapping(value = "/cancelConfirmFlightBusinessInfo")
    public Object cancelConfirmFlightBusinessInfo(@RequestParam @ApiParam(name = "flightIds", value = "航班id") String flightIds) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.cancelConfirmFlightBusinessInfo(flightIds, user);
    }

    @ApiOperation("根据航班id和操作对象查询操作记录")
    @GetMapping(value = "/listOperateByFlightIdAndObject")
    public Object listOperateByFlightIdAndObject(@RequestParam @ApiParam(name = "flightId", value = "航班id") String flightId,
                                                 @RequestParam @ApiParam(name = "object", value = "操作对象") String object) {
        return flightInfoService.listOperateByFlightIdAndObject(flightId, object);
    }

    @ApiOperation("航班桥载数据下载")
    @PostMapping(value = "/exportBridgeServiceRecord")
    public void exportBridgeServiceRecord(@RequestBody @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件") FlightInfoSearchForm flightInfoSearchForm,
                                          @RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName, HttpServletResponse response) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightInfoService.exportBridgeServiceRecord(flightInfoSearchForm, response, user, urlName);
    }


    @PostMapping("/importActualFlightTime")
    @ApiOperation(value = "导入航班实际起降时间")
    public ResultBuilder<Object> importActualFlightTime(@RequestBody @ApiParam(name = "file", value = "导入航班实际起降时间的excel文件") MultipartFile file,
                                                        @RequestParam String airportCode,
                                                        @RequestParam String urlName) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightInfoService.importActualFlightTime(file, airportCode, user, urlName);
        return new ResultBuilder.Builder<>().builder();
    }


    @PostMapping("/saveDeviceUsedListData")
    @ApiOperation(value = "批量上传航班的特车/设备数据)")
    public Object saveDeviceUsedListData(
            @RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
            @RequestBody @Validated @ApiParam(name = "serviceRecordForm", value = "航班相关设备使用数据导入表单",
                    required = true) List<ServiceSecordBatchForm> serviceRecordForms) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.saveDeviceUsedListData(serviceRecordForms, user, urlName);
    }


    @ApiOperation(value = "查询多个航班的特车/设备数据")
    @GetMapping("/listDeviceUsedDataByFlightIds")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ServiceRecord.class)})
    public Object listDeviceUsedDataByFlightIds(@ApiParam(name = "flightId", value = "航班id", required = true)
                                                @RequestParam @NotBlank(message = "flightId不能为空") String flightId,
                                                @RequestParam @NotBlank(message = "serviceCode不能为空") String serviceCode) {
        return flightInfoService.listDeviceUsedDataByFlightIds(flightId, serviceCode);
    }


    @PostMapping("/searchForFlights")
    @ApiOperation(value = "模糊搜索航班号")
    public Object searchForFlights(@RequestParam @ApiParam(name = "flightNum", value = "航班号") String flightNum) {
        return flightInfoService.searchForFlights(flightNum);
    }

    @PostMapping("/updatePassengerDataBatch")
    @ApiOperation(value = "批量录入头等舱/重要旅客/持卡旅客")
    public ResultBuilder<Object> updatePassengerDataBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                          @RequestBody @Validated @ApiParam(name = "serviceRecordForm", value = "航班相关设备使用数据导入表单", required = true) List<FlightUpdatePassengerForm> flightInfoForms) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.updatePassengerDataBatch(flightInfoForms, user, urlName);
    }

    @GetMapping("/getFlightStayTime")
    @ApiOperation(value = "获取离港航班的停机时长，停机开始结束时间")
    public ResultBuilder<Object> getFlightStayTime(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                   @ApiParam(name = "airportCode", value = "机场三字码") @NotBlank @RequestParam String airportCode,
                                                   @ApiParam(name = "startTime", value = "查询离港航班起始时间(yyyy-MM-dd HH:mm:ss)") @NotBlank @RequestParam(required = false) String startTime,
                                                   @ApiParam(name = "endTime", value = "查询离港航班结束时间(yyyy-MM-dd HH:mm:ss)") @NotBlank @RequestParam(required = false) String endTime) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.getFlightStayTime(airportCode, startTime, endTime, user, urlName);

    }

    @ApiOperation(value = "业务保障数据导出")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/exportFlightBusinessData")
    public void exportFlightBusinessData(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                         @Validated @ApiParam(name = "flightInfoSearchForm", value = "航班信息查询条件") FlightInfoSearchForm flightInfoSearchForm,
                                         HttpServletResponse response) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightInfoService.exportFlightBusinessData(flightInfoSearchForm, response, user, urlName);
    }

    @PostMapping("/importFlightBusinessData")
    @ApiOperation(value = "业务保障数据导入")
    public ResultBuilder<Object> importFlightBusinessData(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                                          @RequestBody @ApiParam(name = "file", value = "导入文件") MultipartFile file,
                                                          @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightInfoService.importFlightBusinessData(file, airportCode, urlName, user);
    }

    @GetMapping(value = "/exportFlightBusinessDataTemplate")
    @ApiOperation(value = "业务保障数据-模板下载")
    public void exportFlightBusinessDataTemplate(HttpServletResponse response) {
        flightInfoService.exportFlightBusinessDataTemplate(response);
    }
}
