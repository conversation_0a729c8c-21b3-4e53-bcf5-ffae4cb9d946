package com.swcares.aiot.service;

import com.swcares.aiot.core.model.vo.FlightLineInfoVo;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.form.FlightLineInfoForm;

import java.util.List;

/**
 * ClassName：com.swcares.service.FlightLineInfoService
 * Description：航线补贴航线信息Service
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/2 10:15
 * @version v1.0
 */
public interface FlightLineInfoService {

    List<FlightLineInfoVo> getFlightLineInfoList(String airlineId, String airportCode, String flightLine);

    void saveFlightLineInfo(FlightLineInfoForm form, LoginUserDetails user);

    void deleteFlightLine(String id, LoginUserDetails user);
}
