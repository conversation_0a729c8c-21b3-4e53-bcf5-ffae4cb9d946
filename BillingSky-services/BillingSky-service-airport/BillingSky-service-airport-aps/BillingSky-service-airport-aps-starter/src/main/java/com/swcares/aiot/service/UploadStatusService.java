package com.swcares.aiot.service;

import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.model.vo.UploadStatusVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.service.ErrorFileNotificationService
 * Description：上传文件通知
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/10/17 10:04
 * @version v1.0
 */
public interface UploadStatusService {
    /**
     * Title: 新建上传<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/17 11:40 <br>
     *
     * @param uploadName 上传接口名
     * @param user       用户信息
     */
    String newUpload(String uploadName, String airportCode, LoginUserDetails user);

    /**
     * Title: 开始上传<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/19 15:37 <br>
     *
     * @param uploadStatusId 上传信息表id
     */
    void startUpload(String uploadStatusId);

    /**
     * Title: 上传失败接口<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/17 10:46 <br>
     *
     * @param uploadStatusId 上传信息表id
     * @param failReason     失败原因
     */
    void uploadFail(String uploadStatusId, String failReason);

    /**
     * Title: 上传成功接口<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/18 10:20 <br>
     *
     * @param uploadStatusId :
     * @param fileName       上传文件名
     * @param wb             错误数据文件XSSFWorkbook
     */
    void uploadSuccess(String uploadStatusId, String fileName, Workbook wb);

    /**
     * Title: 获取用户未读上传信息条数<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/18 10:49 <br>
     *
     * @param user :
     */
    int getUnreadUploadCount(String airportCode, LoginUserDetails user);

    /**
     * Title: 获取上传与结算的总共未读条数<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/11/2 10:57 <br>
     */
    int getUnreadTotal(String airportCode, LoginUserDetails user);

    /**
     * Title: 分页查询上传信息<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/18 11:34 <br>
     *
     * @param pageParam :
     */
    Pager<UploadStatusVo> pageUploadStatus(String airportCode, Date startDate, Date endDate, PageParam pageParam, LoginUserDetails user);

    /**
     * Title: 查询最新十条记录<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/19 9:23 <br>
     *
     * @param user :
     */
    List<UploadStatusVo> listLatelyUploadStatus(String airportCode, LoginUserDetails user);

    /**
     * Title: 批量删除上传记录<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/20 14:15 <br>
     *
     * @param ids  :
     * @param user :
     */
    void delete(String ids, LoginUserDetails user);
    /**
     * Title: 上传成功接口传入输出流<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2025/01/13 10:20 <br>
     *
     * @param uploadStatusId :
     * @param fileName       上传文件名
     * @param outputStream   错误数据文件outputStream
     */
    void uploadSuccessOutputStream(String uploadStatusId, String fileName, ByteArrayOutputStream outputStream);
    /**
     * Title: 上传文件功能通过id获取下载地址<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2025/01/13 10:20 <br>
     *
     * @param id       上传状态id
     */
    String uploadGetUrlByFileName( String id);
}
