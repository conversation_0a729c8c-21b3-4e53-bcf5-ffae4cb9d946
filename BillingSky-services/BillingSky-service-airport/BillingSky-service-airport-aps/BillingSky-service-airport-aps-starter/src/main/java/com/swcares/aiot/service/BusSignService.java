package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.BugSignSearchForm;
import com.swcares.aiot.core.form.SubmitForm;
import com.swcares.aiot.core.model.entity.BusSignHistory;
import com.swcares.aiot.core.model.vo.BillItemBusVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.service.BusSignService
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/11/15 9:03
 * @version v1.0
 */
public interface BusSignService {
    ResultBuilder<Object[]> pageBusSign(BugSignSearchForm bugSignSearchForm);

    void exportBusSign(BugSignSearchForm bugSignSearchForm, HttpServletResponse res);

    ResultBuilder<List<BillItemBusVo>> getQueryCondition(String airpotCode);

    ResultBuilder<?> submit(SubmitForm form);

    List<BusSignHistory> busSignhistoryList(Long signId, int page);

    Integer busSignhistoryTotal(Long signId);

    void changeBusSignStatus(String id, String operation, String reason);

}
