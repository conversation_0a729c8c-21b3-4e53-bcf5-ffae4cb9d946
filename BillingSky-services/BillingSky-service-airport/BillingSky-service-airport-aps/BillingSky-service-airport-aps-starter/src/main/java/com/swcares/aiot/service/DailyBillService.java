package com.swcares.aiot.service;

import com.swcares.aiot.core.model.dto.ErAutoConfirmDto;
import com.swcares.aiot.core.model.dto.ErBillFulfillAndConfirmDto;
import com.swcares.aiot.core.model.dto.ErDailyBillPageDto;
import com.swcares.aiot.core.model.vo.ErDailyBillAccVo;
import com.swcares.aiot.core.model.vo.ErBillFulfillAndConfirmVo;
import com.swcares.aiot.core.model.vo.ErDailyBillPageVo;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.DailyBillService
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/3/26 10:00
 * @version v1.0
 */
public interface DailyBillService {


    /**
     * 分页查询[每日账单]
     *
     * @param erDailyBillPageDto 查询条件
     * @return 分页显示[每日账单]
     */
    PagedResult<List<ErDailyBillPageVo>> pageErDailyBill(ErDailyBillPageDto erDailyBillPageDto);

    /**
     * 分页-统计[每日账单]
     *
     * @param erDailyBillPageDto :
     * @return 统计结果
     */
    BaseResult<ErDailyBillAccVo> accErDailyBill(ErDailyBillPageDto erDailyBillPageDto);

    /**
     * 支付-[每日账单]确认支付
     *
     * @param erBillFulfillAndConfirmDto 计算参数
     * @return 结果
     */
    BaseResult<ErBillFulfillAndConfirmVo> confirmPay(ErBillFulfillAndConfirmDto erBillFulfillAndConfirmDto);

    /**
     * 开启/关闭日账单自动确认
     *
     * @param erAutoConfirmDto 参数
     * @return 结果
     */
    BaseResult<Boolean> switchDailyAutoConfirm(ErAutoConfirmDto erAutoConfirmDto);

    /**
     * 日账单自动确认查询
     *
     * @param airportCode 参数
     * @return 结果
     */
    BaseResult<Boolean> dailyAutoConfirmQuery(String airportCode);

    /**
     * 查询-下载[每日账单]
     *
     * @param erDailyBillPageDto 查询条件
     */
    void downloadErDailyBill(ErDailyBillPageDto erDailyBillPageDto, HttpServletResponse response) throws IOException;
}
