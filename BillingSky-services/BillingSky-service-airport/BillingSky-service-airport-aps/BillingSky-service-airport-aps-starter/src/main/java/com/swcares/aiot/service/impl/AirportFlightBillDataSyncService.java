package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aiot.ComplexMessage;
import com.swcares.aiot.core.common.util.SsssUtil;
import com.swcares.aiot.core.common.util.ValidationUtils;
import com.swcares.aiot.core.model.dto.FlightBillDto;
import com.swcares.aiot.core.model.dto.FlightBillExportDto;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.common.util.ThreadUtil;
import com.swcares.aiot.dao.FlightBillDao;
import com.swcares.aiot.dao.FlightBillHistoryDao;
import com.swcares.aiot.service.FlightBillService;
import com.swcares.aiot.synergy.annotation.DataType;
import com.swcares.aiot.synergy.service.IDataSyncService;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DataType("AIRPORT_FLIGHT_BILL")
@Service
public class AirportFlightBillDataSyncService implements IDataSyncService {

    @Resource
    private FlightBillDao flightBillDao;
    @Resource
    private SsssUtil ssssUtil;
    @Resource
    private FlightBillService flightBillService;
    @Resource
    private FlightBillHistoryDao flightBillHistoryDao;

    @Override
    public Long getTenantId(String tenantCode) {
        return ConfigUtil.getLong("signature_config", tenantCode.toUpperCase(), GlobalConstants.TENANT_DEFAULT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncData(ComplexMessage message) {
        List<FlightBillDto> dtos = JSON.parseArray(JSONUtil.toJsonStr(message.getPayload().getData()), FlightBillDto.class);
        log.info("收到账单信息：{}", dtos);

        List<FlightBill> bills = new ArrayList<>();
        dtos.forEach(dto -> {
            //数据规范校验
            String valid = ValidationUtils.valid(dto);
            if (CharSequenceUtil.isNotBlank(valid)) {
                //加入错误信息列表 调第三方接口告诉他们数据异常
                FlightBillExportDto exportDto = new FlightBillExportDto();
                BeanUtils.copyProperties(dto, exportDto);
                exportDto.setErrorMsg(valid);
                log.warn("账单入库失败,{}", valid);
                return;
            }

            FlightBill bill = new FlightBill();
            BeanUtils.copyProperties(dto, bill);
            bills.add(bill);
        });

        ThreadPoolExecutor executor = ThreadUtil.getExecutor();
        if (CollUtil.isNotEmpty(bills)) {
            int baseCount = 1000;
            int times = (bills.size() + baseCount - 1) / baseCount;
            AtomicInteger i = new AtomicInteger(0);
            while (i.get() < times) {
                int j = i.getAndAdd(1);
                executor.submit(() -> {
                    List<FlightBill> currentList = bills.stream().skip((long) j * baseCount).limit(baseCount).collect(Collectors.toList());

                    flightBillService.saveOrUpdateBills(bills, "第三方接口导入", TenantHolder.getTenant());
                    //按航司提交对账
                    Map<String, List<FlightBill>> map = currentList.stream().collect(Collectors.groupingBy(FlightBill::getSettleCode, Collectors.toList()));
                    map.forEach((key, value) -> ssssUtil.send("FLIGHT_BILL", value.get(0).getAirportCode(), Collections.singletonList(key), value));
                    //更新账单提交状态
                    List<String> ids = currentList.stream().map(FlightBill::getId).collect(Collectors.toList());
                    flightBillDao.updateFlightBillStatusByIds(ids, 3);
                    //记录账单变更历史
                    flightBillHistoryDao.copyBillHistory(ids, "第三方接口数据录入自动对账", BillOperation.SUBMIT.getCode());
                });
            }
        }

        //调第三方接口告诉他们账单入库情况

    }

}
