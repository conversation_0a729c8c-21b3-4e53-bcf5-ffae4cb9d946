package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.FormulaForm;
import com.swcares.aiot.core.form.UseFormulaForm;
import com.swcares.aiot.core.model.entity.FormulaInfo;
import com.swcares.aiot.core.model.entity.FunctionInfo;
import com.swcares.aiot.core.model.entity.RulesVariableRecord;
import com.swcares.aiot.service.FormulaService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * ClassName：FormulaContrller <br>
 * Description：该接口主要用于对公式的操作<br>
 * Copyright © 2020-5-14 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-5-14 15:46<br>
 * @version v1.0 <br>
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/formula")
@Api(value = "FormulaContrller", tags = {"公式操作接口"})
@Slf4j
public class FormulaContrller {
    @Resource
    private FormulaService formulaService;

    /*
     * Title: saveFormula<br>
     * Author: 李龙<br>
     * Description: 根据费用信息，创建公式<br>
     * Date:  14:09 <br>
     * @param formulaForm
     * return: java.lang.Object
     */
    @ApiOperation(value = "创建计算公式")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/saveFormula")
    public Object saveFormula(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                              @RequestBody @ApiParam(name = "formulaForm", value = "公式设置") @Valid FormulaForm formulaForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return formulaService.saveFormula(formulaForm, user, urlName);
    }

    /*
     * Title: delFormulaById<br>
     * Author: 李龙<br>
     * Description: 根据公式id删除公式<br>
     * Date:  16:05 <br>
     * @param id
     * return: java.lang.Object
     */
    @ApiOperation(value = "删除公式")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @DeleteMapping(value = "/delFormulaById")
    public Object delFormulaById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                 @RequestParam @ApiParam(name = "formulaId", value = "公式ID") String formulaId) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return formulaService.delFormulaById(formulaId, user, urlName);
    }

    /*
     * Title: getFormulaById<br>
     * Author: 李龙<br>
     * Description: 根据公式的id查询公式信息<br>
     * Date:  15:58 <br>
     * @param id
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据id查询公式")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FormulaInfo.class)})
    @GetMapping(value = "/getFormulaById")
    public Object getFormulaById(@RequestParam @ApiParam(name = "formulaId", value = "公式id") String formulaId) {
        return formulaService.getFormulaById(formulaId);
    }

    /*
     * Title: updateFormula<br>
     * Author: 李龙<br>
     * Description: 根据公式id修改公式内容<br>
     * Date:  16:00 <br>
     * @param formulaForm
     * return: java.lang.Object
     */
    @ApiOperation(value = "修改公式")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PutMapping(value = "/updateFormula")
    public Object updateFormula(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                @RequestBody @ApiParam(name = "formulaForm", value = "公式信息") @Validated({Update.class}) FormulaForm formulaForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return formulaService.updateFormula(formulaForm, user, urlName);
    }

    /*
     * Title: listFormulaByFeeId<br>
     * Author: 李龙<br>
     * Description: 根据费用id，查询其所属公式<br>
     * Date:  16:07 <br>
     * @param feeRuleId
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据费用id,查询所属费用的公式")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FormulaForm.class)})
    @GetMapping(value = "/listFormulaByFeeId")
    public Object listFormulaByFeeId(@RequestParam @ApiParam(name = "feeId", value = "费用id") String feeId) {
        return formulaService.listFormulaByFeeId(feeId);
    }


    /*
     * Title: listVariableRulesByUnit<br>
     * Author: 李龙<br>
     * Description: 根据单位查规则变量<br>
     * Date:  15:32 <br>
     * @param unit
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据单位查规则变量")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = RulesVariableRecord.class)})
    @GetMapping(value = "/listVariableRulesByUnit")
    public Object listVariableRulesByUnit(@RequestParam @ApiParam(name = "variableUnit", value = "收费单位 T：吨|N：人|H：小时|F：次|M：人工时|S：架次") String variableUnit) {
        return formulaService.listVariableRulesByUnit(variableUnit);

    }

    /*
     * Title: useFormula<br>
     * Author: 李龙<br>
     * Description: 采用公式<br>
     * Date:  16:50 <br>
     * @param useFormulaForm
     * return: java.lang.Object
     */
    @ApiOperation(value = "采用公式")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PutMapping(value = "/useFormula")
    public Object useFormula(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                             @RequestBody @ApiParam(name = "useFormulaForm", value = "航司采用公式提交表单") UseFormulaForm useFormulaForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return formulaService.useFormula(useFormulaForm, user, urlName);
    }

    /*
     * Title: fastCopyFormula<br>
     * Author: 李龙<br>
     * Description: 根据快速复制航司的公式<br>
     * Date:  9:42 <br>
     * @param airlineId
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据快速复制航司的公式")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/fastCopyFormula")
    public Object fastCopyFormula(@RequestParam @ApiParam(name = "airlineId", value = "航司id", required = true) String airlineId,
                                  @RequestParam @ApiParam(name = "useAirlineId", value = "需要复制航司的Id", required = true) String useAirlineId,
                                  @RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true) String airportCode) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return formulaService.fastCopyFormula(airlineId, useAirlineId, user, airportCode);
    }

    /*
     * Title: findFormulaInfoByName<br>
     * Author: 李龙<br>
     * Description: 根据公式名称查询费用下的公式，用于判断公式名称的唯一性<br>
     * Date:  15:34 <br>
     * @param formulaName
     * @param feeId
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据公式名称查询费用下的公式，用于判断公式名称的唯一性")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FormulaInfo.class)})
    @GetMapping(value = "/findFormulaInfoByName")
    public Object findFormulaInfoByName(@RequestParam @ApiParam(name = "formulaName", value = "公式名称", required = true) String formulaName,
                                        @RequestParam @ApiParam(name = "feeId", value = "费用id", required = true) String feeId) {
        return formulaService.findFormulaInfoByName(formulaName, feeId);
    }

    /*
     * Title: listFunction<br>
     * Author: 李龙<br>
     * Description: 查询内置函数<br>
     *     设置公式时输入$使用
     * Date:  15:30 <br>
     * @param
     * return: java.lang.Object
     */
    @ApiOperation(value = "查询内置函数")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FunctionInfo.class)})
    @GetMapping(value = "/listFunction")
    public Object listFunction() {
        return formulaService.listFunction();
    }

    /*
     * Title: cancelUseFormula<br>
     * Author: 李龙<br>
     * Description: 取消公式的采用<br>
     * Date:  15:05 <br>
     * @param formulaId
     * return: java.lang.Object
     */
    @ApiOperation(value = "取消公式的采用")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PutMapping(value = "/cancelUseFormula")
    public Object cancelUseFormula(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                   @RequestParam @ApiParam(name = "formulaId") String formulaId) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return formulaService.cancelUseFormula(formulaId, user, urlName);
    }

    /*
     * Title: getCountFormulaByAirline<br>
     * Author: 李龙<br>
     * Description: (根据航司id查询航司下的公式数量)<br>
     * Date:  10:41 <br>
     * @param airlineId
     * return: java.lang.Object
     */
    @ApiOperation(value = "根据航司id查询航司下的公式数量")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/getCountFormulaByAirline")
    public Object getCountFormulaByAirline(@RequestParam @ApiParam(name = "airlineId") String airlineId) {
        return formulaService.getCountFormulaByAirline(airlineId);
    }

    @ApiOperation(value = "获取保障类型列表")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/getSafeguardType")
    public Object getSafeguardType(@RequestParam @ApiParam(name = "airportCode") String airportCode) {
        return new ResultBuilder.Builder<>().data(formulaService.getSafeguardType(airportCode)).builder();
    }
}
