package com.swcares.aiot.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.FlightBillForm;
import com.swcares.aiot.core.form.FlightBillRefuseForm;
import com.swcares.aiot.core.form.SubmitForm;
import com.swcares.aiot.core.model.dto.FlightBillImportDto;
import com.swcares.aiot.core.model.entity.FlightBillHistory;
import com.swcares.aiot.core.model.vo.FlightBillPageVo;
import com.swcares.aiot.core.model.vo.SubmitVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.FlightBillService;
import com.swcares.aiot.service.UpperChainService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.worm.hutool.HttpUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ClassName：FlightBillController <br>
 * Description：(航班明细账单接口)<br>
 * Copyright © 2020/6/17 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/17 18:50<br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@RequestMapping("/api/flightBill")
@Api(value = "FlightBillController", tags = {"航班明细账单接口"})
public class FlightBillController {

    @Resource
    private FlightBillService flightBillService;

    @Resource
    private UpperChainService upperChainService;

    @PostMapping("/pageFlightBillInfoByCondition")
    @ApiOperation(value = "航班明细账单动态条件分页查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightBillPageVo.class)})
    public ResultBuilder<Map<String, Object>> pageFlightBillInfoByCondition(@Validated @ApiParam(name = "pageParam", value = "分页与条件") PageParam pageParam,
                                                                            @Validated @ApiParam(name = "flightBillForm", value = "航班明细账单查询条件")
                                                                            FlightBillForm flightBillForm) {
        return flightBillService.pageFlightBillInfoByCondition(pageParam, flightBillForm);
    }

    @PostMapping("/countTotal")
    @ApiOperation(value = "航班明细账单总计")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Double.class)})
    public Object countTotal(@Validated @ApiParam(name = "flightBillForm", value = "航班明细账单查询条件")
                             FlightBillForm flightBillForm) {
        return flightBillService.countTotal(flightBillForm);
    }

    @PostMapping(value = "/exportFlightBillInfo")
    @ApiOperation(value = "导出航班明细账单")
    public void exportFlightBillInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                     @Validated @ApiParam(name = "flightBillForm", value = "航班明细账单查询条件")
                                     FlightBillForm flightBillForm, HttpServletResponse response) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightBillService.exportFlightBillInfo(flightBillForm, response, user, urlName);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "airportCode", value = "机场三字码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flightNos", value = "航班号,多个用逗号隔开", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "String", paramType = "query"),
    })
    @ApiOperation(value = "重新结算航班相关费用")
    @GetMapping(value = "/doReSettlement")
    public Object doReSettlement(@NotBlank(message = "机场三字码不能为空！") @Size(min = 3, max = 3, message = "机场三字码必须为3个字符长度！")
                                 String airportCode, String flightNos, String feeCode,
                                 @NotNull(message = "时间类型不能为空！") Integer dateType,
                                 @NotNull(message = "开始时间不能为空！") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                 @NotNull(message = "结束时间不能为空！") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return flightBillService.doReSettlement(airportCode, flightNos, feeCode, startDate, endDate, user, dateType);
    }

    @ApiOperation(value = "获取某个机场下的所有航司和费用列表")
    @GetMapping(value = "/listAirlineRelatedFeeInfo")
    public Object listAirlineRelatedFeeInfo(@ApiParam(name = "airportCode", value = "机场三字码")
                                            @RequestParam String airportCode) {
        return flightBillService.listAirlineRelatedFeeInfo(airportCode);
    }

    @ApiOperation(value = "提交对账表")
    @PostMapping(value = "/submit")
    public Object submit(@RequestBody @Validated @ApiParam(name = "SubmitForm", value = "提交对账表单", required = true) SubmitForm form) {
        List<TFlightBill> tFlightBills = upperChainService.getTFlightBill(form);
        ResultBuilder<SubmitVo> object = flightBillService.submit(form);
        upperChainService.upperChain(tFlightBills);
        return object;
    }

    @ApiOperation(value = "校验上传账单")
    @PostMapping(value = "/checkUploadFile")
    public Object checkUploadFile(@RequestParam @NotNull(message = "文件不能为空！") MultipartFile file) {
        return flightBillService.checkUploadFile(file);
    }

    @ApiOperation(value = "上传账单")
    @PostMapping(value = "/upload")
    public Object upload(@RequestParam @NotNull(message = "文件不能为空！") MultipartFile file) {
        return flightBillService.upload(file);
    }

    @ApiOperation(value = "上传账单(补充)")
    @PostMapping(value = "/uploadBatch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Object uploadBatch(@RequestPart("file") MultipartFile file) {
        return flightBillService.uploadBatch(file);
    }

    @ApiOperation(value = "下载账单模板")
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        List<FlightBillImportDto> list = new ArrayList<>();
        list.add(new FlightBillImportDto());
        try {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode("航班明细账单导入模板.xlsx", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            OutputStream out = response.getOutputStream();
            EasyExcelFactory.write(out, FlightBillImportDto.class).sheet("模板").doWrite(list);
            out.flush();
            out.close();
        } catch (Exception e) {
            throw new GenericException(BusinessMessageEnum.IO_ERROR.getCode(), BusinessMessageEnum.IO_ERROR.getMsg());
        }
    }

    @ApiOperation(value = " 拒绝处理")
    @PostMapping(value = "/refuseToHandle")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "FlightBillRefuseForm", value = "航班明细账单拒绝处理条件", required = true)
    })
    public Object refuseToHandle(@RequestBody @Validated FlightBillRefuseForm param) {
        return flightBillService.refuseToHandle(param);
    }

    @ApiOperation(value = " 账单记录")
    @GetMapping(value = "/billRecord")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = FlightBillHistory.class)})
    public Object billRecord(@RequestParam @NotBlank String fltBillId, @RequestParam Integer page) {
        return flightBillService.billRecord(fltBillId, page * 10);
    }

    @ApiOperation(value = " 撤销账单")
    @PostMapping(value = "/revocation/{id}")
    public Object revocation(@PathVariable String id) {
        return flightBillService.revocation(id);
    }

    @ApiOperation(value = " 批量删除账单")
    @DeleteMapping(value = "/deleteFlightBillById")
    public ResultBuilder<Integer> deleteFlightBillById(@RequestBody List<String> idList) {
        return new ResultBuilder.Builder<Integer>().data(flightBillService.deleteFlightBillById(idList)).builder();
    }

}
