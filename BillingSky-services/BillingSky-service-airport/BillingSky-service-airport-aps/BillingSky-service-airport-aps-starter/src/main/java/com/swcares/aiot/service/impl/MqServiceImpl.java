package com.swcares.aiot.service.impl;

import com.swcares.aiot.core.model.entity.FeeInfo;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.entity.MSettleInfo;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.dao.FlightBillDao;
import com.swcares.aiot.dao.MqDao;
import com.swcares.aiot.service.MqService;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.service.impl.MqServiceImpl
 * Description：将中间表和账单表还有费用表的数据发送到mq中，供经营管理平台使用
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/3/23 9:19
 * @version v1.0
 */
@Service
@Slf4j
public class MqServiceImpl implements MqService<Object> {

    //mq交换机名字
    private static final String EXCHANG_NAME = "aps.direct";
    //结算中间表数据消息keyname
    private static final String SETTLE_KEY_NAME = "addsettle";
    //账单表数据消息keyname
    private static final String BILL_KEY_NAME = "addbill";
    //费用发送消息keyname
    private static final String FEE_KEY_NAME = "fee";
    //费用新增消息mapkeyname
    private static final String FEE_ADD_KEY_NAME = "add";
    //费用修改消息mapkeyname
    private static final String FEE_UPDATE_KEY_NAME = "update";
    //费用表删除消息mapkeyname
    private static final String FEE_DELETE_KEY_NAME = "delete";

    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private MqDao mqDao;
    @Resource
    private FlightBillDao flightBillDao;
    @Resource
    private FeeDao feeDao;

    @Override
    public void send(Object message, String keyName) {
        //添加租户id
        Map<String, Object> map = new HashMap<>();
        map.put("tenantId", TenantHolder.getTenant());
        map.put("msg", message);
        // 发送消息
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        log.info("发送到mq==========");
        rabbitTemplate.convertAndSend(EXCHANG_NAME, keyName, map);
    }


    @Override
    public void sendReCaleDat() {
        List<MSettleInfo> mlist = mqDao.getMSettleInfo();
        send(mlist, SETTLE_KEY_NAME);
        Set<String> flightIds = new HashSet<>();
        for (MSettleInfo ms : mlist) {
            flightIds.add(ms.getFlightId());
        }
        List<FlightBill> billList = new ArrayList<>();
        for (String id : flightIds) {
            billList.addAll(flightBillDao.getFlightBillByFlightId(id));
        }
        send(billList, BILL_KEY_NAME);
    }

    @Override
    public void addfee(String feeId) {
        FeeInfo addFeeInfo = feeDao.getByFeeId(feeId);
        sendFee(addFeeInfo, FEE_ADD_KEY_NAME);
    }

    @Override
    public void updatefee(String feeId) {
        FeeInfo updateFeeInfo = feeDao.getByFeeId(feeId);
        sendFee(updateFeeInfo, FEE_UPDATE_KEY_NAME);
    }

    @Override
    public void deletefee(String feeId) {
        FeeInfo deleteFeeInfo = feeDao.getByFeeId(feeId);
        sendFee(deleteFeeInfo, FEE_DELETE_KEY_NAME);
    }

    public void sendFee(FeeInfo fee, String key) {
        Map<String, Object> map = new HashMap<>();
        map.put("tFeeInfo", fee);
        map.put("methodName", key);
        send(map, FEE_KEY_NAME);
    }

}
