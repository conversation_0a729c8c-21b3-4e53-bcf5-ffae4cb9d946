package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.ObjectUtil;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.FlightLineInfoForm;
import com.swcares.aiot.core.model.entity.AirlineInfoAps;
import com.swcares.aiot.core.model.entity.AirportThreeCharCode;
import com.swcares.aiot.core.model.entity.FlightLineInfo;
import com.swcares.aiot.core.model.vo.FlightLineInfoVo;
import com.swcares.aiot.dao.AirlineDao;
import com.swcares.aiot.dao.AirportThreeCharCodeDao;
import com.swcares.aiot.dao.FlightLineInfoDao;
import com.swcares.aiot.dao.SubsidyFormulaDao;
import com.swcares.aiot.service.FlightLineInfoService;
import com.swcares.aiot.service.SubsidyFormulaService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * ClassName：com.swcares.service.impl.FlightLineInfoServiceImpl
 * Description：航线补贴航线信息service接口实现类
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/2 10:16
 * @version v1.0
 */
@Service
public class FlightLineInfoServiceImpl implements FlightLineInfoService {
    @Resource
    private FlightLineInfoDao flightLineInfoDao;
    @Resource
    private AirlineDao airlineDao;
    @Resource
    private AirportThreeCharCodeDao airportThreeCharCodeDao;
    @Resource
    private SubsidyFormulaDao subsidyFormulaDao;
    @Resource
    private SubsidyFormulaService subsidyFormulaService;

    @Override
    public List<FlightLineInfoVo> getFlightLineInfoList(String airlineCode, String airportCode, String flightLine) {
        List<Object[]> flightLineInfoList;
        List<FlightLineInfoVo> resVoList = new ArrayList<>();
        String airlineShortName = "";
        //如果航司包含中文
        if (ObjectUtil.checkStringContainChinese(airlineCode)) {
            airlineShortName = airlineCode;
            airlineCode = "";
        }
        //如果航线包含中文
        if (ObjectUtil.checkStringContainChinese(flightLine)) {
            flightLineInfoList = flightLineInfoDao.getFlightLineInfoListByFlightLineCN(airlineCode, airlineShortName, airportCode, flightLine);
        } else {
            flightLineInfoList = flightLineInfoDao.getFlightLineInfoListByFlightLine(airlineCode, airlineShortName, airportCode, flightLine);
        }
        for (Object[] objs : flightLineInfoList) {
            FlightLineInfoVo vo = new FlightLineInfoVo();
            vo.setId((String) objs[0]);
            vo.setFlightLine((String) objs[1]);
            vo.setFlightLineCn((String) objs[2]);
            vo.setAirlineCode((String) objs[3]);
            vo.setAirlineShortName((String) objs[4]);
            vo.setBudget((Double) objs[5]);
            vo.setUsed((Double) objs[6]);
            if (objs[7] != null) {
                vo.setFormulaNum(Integer.valueOf(objs[7].toString()));
            } else {
                vo.setFormulaNum(0);
            }
            resVoList.add(vo);
        }
        return resVoList;
    }

    /**
     * Title: saveFlightInfo<br>
     * Author: 刘志恒<br>
     * Description: 保存补贴航线<br>
     * Date:  2022/8/4 9:41 <br>
     */
    @Override
    public void saveFlightLineInfo(FlightLineInfoForm form, LoginUserDetails user) {
        String airportCode = form.getAirportCode();
        //判断航司二字码是否存在
        String airlineCode = form.getAirlineCode();
        List<AirlineInfoAps> airlineInfoList;
        if ((airlineInfoList = airlineDao.listAirlineByAirlineCode(airlineCode, airportCode)) == null
                || airlineInfoList.isEmpty()) {
            // 抛出航司二字码不存在异常
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_AIRLINE_NULL.getCode(),
                    BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_AIRLINE_NULL.getMsg());
        }
        List<FlightLineInfo> fliList = flightLineInfoDao.getFlightLineInfoByAirlineAndLine(airlineCode, airportCode, form.getFlightLine());
        if (!fliList.isEmpty()) {
            //  抛出航司下航线已存在异常
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_REPEAT.getCode(),
                    BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_REPEAT.getMsg());
        }

        //判断机场三字码是否存在，中文是否对应
        String[] fligthLines = form.getFlightLine().split("-");
        StringBuilder newFlightLine = new StringBuilder();
        StringBuilder newFlightLineCn = new StringBuilder();
        for (String line : fligthLines) {
            List<AirportThreeCharCode> atcs = airportThreeCharCodeDao.getAirportCodeByCode(line);

            if (atcs == null || atcs.isEmpty()) {
                // 抛出传入机场三字码异常
                throw new GenericException(BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_AIRPORT_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_AIRPORT_ERROR.getMsg());

            }
            AirportThreeCharCode atc = atcs.get(0);
            if (newFlightLine.length() != 0) {
                newFlightLine.append("-");
            }
            if (newFlightLineCn.length() != 0) {
                newFlightLineCn.append("-");
            }
            newFlightLine.append(atc.getCode());
            newFlightLineCn.append(atc.getAirportName());
        }
        FlightLineInfo fli = new FlightLineInfo();
        fli.setFlightLine(newFlightLine.toString());
        fli.setFlightLineCn(newFlightLineCn.toString());
        fli.setAirlineCode(airlineCode);
        fli.setAirlineShortName(form.getAirlineShortName());
        fli.setAirportCode(airportCode);
        String budget = form.getBudget();
        if (CharSequenceUtil.isNotBlank(budget) && !isDouble(budget)) {
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_BUDGET_DOUBLE_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_BUDGET_DOUBLE_ERROR.getMsg());
        }
        fli.setBudget(CharSequenceUtil.isBlank(budget) ? BigDecimal.ZERO : BigDecimal.valueOf(Double.parseDouble(budget)));
        fli.setUsed(BigDecimal.ZERO);

        fli.setCreateBy(user.getUsername());
        fli.setCreateTime(new Date());
        fli.setModifiedBy(user.getUsername());
        fli.setModifiedTime(new Date());

        flightLineInfoDao.save(fli);
    }


    @Transactional
    @Override
    public void deleteFlightLine(String id, LoginUserDetails user) {
        //查询id对应航线信息是否存在
        FlightLineInfo fl;
        if ((fl = flightLineInfoDao.getFlightLineInfoById(id)) == null) {
            //  抛出id对应航线为空异常
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_ID_NULL.getCode(),
                    BusinessMessageEnum.SUBSIDY_FLIGHT_LINE_ID_NULL.getMsg());
        }
        fl.setInvalid("0");
        fl.setModifiedTime(new Date());
        fl.setModifiedBy(user.getUsername());
        flightLineInfoDao.save(fl);

        //获取航线关联公式
        List<String> formulaIdList = subsidyFormulaDao.getFormulaIdByLineId(id);
        //删除关联公式
        formulaIdList.forEach(fId -> subsidyFormulaService.deleteFormula(fId, user));
    }

    // 判断整数（int）
    private boolean isInteger(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-+]?\\d*$");
        return pattern.matcher(str).matches();
    }

    // 判断浮点数（double和float）
    private boolean isDouble(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        if (isInteger(str)) {
            return true;
        }
        Pattern pattern = Pattern.compile("^[-+]?\\d*[.]\\d+$");
        return pattern.matcher(str).matches();
    }

}
