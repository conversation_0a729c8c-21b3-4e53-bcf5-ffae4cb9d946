package com.swcares.aiot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.EstimateBillConfirmForm;
import com.swcares.aiot.core.form.EstimateBillPageForm;
import com.swcares.aiot.core.model.dto.HostAviationDownloadDto;
import com.swcares.aiot.core.model.dto.RaiseAnObjectionDto;
import com.swcares.aiot.service.EstimateBillBizService;
import com.swcares.aiot.vo.EstimateBillPageVo;
import com.swcares.aiot.vo.EstimateBillProveRecordVo;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ClassName：com.swcares.controller.EstimateBillBizController
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/07/16
 * @version v1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/estimateBill")
@Api(value = "EstimateBillController", tags = {"离港返还对账接口"})
public class EstimateBillBizController {

    @Resource
    private EstimateBillBizService estimateBillBizService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询离港返还账单")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ResultBuilder.class)})
    public ResultBuilder<IPage<EstimateBillPageVo>> page(@Validated @RequestBody EstimateBillPageForm form) {
        return new ResultBuilder.Builder<IPage<EstimateBillPageVo>>().data(estimateBillBizService.page(form)).builder();
    }

    @PostMapping(value = "/confirm/{estimateBillId}")
    @ApiOperation(value = "确认账单")
    public ResultBuilder<Object> confirm(@PathVariable("estimateBillId") Long estimateBillId, @RequestBody @Validated EstimateBillConfirmForm form) {
        form.setId(estimateBillId);
        estimateBillBizService.confirm(form);
        return new ResultBuilder.Builder<>().builder();
    }

    @GetMapping("/getEstimateBillProveList/{id}")
    @ApiOperation(value = "账单存证列表")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ResultBuilder.class)})
    public ResultBuilder<List<EstimateBillProveRecordVo>> getEstimateBillProveList(@PathVariable("id") String id) {
        return new ResultBuilder.Builder<List<EstimateBillProveRecordVo>>().data(estimateBillBizService.getEstimateBillProveList(id)).builder();
    }

    @PostMapping("/hostAviationFileDownload")
    @ApiOperation(value = "HOST航账单文件下载")
    public void hostAviationFileDownload(@RequestBody @Validated HostAviationDownloadDto dto, HttpServletResponse response) throws IOException {
        estimateBillBizService.hostAviationFileDownload(dto, response);
    }

    @PostMapping("/raiseAnObjection/{estimateBillId}")
    @ApiOperation(value = "提异议")
    public BaseResult<Object> raiseAnObjection(@PathVariable("estimateBillId") Long estimateBillId, @RequestBody @Validated RaiseAnObjectionDto dto) {
        dto.setEstimateBillId(estimateBillId);
        estimateBillBizService.raiseAnObjection(dto);
        return BaseResult.ok();
    }

    @PostMapping("/hostAviationFilePreview")
    @ApiOperation(value = "HOST航账单文件预览")
    public BaseResult<Object> hostAviationFilePreview(@RequestBody @Validated HostAviationDownloadDto dto) {
        return BaseResult.ok(estimateBillBizService.hostAviationFilePreview(dto));
    }

}
