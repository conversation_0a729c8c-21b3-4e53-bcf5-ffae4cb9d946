package com.swcares.aiot.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.client.IErPurseBizClient;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.model.dto.ErPurseDto;
import com.swcares.aiot.core.model.dto.ErPursePageDto;
import com.swcares.aiot.core.model.vo.ErPursePageVo;
import com.swcares.aiot.core.model.vo.ErPursePeVo;
import com.swcares.aiot.core.model.vo.ErPurseVo;
import com.swcares.aiot.service.PurseService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.sys.util.ConfigUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.impl.PurseServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/28 11:30
 * @version v1.0
 */
@Service
public class PurseServiceImpl implements PurseService {

    @Resource
    private IErPurseBizClient erPurseBizClient;

    @Override
    public BaseResult<Page<ErPursePageVo>> page(ErPursePageDto erPursePageDto) {
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG,CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
        erPursePageDto.setPurseStats(airportCode);
        return erPurseBizClient.airportPage(erPursePageDto);
    }

    @Override
    public BaseResult<Boolean> addErPurse(ErPurseDto erPurseDto) {
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
        erPurseDto.setPurseStats(airportCode);
        return erPurseBizClient.addErPurseAirport(erPurseDto);
    }

    @Override
    public BaseResult<Boolean> updateErPurse(ErPurseDto erPurseDto) {
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
        erPurseDto.setPurseStats(airportCode);
        return erPurseBizClient.updateErPurse(erPurseDto);
    }

    @Override
    public BaseResult<ErPurseVo> getById(Long id) {
        return erPurseBizClient.getById(id);
    }

    @Override
    public BaseResult<List<ErPursePeVo>> getErPursePeList() {
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
        return BaseResult.ok(erPurseBizClient.getAirportErPursePeList(airportCode));
    }
}
