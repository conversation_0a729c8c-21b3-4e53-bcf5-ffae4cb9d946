package com.swcares.aiot.core.importer.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Lists;
import com.swcares.aiot.core.common.util.SsssUtil;
import com.swcares.aiot.core.importer.service.ImportToAirlineService;
import com.swcares.aiot.core.model.entity.AircraftInfo;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.dao.AircraftDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.importer.service.impl.ImportToAirlineServiceImpl
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/1/23 10:18
 * @version v1.0
 */
@Service
@Slf4j
public class ImportToAirlineServiceImpl implements ImportToAirlineService {

    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private AircraftDao aircraftDao;
    @Resource
    private SsssUtil ssssUtil;

    @Async
    @Override
    public void sendToAirline(String fiId, Long tenantId) {
        TenantHolder.setTenant(tenantId);
        FlightInfo fi = this.flightInfoDao.getFlightInfoById(fiId);
        if (fi == null) {
            return;
        }
        String settleCode = null;
        if (CharSequenceUtil.isBlank(fi.getRegNo())) {
            return;
        }
        List<AircraftInfo> aircraftInfoList = this.aircraftDao.findByRegNo(fi.getRegNo());
        Date flightDate = fi.getFlightDate();
        for (AircraftInfo ai : aircraftInfoList) {
            if (ai.getStartDate().before(flightDate) && ai.getEndDate().after(flightDate)) {
                settleCode = ai.getSettleCode();
                break;
            }
        }
        if (settleCode == null) {
            return;
        }
        try {
            this.ssssUtil.send("FLIGHT_INFO", fi.getAirportCode(), Collections.singletonList(settleCode),
                    Collections.singletonList(fi));
        } catch (Exception e) {
            ImportToAirlineServiceImpl.log.error("出现业务异常", e);
        }
        TenantHolder.clear();
    }

    @Override
    @Async
    public void sendToAirline(List<FlightInfo> flightInfoList, Long tenant) {
        if (CollectionUtils.isEmpty(flightInfoList)) {
            return;
        }
        try {
            TenantHolder.setTenant(tenant);
            Map<String, List<FlightInfo>> settleCodeFlightInfoListMap = this.querySettleCodeFlightInfoMap(flightInfoList);
            for (Map.Entry<String, List<FlightInfo>> entry : settleCodeFlightInfoListMap.entrySet()) {
                try {
                    List<List<FlightInfo>> lists = Lists.partition(flightInfoList, 800);
                    String airportCode = lists.get(0).get(0).getAirportCode();
                    for (List<FlightInfo> itemList : lists) {
                        this.ssssUtil.send("FLIGHT_INFO", airportCode, Collections.singletonList(entry.getKey()), itemList);
                    }
                } catch (Exception e) {
                    ImportToAirlineServiceImpl.log.error("批量发送航班数据出错,settleCode:{} : {}", entry.getKey(), e.getMessage(), e);
                }

            }

        } catch (Exception e) {
            ImportToAirlineServiceImpl.log.error(e.getMessage(), e);
        } finally {
            TenantHolder.clear();
        }

    }

    @Override
    public void sendToAirlineById(List<String> flightIdList, Long tenant) {
        List<FlightInfo> flightInfoList = this.flightInfoDao.queryFlightDateByIds(new HashSet<>(flightIdList));
        this.sendToAirline(flightInfoList, tenant);
    }


    private Map<String, List<FlightInfo>> querySettleCodeFlightInfoMap(List<FlightInfo> flightInfoList) {
        List<String> regNoList = flightInfoList.stream().map(FlightInfo::getRegNo).collect(Collectors.toList());
        //一个机型有多条？
        List<AircraftInfo> aircraftInfoList = this.aircraftDao.findByRegNoList(regNoList);
        Map<String, List<FlightInfo>> regFlightInfoListMap = flightInfoList.stream().collect(Collectors.groupingBy(FlightInfo::getRegNo,
                Collectors.mapping(t -> t, Collectors.toList())));
        Map<String, List<FlightInfo>> resultMap = new HashMap<>();
        for (AircraftInfo ai : aircraftInfoList) {
            List<FlightInfo> flightInfos = regFlightInfoListMap.get(ai.getRegNo());
            for (FlightInfo item : flightInfos) {
                if (ai.getStartDate().before(item.getFlightDate()) && ai.getEndDate().after(item.getFlightDate())) {
                    resultMap.computeIfAbsent(ai.getSettleCode(), k -> new ArrayList<>()).add(item);
                }
            }
        }
        return resultMap;
    }
}
