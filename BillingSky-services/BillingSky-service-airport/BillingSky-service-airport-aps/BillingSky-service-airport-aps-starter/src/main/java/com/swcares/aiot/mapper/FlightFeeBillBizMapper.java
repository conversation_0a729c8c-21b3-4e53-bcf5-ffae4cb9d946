package com.swcares.aiot.mapper;

import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.entity.ServiceRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface FlightFeeBillBizMapper {
    List<FlightBill> listFlightBillInfoByCondition(@Param("airportCode") String airportCode,
                                                   @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate,
                                                   @Param("flightTimeStartDate") Date flightTimeStartDate,
                                                   @Param("flightTimeEndDate") Date flightTimeEndDate,
                                                   @Param("fromAirportCode") String fromAirportCode,
                                                   @Param("toAirportCode") String toAirportCode,
                                                   @Param("flightNo") String flightNo,
                                                   @Param("choseFeeInfos") List<String> choseFeeInfos,
                                                   @Param("settleCodeList") List<String> settleCodeList,
                                                   @Param("regNo") String regNo,
                                                   @Param("flightFlag") String flightFlag,
                                                   @Param("submit") String submit);


    List<ServiceRecord> listServiceRecordByFlightIdAndServiceCode(@Param("powercarFlightIdList") List<String> powercarFlightIdList,
                                                                  @Param("airportCode") String airportCode,
                                                                  @Param("serviceCode") String serviceCode);

}
