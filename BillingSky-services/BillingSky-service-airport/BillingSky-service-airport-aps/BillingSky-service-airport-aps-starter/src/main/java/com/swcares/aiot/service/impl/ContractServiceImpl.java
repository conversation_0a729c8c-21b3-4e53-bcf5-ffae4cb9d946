package com.swcares.aiot.service.impl;

import com.swcares.aiot.client.IErContractBizClient;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.model.dto.ContractConfirmDto;
import com.swcares.aiot.core.model.dto.ErContractPageDto;
import com.swcares.aiot.core.model.vo.ErContractPageVo;
import com.swcares.aiot.core.model.vo.ErContractVo;
import com.swcares.aiot.service.ContractService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.sys.util.ConfigUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.impl.ContractServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/28 11:29
 * @version v1.0
 */
@Service
public class ContractServiceImpl implements ContractService {

    @Resource
    private IErContractBizClient contractBizClient;

    @Override
    public PagedResult<List<ErContractPageVo>> page(ErContractPageDto erContractPageDto) {
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
        erContractPageDto.setContractAirport(airportCode);
        return contractBizClient.page(erContractPageDto);
    }

    @Override
    public BaseResult<ErContractVo> getById(String id) {
        return contractBizClient.getById(id);
    }

    @Override
    public BaseResult<Boolean> confirm(ContractConfirmDto contractConfirmDto) {
        return contractBizClient.confirm(contractConfirmDto);
    }
}
