package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aiot.core.entity.TServiceRecord;
import com.swcares.aiot.core.model.vo.FlightBusinessDataExcelVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ServiceRecordMapper extends BaseMapper<TServiceRecord> {


    List<FlightBusinessDataExcelVO> exportFlightBusinessData(@Param("airportCode") String airportCode, @Param("startDate")Date startDate,
                                                             @Param("endDate")Date endDate, @Param("flightTimeStartDate") Date flightTimeStartDate,
                                                             @Param("flightTimeEndDate") Date flightTimeEndDate, @Param("airlineCode")String airlineCode,
                                                             @Param("flightNo")String flightNo, @Param("flightFlag")String flightFlag);
}
