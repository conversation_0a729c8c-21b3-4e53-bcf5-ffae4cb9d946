package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.swcares.aiot.ComplexMessage;
import com.swcares.aiot.bell.BellTemplate;
import com.swcares.aiot.bell.model.dto.BelMsgCollateFeedbackSaveDto;
import com.swcares.aiot.core.common.entity.FlightBillStatusDto;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.service.ITFlightBillService;
import com.swcares.aiot.service.FlightBillHistoryService;
import com.swcares.aiot.statemachine.StatemachineTemplate;
import com.swcares.aiot.statemachine.biz.events.EnumBillStatusChangeEvent;
import com.swcares.aiot.statemachine.biz.events.EnumRevocationStatusChangeEvent;
import com.swcares.aiot.synergy.annotation.DataType;
import com.swcares.aiot.synergy.service.IDataSyncService;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.swcares.aiot.core.model.enums.BillOperation.*;

/**
 * <AUTHOR>
 */
@DataType("FLIGHT_BILL_CHECK")
@Slf4j
@Service
public class FlightBillCheckDataSyncService implements IDataSyncService {

    @Resource
    private BellTemplate bellTemplate;
    @Resource
    private StatemachineTemplate<TFlightBill> statemachineTemplate;
    @Resource
    private ITFlightBillService iTFlightBillService;
    @Resource
    private FlightBillHistoryService flightBillHistoryService;

    @Override
    public Long getTenantId(String tenantCode) {
        return ConfigUtil.getLong("signature_config", tenantCode.toUpperCase(), GlobalConstants.TENANT_DEFAULT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncData(ComplexMessage message) {
        List<FlightBillStatusDto> dtos = JSONObject.parseArray(JSONUtil.toJsonStr(message.getPayload().getData()), FlightBillStatusDto.class);
        log.info("收到账单反馈信息：{}", dtos);
        if (CollectionUtil.isEmpty(dtos)) {
            return;
        }
        //查航司简称
        List<String> idList = dtos.stream().map(FlightBillStatusDto::getFlightBillId).collect(Collectors.toList());
        TFlightBill flightBill = iTFlightBillService.getById(idList.get(0));
        // 记录消息
        this.saveMsgCollateFeedback(flightBill.getAirlineShortName(), dtos.size());
        //业务数据处理
        String operation = "";
        for (FlightBillStatusDto dto : dtos) {
            operation = dto.getOperation();
            String subOperation = dto.getSubOperation();
            String billId = dto.getFlightBillId();

            //修改备注
            TFlightBill bill = new TFlightBill();
            bill.setId(dto.getFlightBillId());
            bill.setFeedback(dto.getComment());
            bill.setProveFile(dto.getProveFile());
            iTFlightBillService.updateById(bill);

            EnumBillStatusChangeEvent event = null;
            if (CHECK.getCode().equals(operation)) {
                operation = ACCEPT_FEEDBACK.getCode();
                if (CHECK_CONFIRM.getCode().equals(subOperation)) {
                    event = EnumBillStatusChangeEvent.AIRLINE_AUTOMATIC_RECONCILIATION_OK;
                } else if (CHECK_DISPUTE.getCode().equals(subOperation)) {
                    event = EnumBillStatusChangeEvent.AIRLINE_CONTROVERSIAL;
                }
            } else if (AGREE_CANCEL.getCode().equals(operation)) {
                event = EnumBillStatusChangeEvent.AIRLINE_AGREE_REVOKED;
                statemachineTemplate.updateRevocationStatusEvent(iTFlightBillService, TFlightBill::getRevocation, TFlightBill::getId, billId, EnumRevocationStatusChangeEvent.AIRLINE_AGREE_REVOCATION);
            } else if (REJECT_CANCEL.getCode().equals(operation)) {
                statemachineTemplate.updateRevocationStatusEvent(iTFlightBillService, TFlightBill::getRevocation, TFlightBill::getId, billId, EnumRevocationStatusChangeEvent.AIRLINE_REJECT_REVOCATION);
            }

            if (event != null) {
                statemachineTemplate.updateBillStatusEvent(iTFlightBillService, TFlightBill::getSubmit, TFlightBill::getId, billId, event);
            }
        }

        //历史记录 消息每次过来都只有一种操作类型
        List<List<String>> partition = Lists.partition(idList, 1000);
        for (List<String> ids : partition) {
            flightBillHistoryService.copyBillHistory(ids, null, operation);
        }
    }

    /**
     * 记录消息
     *
     * @param dataSource 数据源头
     * @param size       大小
     */
    private void saveMsgCollateFeedback(String dataSource, int size) {
        BelMsgCollateFeedbackSaveDto belMsgCollateFeedbackSaveDto = new BelMsgCollateFeedbackSaveDto()
                .setRecordNum(String.valueOf(size))
                .setDataSource(dataSource);
        bellTemplate.saveMsgCollateFeedback(belMsgCollateFeedbackSaveDto);
    }

}
