package com.swcares.aiot.core.importer.service.impl;

import cn.hutool.core.date.DateUtil;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.baseframe.common.enums.DeletedEnum;
import com.swcares.aiot.core.importer.dao.ApsFlightInfoDao;
import com.swcares.aiot.core.importer.dao.FlightInfoSegmentDao;
import com.swcares.aiot.core.importer.entity.*;
import com.swcares.aiot.core.importer.service.FlightDataImportService;
import com.swcares.aiot.core.importer.service.ImportService;
import com.swcares.aiot.core.importer.service.ImportToAirlineService;
import com.swcares.aiot.core.model.entity.AircraftInfo;
import com.swcares.aiot.dao.AircraftDao;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * ClassName：com.swcares.importer.service.impl.FlightDataImportServiceImpl
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/9/16 10:25
 * @version v1.0
 */
@Slf4j
@Service
public class FlightDataImportServiceImpl implements FlightDataImportService {
    @Resource
    private AircraftDao aircraftDao;
    @Resource
    private ApsFlightInfoDao apsFlightInfoDao;
    @Resource
    private FlightInfoSegmentDao flightInfoSegmentDao;
    @Resource
    private ImportService importService;
    @Resource
    private ImportToAirlineService importToAirlineService;

    @Transactional
    @Override
    public void importFlightData(BaseFlightInfo bfi, boolean firstParse) {
        if (checkFlightNull(bfi)) {
            return;
        }
        String id = "" + bfi.getId();
        String flightFlag = "1".equals(bfi.getIsArrv()) ? "A" : "D";
        Date flightDate = Date.from(
                bfi.getFlightDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        String flightNo = bfi.getFlightNo();
        String airportCode = bfi.getAirportCode();
        String flightInfoFlag = DateUtils.format(flightDate) + "_" + flightNo + "_" + flightFlag + "_"
                + airportCode;
        log.info("{}航班开始导入航班数据+++++++++++++++++++++++++++++++++++", flightInfoFlag);
        try {
            // 货邮行数据可能比航班数据先到
            ApsFlightInfo getFi = apsFlightInfoDao.getFlightInfoById(id);
            ApsFlightInfo fi = new ApsFlightInfo();
            // 如果根据id查不到航班信息，则根据日期、航班号、起降标识、机场查询
            ApsFlightInfo origin = new ApsFlightInfo();
            setId(getFi, fi, id, origin);
            fi.setModifiedBy("数据中台航班自动导入");
            fi.setModifiedTime(new Date());

            fi.setFlightDate(flightDate);
            fi.setFlightNo(flightNo);
            fi.setFlightFlag(flightFlag);
            fi.setAirportCode(airportCode);

            // 停场时间处理
            stayTimeHand(fi, bfi);
            String status = Strings.isBlank(bfi.getStatus()) ? "" : bfi.getStatus();

            if (judgeDeleteCondition(bfi, status, fi.getDataStatus())) {
                log.info("暂存确认后的数据******");
                // 如果航班数据已确认，将航班数据暂存到数据库中，等航班确认时再同步更新
                importService.cacheFlightData(bfi, fi, flightInfoFlag, airportCode, flightFlag, origin, fi.getDataStatus());
                return;
            }

            // 判断航班是否逻辑删除,或者取消,或者备降时航线到港当前机场
            if (checkDeleted(bfi, status)) {
                //删除航班
                log.info("删除航班******");
                fi.setInvalid("0");
            } else {
                //如果航线为空，则用历史数据填充
                setFlightline(bfi, flightNo, flightFlag, fi);
                //设置起降时间
                setFlightTime(bfi, fi);
                //设置航班信息
                setInfo(bfi, fi, status, flightFlag);
            }
            apsFlightInfoDao.saveAndFlush(fi);
            //推送到航司端
            importToAirlineService.sendToAirline(fi.getId(), TenantHolder.getTenant());

        } catch (Exception e) {
            log.error("aps消费数据中心的航班数据失败，航班信息{}，失败原因：", flightInfoFlag, e);
        } finally {
            log.info("{}_{}_{}_{}航班导入航班数据结束+++++++++++++++++++++++++++++++++++", DateUtils.format(flightDate), flightNo, flightFlag, airportCode);
        }

    }

    private void setInfo(BaseFlightInfo bfi, ApsFlightInfo fi, String status, String flightFlag) {
        //未删除航班
        fi.setInvalid("1");
        // 航线状态为空默认为国内
        fi.setFlightLineType(StringUtil.isBlank(bfi.getAirlineProperty()) ? null : bfi.getAirlineProperty());
        fi.setFlightSegment(bfi.getFlightSegment());
        fi.setFlightSegmentType(StringUtil.isBlank(bfi.getFlightSegmentProperty()) ? null : bfi.getFlightSegmentProperty());

        fi.setFlightType(fi.getFlightSegmentType());
        fi.setAirlineCode(bfi.getAirlineCode());
        fi.setRegNo(bfi.getAircraftNo());
        fi.setFlightModel(bfi.getAircraftModel());
        fi.setFromAirportCode(bfi.getDepartureAirportCode());
        fi.setToAirportCode(bfi.getDestinationAirportCode());

        if ("alt".equalsIgnoreCase(status) && "A".equals(flightFlag)) {
            fi.setFlightStatus("alt");
        } else {
            fi.setFlightStatus(bfi.getPublishStatus());
        }
        fi.setTaskFlag(bfi.getTask());


        fi.setPreFlightId(
                bfi.getConnectFlightId() == null ? null : "" + bfi.getConnectFlightId());


        // 如果航班旅客数已同步，则根据机号计算出客座率
        if ((fi.getPlf() == null || fi.getPlf() == 0.0) && fi.getPsgNumber() != null) {
            getPlf(fi);
        }
    }

    private void setFlightTime(BaseFlightInfo bfi, ApsFlightInfo fi) {
        LocalDateTime bFlightTime = getbFlightTime(bfi, fi);
        if (bFlightTime != null) {
            // 将此日期时间与时区相结合以创建 ZonedDateTime
            ZonedDateTime zonedDateTime = bFlightTime.atZone(ZoneId.systemDefault());
            // 本地时间线LocalDateTime到即时时间线Instant时间戳
            Instant instant = zonedDateTime.toInstant();
            // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
            fi.setFlightTime(Date.from(instant));
        }
    }

    private void setFlightline(BaseFlightInfo bfi, String flightNo, String flightFlag, ApsFlightInfo fi) {
        if (Strings.isBlank(bfi.getAirline())) {
            List<ApsFlightInfo> flightLineList =
                    apsFlightInfoDao.listFlightInfoByFlightNoAndFlag(flightNo, flightFlag);
            for (ApsFlightInfo flightLineInfo : flightLineList) {
                if (Strings.isNotBlank(flightLineInfo.getFlightLine())) {
                    fi.setFlightLine(flightLineInfo.getFlightLine());
                    break;
                }
            }
        } else {
            fi.setFlightLine(bfi.getAirline());
        }
    }

    private static boolean checkDeleted(BaseFlightInfo bfi, String status) {
        return DeletedEnum.DELETED.equals(bfi.getDeleted()) || DeletedEnum.DELETED.equals(bfi.getAcdmDeleted())
                || "can".equalsIgnoreCase(status);
    }

    private static void setId(ApsFlightInfo getFi, ApsFlightInfo fi, String id, ApsFlightInfo origin) {
        if (getFi == null) {
            fi.setId(id);
            fi.setCreateBy("数据中台航班自动导入");
            fi.setCreateTime(new Date());
        } else {
            BeanUtils.copyProperties(getFi, fi);
            BeanUtils.copyProperties(fi, origin);
        }
    }

    private static boolean checkFlightNull(BaseFlightInfo bfi) {
        return bfi == null || bfi.getId() == null || bfi.getIsArrv() == null
                || bfi.getFlightDate() == null || bfi.getFlightNo() == null
                || bfi.getAirportCode() == null;
    }

    private LocalDateTime getbFlightTime(BaseFlightInfo bfi, ApsFlightInfo fi) {
        LocalDateTime bFlightTime = null;
        // 解析起降时间
        // 如果为降落航班
        if ("A".equals(fi.getFlightFlag())) {
            if (bfi.getRealLandingDatetime() != null) {
                bFlightTime = bfi.getRealLandingDatetime();
            } else if (bfi.getPredictLandingDatetime() != null) {
                bFlightTime = bfi.getPredictLandingDatetime();
            } else if (bfi.getPlanLandingDatetime() != null) {
                bFlightTime = bfi.getPlanLandingDatetime();
            }
        } else {
            if (bfi.getRealTakeOffDatetime() != null) {
                bFlightTime = bfi.getRealTakeOffDatetime();
            } else if (bfi.getPredictTakeOffDatetime() != null) {
                bFlightTime = bfi.getPredictTakeOffDatetime();
            } else if (bfi.getPlanTakeOffDatetime() != null) {
                bFlightTime = bfi.getPlanTakeOffDatetime();
            }
        }
        return bFlightTime;
    }

    private boolean judgeDeleteCondition(BaseFlightInfo bfi, String status, Integer dataStatus) {
        //数据状态已确认或者已修改，且航班状态非异常
        return dataStatus != 0 && !(bfi.getDeleted().equals(DeletedEnum.DELETED) || bfi.getAcdmDeleted().equals(DeletedEnum.DELETED) || "can".equalsIgnoreCase(status));
    }

    @Transactional
    @Override
    public void importFlightCargo(BaseFlightCargo bfc, boolean firstParse) {
        if (bfc == null || bfc.getBaseFlightId() == null || bfc.getLandFlag() == null
                || bfc.getFlightDate() == null || bfc.getFlightNo() == null
                || bfc.getAirportCode() == null) {
            return;
        }
        String flightId = String.valueOf(bfc.getBaseFlightId());
        String flightFlag = "1".equals(bfc.getLandFlag()) ? "A" : "D";
        Date flightDate = Date.from(bfc.getFlightDate().atZone(ZoneId.systemDefault()).toInstant());
        String flightNo = bfc.getFlightNo();
        String airportCode = bfc.getAirportCode();
        String flightInfoFlag = DateUtils.format(flightDate) + "_" + flightNo + "_" + flightFlag + "_" + airportCode;
        log.info("{}  航班开始导入货邮行数据=====================================", flightInfoFlag);
        try {
            // 如果航班信息还未传输
            ApsFlightInfo getFi = apsFlightInfoDao.getFlightInfoById(flightId);
            ApsFlightInfo fi = new ApsFlightInfo();
            // 如果根据id查不到航班信息，则根据日期、航班号、起降标识、机场查询
            ApsFlightInfo origin = new ApsFlightInfo();
            // 如果根据id查不到航班信息，则根据日期、航班号、起降标识、机场查询
            if (getFi == null) {
                fi.setId(flightId);
                fi.setFlightDate(flightDate);
                fi.setFlightNo(flightNo);
                fi.setFlightFlag(flightFlag);
                fi.setAirportCode(airportCode);

                fi.setCreateBy("数据中台货邮行自动导入");
                fi.setCreateTime(new Date());
            } else {
                BeanUtils.copyProperties(getFi, fi);
                BeanUtils.copyProperties(fi, origin);
            }

            fi.setModifiedBy("数据中台货邮行自动导入");
            fi.setModifiedTime(new Date());

            fi.setCargo((double) (bfc.getCargo() == null ? 0 : bfc.getCargo()));
            fi.setBag((double) (bfc.getBag() == null ? 0 : bfc.getBag()));
            fi.setMail((double) (bfc.getMail() == null ? 0 : bfc.getMail()));

            fi.setBagNumber(bfc.getBagNum() == null ? 0 : bfc.getBagNum());

            fi.setWeightUints("KG");

            if (!fi.getDataStatus().equals(0)) {
                importService.cacheFlightCargoTravelerData(fi, origin, false, fi.getDataStatus());
                return;
            }

            apsFlightInfoDao.save(fi);
            apsFlightInfoDao.flush();
            //推送到航司端
            importToAirlineService.sendToAirline(fi.getId(), TenantHolder.getTenant());
        } catch (Exception e) {
            log.error("aps消费数据中心的货邮数据失败，航班信息{}，失败原因：", flightInfoFlag, e);
        } finally {
            log.info("{}_{}_{}_{}  航班导入货邮行数据结束=====================================", DateUtils.format(flightDate), flightNo, flightFlag, airportCode);
        }
    }

    @Transactional
    @Override
    public void importFlightTraveler(BaseFlightTraveler bft, boolean firstParse) {
        if (checkFlightTravelerInfo(bft)) {
            return;
        }
        String flightId = String.valueOf(bft.getBaseFlightId());
        String flightFlag = "1".equals(bft.getLandFlag()) ? "A" : "D";
        Date flightDate = Date.from(bft.getFlightDate().atZone(ZoneId.systemDefault()).toInstant());
        String flightNo = bft.getFlightNo();
        String airportCode = bft.getAirportCode();
        String flightInfoFlag = DateUtils.format(flightDate) + "_" + flightNo + "_" + flightFlag + "_"
                + airportCode;
        log.info("{}  航班开始导入旅客数据-----------------------------------------", flightInfoFlag);
        try {
            // 如果航班信息还未传输
            ApsFlightInfo fiGet = apsFlightInfoDao.getFlightInfoById(flightId);
            ApsFlightInfo fi = new ApsFlightInfo();
            // 如果根据id查不到航班信息，则根据日期、航班号、起降标识、机场查询
            ApsFlightInfo origin = new ApsFlightInfo();
            if (fiGet == null) {
                fi.setId(flightId);
                fi.setFlightDate(flightDate);
                fi.setFlightNo(flightNo);
                fi.setFlightFlag(flightFlag);
                fi.setAirportCode(airportCode);

                fi.setCreateBy("数据中台旅客数据自动导入");
                fi.setCreateTime(new Date());
            } else {
                BeanUtils.copyProperties(fiGet, fi);
                BeanUtils.copyProperties(fi, origin);
            }

            fi.setModifiedBy("数据中台旅客数据自动导入");
            fi.setModifiedTime(new Date());
            // 填充旅客信息
            setTraveler(bft, fi);
            // 填充特殊旅客信息
            setSpecialTraveler(bft, fi);

            // 填充客座率
            if (bft.getPsgrPlf() != null && bft.getPsgrPlf().compareTo(BigDecimal.ZERO) != 0) {
                fi.setPlf(100 * bft.getPsgrPlf().doubleValue());
            } else if (fi.getRegNo() != null) {
                // 如果航班机号信息已同步，则根据机号计算出客座率
                getPlf(fi);
            } else {
                fi.setPlf(0.0);
            }

            if (!fi.getDataStatus().equals(0)) {
                importService.cacheFlightCargoTravelerData(fi, origin, true, fi.getDataStatus());
                return;
            }

            apsFlightInfoDao.save(fi);
            apsFlightInfoDao.flush();
            //推送到航司端
            importToAirlineService.sendToAirline(fi.getId(), TenantHolder.getTenant());
        } catch (Exception e) {
            log.error("aps消费数据中心的旅客数据失败，航班信息{}，失败原因：", flightInfoFlag, e);
        } finally {
            log.info("{}_{}_{}_{}  航班导入旅客数据结束-----------------------------------------", DateUtils.format(flightDate), flightNo, flightFlag, airportCode);
        }
    }

    private static void setSpecialTraveler(BaseFlightTraveler bft, ApsFlightInfo fi) {
        // 商务舱旅客
        fi.setClubClassNumber(
                bft.getBusinessClassNum() == null ? 0 : bft.getBusinessClassNum());
        // 头等舱
        fi.setFirstClassNumber(bft.getFirstClassNum() == null ? 0 : bft.getFirstClassNum());
        // 经济舱
        fi.setEconomyClassNumber(
                bft.getEconomyClassNum() == null ? 0 : bft.getEconomyClassNum());
        // 持外交护照旅客
        fi.setDiplomaticPassportNumber(bft.getDiplomaticPassportTravelerNum() == null ? 0
                : bft.getDiplomaticPassportTravelerNum());
        // "持卡旅客
        fi.setCardHolderNumber(
                bft.getCardholdersTravelerNum() == null ? 0 : bft.getCardholdersTravelerNum());
        // 持卡随行旅客
        fi.setAccompanyingCardHolderNumber(bft.getCardholdersAccompanyTravelerNum() == null ? 0
                : bft.getCardholdersAccompanyTravelerNum());
        // 重要旅客
        fi.setImportantNumber(
                bft.getImportantTravelerNum() == null ? 0 : bft.getImportantTravelerNum());
        // 重要随行旅客
        fi.setAccompanyingImportantNumber(bft.getImportantAccompanyTravelerNum() == null ? 0
                : bft.getImportantAccompanyTravelerNum());
    }

    private static void setTraveler(BaseFlightTraveler bft, ApsFlightInfo fi) {
        // 设置成人儿童婴儿
        Integer adultNum = bft.getAdultNum() == null ? 0 : bft.getAdultNum();
        Integer childNum = bft.getChildNum() == null ? 0 : bft.getChildNum();
        Integer infantNum = bft.getInfantNum() == null ? 0 : bft.getInfantNum();
        fi.setAdultNumber(adultNum);
        fi.setChildNumber(childNum);
        fi.setInfantNumber(infantNum);

        //过站成人儿童婴儿
        Integer tAdultNum = bft.getTransitAdultNum() == null ? 0 : bft.getTransitAdultNum();
        Integer tChildNum = bft.getTransitChildNum() == null ? 0 : bft.getTransitChildNum();
        Integer tInfantNum = bft.getTransitInfantNum() == null ? 0 : bft.getTransitInfantNum();
        fi.setTransitAdultNumber(tAdultNum);
        fi.setTransitChildNumber(tChildNum);
        fi.setTransitInfantNumber(tInfantNum);
        // 设置旅客总人数，如果为空或0，则手动计算
        fi.setPsgNumber(adultNum + childNum + infantNum);
    }

    private static boolean checkFlightTravelerInfo(BaseFlightTraveler bft) {
        return bft == null || bft.getBaseFlightId() == null || bft.getLandFlag() == null
                || bft.getFlightDate() == null || bft.getFlightNo() == null
                || bft.getAirportCode() == null;
    }


    @Override
    public void importSplitFlightCargo(BaseFlightCargoSegment bfc) {
        if (checkFlightCargoInfo(bfc)) {
            return;
        }
        String flightId = String.valueOf(bfc.getBaseFlightId());
        String flightFlag = "1".equals(bfc.getLandFlag()) ? "A" : "D";
        Date flightDate = Date.from(bfc.getFlightDate().atZone(ZoneId.systemDefault()).toInstant());
        String flightNo = bfc.getFlightNo();
        String airportCode = bfc.getAirportCode();
        String flightInfoFlag = DateUtils.format(flightDate) + "_" + flightNo + "_" + flightFlag + "_"
                + airportCode;
        log.info("{}  航班开始导入拆分航段后货邮行数据=====================================", flightInfoFlag);
        try {
            // 如果航班信息还未传输
            FlightInfoSegment fi = flightInfoSegmentDao.getFlightInfoByFlightIdAnd(flightId, bfc.getIsNear());
            // 如果根据id查不到航班信息，则根据日期、航班号、起降标识、机场查询
            if (fi == null) {
                fi = new FlightInfoSegment();

                fi.setFlightDate(flightDate);
                fi.setFlightNo(flightNo);
                fi.setFlightFlag(flightFlag);
                fi.setAirportCode(airportCode);
                fi.setFlightSegment(bfc.getFlightSegment());
                fi.setBaseFlightId(flightId);
                fi.setIsNear(bfc.getIsNear());

                fi.setCreateBy("数据中台拆分航段后货邮行自动导入");
                fi.setCreateTime(new Date());
            }

            fi.setModifiedBy("数据中台拆分航段后货邮行自动导入");
            fi.setModifiedTime(new Date());

            fi.setCargo((double) (bfc.getCargo() == null ? 0 : bfc.getCargo()));
            fi.setBag((double) (bfc.getBag() == null ? 0 : bfc.getBag()));
            fi.setMail((double) (bfc.getMail() == null ? 0 : bfc.getMail()));
            fi.setBagNumber(bfc.getBagNum() == null ? 0.0 : bfc.getBagNum());

            fi.setWeightUints("KG");

            ApsFlightInfo afi = apsFlightInfoDao.getFlightInfoById(fi.getBaseFlightId());
            if (afi != null && afi.getDataStatus().equals(1)) {
                importService.cacheSplitFlightCargoTravelerData(fi, afi);
                return;
            }

            flightInfoSegmentDao.save(fi);
            flightInfoSegmentDao.flush();
        } catch (Exception e) {
            log.error("aps消费数据中心的拆分货邮数据失败，航班信息{}，失败原因：", flightInfoFlag, e);
        } finally {
            log.info("{}_{}_{}_{}  航班导入拆分航段后货邮行数据结束=====================================", DateUtils.format(flightDate), flightNo, flightFlag, airportCode);
        }
    }

    private static boolean checkFlightCargoInfo(BaseFlightCargoSegment bfc) {
        return bfc == null || bfc.getBaseFlightId() == null || bfc.getLandFlag() == null
                || bfc.getFlightDate() == null || bfc.getFlightNo() == null
                || bfc.getAirportCode() == null;
    }

    @Override
    public void importSplitFlightTraveler(BaseFlightTravelerSegment bft) {
        if (bft == null || bft.getBaseFlightId() == null || bft.getLandFlag() == null
                || bft.getFlightDate() == null || bft.getFlightNo() == null
                || bft.getAirportCode() == null) {
            return;
        }
        String flightId = String.valueOf(bft.getBaseFlightId());
        String flightFlag = "1".equals(bft.getLandFlag()) ? "A" : "D";
        Date flightDate = Date.from(bft.getFlightDate().atZone(ZoneId.systemDefault()).toInstant());
        String flightNo = bft.getFlightNo();
        String airportCode = bft.getAirportCode();
        String flightInfoFlag = DateUtils.format(flightDate) + "_" + flightNo + "_" + flightFlag + "_"
                + airportCode;
        log.info("{}  航班开始导入拆分航段后旅客数据-----------------------------------------", flightInfoFlag);
        try {
            // 如果航班信息还未传输
            FlightInfoSegment fi = flightInfoSegmentDao.getFlightInfoByFlightIdAnd(flightId, bft.getIsNear());
            // 如果根据id查不到航班信息，则根据日期、航班号、起降标识、机场查询
            if (fi == null) {
                fi = new FlightInfoSegment();
                fi.setFlightDate(flightDate);
                fi.setFlightNo(flightNo);
                fi.setFlightFlag(flightFlag);
                fi.setAirportCode(airportCode);
                fi.setFlightSegment(bft.getFlightSegment());
                fi.setBaseFlightId(flightId);
                fi.setIsNear(bft.getIsNear());

                fi.setCreateBy("数据中台拆分航段后旅客数据自动导入");
                fi.setCreateTime(new Date());
            }


            fi.setModifiedBy("数据中台拆分航段后旅客数据自动导入");
            fi.setModifiedTime(new Date());
            // 填充拆分旅客信息
            setSegmentTraveler(bft, fi);
            // 填充拆分特殊旅客信息
            setSpecialTraveler(bft, fi);

            // 填充客座率
            if (bft.getPsgrPlf() != null && bft.getPsgrPlf().compareTo(BigDecimal.ZERO) != 0) {
                fi.setPlf(100 * bft.getPsgrPlf().doubleValue());
            } else {
                fi.setPlf(0.0);
            }

            ApsFlightInfo afi = apsFlightInfoDao.getFlightInfoById(fi.getBaseFlightId());
            if (afi != null && afi.getDataStatus().equals(1)) {
                importService.cacheSplitFlightCargoTravelerData(fi, afi);
                return;
            }

            flightInfoSegmentDao.save(fi);
            flightInfoSegmentDao.flush();
        } catch (Exception e) {
            log.error("aps消费数据中心的拆分旅客数据失败，航班信息{}，失败原因：", flightInfoFlag, e);
        } finally {
            log.info("{}_{}_{}_{}  航班导入拆分航段后旅客数据结束-----------------------------------------", DateUtils.format(flightDate), flightNo, flightFlag, airportCode);
        }
    }

    private static void setSpecialTraveler(BaseFlightTravelerSegment bft, FlightInfoSegment fi) {
        // 商务舱旅客
        fi.setClubClassNumber(
                bft.getBusinessClassNum() == null ? 0 : bft.getBusinessClassNum());
        // 头等舱
        fi.setFirstClassNumber(bft.getFirstClassNum() == null ? 0 : bft.getFirstClassNum());
        // 经济舱
        fi.setEconomyClassNumber(
                bft.getEconomyClassNum() == null ? 0 : bft.getEconomyClassNum());
        // 持外交护照旅客
        fi.setDiplomaticPassportNumber(bft.getDiplomaticPassportTravelerNum() == null ? 0
                : bft.getDiplomaticPassportTravelerNum());
        // "持卡旅客
        fi.setCardHolderNumber(
                bft.getCardholdersTravelerNum() == null ? 0 : bft.getCardholdersTravelerNum());
        // 持卡随行旅客
        fi.setAccompanyingCardHolderNumber(bft.getCardholdersAccompanyTravelerNum() == null ? 0
                : bft.getCardholdersAccompanyTravelerNum());
        // 重要旅客
        fi.setImportantNumber(
                bft.getImportantTravelerNum() == null ? 0 : bft.getImportantTravelerNum());
        // 重要随行旅客
        fi.setAccompanyingImportantNumber(bft.getImportantAccompanyTravelerNum() == null ? 0
                : bft.getImportantAccompanyTravelerNum());
    }

    private static void setSegmentTraveler(BaseFlightTravelerSegment bft, FlightInfoSegment fi) {
        // 设置成人儿童婴儿
        Integer adultNum = bft.getAdultNum() == null ? 0 : bft.getAdultNum();
        Integer childNum = bft.getChildNum() == null ? 0 : bft.getChildNum();
        Integer infantNum = bft.getInfantNum() == null ? 0 : bft.getInfantNum();
        fi.setAdultNumber(adultNum);
        fi.setChildNumber(childNum);
        fi.setInfantNumber(infantNum);

        //过站成人儿童婴儿
        Integer tAdultNum = bft.getTransitAdultNum() == null ? 0 : bft.getTransitAdultNum();
        Integer tChildNum = bft.getTransitChildNum() == null ? 0 : bft.getTransitChildNum();
        Integer tInfantNum = bft.getTransitInfantNum() == null ? 0 : bft.getTransitInfantNum();
        fi.setTransitAdultNumber(tAdultNum);
        fi.setTransitChildNumber(tChildNum);
        fi.setTransitInfantNumber(tInfantNum);
        // 设置旅客总人数，如果为空或0，则手动计算
        fi.setPsgNumber(adultNum + childNum + infantNum);
    }

    /**
     * Title: 计算客座率<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/9/21 17:25 <br>
     */
    private void getPlf(ApsFlightInfo fi) {
        // 通过飞机注册号和航司二字码获取飞机信息
        AircraftInfo aircraftInfo = aircraftDao.getAircraftInfoByRegNoAndAirlineCode(fi.getRegNo(),
                fi.getFlightNo().substring(0, 2), new Date());
        if (aircraftInfo == null || aircraftInfo.getMaxSeat() == null) {
            return;
        }
        if (aircraftInfo.getMaxSeat() == 0) {
            fi.setPlf(0d);
            return;
        }
        Integer adultNumber = fi.getAdultNumber() == null ? 0 : fi.getAdultNumber();
        Integer childNumber = fi.getChildNumber() == null ? 0 : fi.getChildNumber();
        Integer transitAdultNumber = fi.getTransitAdultNumber() == null ? 0 : fi.getTransitAdultNumber();
        Integer transitChildNumber = fi.getTransitChildNumber() == null ? 0 : fi.getTransitChildNumber();
        double plf =
                FormatUtils.formatData((double) (adultNumber + childNumber + transitAdultNumber + transitChildNumber) / (double) aircraftInfo.getMaxSeat());
        fi.setPlf(100 * plf);
    }


    /**
     * Title：stayTimeHand
     * Description：赋值停场时间
     * author：李军呈
     * date： 2024/12/19 17:41
     * @param fi 航班信息
     */
    private void stayTimeHand(ApsFlightInfo fi, BaseFlightInfo bfi) {
        if (Objects.nonNull(bfi.getStayStartTime()) && Objects.nonNull(bfi.getStayEndTime())) {
            fi.setStayTime(bfi.getStayTime());
            fi.setStayStartTime(DateUtil.parseDateTime(DateUtil.formatLocalDateTime(bfi.getStayStartTime())));
            fi.setStayEndTime(DateUtil.parseDateTime(DateUtil.formatLocalDateTime(bfi.getStayEndTime())));
        } else {
            fi.setStayTime(BigDecimal.ZERO);
            fi.setStayStartTime(null);
            fi.setStayEndTime(null);
        }
    }

}
