package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.client.IConfDictMgtBizClient;
import com.swcares.aiot.core.service.ITFlightInfoService;
import com.swcares.aiot.mapper.FlightInfoManageBizMapper;
import com.swcares.aiot.model.vo.ConfDictCacheVo;
import com.swcares.aiot.service.IFlightInfoManageBizService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.components.api.bd.BaseDataCommonRemoteService;
import com.swcares.components.bd.vo.AirlineInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IFlightInfoManageBizServiceImpl implements IFlightInfoManageBizService {
    @Resource
    private ITFlightInfoService itFlightInfoService;
    @Resource
    private BaseDataCommonRemoteService baseDataCommonRemoteService;
    @Resource
    private FlightInfoManageBizMapper flightInfoManageBizMapper;

    @Resource
    private IConfDictMgtBizClient iConfDictMgtBizClient;


    @Override
    public List<String> retrieveAirlineCodes() {
        return flightInfoManageBizMapper.retrieveAirlineSettleCodes();
    }

    @Override
    public List<String> getAirlineNames(List<String> allAirlineCodes, List<String> expenseAirlineCodes) {
        List<String> diff = CollUtil.subtractToList(allAirlineCodes, expenseAirlineCodes);
        log.info("差集:{}", diff);
        BaseResult<List<ConfDictCacheVo>> dict = iConfDictMgtBizClient.getDict("APS_SETTLE_CODE_AIRLINE_ABBR", "0");
        List<ConfDictCacheVo> data = dict.getData();
        return diff.stream()
                .map(airlineSettleCode -> data.stream()
                        .filter(e -> {
                            String settleCode = e.getDictKey();
                            return !Objects.isNull(settleCode) && settleCode.equalsIgnoreCase(airlineSettleCode);
                        })
                        .findFirst()
                        .orElse(null))
                .filter(Objects::nonNull)
                .map(ConfDictCacheVo::getDictValue)
                .collect(Collectors.toList());
    }

}
