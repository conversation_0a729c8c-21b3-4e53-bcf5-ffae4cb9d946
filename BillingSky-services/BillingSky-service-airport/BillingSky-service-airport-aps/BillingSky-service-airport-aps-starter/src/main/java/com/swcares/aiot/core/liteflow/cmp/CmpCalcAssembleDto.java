package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.enums.VariableStatusEnum;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TAircraftInfo;
import com.swcares.aiot.core.entity.TServiceRecord;
import com.swcares.aiot.core.enums.EnumCustomerType;
import com.swcares.aiot.core.enums.EnumIndicatorDataFormat;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.dto.ReCalcErrorDto;
import com.swcares.aiot.model.dto.ActExpensesResultCaluDto;
import com.swcares.aiot.model.dto.ActFlightIndexCaluDto;
import com.swcares.aiot.model.dto.ActIndexCaluDto;
import com.swcares.aiot.model.vo.ActExpensesVo;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.baseframe.common.exception.BusinessException;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.CmpCalcAssembleDto
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/12 16:24
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcAssembleDto", name = "组件-对账通机场端-计算模块-组装计算请求对象")
public class CmpCalcAssembleDto extends NodeComponent {
    @Override
    public void process() {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        ReCalcDto dto = ctxCalc.getDto();
        Map<String,ReCalcErrorDto> errorDtoMap = ctxCalc.getErrorDtoMap();
        //需计算航班数据
        Map<String, List<FlightInfoMb>> calcFlightInfoMbMap = ctxCalc.getCalcFlightInfoMbMap();
        //封装最外层对象
        ActExpensesResultCaluDto actExpensesResultCaluDto = new ActExpensesResultCaluDto();
        actExpensesResultCaluDto.setCustomerIataA(dto.getAirportCode());
        //设定主题类型为机场
        actExpensesResultCaluDto.setCustomerType(EnumCustomerType.AIRPORT_TYPE.getCode());
        actExpensesResultCaluDto.setExpensesCode(dto.getFeeCodes());
        actExpensesResultCaluDto.setActFlightIndexCaluDtos(new ArrayList<>());
        //不需要提示信息的航班——费用（航班未确认或业务保障数据未确认）
        Set<String> noErrorMsgSet = new HashSet<>();
        calcFlightInfoMbMap.forEach((airlineSettleCode, flightInfoMbList) ->
                assembleFlightIndexCaluDtos(airlineSettleCode, flightInfoMbList, actExpensesResultCaluDto, ctxCalc, noErrorMsgSet, errorDtoMap)
        );
        if (CollUtil.isEmpty(actExpensesResultCaluDto.getActFlightIndexCaluDtos())) {
            log.error("最终要发送的数据为空:{}", actExpensesResultCaluDto);
            throw new BusinessException(ExceptionCodes.CALC_FLIGHT_DATA_NULL);
        }
        log.info("最终要发送的数据:{}", actExpensesResultCaluDto);
        ctxCalc.setActExpensesResultCaluDto(actExpensesResultCaluDto);
        ctxCalc.setNoErrorMsgSet(noErrorMsgSet);
    }

    private static void assembleFlightIndexCaluDtos(String airlineSettleCode, List<FlightInfoMb> flightInfoMbList, ActExpensesResultCaluDto actExpensesResultCaluDto, CtxCalc ctxCalc, Set<String> noErrorMsgSet, Map<String,ReCalcErrorDto> errorDtoMap) {
        //需计算费用公式信息
        Map<String, Set<ActExpensesVo>> customerIndicatMap = ctxCalc.getCustomerIndicatMap();

        // 带指标项的航班集合
        List<ActFlightIndexCaluDto> actFlightIndexCaluDtos = actExpensesResultCaluDto.getActFlightIndexCaluDtos();
        // 计算需要的费用项目
        Set<ActExpensesVo> actExpensesVoSet = customerIndicatMap.get(airlineSettleCode);

        for (FlightInfoMb flightInfoMb : flightInfoMbList) {
            // 航班计算参数组装
            ActFlightIndexCaluDto actFlightIndexCaluDto = new ActFlightIndexCaluDto();
            actFlightIndexCaluDto.setFlightId(flightInfoMb.getId());
            actFlightIndexCaluDto.setFlightNo(flightInfoMb.getFlightNo());
            actFlightIndexCaluDto.setFlightAttribute(flightInfoMb.getFlightSegmentType());
            actFlightIndexCaluDto.setCollectionMode(flightInfoMb.getFlightFlag());
            //归集分类不限
            actFlightIndexCaluDto.setAggregationSort("1");

            actFlightIndexCaluDto.setOrg(flightInfoMb.getFromAirportCode());
            actFlightIndexCaluDto.setDst(flightInfoMb.getToAirportCode());
            //设置计算对象（航司计算代码）
            actFlightIndexCaluDto.setCustomerIataB(airlineSettleCode);

            // 基础信息
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            actFlightIndexCaluDto.setFlightDate(flightInfoMb.getFlightDate().format(df));

            if (actExpensesVoSet == null) {
                log.error("{}航司无计算需要的指标项", airlineSettleCode);
                ReCalcErrorDto reCalcErrorDto = errorDtoMap.get(flightInfoMb.getId());
                if (reCalcErrorDto == null) {
                    reCalcErrorDto = new ReCalcErrorDto(flightInfoMb, "未创建航司信息", "全部");
                }else{
                    reCalcErrorDto.setErrorMsg(reCalcErrorDto.getErrorMsg()+"、未创建航司信息");
                    reCalcErrorDto.setErrorFee("全部");
                }
                errorDtoMap.put(flightInfoMb.getId(), reCalcErrorDto);
            } else {
                //遍历费用项下的所有需要的指标项
                handleIndicator(noErrorMsgSet, flightInfoMb, actExpensesVoSet, ctxCalc, actFlightIndexCaluDto);
                if (CollUtil.isNotEmpty(actFlightIndexCaluDto.getActIndexCaluDtos())) {
                    actFlightIndexCaluDtos.add(actFlightIndexCaluDto);
                }
            }

        }
    }

    private static void handleIndicator(Set<String> noErrorMsgSet, FlightInfoMb flightInfoMb, Set<ActExpensesVo> actExpensesVoSet, CtxCalc ctxCalc, ActFlightIndexCaluDto actFlightIndexCaluDto) {
        //指标项字典数据
        Map<String, IndAllIndicatorRetrVo> indicatorDictMap = ctxCalc.getIndicatorDictMap();
        //已组装的指标code，避免重复
        Set<String> usedIndicatorSet = new HashSet<>();
        actExpensesVoSet.forEach(actExpensesVo ->
                actExpensesVo.getActIndicatorVos().forEach(actIndicatorVo -> {
                    String code = actIndicatorVo.getCode();
                    IndAllIndicatorRetrVo indAllIndicatorRetrVo = indicatorDictMap.get(code);
                    if (indAllIndicatorRetrVo == null) {
                        return;
                    }
                    if (usedIndicatorSet.contains(code)) {
                        return;
                    }
                    usedIndicatorSet.add(code);
                    if (StringUtils.isNotBlank(indAllIndicatorRetrVo.getName()) && StringUtils.isNotBlank(indAllIndicatorRetrVo.getClazzField())) {
                        //获取航班信息/飞机信息的指标项
                        getFlightAndAircraftValue(flightInfoMb, actExpensesVo, noErrorMsgSet, indAllIndicatorRetrVo, ctxCalc, code, actFlightIndexCaluDto);
                    } else if (StringUtils.isNotBlank(indAllIndicatorRetrVo.getCode())) {
                        //获取业务保障数据的指标项
                        getServiceValue(flightInfoMb, actExpensesVo, noErrorMsgSet, ctxCalc, indAllIndicatorRetrVo, code, actFlightIndexCaluDto);
                    } else {
                        log.error("指标项未配置映射字段或code,{} ", indAllIndicatorRetrVo);
                    }
                }));
    }

    private static void getFlightAndAircraftValue(FlightInfoMb flightInfoMb, ActExpensesVo actExpensesVo, Set<String> noErrorMsgSet, IndAllIndicatorRetrVo indAllIndicatorRetrVo, CtxCalc ctxCalc, String code, ActFlightIndexCaluDto actFlightIndexCaluDto) {
        //航班对应飞机信息数据
        Map<String, TAircraftInfo> flightAircraftInfoMap = ctxCalc.getFlightAircraftInfoMap();
        //航班-费用 对应 指标项数据
        Map<String, List<ActIndexCaluDto>> flightFeeIndexCaluMap = ctxCalc.getFlightFeeIndexCaluMap();
        String key = flightInfoMb.getId() + "_" + actExpensesVo.getExpensesCode();
        if (!Integer.valueOf(1).equals(flightInfoMb.getDataStatus())) {
            noErrorMsgSet.add(key);
        } else {
            String attributeMethods = CharSequenceUtil.upperFirstAndAddPre(indAllIndicatorRetrVo.getClazzField(), "get");
            Object value = null;
            if (indAllIndicatorRetrVo.getClazzName().equalsIgnoreCase("FlightInfo")) {
                try {
                    value = FlightInfoMb.class.getMethod(attributeMethods).invoke(flightInfoMb);
                } catch (Exception e) {
                    log.error("出现业务异常", e);
                }

            } else if (indAllIndicatorRetrVo.getClazzName().equalsIgnoreCase("AircraftInfo")) {
                try {
                    TAircraftInfo tAircraftInfo = flightAircraftInfoMap.get(flightInfoMb.getId());
                    value = TAircraftInfo.class.getMethod(attributeMethods).invoke(tAircraftInfo);
                } catch (Exception e) {
                    log.error("出现业务异常", e);
                }
            } else {
                return;
            }
            //获取快照显示指标值暂存map
            List<ActIndexCaluDto> indexCaluDtoList = flightFeeIndexCaluMap.getOrDefault(key, new ArrayList<>());
            //生成指标项对象
            setActIndexCaluDto(code, actFlightIndexCaluDto, value, indAllIndicatorRetrVo, indexCaluDtoList);
            flightFeeIndexCaluMap.put(key, indexCaluDtoList);
        }
    }

    private static void setActIndexCaluDto(String code, ActFlightIndexCaluDto actFlightIndexCaluDto, Object value,
                                           IndAllIndicatorRetrVo indAllIndicatorRetrVo, List<ActIndexCaluDto> indexCaluDtoList) {
        if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_TIME.getCode().equals(indAllIndicatorRetrVo.getDataFmt())
                && value instanceof LocalDateTime) {
            LocalDateTime tempLocalDateTime = (LocalDateTime) value;
            // 定义格式化器：指定格式为"HH:mm"（24小时制）
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            // 转换为字符串
            value = tempLocalDateTime.format(formatter);
        }
        if (value != null) {
            ActIndexCaluDto actIndexCaluDto = new ActIndexCaluDto();
            actIndexCaluDto.setIndicatorCode(code);
            actIndexCaluDto.setIndicatorValue(value);
            actIndexCaluDto.setIndicatorOrigin(BigDecimal.ONE.intValue());
            actIndexCaluDto.setSort(indAllIndicatorRetrVo.getSort());
            actIndexCaluDto.setDataFmt(indAllIndicatorRetrVo.getDataFmt());
            actIndexCaluDto.setDictItems(indAllIndicatorRetrVo.getDictItems());
            //添加指向标对象到请求对象中
            addIndexDto(actFlightIndexCaluDto, actIndexCaluDto);
            //添加到快照展示map
            indexCaluDtoList.add(actIndexCaluDto);
        }
    }

    private static void getServiceValue(FlightInfoMb flightInfoMb, ActExpensesVo actExpensesVo, Set<String> noErrorMsgSet,
                                        CtxCalc ctxCalc, IndAllIndicatorRetrVo indAllIndicatorRetrVo,
                                        String code, ActFlightIndexCaluDto actFlightIndexCaluDto) {
        //航班对应业务保障数据
        Map<String, Map<String, List<TServiceRecord>>> serviceRecordMap = ctxCalc.getServiceRecordMap();
        //航班-费用 对应 指标项数据
        Map<String, List<ActIndexCaluDto>> flightFeeIndexCaluMap = ctxCalc.getFlightFeeIndexCaluMap();
        String key = flightInfoMb.getId() + "_" + actExpensesVo.getExpensesCode();
        if (!VariableStatusEnum.CONFIRMED.getValue().equals(flightInfoMb.getVariableStatus())) {
            noErrorMsgSet.add(key);
        } else {
            //获取航班id下所有业务保障数据map
            Map<String, List<TServiceRecord>> serviceMap = serviceRecordMap.get(flightInfoMb.getId());
            if (serviceMap == null) {
                return;
            }
            //获取当前航班计算需要的servideCode的业务保障数据
            List<TServiceRecord> serviceRecordList = serviceMap.get(indAllIndicatorRetrVo.getCode());
            if (serviceRecordList == null || serviceRecordList.isEmpty()) {
                return;
            }
            List<ActIndexCaluDto> indexCaluDtosList = flightFeeIndexCaluMap.getOrDefault(key, new ArrayList<>());
            for (TServiceRecord serviceRecord : serviceRecordList) {
                //组装业务保障数据的计算请求对象
                ActIndexCaluDto actIndexCaluDto = getActIndexCaluDto(indAllIndicatorRetrVo, code, serviceRecord);
                //添加指向标对象到请求对象中
                addIndexDto(actFlightIndexCaluDto, actIndexCaluDto);
                indexCaluDtosList.add(actIndexCaluDto);
            }
            flightFeeIndexCaluMap.put(key, indexCaluDtosList);
        }
    }

    private static void addIndexDto(ActFlightIndexCaluDto actFlightIndexCaluDto, ActIndexCaluDto actIndexCaluDto) {
        if (actFlightIndexCaluDto.getActIndexCaluDtos() == null) {
            actFlightIndexCaluDto.setActIndexCaluDtos(new ArrayList<>());
        }
        actFlightIndexCaluDto.getActIndexCaluDtos().add(actIndexCaluDto);
    }

    private static ActIndexCaluDto getActIndexCaluDto(IndAllIndicatorRetrVo indAllIndicatorRetrVo, String code, TServiceRecord serviceRecord) {
        ActIndexCaluDto actIndexCaluDto = new ActIndexCaluDto();
        actIndexCaluDto.setIndicatorCode(code);
        actIndexCaluDto.setDataFmt(indAllIndicatorRetrVo.getDataFmt());
        //如果是选择器类型，则使用业务保障数据的选择器选项
        if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_SELECTOR.getCode().equals(indAllIndicatorRetrVo.getDataFmt())) {
            actIndexCaluDto.setIndicatorValue(serviceRecord.getSelectorOption());
            actIndexCaluDto.setDictItems(indAllIndicatorRetrVo.getDictItems());
        } else {
            actIndexCaluDto.setIndicatorValue(serviceRecord.getUsedNumber());
        }
        actIndexCaluDto.setIndicatorOrigin(BigDecimal.ONE.intValue());
        return actIndexCaluDto;
    }

}
