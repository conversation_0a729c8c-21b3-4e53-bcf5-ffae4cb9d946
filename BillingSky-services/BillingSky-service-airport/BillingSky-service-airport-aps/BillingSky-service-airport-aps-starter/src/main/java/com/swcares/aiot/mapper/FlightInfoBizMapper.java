package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.form.FlightInfoSearchForm;
import com.swcares.aiot.core.model.dto.FlightInfoConfirmDto;
import com.swcares.aiot.core.model.vo.ACCAExcelVo;
import com.swcares.aiot.core.model.vo.FlightInfoVoNew;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FlightInfoBizMapper {

    IPage<FlightInfoVoNew> pageFlightInfoByCondition(@Param("form") FlightInfoSearchForm form, IPage<FlightInfoVoNew> page);

    List<ACCAExcelVo> getACCAData(@Param("form") FlightInfoSearchForm form);

    List<FlightInfoMb> listFlightByConfirmCondition(@Param("dto")FlightInfoConfirmDto dto);
}
