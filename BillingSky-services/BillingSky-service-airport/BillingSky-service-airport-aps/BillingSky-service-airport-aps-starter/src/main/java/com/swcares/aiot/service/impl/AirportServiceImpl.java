package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.entity.ExcelData;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.AirportBillSearchForm;
import com.swcares.aiot.core.form.AirportForm;
import com.swcares.aiot.core.model.entity.AirlineInfoAps;
import com.swcares.aiot.core.model.entity.AirportInfoAps;
import com.swcares.aiot.core.model.entity.FeeInfo;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.core.model.vo.AirlinePercentVo;
import com.swcares.aiot.core.model.vo.AirportBillCountVo;
import com.swcares.aiot.core.model.vo.AirportBillVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.AirlineDao;
import com.swcares.aiot.dao.AirportDao;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.aiot.service.AirportService;
import com.swcares.aiot.service.LogService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.worm.hutool.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * ClassName：AirportServiceImpl <br>
 * Description：(AirportService的实现类)<br>
 * Copyright © 2020-9-29 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-9-29 14:31<br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class AirportServiceImpl implements AirportService {
    @Resource
    private AirportDao airportDao;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private AirlineDao airlineDao;
    @Resource
    private FeeDao feeDao;
    @Resource
    private LogService logService;

    @Override
    public ResultBuilder<Page<AirportInfoAps>> pageAirportInfo(PageParam pageParam) {
        Pageable pageable = PageRequest.of(pageParam.getPage() - 1, pageParam.getLimit());
        Page<AirportInfoAps> airportInfos = airportDao.pageAirportInfo(pageable);
        //计算昨天日期
        Date yesterDay = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(yesterDay);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        yesterDay = calendar.getTime();
        for (AirportInfoAps airportInfo : airportInfos
        ) {
            AirportBillCountVo airportBillCountVo = this.getAirportBillCountVo(airportInfo.getAirportCode(), yesterDay, yesterDay, "");
            airportInfo.setYesterdayFlightNumber(airportBillCountVo.getTotalFlightNumber().intValue());
            airportInfo.setYesterdayMailCargo(airportBillCountVo.getTotalMailAndCargo().intValue());
            airportInfo.setYesterdayPsgThroughput(airportBillCountVo.getTotalPsgThroughput().intValue());
        }
        return new ResultBuilder.Builder<Page<AirportInfoAps>>().data(airportInfos).builder();
    }

    /**
     * Title: getAirportBillCountVo<br>
     * Author: 李龙<br>
     * Description: (获取某一时间段机场运营总数据)<br>
     * Date:  13:15 <br>
     *
     * @param airportCode :
     * @param start       :
     * @param end         :
     * @param airlineCode :
     * @return AirportBillCountVo
     */
    private AirportBillCountVo getAirportBillCountVo(String airportCode, Date start, Date end, String airlineCode) {
        List<Object[]> lists = airportDao.getTotalAirportDetailsBill(airportCode, start, end, airlineCode);
        AirportBillCountVo airportBillCountVo = new AirportBillCountVo();
        for (Object[] object : lists
        ) {
            airportBillCountVo.setTotalFlightNumber((BigInteger) object[0]);
            airportBillCountVo.setTotalPsgThroughput((BigDecimal) object[1]);
            airportBillCountVo.setTotalMailAndCargo((BigDecimal) object[2]);
            BigDecimal bignum1 = (BigDecimal) object[3];
            airportBillCountVo.setTotalArrivePlf(bignum1);
            BigDecimal bignum2 = (BigDecimal) object[4];
            airportBillCountVo.setTotalDeparturePlf(bignum2);
        }
        return airportBillCountVo;
    }

    @Override
    public ResultBuilder<AirportBillCountVo> getTotalAirportDetailsBill(String airportCode, String startDate, String endDate, String airlineCode) {
        Date start = DateUtils.parseDate(startDate);
        Date end = DateUtils.parseDate(endDate);
        //计算查询周期天数
        List<String> dates = DateUtils.getBetweenDates(start, end);
        long days = dates.size();
        AirportBillCountVo airportBillCountVo = this.getAirportBillCountVo(airportCode, start, end, airlineCode);
        airportBillCountVo = averageData(airportBillCountVo, days);
        //计算开始日期前days是哪一天
        Calendar calendar = Calendar.getInstance();
        Date beforeEnd = new Date();
        beforeEnd.setTime(start.getTime() - 24 * 60 * 60 * 1000);
        calendar.setTime(beforeEnd);
        calendar.set(Calendar.DATE, (int) (calendar.get(Calendar.DATE) - days + 1));
        Date beforeStart = calendar.getTime();
        AirportBillCountVo beforeAirportBillCountVo = this.getAirportBillCountVo(airportCode, beforeStart, beforeEnd, airlineCode);
        beforeAirportBillCountVo = averageData(beforeAirportBillCountVo, days);
        //计算环比时间对比率：（当前数据-前时间段数据）/前时间段数据*100，如果前端时间数据为0，对比率为0%
        //计算航班起降对比率
        if (beforeAirportBillCountVo.getTotalFlightNumber().doubleValue() == 0) {
            airportBillCountVo.setFlightNumberContrast(0);
        } else {
            airportBillCountVo.setFlightNumberContrast(
                    FormatUtils.formatData(
                            Math.ceil(
                                    FormatUtils.mul(
                                            FormatUtils.div(
                                                    FormatUtils.sub(Math.round(airportBillCountVo.getTotalFlightNumber().doubleValue()),
                                                            Math.round(beforeAirportBillCountVo.getTotalFlightNumber().doubleValue())
                                                    ), Math.round(beforeAirportBillCountVo.getTotalFlightNumber().doubleValue())
                                            ), 100
                                    )
                            )
                    )
            );
        }
        //计算旅客吞吐量对比率
        if (beforeAirportBillCountVo.getTotalPsgThroughput().doubleValue() == 0) {
            airportBillCountVo.setPsgThroughputContrast(0);
        } else {
            airportBillCountVo.setPsgThroughputContrast(
                    FormatUtils.formatData(
                            Math.ceil(
                                    FormatUtils.mul(
                                            FormatUtils.div(
                                                    FormatUtils.sub(Math.round(airportBillCountVo.getTotalPsgThroughput().doubleValue()),
                                                            Math.round(beforeAirportBillCountVo.getTotalPsgThroughput().doubleValue()))
                                                    , Math.round(beforeAirportBillCountVo.getTotalPsgThroughput().doubleValue())
                                            ), 100
                                    )
                            )
                    )
            );
        }
        //计算货邮吞吐量对比率
        if ((beforeAirportBillCountVo.getTotalMailAndCargo().doubleValue()) == 0) {
            airportBillCountVo.setMailAndCargoContrast(0);
        } else {
            airportBillCountVo.setMailAndCargoContrast(
                    FormatUtils.formatData(
                            Math.ceil(
                                    FormatUtils.mul(
                                            FormatUtils.div(
                                                    FormatUtils.sub(Math.round(airportBillCountVo.getTotalMailAndCargo().doubleValue()),
                                                            Math.round(beforeAirportBillCountVo.getTotalMailAndCargo().doubleValue())
                                                    ), Math.round(beforeAirportBillCountVo.getTotalMailAndCargo().doubleValue())
                                            ), 100
                                    )

                            )
                    )
            );
        }

        //计算进港客对比率
        if (beforeAirportBillCountVo.getTotalArrivePlf().doubleValue() == 0) {
            airportBillCountVo.setArrivePlfContrast(0);
        } else {
            airportBillCountVo.setArrivePlfContrast(
                    FormatUtils.formatData(
                            Math.ceil(
                                    FormatUtils.mul(
                                            FormatUtils.div(
                                                    FormatUtils.sub(Math.round(airportBillCountVo.getTotalArrivePlf().doubleValue()),
                                                            Math.round(beforeAirportBillCountVo.getTotalArrivePlf().doubleValue()))
                                                    , Math.round(beforeAirportBillCountVo.getTotalArrivePlf().doubleValue())
                                            ), 100
                                    )
                            )
                    )
            );
        }
        //计算离港对比率
        if (beforeAirportBillCountVo.getTotalDeparturePlf().doubleValue() == 0) {
            airportBillCountVo.setDeparturePlfContrast(0);
        } else {
            airportBillCountVo.setDeparturePlfContrast(
                    FormatUtils.formatData(
                            Math.ceil(
                                    FormatUtils.mul(
                                            FormatUtils.div(
                                                    FormatUtils.sub(Math.round(airportBillCountVo.getTotalDeparturePlf().doubleValue()),
                                                            Math.round(beforeAirportBillCountVo.getTotalDeparturePlf().doubleValue()))
                                                    , Math.round(beforeAirportBillCountVo.getTotalDeparturePlf().doubleValue())
                                            ), 100
                                    )
                            )
                    )
            );
        }
        return new ResultBuilder.Builder<AirportBillCountVo>().data(airportBillCountVo).builder();
    }

    /*
     * Title: averageData<br>
     * Author: 李龙<br>
     * Description: 计算日均率<br>
     * Date:  17:13 <br>
     * @param airportBillCountVo
     * @param days
     * return: AirportBillCountVo
     */
    private AirportBillCountVo averageData(AirportBillCountVo airportBillCountVo, long days) {
        //计算日均起降航班（向上取整）
        airportBillCountVo.setDayFlightNumber(
                Math.ceil(
                        FormatUtils.div(airportBillCountVo.getTotalFlightNumber().doubleValue(), days)
                )
        );
        //计算日均旅客吞吐量（向上取整）
        airportBillCountVo.setDayPsgThroughput(
                Math.ceil(
                        FormatUtils.div(airportBillCountVo.getTotalPsgThroughput().doubleValue(), days)
                )
        );
        //计算日均货邮吞吐量（向上取整）
        airportBillCountVo.setDayMailAndCargo(
                Math.ceil(
                        FormatUtils.div(
                                airportBillCountVo.getTotalMailAndCargo().doubleValue(), days
                        )
                )
        );
        return airportBillCountVo;
    }

    /*
     * Title: listAirportBillVo<br>
     * Author: 李龙<br>
     * Description: (计算机场日航班数据)<br>
     * Date:  8:53 <br>
     * @param airportCode
     * @param airlineCode
     * @param startDate
     * @param endDate
     * return: java.util.List<AirportBillVo>
     */
    private List<AirportBillVo> listAirportBillVo(String airportCode, String airlineCode, String startDate, String endDate) {
        List<AirportBillVo> list = new ArrayList<>();
        List<String> days = DateUtils.getBetweenDates(DateUtils.parseDate(startDate), DateUtils.parseDate(endDate));
        int k = 0;
        while (k < days.size()) {
            AirportBillVo airportBillVo = new AirportBillVo();
            List<Object[]> objects = airportDao.getAirportBillByAirportCodeAndFlightDate(airportCode, airlineCode, DateUtils.parseDate(days.get(k)));
            if (objects.isEmpty()) {
                airportBillVo.setDetailsDate(DateUtils.parseDate(days.get(k)));
            }
            for (Object[] object : objects
            ) {
                airportBillVo.setDetailsDate((Date) object[0]);
                airportBillVo.setFlightNumber((BigInteger) object[1]);
                airportBillVo.setDepartureFlightNumber((BigInteger) object[2]);
                airportBillVo.setArrivalFlightNumber((BigInteger) object[3]);
                airportBillVo.setPsgThroughput((BigDecimal) object[4]);
                airportBillVo.setPsgThroughputExcludTransit((BigDecimal) object[5]);
                airportBillVo.setDeparturePsgNumber((BigDecimal) object[6]);
                airportBillVo.setArrivalPsgNumber((BigDecimal) object[7]);
                airportBillVo.setTransitPsgNumber((BigDecimal) object[8]);
                airportBillVo.setMail((BigDecimal) object[9]);
                airportBillVo.setCargo((BigDecimal) object[10]);
                BigDecimal bignum1 = (BigDecimal) object[11];
                airportBillVo.setPlf(bignum1.setScale(0, RoundingMode.UP));
            }
            list.add(airportBillVo);
            k++;
        }
        return list;
    }

    @Override
    public ResultBuilder<Pager<AirportBillVo>> pageAirportDetailsBill(String airportCode, String airlineCode, String startDate, String endDate, PageParam pageParam) {
        List<AirportBillVo> list = this.listAirportBillVo(airportCode, airlineCode, startDate, endDate);
        Pager<AirportBillVo> pager = new Pager<>(pageParam.getPage(), pageParam.getLimit(), list);
        return new ResultBuilder.Builder<Pager<AirportBillVo>>().data(pager).builder();
    }

    @Override
    public ResultBuilder<AirportInfoAps> getAirportById(String id) {
        return new ResultBuilder.Builder<AirportInfoAps>().data(airportDao.findByAirportId(id)).builder();
    }

    @Override
    @Transactional
    public ResultBuilder<Object> updateAirport(AirportForm airportForm, LoginUserDetails user, String urlName) {
        AirportInfoAps airportInfo = airportDao.findByAirportId(airportForm.getAirportId());
        AirportForm airportForm1 = new AirportForm();
        try {
            BeanUtils.copyProperties(airportForm1, airportInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("出现业务异常", e);
        }
        airportInfo.setAirportCode(airportForm.getAirportCode());
        airportInfo.setAirportLevel(airportForm.getAirportLevel());
        airportInfo.setAirportType(airportForm.getAirportType());
        airportInfo.setAirportName(airportForm.getAirportName());
        airportInfo.setAirportShortName(airportForm.getAirportShortName());
        airportInfo.setCompany(airportForm.getCompany());
        airportInfo.setAirportProvince(airportForm.getAirportProvince());
        airportInfo.setModifiedBy(user.getUsername());
        airportInfo.setModifiedTime(new Date());
        airportDao.save(airportInfo);
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<AirportInfoAps> getAirportByAirportCode(String airportCode) {
        return new ResultBuilder.Builder<AirportInfoAps>().data(airportDao.getAirportByAirportCode(airportCode)).builder();
    }

    @Override
    @Transactional
    public ResultBuilder delAirportById(String id, LoginUserDetails user, String urlName) {
        AirportInfoAps airportInfo = airportDao.findByAirportId(id);
        int count = flightInfoDao.countByAirportCode(airportInfo.getAirportCode());
        List<AirlineInfoAps> list = airlineDao.listMaintainedAirlineByAirport(airportInfo.getAirportCode());
        List<FeeInfo> feeInfoList = feeDao.listFeeRulesByFeeName("", airportInfo.getAirportCode());
        if (count != 0) {
            throw new GenericException(BusinessMessageEnum.DELETE_AIRPORT_ERROR_FLIGHT.getCode(), BusinessMessageEnum.DELETE_AIRPORT_ERROR_FLIGHT.getMsg());
        }
        if (!list.isEmpty()) {
            throw new GenericException(BusinessMessageEnum.DELETE_AIRPORT_ERROR_AIRLINE.getCode(), BusinessMessageEnum.DELETE_AIRPORT_ERROR_AIRLINE.getMsg());
        }
        if (!feeInfoList.isEmpty()) {
            throw new GenericException(BusinessMessageEnum.DELETE_AIRPORT_ERROR_FEE.getCode(), BusinessMessageEnum.DELETE_AIRPORT_ERROR_FEE.getMsg());
        }
        airportDao.deleteByAirportId(id, user.getUsername(), new Date());
        return new ResultBuilder.Builder().builder();
    }

    @Override
    @Transactional
    public ResultBuilder saveAirport(AirportForm airportForm, LoginUserDetails user, String urlName) {
        if (airportDao.getAirportByAirportCode(airportForm.getAirportCode()) != null) {
            throw new GenericException(BusinessMessageEnum.SAVE_AIRPORT_ERROR.getCode(), BusinessMessageEnum.SAVE_AIRPORT_ERROR.getMsg());
        }
        AirportInfoAps airportInfo = new AirportInfoAps();
        try {
            BeanUtils.copyProperties(airportInfo, airportForm);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("出现业务异常", e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
        airportInfo.setCreateTime(new Date());
        airportInfo.setCreateBy(user.getUsername());
        airportDao.save(airportInfo);
        return new ResultBuilder.Builder().builder();
    }

    @Override
    public ResultBuilder getAirlinePercent(String airportCode, String startDate, String endDate, String airlineCode) {
        Date start = DateUtils.parseDate(startDate);
        Date end = DateUtils.parseDate(endDate);
        List<AirlinePercentVo> list = airportDao.getAirlinePercent(airportCode, start, end, airlineCode);
        //获取总的数据
        double totalFlightNumber = 0;
        double totalPsgThroughput = 0;
        double totalMail = 0;
        double totalCargo = 0;
        double totalPlf = 0;
        double totalPsgTransit = 0;
        for (AirlinePercentVo airline : list
        ) {
            totalFlightNumber = FormatUtils.sum(totalFlightNumber, airline.getTotalFlightNumber());
            totalPsgThroughput = FormatUtils.sum(totalPsgThroughput, airline.getTotalPsgThroughput());
            totalMail = FormatUtils.sum(totalMail, airline.getTotalMail());
            totalCargo = FormatUtils.sum(totalCargo, airline.getTotalCargo());
            totalPlf = FormatUtils.sum(totalPlf, airline.getTotalPlf());
            totalPsgTransit = FormatUtils.sum(totalPsgTransit, airline.getTotalPsgTransit());
        }
        for (AirlinePercentVo air : list
        ) {
            //计算占比率
            //航班起降架次
            if (totalFlightNumber == 0) {
                air.setFlightNumberPercent(0);
            } else {
                air.setFlightNumberPercent(
                        FormatUtils.formatData(
                                FormatUtils.mul(
                                        FormatUtils.div(air.getTotalFlightNumber(), totalFlightNumber),
                                        100
                                )
                        )
                );
            }
            //旅客吞吐量（不含过站）
            if (FormatUtils.sub(totalPsgThroughput, totalPsgTransit) == 0) {
                air.setPsgThroughputExcludTransitPercent(0);
            } else {
                air.setPsgThroughputExcludTransitPercent(
                        FormatUtils.formatData(
                                FormatUtils.mul(
                                        FormatUtils.div(
                                                FormatUtils.sub(air.getTotalPsgThroughput(), air.getTotalPsgTransit()),
                                                FormatUtils.sub(totalPsgThroughput, totalPsgTransit)
                                        ), 100
                                )
                        )
                );
            }
            //货邮吞吐量
            if (FormatUtils.sum(totalCargo, totalMail) == 0) {
                air.setMailCargoPercent(0);
            } else {
                air.setMailCargoPercent(
                        FormatUtils.formatData(
                                FormatUtils.mul(
                                        FormatUtils.div(
                                                FormatUtils.sum(air.getTotalMail(), air.getTotalCargo()),
                                                FormatUtils.sum(totalCargo, totalMail)
                                        ), 100
                                )
                        )
                );
            }
            //航班客座率
            if (totalPlf == 0) {
                air.setPlfPencent(0);
            } else {
                air.setPlfPencent(
                        FormatUtils.formatData(
                                FormatUtils.mul(
                                        FormatUtils.div(
                                                air.getTotalPlf(), totalPlf),
                                        100
                                )
                        )
                );
            }
            //旅客吞吐量（含过站）
            if (totalPsgThroughput == 0) {
                air.setPsgThroughputPercent(0);
            } else {
                air.setPsgThroughputPercent(
                        FormatUtils.formatData(
                                FormatUtils.mul(
                                        FormatUtils.div(air.getTotalPsgThroughput(), totalPsgThroughput),
                                        100
                                )
                        )
                );
            }
        }
        return new ResultBuilder.Builder().data(list).builder();

    }

    @Override
    public ResultBuilder listFlightInfoByFlightTime(String airportCode, String detailsDate, String airlineCode) {
        if (CharSequenceUtil.isBlank(airlineCode)) {
            airlineCode = null;
        }
        Date detailsDay = DateUtils.parseDate(detailsDate);
        List<FlightInfo> flightInfos = airportDao.listFlightInfoByFlightTime(airportCode, detailsDay, airlineCode);
        for (FlightInfo flightInfo : flightInfos
        ) {
            flightInfo.setPlf(flightInfo.getPlf() == null ? null : flightInfo.getPlf());
        }
        return new ResultBuilder.Builder().data(flightInfos).builder();
    }

    @Override
    public void exportAirportOperationData(String airportCode, String startDate, String endDate, String airlineCode, HttpServletResponse response, LoginUserDetails user, String urlName) {
        //根据条件从数据库中获取数据
        List<AirportBillVo> resultList = this.listAirportBillVo(airportCode, airlineCode, startDate, endDate);
        ExcelData excelData = new ExcelData();
        //表名
        excelData.setFileName(Constants.AirportBillEnum.AIRPORT_BILL_EXCEL_NAME.getValue() +
                FormatUtils.formatDateToString(new Date()) +
                Constants.AirportBillEnum.AIRPORT_BILL_EXCEL_NAME_SUFFIX.getValue());
        //表头
        String[] head = {
                Constants.AirportBillEnum.FLIGHT_DATE.getValue(),
                Constants.AirportBillEnum.FLIGHT_NUMBER.getValue(),
                Constants.AirportBillEnum.DEPARTURE_FLIGHT_NUMBER.getValue(),
                Constants.AirportBillEnum.ARRIVAL_FLIGHT_NUMBER.getValue(),
                Constants.AirportBillEnum.PSG_THROUGHPUT.getValue(),
                Constants.AirportBillEnum.PSG_THROUGHPUT_EXCLUDE_TRANSIT.getValue(),
                Constants.AirportBillEnum.DEPARTURE_PSG_NUMBER.getValue(),
                Constants.AirportBillEnum.ARRIVAL_PSG_NUMBER.getValue(),
                Constants.AirportBillEnum.TRANSIT_PSG_NUMBER.getValue(),
                Constants.AirportBillEnum.MAIL.getValue(),
                Constants.AirportBillEnum.CARGO.getValue(),
                Constants.AirportBillEnum.PLF.getValue()
        };
        excelData.setHead(head);
        //给excel赋值
        List<String[]> data = new ArrayList<>();
        for (AirportBillVo airportBillVo : resultList) {
            String[] airport = {
                    DateUtils.format(airportBillVo.getDetailsDate()),
                    String.valueOf(airportBillVo.getFlightNumber()),
                    String.valueOf(airportBillVo.getDepartureFlightNumber()),
                    String.valueOf(airportBillVo.getArrivalFlightNumber()),
                    String.valueOf(airportBillVo.getPsgThroughput()),
                    String.valueOf(airportBillVo.getPsgThroughputExcludTransit()),
                    String.valueOf(airportBillVo.getDeparturePsgNumber()),
                    String.valueOf(airportBillVo.getArrivalPsgNumber()),
                    String.valueOf(airportBillVo.getTransitPsgNumber()),
                    String.valueOf(airportBillVo.getMail()),
                    String.valueOf(airportBillVo.getCargo()),
                    //客座率加上%单位
                    airportBillVo.getPlf() + "%"
            };
            data.add(airport);
        }
        excelData.setData(data);
        FileUtils.exportExcel(response, excelData);
        String content = Constants.LogEnum.LOG_MEG_1.getValue()
                + urlName +
                Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        AirportBillSearchForm form = new AirportBillSearchForm();
        form.setAirportCode(airportCode);
        form.setStartDate(startDate);
        form.setEndDate(endDate);
        logService.addLogForExport(user, form, content, form.getAirportCode(), "机场运行账单");
    }

}
