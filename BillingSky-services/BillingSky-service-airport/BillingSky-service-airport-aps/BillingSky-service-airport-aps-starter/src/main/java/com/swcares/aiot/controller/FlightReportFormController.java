package com.swcares.aiot.controller;

import com.swcares.aiot.core.model.dto.FlightReportFormDTO;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.FlightReportFormService;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


/**
 * ClassName：FlightReportFormController
 * Description：航班客座率报表 前端控制器
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2024/6/25 13:22
 * Version v1.0
 */
@RestController
@RequestMapping("/flightReportForm")
@Api(tags = "航班客座率报表接口")
public class FlightReportFormController extends BaseController {

    @Resource
    private FlightReportFormService flightReportFormService;

    @GetMapping("/page")
    @ApiOperation(value = "航班客座率报表分页")
    public Object page(@Validated @ApiParam(name = "pageParam", value = "分页/条件") PageParam pageParam,
                       @Validated @ApiParam(name = "FlightReportFormDTO", value = "查询条件")
                       FlightReportFormDTO dto) {
        return ok(flightReportFormService.page(pageParam, dto));
    }

    @PostMapping("/getThroughputAndPlfTrend")
    @ApiOperation(value = "客座率趋势和吞吐量趋势接口")
    public Object getThroughputAndPlfTrend(@RequestBody @Validated FlightReportFormDTO dto) {
        return ok(flightReportFormService.getThroughputAndPlfTrend(dto));
    }

    @GetMapping("/exportFlightReportForm")
    @ApiOperation(value = "航班客座率报表导出")
    public void exportFlightReportForm(@Validated @ApiParam(name = "FlightReportFormDTO", value = "查询条件") FlightReportFormDTO dto,
                                       HttpServletResponse response) {
        flightReportFormService.exportFlightReportForm(dto, response);
    }

}
