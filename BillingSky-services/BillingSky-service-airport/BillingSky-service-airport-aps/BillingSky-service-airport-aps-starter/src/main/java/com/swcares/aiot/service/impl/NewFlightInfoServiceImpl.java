package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.enums.DataStatusEnum;
import com.swcares.aiot.core.common.enums.InvalidEnum;
import com.swcares.aiot.core.common.enums.VariableStatusEnum;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.*;
import com.swcares.aiot.core.enums.IsServiceFeeEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.importer.service.ImportToAirlineService;
import com.swcares.aiot.core.model.dto.FlightInfoConfirmDto;
import com.swcares.aiot.core.service.*;
import com.swcares.aiot.mapper.FlightInfoBizMapper;
import com.swcares.aiot.service.NewFlightInfoService;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.NewFlightInfoServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/7 15:47
 * @version v1.0
 */
@Service
@Slf4j
public class NewFlightInfoServiceImpl implements NewFlightInfoService {

    private static final String CONFIRM_CODE = "111111111111111111";
    @Resource
    private ITFlightInfoService flightInfoService;
    @Resource
    private ITFlightInfoCacheService flightInfoCacheService;
    @Resource
    private ITFlightBillService flightBillService;
    @Resource
    private ITFlightInfoSegmentService flightInfoSegmentService;
    @Resource
    private ITFlightInfoSegmentCacheService flightInfoSegmentCacheService;
    @Resource
    private ImportToAirlineService importToAirlineService;
    @Resource
    private FlightInfoBizMapper flightInfoBizMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ITServiceRecordService serviceRecordService;
    @Resource
    private ITServiceRecordConfirmService serviceRecordConfirmService;


    public List<String> confirmFlightInfo(FlightInfoConfirmDto dto) {
        if ((dto.getStartDate() == null || dto.getEndDate() == null)
                && (dto.getFlightTimeStartDate() == null || dto.getFlightTimeEndDate() == null)
                &&CollUtil.isEmpty(dto.getIdList())) {
            throw new BusinessException(ExceptionCodes.DATE_TIME_ERROR);
        }
        List<FlightInfoMb> allById = flightInfoBizMapper.listFlightByConfirmCondition(dto);
        for (FlightInfoMb flightInfo : allById) {
            if (StringUtils.isBlank(flightInfo.getRegNo())) {
                throw new GenericException(BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getCode(),
                        BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getMsg() + "，航班号：" + flightInfo.getFlightNo());
            }
            //如果状态为已确认则跳过
            if(DataStatusEnum.CONFIRMED.getValue().equals(flightInfo.getDataStatus())){
              continue;
            }
            flightInfo.setDataStatus(DataStatusEnum.CONFIRMED.getValue());
            flightInfo.setConfirmCode(CONFIRM_CODE);
        }
        //数据库更新
        flightInfoService.updateBatchById(allById);
        //同步备份数据
        syncFlightData(allById);
        return allById.stream().map(FlightInfoMb::getId).collect(Collectors.toList());
    }

    public void syncFlightData(List<FlightInfoMb> flightInfoList) {
        if(CollUtil.isEmpty(flightInfoList)) {
            return;
        }
        List<String> flightIdList = flightInfoList.stream().map(FlightInfoMb::getId).collect(Collectors.toList());
        List<TFlightInfoCache> fiCacheList = flightInfoCacheService.lambdaQuery().in(TFlightInfoCache::getId, flightIdList).list();
        if (CollectionUtils.isEmpty(fiCacheList)) {
            return;
        }
        List<FlightInfoMb> toProcessedList = new ArrayList<>();
        for (TFlightInfoCache cacheItem : fiCacheList) {
            for (FlightInfoMb item : flightInfoList) {
                if (item.getId().equals(cacheItem.getId())) {
                    BeanUtils.copyProperties(cacheItem, item);
                    toProcessedList.add(item);
                }
            }
        }
        if (CollectionUtils.isEmpty(toProcessedList)) {
            return;
        }
        for (FlightInfoMb fi : toProcessedList) {
            // 判断航班是否逻辑删除,或者取消,或者备降时航线到港当前机场
            if (InvalidEnum.DELETED.getValue().equals(fi.getInvalid())) {
                //如果航班删除了，需要删除该航班的所有账单数据
                flightBillService.lambdaUpdate().eq(TFlightBill::getFlightId, fi.getId())
                        .set(TFlightBill::getInvalid, InvalidEnum.DELETED.getValue())
                        .update();
            }
        }
        flightInfoService.saveOrUpdateBatch(toProcessedList);
        List<String> toProcessedFlightIdList =
                toProcessedList.stream().map(FlightInfoMb::getId).collect(Collectors.toList());
        List<TFlightInfoSegmentCache> fiscList =flightInfoSegmentCacheService.lambdaQuery()
                        .in(TFlightInfoSegmentCache::getBaseFlightId, toProcessedFlightIdList).list();
        if (CollUtil.isNotEmpty(fiscList)) {
            Map<Long, TFlightInfoSegmentCache> collect = fiscList.stream().collect(Collectors.toMap(TFlightInfoSegmentCache::getBaseFlightId, item -> item));
            List<Long> toUpdatedSegmentIdList = fiscList.stream()
                    .map(TFlightInfoSegmentCache::getBaseFlightId).collect(Collectors.toList());
            List<TFlightInfoSegment> fisList = flightInfoSegmentService.lambdaQuery().in(TFlightInfoSegment::getBaseFlightId, toUpdatedSegmentIdList).list();
            for (TFlightInfoSegment fis : fisList) {
                TFlightInfoSegmentCache fisc = collect.get(fis.getBaseFlightId());
                BeanUtils.copyProperties(fisc, fis);
            }
            flightInfoSegmentService.saveOrUpdateBatch(fisList);
        }
        // 批量推送到航司端
        importToAirlineService.sendToAirlineById(toProcessedList.stream().map(FlightInfoMb::getId).collect(Collectors.toList()), TenantHolder.getTenant());
    }

    @Override
    public void cancelConfirmFlightInfo(String flightIds, LoginUserDetails user) {
        String[] listId = flightIds.split(",");
        for (String flightId : listId) {
            FlightInfoMb flightInfo = flightInfoService.lambdaQuery().eq(FlightInfoMb::getId, flightId)
                    .eq(FlightInfoMb::getInvalid, InvalidEnum.NORMAL.getValue()).one();
            flightInfo.setDataStatus(DataStatusEnum.TO_BE_CONFIRMED.getValue());
            flightInfo.setModifiedBy(user.getUsername());
            flightInfo.setModifiedTime(LocalDateTime.now());
            flightInfoService.updateById(flightInfo);
            //删除航班下所有账单
            flightBillService.lambdaUpdate().eq(TFlightBill::getFlightId, flightId)
                    .eq(TFlightBill::getInvalid, InvalidEnum.NORMAL.getValue())
                    .set(TFlightBill::getInvalid, InvalidEnum.DELETED.getValue())
                    .update();

        }
    }

    @Override
    public List<String> confirmFlightBusinessInfo(FlightInfoConfirmDto dto) {
        if ((dto.getStartDate() == null || dto.getEndDate() == null) && (dto.getFlightTimeStartDate() == null || dto.getFlightTimeEndDate() == null)) {
            throw new BusinessException(ExceptionCodes.DATE_TIME_ERROR);
        }
        List<String> idList = new ArrayList<>();
        List<TServiceRecord> saveServiceList = new ArrayList<>();
        List<FlightInfoMb> allById =flightInfoBizMapper.listFlightByConfirmCondition(dto);
        if (CollectionUtils.isEmpty(allById)) {
            return Collections.emptyList();
        }
        for (FlightInfoMb flightInfo : allById) {
            // 如果业务状态为已确认则跳过
            if(VariableStatusEnum.CONFIRMED.getValue().equals(flightInfo.getVariableStatus())){
                continue;
            }
            RLock rLock = redissonClient.getLock(buildTenantKey(flightInfo.getId()));
            boolean lockFlag;
            try {
                lockFlag = rLock.tryLock(30, TimeUnit.SECONDS);
            } catch (Exception e) {
                Thread.currentThread().interrupt();
                throw new GenericException(BusinessMessageEnum.LOCK_ERROR.getCode(), BusinessMessageEnum.LOCK_ERROR.getMsg() + flightInfo.getFlightNo());
            }
            if (!lockFlag) {
                throw new GenericException(BusinessMessageEnum.DATA_IS_CONFIRMED.getCode(), BusinessMessageEnum.DATA_IS_CONFIRMED.getMsg() + flightInfo.getFlightNo());
            }
            try {
                flightInfo = flightInfoService.lambdaQuery().eq(FlightInfoMb::getId,flightInfo.getId())
                        .eq(FlightInfoMb::getInvalid,InvalidEnum.NORMAL.getValue())
                        .one();
                judgeFlightInfo(flightInfo);
                flightInfo.setVariableStatus(VariableStatusEnum.CONFIRMED.getValue());
                flightInfo.setConfirmCode(CONFIRM_CODE);
                flightInfoService.updateById(flightInfo);
                idList.add(flightInfo.getId());
                //需要从备份表同步到主表的数据
                List<TServiceRecordConfirm> srcList = serviceRecordConfirmService.lambdaQuery()
                        .eq(TServiceRecordConfirm::getFlightId,flightInfo.getId())
                        .eq(TServiceRecordConfirm::getInvalid,InvalidEnum.NORMAL.getValue())
                        .list();
                for (TServiceRecordConfirm src : srcList) {
                    TServiceRecord sr = new TServiceRecord();
                    copyProperties(src, sr);
                    sr.setId(null);
                    saveServiceList.add(sr);
                }
            } finally {
                rLock.unlock();
            }
        }
        if(!saveServiceList.isEmpty()){
            //删除当前航班的业务保障数据中电子签单已经删除或修改的的数据库
            serviceRecordService.lambdaUpdate().in(TServiceRecord::getFlightId,idList)
                    .set(TServiceRecord::getInvalid,InvalidEnum.DELETED.getValue())
                    .update();
            //删除该航班备份库的数据
            serviceRecordConfirmService.lambdaUpdate().in(TServiceRecordConfirm::getFlightId,idList)
                    .set(TServiceRecordConfirm::getInvalid,InvalidEnum.DELETED.getValue())
                    .update();
        }
        //从备份表将数据同步保存到service_record表
        serviceRecordService.saveOrUpdateBatch(saveServiceList);
        return idList;
    }

    public String buildTenantKey(String key) {
        return "aps:service:confirm:"+TenantHolder.getTenant() + ":" + key;
    }

    public void copyProperties(Object source, Object target) {
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.error("BeanUtils property copy failed,caused by:", e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
    }

    private static void judgeFlightInfo(FlightInfoMb flightInfo) {
        if (flightInfo.getVariableStatus().equals(1)) {
            throw new GenericException(BusinessMessageEnum.DATA_IS_CONFIRMED.getCode(), BusinessMessageEnum.DATA_IS_CONFIRMED.getMsg() + flightInfo.getFlightNo());
        }
        if (StringUtils.isBlank(flightInfo.getRegNo())) {
            throw new GenericException(BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getCode(), BusinessMessageEnum.FLIGHT_CONFIRMED_REG_NULL.getMsg() + flightInfo.getFlightNo());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelConfirmFlightBusinessInfo(String flightIds, LoginUserDetails user) {
        String[] flightIdArr = flightIds.split(",");
        for (String flightId : flightIdArr) {
            FlightInfoMb flightInfo = flightInfoService.lambdaQuery().eq(FlightInfoMb::getId, flightId)
                    .eq(FlightInfoMb::getInvalid, InvalidEnum.NORMAL.getValue()).one();
            flightInfo.setVariableStatus(VariableStatusEnum.TO_BE_CONFIRMED.getValue());
            flightInfoService.updateById(flightInfo);
            //删除业务保障费用账单
            flightBillService.lambdaUpdate().eq(TFlightBill::getFlightId, flightId)
                    .eq(TFlightBill::getInvalid, InvalidEnum.NORMAL.getValue())
                    .eq(TFlightBill::getIsServiceFee, IsServiceFeeEnum.IS_SERVICE_FEE.getValue())
                    .set(TFlightBill::getInvalid, InvalidEnum.DELETED.getValue())
                    .update();
        }
    }



}
