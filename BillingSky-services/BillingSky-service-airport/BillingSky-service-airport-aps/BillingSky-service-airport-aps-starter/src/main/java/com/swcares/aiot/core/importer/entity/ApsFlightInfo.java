package com.swcares.aiot.core.importer.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * ClassName：com.swcares.importer.entity.ApsFlightInfo
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/9/16 11:10
 * @version v1.0
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_flight_info")
public class ApsFlightInfo  implements Serializable, Cloneable {

    /** ID;ID */
    @Id
    @GeneratedValue(generator="myid",strategy = GenerationType.AUTO)
//    @GenericGenerator(name="system-uuid", strategy = "uuid")
    @GenericGenerator(name = "myid", strategy = "com.swcares.aiot.core.importer.generator.ManulInsertGenerator")
    @ApiModelProperty(name = "id",value = "ID")
    @Column(name = "ID",columnDefinition = "VARCHAR(128) NOT NULL   COMMENT 'ID'")
    private String id ;

    /** 创建人 */
    @ApiModelProperty(name = "createBy",value = "创建人")
    @Column(name = "CREATE_BY",columnDefinition = "VARCHAR(128)    COMMENT '创建人'")
    private String createBy ;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8") //出参格式化
    @ApiModelProperty(name = "createTime",value = "创建时间")
    @Column(name = "CREATE_TIME",columnDefinition = "DATETIME    COMMENT '创建时间'")
    private Date createTime ;
    /** 修改人 */
    @ApiModelProperty(name = "modifiedBy",value = "修改人")
    @Column(name = "MODIFIED_BY",columnDefinition = "VARCHAR(128)    COMMENT '修改人'")
    private String modifiedBy ;
    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8") //出参格式化
    @ApiModelProperty(name = "modifiedTime",value = "修改时间")
    @Column(name = "MODIFIED_TIME",columnDefinition = "DATETIME    COMMENT '修改时间'")
    private Date modifiedTime ;

    /** 机场三字码 */
    @ApiModelProperty(name = "airportCode",value = "机场三字码")
    @Column(name = "airport_code",columnDefinition = "VARCHAR(3)    COMMENT '机场三字码'")
    private String airportCode;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(name = "flightDate", value = "航班日期")
    @Column(name = "flight_date", columnDefinition = "DATE    COMMENT '航班日期'")
    private Date flightDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "flightTime", value = "起降时间")
    @Column(name = "flight_time", columnDefinition = "DATETIME    COMMENT '起降时间'")
    private Date flightTime;

    //航班航线信息
    @ApiModelProperty(name = "flightNo", value = "航班号")
    @Column(name = "flight_no", columnDefinition = "VARCHAR(32)    COMMENT '航班号'")
    private String flightNo;

    @ApiModelProperty(name = "flightLine", value = "航线")
    @Column(name = "flight_line", columnDefinition = "VARCHAR(32)    COMMENT '航线'")
    private String flightLine;

    @ApiModelProperty(name = "flightSegment", value = "航段")
    @Column(name = "flight_segment", columnDefinition = "VARCHAR(32)    COMMENT '航段'")
    private String flightSegment;

    @ApiModelProperty(name = "flightType", value = "航班性质 (国际:I|国内:D) ")
    @Column(name = "flight_type", columnDefinition = "CHAR(1)    COMMENT '航班性质 国际:I|国内:D'")
    private String flightType;

    @ApiModelProperty(name = "flightLineType", value = "航线性质 (国际:I|国内:D)")
    @Column(name = "flight_line_type", columnDefinition = "CHAR(1)    COMMENT '航线性质 国际:I|国内:D'")
    private String flightLineType;

    @ApiModelProperty(name = "flightSegmentType", value = "航段性质 (国际:I|国内:D)")
    @Column(name = "flight_segment_type", columnDefinition = "CHAR(1)    COMMENT '航段性质 国际:I|国内:D'")
    private String flightSegmentType;

    @ApiModelProperty(name = "airlineCode", value = "航空公司")
    @Column(name = "airline_code", columnDefinition = "VARCHAR(2)    COMMENT '航空公司'")
    private String airlineCode;

    @ApiModelProperty(name = "regNo", value = "飞机注册号")
    @Column(name = "reg_no", columnDefinition = "VARCHAR(32)    COMMENT '飞机注册号'")
    private String regNo;

    @ApiModelProperty(name = "flightModel", value = "机型")
    @Column(name = "flight_model", columnDefinition = "VARCHAR(32)    COMMENT '机型'")
    private String flightModel;

    @ApiModelProperty(name = "fromAirportCode", value = "出发航站三字码")
    @Column(name = "from_airport_code", columnDefinition = "VARCHAR(3)    COMMENT '出发航站三字码'")
    private String fromAirportCode;

    @ApiModelProperty(name = "toAirportCode", value = "到达航站三字码")
    @Column(name = "to_airport_code", columnDefinition = "VARCHAR(3)    COMMENT '到达航站三字码'")
    private String toAirportCode;

    //航班旅客信息
    @ApiModelProperty(name = "psgNumber", value = "进出港人数")
    @Column(name = "psg_number", columnDefinition = "INT(10)    COMMENT '进出港人数'")
    private Integer psgNumber=0;

    @ApiModelProperty(name = "firstClassNumber", value = "头等舱人数")
    @Column(name = "first_class_number", columnDefinition = "INT(10)    COMMENT '头等舱人数'")
    private Integer firstClassNumber=0;

    @ApiModelProperty(name = "clubClassNumber", value = "商务舱人数")
    @Column(name = "club_class_number", columnDefinition = "INT(10)    COMMENT '商务舱人数'")
    private Integer clubClassNumber=0;

    @ApiModelProperty(name = "economyClassNumber", value = "经济舱人数")
    @Column(name = "economy_class_number", columnDefinition = "INT(10)    COMMENT '经济舱人数'")
    private Integer economyClassNumber=0;

    @ApiModelProperty(name = "adultNumber", value = "成人数")
    @Column(name = "adult_number", columnDefinition = "INT(10)    COMMENT '成人数'")
    private Integer adultNumber=0;

    @ApiModelProperty(name = "childNumber", value = "儿童数")
    @Column(name = "child_number", columnDefinition = "INT(10)    COMMENT '儿童数'")
    private Integer childNumber=0;

    @ApiModelProperty(name = "infantNumber", value = "婴儿数")
    @Column(name = "infant_number", columnDefinition = "INT(10)    COMMENT '婴儿数'")
    private Integer infantNumber=0;

    @ApiModelProperty(name = "transitAdultNumber", value = "过站成人数")
    @Column(name = "transit_adult_number", columnDefinition = "INT(10)    COMMENT '过站成人数'")
    private Integer transitAdultNumber=0;

    @ApiModelProperty(name = "transitChildNumber", value = "过站儿童数")
    @Column(name = "transit_child_number", columnDefinition = "INT(10)    COMMENT '过站儿童数'")
    private Integer transitChildNumber=0;

    @ApiModelProperty(name = "transitInfantNumber", value = "过站婴儿数")
    @Column(name = "transit_infant_number", columnDefinition = "INT(10)    COMMENT '过站婴儿数'")
    private Integer transitInfantNumber=0;

    @ApiModelProperty(name = "diplomaticPassportNumber", value = "持外交护照人数")
    @Column(name = "diplomatic_passport_number", columnDefinition = "INT(10)    COMMENT '持外交护照人数'")
    private Integer diplomaticPassportNumber=0;

    @ApiModelProperty(name = "cardHolderNumber", value = "持卡旅客人数")
    @Column(name = "card_holder_number", columnDefinition = "INT(10)    COMMENT '持卡旅客人数'")
    private Integer cardHolderNumber=0;

    @ApiModelProperty(name = "accompanyingCardHolderNumber", value = "持卡旅客随行人数")
    @Column(name = "accompanying_card_holder_number", columnDefinition = "INT(10)    COMMENT '持卡旅客随行人数'")
    private Integer accompanyingCardHolderNumber=0;

    @ApiModelProperty(name = "importantNumber", value = "重要旅客人数")
    @Column(name = "important_number", columnDefinition = "INT(10)    COMMENT '重要旅客人数'")
    private Integer importantNumber=0;

    @ApiModelProperty(name = "accompanyingImportantNumber", value = "重要旅客随行人数")
    @Column(name = "accompanying_important_number", columnDefinition = "INT(10)    COMMENT '重要旅客随行人数'")
    private Integer accompanyingImportantNumber=0;

    //行李货邮信息
    @ApiModelProperty(name = "plf", value = "客坐率")
    @Column(name = "plf", columnDefinition = "DECIMAL(5,2)    COMMENT '客坐率'")
    private Double plf=0.0;

    @ApiModelProperty(name = "cargo", value = "货物重量")
    @Column(name = "cargo", columnDefinition = "DECIMAL(32,4)    COMMENT '货物重量'")
    private Double cargo=0.0;

    @ApiModelProperty(name = "mail", value = "邮件重量")
    @Column(name = "mail", columnDefinition = "DECIMAL(32,4)    COMMENT '邮件重量'")
    private Double mail=0.0;

    @ApiModelProperty(name = "bag", value = "行李重量")
    @Column(name = "bag", columnDefinition = "DECIMAL(32,4)    COMMENT '行李重量'")
    private Double bag=0.0;

    @ApiModelProperty(name = "bagNumber", value = "行李件数")
    @Column(name = "bag_number", columnDefinition = "INT(10)    COMMENT '行李件数'")
    private Integer bagNumber=0;

    @ApiModelProperty(name = "weightUints", value = "重量单位")
    @Column(name = "weight_uints", columnDefinition = "VARCHAR(3)    COMMENT '重量单位'")
    private String weightUints="KG";

    @ApiModelProperty(name = "flightFlag", value = "起降标识 (起飞:D|到达:A)")
    @Column(name = "flight_flag", columnDefinition = "CHAR(1)    COMMENT '起降标识'")
    private String flightFlag;

    @ApiModelProperty(name = "taskFlag", value = "任务标识")
    @Column(name = "task_flag", columnDefinition = "VARCHAR(3)    COMMENT '任务标识'")
    private String taskFlag;

    @ApiModelProperty(name = "daAirportCode", value = "采集机场三字码")
    @Column(name = "da_airport_code", columnDefinition = "VARCHAR(3)    COMMENT '采集机场三字码'")
    private String daAirportCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "daTime", value = "采集时间")
    @Column(name = "da_time", columnDefinition = "DATETIME    COMMENT '采集时间'")
    private Date daTime;

    @ApiModelProperty(name = "flightFee", value = "起降费用标识 (起飞费用:1|降落费用:0)")
    @Column(name = "flight_fee", columnDefinition = "CHAR(1)    COMMENT '起降费用标识'")
    private String flightFee;

    @ApiModelProperty(name = "groundFee", value = "地面服务费用标识")
    @Column(name = "ground_fee", columnDefinition = "VARCHAR(3)    COMMENT '地面服务费用标识'")
    private String groundFee;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "stayStartTime", value = "停场开始时间")
    @Column(name = "stay_start_time", columnDefinition = "DATETIME    COMMENT '停场开始时间'")
    private Date stayStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "stayEndTime", value = "停场结束时间")
    @Column(name = "stay_end_time", columnDefinition = "DATETIME    COMMENT '停场结束时间'")
    private Date stayEndTime;

    @ApiModelProperty(name = "stayTime", value = "停场时间")
    @Column(name = "stay_time", columnDefinition = "INT(10)    COMMENT '停场时间'")
    private BigDecimal stayTime;

    @ApiModelProperty(name = "invalid", value = "数据有效 (0:有效|1:无效)")
    @Column(name = "invalid", columnDefinition = "CHAR(1)    COMMENT '数据有效 有效:1|无效:0'")
    private String invalid = "1";

    @ApiModelProperty(name = "dataStatus", value = "数据状态 (0:未确认|1:已确认|2:已修改|3：取消确认后待确认)")
    @Column(name = "data_status", columnDefinition = "INT(1)    COMMENT '数据状态 (0:未确认|1:已确认|2:已修改|3：取消确认后待确认)'")
    private Integer dataStatus = 0;

    @ApiModelProperty(name = "confirmCode", value = "数据确认代码")
    @Column(name = "confirm_code", columnDefinition = "VARCHAR(20)    COMMENT '数据确认代码'")
    private String confirmCode = "111111111111111111";

    @ApiModelProperty(name = "mergeFlag", value = "合并标识")
    @Column(name = "merge_flag", columnDefinition = "VARCHAR(20)    COMMENT '合并标识'")
    private String mergeFlag;

    @ApiModelProperty(name = "flightStatus", value = "航班状态")
    @Column(name = "flight_status", columnDefinition = "VARCHAR(20)    COMMENT '航班状态'")
    private String flightStatus;

    @ApiModelProperty(name = "isModifyFlightTime", value = "是否手动修改起降时间（0为否，1为是）")
    @Column(name = "is_modify_flight_time" , columnDefinition = "INT(1) COMMENT '是否手动修改起降时间（0为否，1为是）'")
    private int isModifyFlightTime=0;

    @ApiModelProperty(name = "variableStatus", value = "保障数据状态（0:未确认|1:已确认|2:已修改|3：取消确认后待确认）")
    @Column(name = "variable_status", columnDefinition = "INT(1)    COMMENT '保障数据状态（0:未确认|1:已确认|2:已修改|3：取消确认后待确认）'")
    private Integer variableStatus=0;

    @ApiModelProperty(name = "preFlightId", value = "前序航班id")
    @Column(name = "pre_flight_id", columnDefinition = "VARCHAR(128)    COMMENT '前序航班id'")
    private String preFlightId;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApsFlightInfo that = (ApsFlightInfo) o;
        return isModifyFlightTime == that.isModifyFlightTime && Objects.equals(flightDate, that.flightDate) && Objects.equals(flightTime, that.flightTime) && Objects.equals(flightNo, that.flightNo) && Objects.equals(flightLine, that.flightLine) && Objects.equals(flightSegment, that.flightSegment) && Objects.equals(flightLineType, that.flightLineType) && Objects.equals(regNo, that.regNo) && Objects.equals(psgNumber, that.psgNumber) && Objects.equals(firstClassNumber, that.firstClassNumber) && Objects.equals(clubClassNumber, that.clubClassNumber) && Objects.equals(economyClassNumber, that.economyClassNumber) && Objects.equals(adultNumber, that.adultNumber) && Objects.equals(childNumber, that.childNumber) && Objects.equals(infantNumber, that.infantNumber) && Objects.equals(transitAdultNumber, that.transitAdultNumber) && Objects.equals(transitChildNumber, that.transitChildNumber) && Objects.equals(transitInfantNumber, that.transitInfantNumber) && Objects.equals(diplomaticPassportNumber, that.diplomaticPassportNumber) && Objects.equals(cardHolderNumber, that.cardHolderNumber) && Objects.equals(accompanyingImportantNumber, that.accompanyingImportantNumber) && Objects.equals(cargo, that.cargo) && Objects.equals(mail, that.mail) && Objects.equals(bag, that.bag) && Objects.equals(bagNumber, that.bagNumber) && Objects.equals(flightFlag, that.flightFlag) && Objects.equals(taskFlag, that.taskFlag) && Objects.equals(stayStartTime, that.stayStartTime) && Objects.equals(stayEndTime, that.stayEndTime) && Objects.equals(confirmCode, that.confirmCode) && Objects.equals(variableStatus, that.variableStatus);
    }

    @Override
    public int hashCode() {
        return Objects.hash(flightDate, flightTime, flightNo, flightLine, flightSegment, flightLineType, regNo, psgNumber, firstClassNumber, clubClassNumber, economyClassNumber, adultNumber, childNumber, infantNumber, transitAdultNumber, transitChildNumber, transitInfantNumber, diplomaticPassportNumber, cardHolderNumber, accompanyingImportantNumber, cargo, mail, bag, bagNumber, flightFlag, taskFlag, stayStartTime, stayEndTime, confirmCode, isModifyFlightTime, variableStatus);
    }

}
