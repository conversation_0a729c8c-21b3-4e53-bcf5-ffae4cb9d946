package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.client.IConfDictMgtBizClient;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormulaUtils;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.dto.FlightSelectorItemDTO;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.FormulaForm;
import com.swcares.aiot.core.form.UseFormulaForm;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.model.vo.FormulaVo;
import com.swcares.aiot.core.model.vo.SafeguardTypeVo;
import com.swcares.aiot.dao.BillItemDao;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.dao.FormulaDao;
import com.swcares.aiot.model.dto.FormulaExpiredDto;
import com.swcares.aiot.model.vo.ConfDictCacheVo;
import com.swcares.aiot.service.FormulaService;
import com.swcares.aiot.service.LogService;
import com.swcares.aiot.utils.FormulaExpiredUtils;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import static com.swcares.aiot.core.common.util.FormulaUtils.checkFormulaData;


/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a>
 * Title：FormulaServiceImpl.java
 * Package：com.swcares.modules.settlement.service.impl
 * Description：公式service的实现类
 * Copyright © 2020-6-8 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * date 2020-6-8 15:05
 */
@Service
@Slf4j
public class FormulaServiceImpl implements FormulaService {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    public static final String EXPIRED_VALUE="1";
    public static final String NOT_EXPIRED_VALUE="0";
    @Resource
    private FormulaDao formulaDao;
    @Resource
    private FeeDao feeDao;
    @Resource
    private LogService logService;
    @Resource
    private IConfDictMgtBizClient confDictMgtBizClient;
    @Resource
    private IConfConfigBizClient iConfConfigBizClient;
    @Resource
    private BillItemDao billItemDao;

    private static final String NOTE = "出现业务异常";
    

    @Override
    public ResultBuilder<List<FunctionInfo>> listFunction() {
        return new ResultBuilder.Builder<List<FunctionInfo>>().data(formulaDao.listFunction()).builder();
    }

    @Transactional
    @Override
    public ResultBuilder<Object> saveFormula(FormulaForm formulaForm, LoginUserDetails user, String urlName) {
        // 查看公式名称是否已存在
        if (formulaDao.findFormulaInfoByName(formulaForm.getFormulaName(), formulaForm.getFeeRulesId()) != null) {
            throw new GenericException(BusinessMessageEnum.FORMULA_SAVE_ERROR.getCode(), BusinessMessageEnum.FORMULA_SAVE_ERROR.getMsg());
        }
        // 校验公式数据
        checkFormulaData(formulaForm);
        List<FormulaInfo> formulaInfos = formulaDao.listFormulaByFeeId(formulaForm.getFeeRulesId());
        handleFormulaInfo(formulaForm, formulaInfos);

        FormulaInfo formulaInfo = new FormulaInfo();
        // 查询该公式所属费用信息
        FeeInfo feeInfo = feeDao.findByFeeId(formulaForm.getFeeRulesId());
        // 判断该公式是否为带条件公式
        if (formulaForm.getPricingWay().equals(Constants.FormulaEnum.PRICING_WAY_CONDITION.getValue())) {
            List<FormulaVo> list = formulaForm.getFormulaVoList();
            formulaForm.setFormula(FormulaUtils.formatFormula(list));
        } else {
            // 判断公式是否为空
            if (CharSequenceUtil.isBlank(formulaForm.getFormula())) {
                throw new GenericException(BusinessMessageEnum.INPUT_ERROR.getCode(), BusinessMessageEnum.INPUT_ERROR.getMsg());
            }
        }
        // 判断是否包含中文字符
        if (FileUtils.hasChinese(formulaForm.getFormula())) {
            log.error(BusinessMessageEnum.FORMULA_FORMAT_ERROR.getMsg());
            throw new GenericException(BusinessMessageEnum.FORMULA_FORMAT_ERROR.getCode(),
                    BusinessMessageEnum.FORMULA_FORMAT_ERROR.getMsg());
        }
        try {
            BeanUtils.copyProperties(formulaInfo, formulaForm);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(NOTE, e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
        formulaInfo.setAirlineId(feeInfo.getAirlineId());

        handleFormula(formulaInfo);
        formulaInfo.setAltSpecial(formulaForm.getAltSpecial());
        formulaInfo.setSpecialVariable(formulaForm.getSpecialVariable());
        formulaInfo.setFlightType(String.join(",",formulaForm.getFlightType()));

        formulaInfo.setCreateBy(user.getUsername());
        formulaInfo.setCreateTime(new Date());
        formulaDao.save(formulaInfo);
        handleFormulaInfo(formulaInfo);

        changeIsExpired(feeInfo);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_SAVE.getValue();
        formulaForm.setFormulaId(null);
        formulaForm.setFormulaVoList(null);
        logService.addLogForSave(formulaForm, user, content, feeInfo.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    private void handleFormula(FormulaInfo formulaInfo) {
        // 根据公式中的引用变量，判断公式参数类型
        // 参数为航班确认数据
        boolean flightVar = false;
        // 参数为保障确定数据
        boolean serviceVar = false;
        String formula = formulaInfo.getFormula();
        if (formula.contains("{F") || formula.contains("{P")) {
            flightVar = true;
        }
        if (formula.contains("{C")) {
            serviceVar = true;
        }
        if (flightVar && serviceVar) {
            formulaInfo.setFormulaType("4");
        } else if (serviceVar) {
            formulaInfo.setFormulaType("3");
        } else if (flightVar) {
            formulaInfo.setFormulaType("2");
        } else {
            formulaInfo.setFormulaType("1");
        }
    }

    private void handleFormulaInfo(FormulaInfo formulaInfo) {
        // 判断该公式是否引用了费用
        if (formulaInfo.getFormula().contains("[") && formulaInfo.getFormula().contains("]")) {
            // 修改引用费用的费用等级
            FeeInfo feeInfo1 = feeDao.findByFeeId(formulaInfo.getFormula().substring(formulaInfo.getFormula().indexOf("[") + 1, formulaInfo.getFormula().indexOf("]")));
            feeInfo1.setFeePriority(feeInfo1.getFeePriority() + 1);
            feeDao.save(feeInfo1);
        }
    }

    private void handleFormulaInfo(FormulaForm formulaForm, List<FormulaInfo> formulaInfos) {
        if (ObjectUtils.isNotEmpty(formulaInfos)) {
            FormulaInfo formulaInfo = formulaInfos.get(0);
            // 判断生效时间互斥性
            // 获取航司下同一费用下相同适用范围已有的所有公式中最大的结束时间
            Date maxEndDate = formulaDao.findMaxEndDateIncludeSelf(formulaInfo.getFlightType(), formulaInfo.getFeeRulesId());
            // 获取航司下同一费用下相同适用范围已有的所有公式中最小的结束时间
            Date minStartDate = formulaDao.findMinStartDateIncludeSelf(formulaInfo.getFlightType(), formulaInfo.getFeeRulesId());

            if ((maxEndDate != null && minStartDate != null && formulaForm.getStartDate().getTime() <= minStartDate.getTime()
                    && formulaForm.getEndDate().getTime() >= minStartDate.getTime())
                    ||(maxEndDate != null && minStartDate != null && formulaForm.getStartDate().getTime() >= minStartDate.getTime()
                    && formulaForm.getStartDate().getTime() <= maxEndDate.getTime())) {
                log.error(BusinessMessageEnum.FORMULA_ERROR.getMsg());
                throw new GenericException(BusinessMessageEnum.FORMULA_ERROR.getCode(), BusinessMessageEnum.FORMULA_ERROR.getMsg() + maxEndDate);
            }
        }
    }

    @Override
    @Transactional
    public ResultBuilder<Object> delFormulaById(String formulaId, LoginUserDetails user, String urlName) {
        // 根据公式id查询该公式信息
        FormulaInfo formulaInfo = formulaDao.findByFormulaId(formulaId);
        // 根据该公式的费用id查询该公式所属的费用信息
        FeeInfo feeInfo = feeDao.findByFeeId(formulaInfo.getFeeRulesId());
        formulaDao.delFormulaById(formulaId, user.getUsername(), new Date());
        // 判断该公式是否引用了费用
        if (formulaInfo.getFormula().contains("[") && formulaInfo.getFormula().contains("]")) {
            // 修改引用费用的费用等级
            FeeInfo feeInfo2 = feeDao.findByFeeId(formulaInfo.getFormula().substring(formulaInfo.getFormula().indexOf("[") + 1, formulaInfo.getFormula().indexOf("]")));
            feeInfo2.setFeePriority(feeInfo2.getFeePriority() - 1);
            feeDao.save(feeInfo2);
        }

        changeIsExpired(feeInfo);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_DELETE.getValue();
        FormulaForm form = new FormulaForm();
        try {
            BeanUtils.copyProperties(form, formulaInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(NOTE, e);
        }
        form.setFormulaId(null);
        form.setFormulaVoList(null);
        logService.addLogForDelete(form, user, content, feeInfo.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<FormulaForm> getFormulaById(String formulaId) {
        FormulaForm formulaForm = new FormulaForm();
        FormulaInfo formulaInfo = formulaDao.findByFormulaId(formulaId);
        if (formulaInfo.getFormula().contains("[") && formulaInfo.getFormula().contains("]")) {
            formulaForm.setQuotativeFeeName(feeDao.findByFeeId(
                            formulaInfo.getFormula().substring(formulaInfo.getFormula().indexOf("[") + 1,
                                    formulaInfo.getFormula().indexOf("]")))
                    .getFeeName());
            formulaForm.setQuotativeFeeInfo(feeDao.findByFeeId(
                    formulaInfo.getFormula().substring(formulaInfo.getFormula().indexOf("[") + 1,
                            formulaInfo.getFormula().indexOf("]"))));
        }
        if (formulaInfo.getFormula().contains("{") && formulaInfo.getFormula().contains("}")) {
            formulaForm
                    .setQuotativeVariableName(
                            formulaDao
                                    .findByVariable(formulaInfo.getFormula().substring(
                                            formulaInfo.getFormula().indexOf("{") + 1,
                                            formulaInfo.getFormula().indexOf("}")))
                                    .getVariableName());
        }
        try {
            BeanUtils.copyProperties(formulaForm, formulaInfo);
            formulaForm.setFlightType(formulaInfo.getFlightType().split(","));
            if (formulaInfo.getPricingWay()
                    .equals(Constants.FormulaEnum.PRICING_WAY_CONDITION.getValue())) {
                dealFormula(formulaForm);
            }
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(NOTE, e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(),
                    BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
        return new ResultBuilder.Builder<FormulaForm>().data(formulaForm).builder();
    }

    /*
     * Title: dealFormula<br> Author: 李龙<br> Description: 处理从数据库中查出的带条件的公式<br> Date: 15:19 <br>
     *
     * @param formulaForm return: void
     */
    private void dealFormula(FormulaForm formulaForm) {
        String formula = formulaForm.getFormula();
        String[] list = formula.split(";");
        ArrayList<FormulaVo> formulaVos = new ArrayList<>();
        for (String s : list) {
            FormulaVo formulaVo = new FormulaVo();
            String[] datas = s.split(",");
            formulaVo.setCondition(datas[0]);
            formulaVo.setConditionValue(datas[1]);
            formulaVo.setResults(datas[2]);
            formulaVos.add(formulaVo);
        }
        formulaForm.setFormulaVoList(formulaVos);
    }

    @Override
    @Transactional
    public ResultBuilder<Object> updateFormula(FormulaForm formulaForm, LoginUserDetails user, String urlName) {
        // 校验公式数据
        checkFormulaData(formulaForm);
        FormulaInfo formulaInfo = formulaDao.findByFormulaId(formulaForm.getFormulaId());
        FormulaForm formulaForm1 = new FormulaForm();
        try {
            BeanUtils.copyProperties(formulaForm1, formulaInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(NOTE, e);
        }
        if (formulaForm.getPricingWay().equals(Constants.FormulaEnum.PRICING_WAY_CONDITION.getValue())) {
            List<FormulaVo> list = formulaForm.getFormulaVoList();
            formulaForm.setFormula(FormulaUtils.formatFormula(list));
        } else {
            if (CharSequenceUtil.isBlank(formulaForm.getFormula())) {
                throw new GenericException(BusinessMessageEnum.INPUT_ERROR.getCode(), BusinessMessageEnum.INPUT_ERROR.getMsg());
            }
        }
        check(formulaForm, formulaInfo);
        // 获取修改前引用费用
        String oldFeeId = "";
        if (formulaInfo.getFormula().contains("[") && formulaInfo.getFormula().contains("]")) {
            oldFeeId = formulaInfo.getFormula().substring(formulaInfo.getFormula().indexOf("[") + 1, formulaInfo.getFormula().indexOf("]"));
        }
        String formula = formulaForm.getFormula();
        formulaInfo.setFormula(formula);

        checkFlightVar(formula, formulaInfo);

        formulaInfo.setModifiedBy(user.getUsername());
        formulaInfo.setModifiedTime(new Date());
        FormulaInfo formulaInfo1 = formulaDao.findByFormulaId(formulaForm.getFormulaId());
        if (ObjectUtils.isNotEmpty(formulaInfo1)) {
            // 判断生效时间互斥性
            List<FormulaInfo> otherFormulaList=formulaDao.findOtherFormula(formulaInfo1.getFeeRulesId(), formulaInfo1.getId());
            for(FormulaInfo otherFormula:otherFormulaList) {
                isDateInvalid(otherFormula.getStartDate(),otherFormula.getEndDate(),formulaForm);
            }
        }
        formulaInfo.setAltSpecial(formulaForm.getAltSpecial());
        formulaInfo.setSpecialVariable(formulaForm.getSpecialVariable());
        formulaInfo.setBelongWay(formulaForm.getBelongWay());
        formulaInfo.setFlightType(String.join(",", formulaForm.getFlightType()));
        formulaInfo.setIsTransit(formulaForm.getIsTransit());
        formulaInfo.setFormulaName(formulaForm.getFormulaName());

        formulaInfo.setStartDate(formulaForm.getStartDate());
        formulaInfo.setEndDate(formulaForm.getEndDate());
        formulaInfo.setSafeguardType(formulaForm.getSafeguardType());
        formulaDao.save(formulaInfo);
        // 判断该公式是否引用了费用
        boolean needChange = true;
        needChange = isNeedChange(formulaInfo, oldFeeId, needChange);
        if (needChange && StringUtils.isNotBlank(oldFeeId)) {
            FeeInfo feeInfo2 = feeDao.findByFeeId(oldFeeId);
            feeInfo2.setFeePriority(feeInfo2.getFeePriority() - 1);
            feeDao.save(feeInfo2);
        }
        // 模板公式修改后，所有引用了模板公式的公式内容也要修改
        // 查询该公式所属的费用信息
        FeeInfo feeInfo = feeDao.findByFeeId(formulaInfo.getFeeRulesId());
        // 更新费用修改时间及人
        feeInfo.setModifiedBy(user.getUsername());
        feeInfo.setModifiedTime(new Date());
        feeDao.save(feeInfo);

        changeIsExpired(feeInfo);
        // 新增日志
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName
                + Constants.LogEnum.LOG_MEG_UPDATE.getValue();
        formulaForm1.setFormulaVoList(null);
        formulaForm.setFormulaVoList(null);
        formulaForm.setFormulaId(null);
        logService.addLogForUpdate(formulaForm1, formulaForm, user, content,
                feeInfo.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    private boolean isNeedChange(FormulaInfo formulaInfo, String oldFeeId, boolean needChange) {
        String newFeeId;
        if (formulaInfo.getFormula().contains("[") && formulaInfo.getFormula().contains("]")) {
            // 如果修改前后引用费用id不一致，则修改引用费用的费用等级
            newFeeId = formulaInfo.getFormula().substring(formulaInfo.getFormula().indexOf("[") + 1, formulaInfo.getFormula().indexOf("]"));
            if (!newFeeId.equals(oldFeeId)) {
                FeeInfo feeInfo1 = feeDao.findByFeeId(newFeeId);
                feeInfo1.setFeePriority(feeInfo1.getFeePriority() + 1);
                feeDao.save(feeInfo1);
                // 如果修改前引用费用id不为空，则将修改前引用费用等级降低
            } else {
                needChange = false;
            }
        }
        return needChange;
    }

    private void checkFlightVar(String formula, FormulaInfo formulaInfo) {
        // 根据公式中的引用变量，判断公式参数类型
        // 参数为航班确认数据
        boolean flightVar = false;
        // 参数为保障确定数据
        boolean serviceVar = false;
        if (formula.contains("{F") || formula.contains("{P")) {
            flightVar = true;
        }
        if (formula.contains("{C")) {
            serviceVar = true;
        }
        if (flightVar && serviceVar) {
            formulaInfo.setFormulaType("4");
        } else if (serviceVar) {
            formulaInfo.setFormulaType("3");
        } else if (flightVar) {
            formulaInfo.setFormulaType("2");
        } else {
            formulaInfo.setFormulaType("1");
        }
    }

    private void check(FormulaForm formulaForm, FormulaInfo formulaInfo) {
        if (FileUtils.hasChinese(formulaForm.getFormula())) {
            log.error(BusinessMessageEnum.FORMULA_FORMAT_ERROR.getMsg());
            throw new GenericException(BusinessMessageEnum.FORMULA_FORMAT_ERROR.getCode(), BusinessMessageEnum.FORMULA_FORMAT_ERROR.getMsg());
        }
        if (CharSequenceUtil.isNotBlank(formulaForm.getDescription())) {
            formulaInfo.setDescription(formulaForm.getDescription());
        } else {
            formulaInfo.setDescription(null);
        }
        if (CharSequenceUtil.isNotBlank(formulaForm.getPricingWay())) {
            formulaInfo.setPricingWay(formulaForm.getPricingWay());
        }
        if (CharSequenceUtil.isNotBlank(formulaForm.getFeeUnit())) {
            formulaInfo.setFeeUnit(formulaForm.getFeeUnit());
        }
    }


    private void isDateInvalid(Date minStartDate, Date maxEndDate, FormulaForm formulaForm) {
        if ((maxEndDate != null && minStartDate != null && formulaForm.getStartDate().getTime() <= minStartDate.getTime()
                && formulaForm.getEndDate().getTime() >= minStartDate.getTime())
                || (maxEndDate != null && minStartDate != null && formulaForm.getStartDate().getTime() <= maxEndDate.getTime()
                && formulaForm.getEndDate().getTime() >= maxEndDate.getTime())) {
            log.error(BusinessMessageEnum.FORMULA_ERROR.getMsg());
            throw new GenericException(BusinessMessageEnum.FORMULA_ERROR.getCode(), BusinessMessageEnum.FORMULA_ERROR.getMsg() + DateUtils.format(minStartDate) + "-" + DateUtils.format(maxEndDate));
        }
    }

    @Override
    @Transactional
    public ResultBuilder<FormulaInfo> useFormula(UseFormulaForm useFormulaForm, LoginUserDetails user, String urlName) {
        if ("+".equals(useFormulaForm.getCalcWay()) && (Double.parseDouble(useFormulaForm.getCalcVariable()) > 9999.99)) {
            throw new GenericException(BusinessMessageEnum.CALCVARIABLE_TOO_BIG.getCode(), BusinessMessageEnum.CALCVARIABLE_TOO_BIG.getMsg());
        }
        // 公式信息
        FormulaInfo formulaInfo = formulaDao.findByFormulaId(useFormulaForm.getFormulaId());
        UseFormulaForm useFormulaForm1 = new UseFormulaForm();
        try {
            BeanUtils.copyProperties(useFormulaForm1, formulaInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(NOTE, e);
        }
        // 获取公式内容
        String formula = formulaInfo.getFormula();
        checkFormula(formula,formulaInfo,useFormulaForm);
        // 判断生效时间互斥性
        // 获取航司下同一费用下相同适用范围已有的所有公式中最大的结束时间
        Date maxEndDate = formulaDao.findMaxEndDate(formulaInfo.getFlightType(),
                formulaInfo.getFeeRulesId(), formulaInfo.getId());
        // 获取航司下同一费用下相同适用范围已有的所有公式中最小的结束时间
        Date minStartDate = formulaDao.findMinStartDate(formulaInfo.getFlightType(), formulaInfo.getFeeRulesId(), formulaInfo.getId());

        if ((maxEndDate != null && minStartDate != null
                && DateUtils.parseDate(useFormulaForm.getStartDate()).getTime() <= minStartDate.getTime()
                && DateUtils.parseDate(useFormulaForm.getEndDate()).getTime() >= minStartDate.getTime())||
                (maxEndDate != null && minStartDate != null
                && DateUtils.parseDate(useFormulaForm.getStartDate()).getTime() >= minStartDate.getTime()
                && DateUtils.parseDate(useFormulaForm.getStartDate()).getTime() <= maxEndDate.getTime())) {
            log.error(BusinessMessageEnum.FORMULA_ERROR.getMsg());
            throw new GenericException(BusinessMessageEnum.FORMULA_ERROR.getCode(), BusinessMessageEnum.FORMULA_ERROR.getMsg() + maxEndDate);
        }

        // 给公式绑定采用公式的各种信息
        formulaInfo.setAirlineId(useFormulaForm.getAirlineId());
        formulaInfo.setCalcVariable(useFormulaForm.getCalcVariable());
        formulaInfo.setActive(useFormulaForm.getActive());
        formulaInfo.setCalcWay(useFormulaForm.getCalcWay());
        Date startDate = DateUtils.parseDate(useFormulaForm.getStartDate());
        Date endDate = DateUtils.parseDate(useFormulaForm.getEndDate());
        Date now = new Date();
        if (now.getTime() >= startDate.getTime() && now.getTime() <= endDate.getTime()) {
            formulaInfo.setFormulaStatus(Constants.FormulaEnum.FORMULA_STATUS_ACTIVE.getValue());
        } else if (now.getTime() < startDate.getTime()) {
            formulaInfo.setFormulaStatus(Constants.FormulaEnum.FORMULA_STATUS_PREPARE.getValue());
        } else {
            formulaInfo.setFormulaStatus(Constants.FormulaEnum.FORMULA_STATUS_OVERDUE.getValue());
        }
        formulaInfo.setStartDate(startDate);
        formulaInfo.setEndDate(endDate);
        formulaInfo.setModifiedBy(user.getUsername());
        formulaInfo.setModifiedTime(new Date());

        // 新增日志
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_UPDATE.getValue();
        FeeInfo feeInfo = feeDao.findByFeeId(formulaInfo.getFeeRulesId());
        // 日志中排除id
        useFormulaForm.setFormulaId(null);
        useFormulaForm.setAirlineId(null);
        useFormulaForm1.setAirlineId(null);
        logService.addLogForUpdate(useFormulaForm1, useFormulaForm, user, content, feeInfo.getAirportCode());
        return new ResultBuilder.Builder<FormulaInfo>().data(formulaDao.save(formulaInfo)).builder();
    }


    private void checkFormula(String formula,FormulaInfo formulaInfo,UseFormulaForm useFormulaForm){
        // 解析公式，判断公式中是否引用了费用
        if (formula.contains("[") && formula.contains("]")) {
            String useFeeId = formula.substring(formula.indexOf("[") + 1, formula.indexOf("]"));
            // 查询引用费用的信息
            FeeInfo useFeeInfo = feeDao.findByFeeId(useFeeId);
            FeeInfo airlineFeeInfo = feeDao.findAirlineFeeByFeeCode(useFeeInfo.getFeeCode(), useFormulaForm.getAirlineId());
            // 如果该费用已经被该航司引用，则将公式中的费用id，替换为航司已经引用公式的费用id,否则抛该航司未维护公式使用的费用
            if (airlineFeeInfo != null) {
                List<FormulaInfo> formulaInfos = formulaDao.listFormulaByFeeId(airlineFeeInfo.getId());
                int temp = 0;
                for (FormulaInfo formulaInfo1 : formulaInfos) {
                    if ("1".equals(formulaInfo1.getActive())) {
                        temp++;
                    }
                }
                // 替换为航司已经引用公式的费用id
                formulaInfo.setFormula(StringUtils.replaceAll(formula, useFeeId, airlineFeeInfo.getId()));
                if (temp <= 0) {
                    log.error(BusinessMessageEnum.USEFORMULA_ERROR.getMsg() + useFeeInfo.getFeeName());
                    throw new GenericException(BusinessMessageEnum.USEFORMULA_ERROR.getCode(), BusinessMessageEnum.USEFORMULA_ERROR.getMsg() + useFeeInfo.getFeeName());
                }
            } else {
                log.error(BusinessMessageEnum.USEFORMULA_ERROR.getMsg() + useFeeInfo.getFeeName());
                throw new GenericException(BusinessMessageEnum.USEFORMULA_ERROR.getCode(), BusinessMessageEnum.USEFORMULA_ERROR.getMsg() + useFeeInfo.getFeeName());
            }
        }
    }

    @Override
    @Transactional
    public ResultBuilder<Object> cancelUseFormula(String formulaId, LoginUserDetails user, String urlName) {
        FormulaInfo formulaInfo = formulaDao.findByFormulaId(formulaId);
        FormulaForm formulaForm = new FormulaForm();
        try {
            BeanUtils.copyProperties(formulaForm, formulaInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(NOTE, e);
        }
        String formula = formulaForm.getFormula();
        // 如果有引用其他费用，则在取消采用时，将引用的航司费用改回模板费用
        if (formula.contains("[") && formula.contains("]")) {
            String useFeeId = formula.substring(formula.indexOf("[") + 1, formula.indexOf("]"));
            FeeInfo useFeeInfo = feeDao.findByFeeId(useFeeId);
            FeeInfo modelFeeInfo = feeDao.findByFeeCode(useFeeInfo.getFeeCode(), useFeeInfo.getFeeType());
            formula = formula.replaceAll(useFeeId, modelFeeInfo.getId());
        }
        // 将采用字段设为未采用，并将公式中引用费用设为模版费用
        formulaDao.cancelUseFormula(formulaId, formula);
        String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_UPDATE.getValue();
        formulaForm.setFormulaId(null);
        FormulaForm formulaForm1 = new FormulaForm();
        try {
            BeanUtils.copyProperties(formulaForm1, formulaForm);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(NOTE, e);
        }
        formulaForm1.setActive("0");
        FeeInfo feeInfo = feeDao.findByFeeId(formulaInfo.getFeeRulesId());
        logService.addLogForUpdate(formulaForm, formulaForm1, user, content, feeInfo.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<String> findFormulaInfoByName(String formulaName, String feeId) {
        return new ResultBuilder.Builder<String>().data(formulaDao.findFormulaInfoByName(formulaName, feeId)).builder();
    }

    @Override
    @Transactional
    public ResultBuilder<List<FeeInfo>> fastCopyFormula(String airlineId, String useAirlineId, LoginUserDetails user, String airportCode) {
        // 根据航司id查询航司下的费用信息
        List<FeeInfo> listFee = feeDao.listFeeRuleByAirlineId(useAirlineId, airportCode);
        // 返回复制操作完后新航司下的费用信息
        List<FeeInfo> feeInfos = new ArrayList<>();
        // 判断选择使用的航司下有没有维护费用
        if (listFee.isEmpty()) {
            log.error(BusinessMessageEnum.CPOY_FEE_ERROR.getMsg());
            throw new GenericException(BusinessMessageEnum.CPOY_FEE_ERROR.getCode(), BusinessMessageEnum.CPOY_FEE_ERROR.getMsg());
        }
        formulaDao.deleteAllByAirlineId(airlineId, airportCode, user.getUsername(), new Date());
        feeDao.deleteAllByAirlineId(airlineId, airportCode, user.getUsername(), new Date());
        Map<String, String> oldFeeIdToNewFeeId = new HashMap<>();
        List<FormulaInfo> saveFormulaList = new ArrayList<>();
        for (FeeInfo fee : listFee) {
            // 根据费用id查询费用下的公式信息
            // 复制，新增查询出来的费用信息
            FeeInfo feeInfo = new FeeInfo();
            try {
                BeanUtils.copyProperties(feeInfo, fee);
                feeInfo.setId(null);
                feeInfo.setAirlineId(airlineId);
                feeInfo.setCreateTime(new Date());
                feeInfo.setCreateBy(user.getUsername());
                feeDao.save(feeInfo);
                feeInfos.add(feeInfo);
                oldFeeIdToNewFeeId.put(fee.getId(), feeInfo.getId());
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(NOTE, e);
                throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
            }
        }
        List<FormulaInfo> formulaInfos = formulaDao.getAllByAirlineId(useAirlineId, airportCode);
        // 复制，新增查询出来的公式信息
        for (FormulaInfo oldFormulaInfo : formulaInfos) {
            FormulaForm formulaForm = new FormulaForm();
            FormulaInfo formulaInfo = new FormulaInfo();
            try {
                BeanUtils.copyProperties(formulaForm, oldFormulaInfo);
                BeanUtils.copyProperties(formulaInfo, formulaForm);
                formulaInfo.setFlightType(oldFormulaInfo.getFlightType());
                formulaInfo.setAirlineId(airlineId);
                formulaInfo.setFeeRulesId(oldFeeIdToNewFeeId.get(oldFormulaInfo.getFeeRulesId()));
                formulaInfo.setCreateTime(new Date());
                formulaInfo.setCreateBy(user.getUsername());
                String formula = formulaInfo.getFormula();
                if (formula.contains("[") && formula.contains("]")) {
                    String useFeeId = formula.substring(formula.indexOf("[") + 1, formula.indexOf("]"));
                    String newFeeId = oldFeeIdToNewFeeId.get(useFeeId);
                    formulaInfo.setFormula(formula.replaceAll(useFeeId, newFeeId));
                }
                saveFormulaList.add(formulaInfo);
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(NOTE, e);
                throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
            }
        }
        formulaDao.saveAll(saveFormulaList);
        return new ResultBuilder.Builder<List<FeeInfo>>().data(feeInfos).builder();
    }

    @Override
    public ResultBuilder<Integer> getCountFormulaByAirline(String airlineId) {
        return new ResultBuilder.Builder<Integer>().data(formulaDao.getCountFormulaByAirline(airlineId)).builder();
    }

    @Override
    public ResultBuilder<List<FormulaForm>> listFormulaByFeeId(String feeId) {
        // 根据费用id查询费用信息
        List<FormulaInfo> formulaInfos = formulaDao.listFormulaByFeeId(feeId);
        List<FormulaForm> formulaForms = new ArrayList<>();
        for (FormulaInfo formula : formulaInfos) {
            FormulaForm formulaForm = new FormulaForm();
            // 判断该公式是否引用了费用
            if (formula.getFormula().contains("[") && formula.getFormula().contains("]")) {
                // 获取引用费用的名称
                formulaForm.setQuotativeFeeName(feeDao.findByFeeId(formula.getFormula().substring(formula.getFormula().indexOf("[") + 1, formula.getFormula().indexOf("]")))
                        .getFeeName());
                formulaForm.setQuotativeFeeInfo(feeDao.findByFeeId(formula.getFormula().substring(
                        formula.getFormula().indexOf("[") + 1, formula.getFormula().indexOf("]"))));
            }
            int index1 = formula.getFormula().indexOf("{", formula.getFormula().indexOf("{") + 1);
            int index2 = formula.getFormula().indexOf("}", formula.getFormula().indexOf("}") + 1);
            if (index1 != -1 && index2 != -1) {
                // 第二个{}号
                formulaForm.setQuotativeVariableName(formulaDao
                        .findByVariable(formula.getFormula().substring(index1 + 1, index2))
                        .getVariableName());
            } else if (formula.getFormula().contains("{") && formula.getFormula().contains("}")) {
                // 第一个{}号
                formulaForm
                        .setQuotativeVariableName(
                                formulaDao
                                        .findByVariable(formula.getFormula().substring(
                                                formula.getFormula().indexOf("{") + 1,
                                                formula.getFormula().indexOf("}")))
                                        .getVariableName());
            }
            formulaForm.setFormulaId(formula.getId());
            try {
                BeanUtils.copyProperties(formulaForm, formula);
                if (formula.getPricingWay().equals(Constants.FormulaEnum.PRICING_WAY_CONDITION.getValue())) {
                    dealFormula(formulaForm);
                }
            } catch (IllegalAccessException | InvocationTargetException e) {
                throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
            }
            formulaForm.setStartDate(formulaForm.getStartDate());
            formulaForm.setEndDate(formulaForm.getEndDate());
            formulaForms.add(formulaForm);
        }
        return new ResultBuilder.Builder<List<FormulaForm>>().data(formulaForms).builder();
    }

    @Override
    public ResultBuilder<List<RulesVariableRecord>> listVariableRulesByUnit(String variableUnit) {
        if (CharSequenceUtil.isBlank(variableUnit) || "F".equals(variableUnit) || "M".equals(variableUnit) || "S".equals(variableUnit) || "undefined".equals(variableUnit)) {
            variableUnit = null;
        }
        return new ResultBuilder.Builder<List<RulesVariableRecord>>().data(formulaDao.listVariableRulesByUnit(variableUnit)).builder();
    }

    @Override
    public void changeIsExpired(FeeInfo feeInfo) {
        List<FormulaInfo> formulaInfoList = formulaDao.listFormulaByFeeId(feeInfo.getId());
        List<FormulaExpiredDto> formulaExpiredDtoList = new ArrayList<>();
        for (FormulaInfo formulaInfo : formulaInfoList) {
            if (formulaInfo.getStartDate() == null || formulaInfo.getEndDate() == null) {
                continue;
            }
            FormulaExpiredDto formulaExpiredDto = new FormulaExpiredDto();
            // 将Date转换为Instant
            Instant startInstant = formulaInfo.getStartDate().toInstant();
            // 使用系统默认时区将Instant转换为ZonedDateTime
            ZonedDateTime startZonedDateTime = startInstant.atZone(ZoneId.systemDefault());
            // 从ZonedDateTime获取LocalDate
            formulaExpiredDto.setEffectiveStartDate(startZonedDateTime.toLocalDate());

            Instant endInstant = formulaInfo.getEndDate().toInstant();
            ZonedDateTime endZonedDateTime = endInstant.atZone(ZoneId.systemDefault());
            formulaExpiredDto.setEffectiveEndDate(endZonedDateTime.toLocalDate());

            formulaExpiredDtoList.add(formulaExpiredDto);
        }
        if (FormulaExpiredUtils.isExpired(formulaExpiredDtoList)) {
            //仅保存过期状态有变化的对象
            if(NOT_EXPIRED_VALUE.equals(feeInfo.getIsExpired())) {
                feeInfo.setIsExpired(EXPIRED_VALUE);
                feeDao.save(feeInfo);
            }
        } else {
            if(EXPIRED_VALUE.equals(feeInfo.getIsExpired())) {
                feeInfo.setIsExpired(NOT_EXPIRED_VALUE);
                feeDao.save(feeInfo);
            }
        }

    }

    @Override
    public List<SafeguardTypeVo> getSafeguardType(String airportCode) {
        String  org=getSignDataOrg(airportCode);
        List<SafeguardTypeVo> safeguardTypeVoList = new ArrayList<>();
        BaseResult<List<ConfDictCacheVo>> apsSettleCodeAirlineAbbr=null;
        if("SIGN".equals(org)) {
            // 获取电子签单配置保障类型
            List<SafeguardTypeVo> safeguardTypeVoList1 = getSafeguardTypeVos(safeguardTypeVoList);
            if (!safeguardTypeVoList1.isEmpty()) {
                return safeguardTypeVoList1;
            }

            apsSettleCodeAirlineAbbr = confDictMgtBizClient.getDict(
                    "APS_SAFEGUARDS_TYPE", TenantHolder.getTenant().toString());

        }else if("DATA_CENTER".equals(org)){
            apsSettleCodeAirlineAbbr = confDictMgtBizClient.getDict(
                    "PROTECTION_TYPE", airportCode);

        }
        if (apsSettleCodeAirlineAbbr== null||apsSettleCodeAirlineAbbr.getCode() != BaseResult.OK_CODE || CollectionUtils.isEmpty(apsSettleCodeAirlineAbbr.getData())) {
            log.error("电子签单未配置保障类型，且机场端未设置默认值");
            throw new BusinessException(ExceptionCodes.NOT_CONFIG_SAFEGUARD_TYPE);
        }
        for (ConfDictCacheVo dictCacheVo : apsSettleCodeAirlineAbbr.getData()) {
            SafeguardTypeVo safeguardTypeVo = new SafeguardTypeVo();
            safeguardTypeVo.setSortOrder(Integer.valueOf(dictCacheVo.getDictKey()));
            safeguardTypeVo.setSelectorItemName(dictCacheVo.getDictValue());
            safeguardTypeVoList.add(safeguardTypeVo);
        }
        return safeguardTypeVoList;

    }

    private List<SafeguardTypeVo> getSafeguardTypeVos(List<SafeguardTypeVo> safeguardTypeVoList) {
        BillItem billItem = billItemDao.getBillItemByVariable("ST");
        if (billItem != null && billItem.getFlightSelectorItem() != null) {
            String flightSelectorItem = billItem.getFlightSelectorItem();
            try {
                List<FlightSelectorItemDTO> flightSelectorItemDTOList = OBJECT_MAPPER.readValue(flightSelectorItem,
                        new TypeReference<List<FlightSelectorItemDTO>>() {
                        });
                for (FlightSelectorItemDTO flightSelectorItemDTO : flightSelectorItemDTOList) {
                    SafeguardTypeVo safeguardTypeVo = new SafeguardTypeVo();
                    safeguardTypeVo.setSortOrder(flightSelectorItemDTO.getSortOrder());
                    safeguardTypeVo.setSelectorItemName(flightSelectorItemDTO.getSelectorItemName());
                    safeguardTypeVoList.add(safeguardTypeVo);
                }
                if (!safeguardTypeVoList.isEmpty()) {
                    return safeguardTypeVoList;
                }
            } catch (JsonProcessingException e) {
                log.error(e.getMessage());
            }
        }
        return new ArrayList<>();
    }

    private String getSignDataOrg(String airportCode) {
        String org = "SIGN";
        try {
            BaseResult<JSONObject> jsonObjectBaseResult = iConfConfigBizClient.getConfig("ELECTRONIC_SIGNING_DICTIONARY_TABLE");
            if (ObjectUtil.isEmpty(jsonObjectBaseResult) || ObjectUtil.isEmpty(jsonObjectBaseResult.getData())) {
                log.error("{}机场未配置签单数据来源 ！ jsonObjectBaseResult = {}", airportCode, jsonObjectBaseResult);
            }

            Map<String, String> collectionSourceMap =
                    jsonObjectBaseResult.getData().toJavaObject(new com.alibaba.fastjson.TypeReference<Map<String, String>>() {
                    });
            String orgTemp = collectionSourceMap.get(airportCode);
            if (StringUtils.isNotBlank(orgTemp)) {
                org = orgTemp;
            }
        }catch (Exception e){
            log.error("获取配置中心签单来源异常，e:{0}",e);
        }
        return org;
    }
}
