package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.cons.ConsAirportAps;
import com.swcares.aiot.core.model.dto.VariableGuaranteeSaveDto;
import com.swcares.aiot.core.model.dto.VariableGuaranteeUpdateDto;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.NewAssureService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.aiot.controller.NewAssureController
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/21 13:46
 * @version v1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/newGuarantee")
@Api(value = "NewAssureController", tags = {"新字段匹配接口"})
@ApiVersion(value = ConsAirportAps.API_MODULE_NAME)
public class NewAssureController {

    @Resource
    private NewAssureService newAssureService;


    @GetMapping(value = "/getVariableId")
    @ApiOperation(value = "获取特车设备字典表")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public Object getVariableId(@RequestParam String variableName, @RequestParam String airportCode) {
        return BaseResult.ok(newAssureService.getVariableId(variableName, airportCode));
    }

    @GetMapping(value = "/pageMatchingRelationship")
    @ApiOperation(value = "特车设备关系表分页查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public Object pageMatchingRelationship(@Validated @ApiParam(name = "pageParam", value = "分页与条件") PageParam pageParam,
                                           @RequestParam String variableId, @RequestParam String airportCode) {
        return BaseResult.ok(newAssureService.getMatchingRelationship(pageParam, variableId, airportCode));
    }

    @PostMapping(value = "/addMatchingRelationship")
    @ApiOperation(value = "匹配特车设备关系")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public Object addMatchingRelationship(@RequestBody @Validated VariableGuaranteeSaveDto dto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return BaseResult.ok(newAssureService.addMatchingRelationship(dto, user));
    }


    @PostMapping(value = "/updateMatchingRelationship")
    @ApiOperation(value = "更新特车设备关系")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public Object updateMatchingRelationship(@RequestBody @Validated VariableGuaranteeUpdateDto dto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return BaseResult.ok(newAssureService.updateMatchingRelationship(dto, user));
    }

    @PostMapping(value = "/deletedMatchingRelationship")
    @ApiOperation(value = "删除特车设备关系")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public Object deletedMatchingRelationship(@RequestParam String id) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return BaseResult.ok(newAssureService.deletedMatchingRelationship(id, user));
    }

    @GetMapping(value = "/getBillItemList")
    @ApiOperation(value = "获取节点保障系统特车设备列表")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public Object getBillItemList(@RequestParam String airportCode) {
        return BaseResult.ok(newAssureService.getBillItemList(airportCode));
    }

    @GetMapping(value = "/importItemAll")
    @ApiOperation(value = "导入节点保障系统字段信息")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public BaseResult<Object> importItemAll() {
        newAssureService.importItemAll();
        return BaseResult.ok();
    }

    @GetMapping(value = "/importSignBusDataSettlement")
    @ApiOperation(value = "从节点保障同步最新数据")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = Object.class)})
    public BaseResult<Object> importSignBusDataSettlement(@RequestParam @ApiParam(name = "synStartDate", value = "同步开始日期") String synStartDate,
                                                          @RequestParam @ApiParam(name = "synEndDate", value = "同步结算日期") String synEndDate,
                                                          @ApiParam(name = "flightNos", value = "同步航班") String flightNos,
                                                          @RequestParam @ApiParam(name = "serviceRecordId", value = "结算字段id") String serviceRecordId,
                                                          @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        try {
            //更新签单字段信息
            newAssureService.importItemAll();
        } catch (Exception e) {
            log.error("更新导入item数据错误!", e);
        }
        newAssureService.importSignBusDataSettlement(synStartDate, synEndDate, flightNos, serviceRecordId, airportCode);
        return BaseResult.ok();
    }
}
