package com.swcares.aiot.core.liteflow.ctx;

import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TAircraftInfo;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.entity.TServiceRecord;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.dto.ReCalcErrorDto;
import com.swcares.aiot.core.model.vo.FlightBillHistorySnapShotVo;
import com.swcares.aiot.model.dto.ActExpensesResultCaluDto;
import com.swcares.aiot.model.dto.ActIndexCaluDto;
import com.swcares.aiot.model.vo.ActExpensesVo;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.baseframe.common.security.LoginUserDetails;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ClassName：com.swcares.aiot.core.liteflow.ctx.CtxReCalc
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/12 10:05
 * @version v1.0
 */
@Data
@Accessors(chain = true)
public class CtxCalc {

    /**
     * 计算请求参数
     */
    private ReCalcDto dto;
    /**
     * 航班id-航班信息Map
     */
    private Map<String, FlightInfoMb> flightInfoMap;
    /**
     * 按航司分类的可计算航班信息
     */
    private Map<String, List<FlightInfoMb>> calcFlightInfoMbMap;
    /**
     * 获取业务保障数据Map
     */
    private Map<String, Map<String, List<TServiceRecord>>> serviceRecordMap;
    /**
     * 计算需要的指标项
     */
    private Map<String, Set<ActExpensesVo>> customerIndicatMap;
    /**
     * 指标项字典表
     */
    private Map<String, IndAllIndicatorRetrVo> indicatorDictMap;
    /**
     * 计算账单发送参数
     */
    private ActExpensesResultCaluDto actExpensesResultCaluDto;
    /**
     * 不需要提示信息的航班——费用（航班未确认或业务保障数据未确认）
     */
    private Set<String> noErrorMsgSet;
    /**
     * 航班对应飞机信息
     */
    private Map<String, TAircraftInfo> flightAircraftInfoMap;
    /**
     * 错误文件信息
     */
    private Map<String,ReCalcErrorDto> errorDtoMap;
    /**
     * 账单list
     */
    private List<TFlightBill> flightBillList;
    /**
     * 操作用户信息
     */
    private LoginUserDetails user;

    /**
     * 账单对应快照map
     */
    private Map<TFlightBill, FlightBillHistorySnapShotVo> snapShotMap;

    /**
     * 航班-费用 对应 指标项数据
     */
    private Map<String, List<ActIndexCaluDto>> flightFeeIndexCaluMap = new HashMap<>();
}
