package com.swcares.aiot.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alicp.jetcache.support.StatInfo;
import com.swcares.aiot.Message;
import com.swcares.aiot.core.common.constant.FlightDataCons;
import com.swcares.aiot.core.importer.entity.*;
import com.swcares.aiot.core.importer.service.FlightDataImportService;
import com.swcares.aiot.dto.ServiceItemDto;
import com.swcares.aiot.service.ImportFlightDataBizService;
import com.swcares.aiot.service.NewSignDataService;
import com.swcares.aiot.vo.DcSignatureInfoVo;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;


/**
 * ClassName：ImportFlightDataBizServiceImpl
 * Description：接收数据中心航班数据 实现类
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2024/12/13 14:37
 * Version v1.0
 */
@Service
@Slf4j
public class ImportFlightDataBizServiceImpl<T> implements ImportFlightDataBizService<T> {

    @Resource
    private FlightDataImportService flightDataImportService;
    @Resource
    private NewSignDataService newSignDataService;

    @Override
    public void dispatchMessage(T msg) {
        // 状态信息我们不处理
        if (msg.getClass() != StatInfo.class) {
            log.info(CharSequenceUtil.format("消费类型：{}, 具体的消息:{}", msg.getClass().getName(), JSONUtil.toJsonStr(msg)));
            Message message = JSONUtil.toBean(JSONUtil.toJsonStr(msg), Message.class);
            if (FlightDataCons.TYPE_FLIGHT_INFO.equals(message.getHeader().getDataType())) {
                log.info("我是航班数据，dateTime= {}", getNowLocalDateTime());
                BaseFlightInfo flightInfo = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), BaseFlightInfo.class);
                flightInfoHandle(message, flightInfo);
            } else if (FlightDataCons.TYPE_FLIGHT_CARGO.equals(message.getHeader().getDataType())) {
                log.info("我是货邮行数据，dateTime= {}", getNowLocalDateTime());
                BaseFlightCargo flightCargo = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), BaseFlightCargo.class);
                flightCargoHandle(message, flightCargo);
            } else if (FlightDataCons.TYPE_FLIGHT_TRAVELER.equals(message.getHeader().getDataType())) {
                log.info("我是旅客数据，dateTime= {}", getNowLocalDateTime());
                BaseFlightTraveler flightTraveler = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), BaseFlightTraveler.class);
                flightTravelerHandle(message, flightTraveler);
            } else if (FlightDataCons.TYPE_FLIGHT_CARGO_SEGMENT.equals(message.getHeader().getDataType())) {
                log.info("我是货邮行拆分数据，dateTime= {}", getNowLocalDateTime());
                BaseFlightCargoSegment cargoSegment = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), BaseFlightCargoSegment.class);
                flightCargoSegmentHandle(message, cargoSegment);
            } else if (FlightDataCons.TYPE_FLIGHT_TRAVELER_SEGMENT.equals(message.getHeader().getDataType())) {
                log.info("我是旅客拆分数据，dateTime= {}", getNowLocalDateTime());
                BaseFlightTravelerSegment travelerSegment = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), BaseFlightTravelerSegment.class);
                flightTravelerSegmentHandle(message, travelerSegment);
            } else if (FlightDataCons.TYPE_DC_SIGNATURE_INFO.equals(message.getHeader().getDataType())) {
                log.info("同步机场签单数据，dateTime= {}", getNowLocalDateTime());
                DcSignatureInfoVo dcSignatureInfoVo = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), DcSignatureInfoVo.class);
                newSignDataService.saveSignData(dcSignatureInfoVo);
            } else if (FlightDataCons.TYPE_SERVICE_ITEM.equals(message.getHeader().getDataType())) {
                log.info("同步机场签单字典表，dateTime= {}", getNowLocalDateTime());
                ServiceItemDto serviceItemDto = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), ServiceItemDto.class);
                newSignDataService.saveDict(serviceItemDto);
            } else {
                log.info("未知类型，不做处理，dateTime= {}", getNowLocalDateTime());
            }
        }
    }

    /**
     * Title：flightInfoHandle
     * Description：航班数据处理
     * author：李军呈
     * date： 2024/12/17 10:41
     *
     * @param message    消息
     * @param flightInfo 数据
     */
    private void flightInfoHandle(Message message, BaseFlightInfo flightInfo) {
        log.info("航班数据消费开始，租户{}，flightId{}，dateTime= {}", message.getHeader().getTargetCustCodes().get(0), flightInfo.getId(), getNowLocalDateTime());
        TenantHolder.setTenant(Long.valueOf(message.getHeader().getTargetCustCodes().get(0)));
        flightDataImportService.importFlightData(flightInfo, false);
        log.info("航班数据消费完成，dateTime= {}", getNowLocalDateTime());
    }

    /**
     * Title：flightCargoHandle
     * Description：货邮行数据处理
     * author：李军呈
     * date： 2024/12/17 10:16
     *
     * @param message     消息
     * @param flightCargo 数据
     */
    private void flightCargoHandle(Message message, BaseFlightCargo flightCargo) {
        log.info("货邮行数据消费开始，租户{}，flightId{}，dateTime= {}", message.getHeader().getTargetCustCodes().get(0), flightCargo.getId(), getNowLocalDateTime());
        TenantHolder.setTenant(Long.valueOf(message.getHeader().getTargetCustCodes().get(0)));
        flightDataImportService.importFlightCargo(flightCargo, false);
        log.info("货邮行数据消费完成，dateTime= {}", getNowLocalDateTime());
    }

    /**
     * Title：flightTravelerHandle
     * Description：旅客数据处理
     * author：李军呈
     * date： 2024/12/17 10:39
     *
     * @param message        消息
     * @param flightTraveler 数据
     */
    private void flightTravelerHandle(Message message, BaseFlightTraveler flightTraveler) {
        log.info("旅客数据消费开始，租户{}，flightId{}，dateTime= {}", message.getHeader().getTargetCustCodes().get(0), flightTraveler.getId(), getNowLocalDateTime());
        TenantHolder.setTenant(Long.valueOf(message.getHeader().getTargetCustCodes().get(0)));
        flightDataImportService.importFlightTraveler(flightTraveler, false);
        log.info("旅客数据消费完成，dateTime= {}", getNowLocalDateTime());
    }

    /**
     * Title：flightCargoSegmentHandle
     * Description：货邮行拆分数据处理
     * author：李军呈
     * date： 2024/12/17 10:44
     *
     * @param message      消息
     * @param cargoSegment 数据
     */
    private void flightCargoSegmentHandle(Message message, BaseFlightCargoSegment cargoSegment) {
        log.info("货邮行拆分数据消费开始，租户{}，flightId{}，dateTime= {}", message.getHeader().getTargetCustCodes().get(0), cargoSegment.getId(), getNowLocalDateTime());
        TenantHolder.setTenant(Long.valueOf(message.getHeader().getTargetCustCodes().get(0)));
        flightDataImportService.importSplitFlightCargo(cargoSegment);
        log.info("货邮行拆分数据消费完成，dateTime= {}", getNowLocalDateTime());
    }

    /**
     * Title：flightTravelerSegmentHandle
     * Description：旅客拆分数据处理
     * author：李军呈
     * date： 2024/12/17 10:53
     *
     * @param message         消息
     * @param travelerSegment 数据
     */
    private void flightTravelerSegmentHandle(Message message, BaseFlightTravelerSegment travelerSegment) {
        log.info("旅客拆分数据消费开始，租户{}，flightId{}，dateTime= {}", message.getHeader().getTargetCustCodes().get(0), travelerSegment.getId(), getNowLocalDateTime());
        TenantHolder.setTenant(Long.valueOf(message.getHeader().getTargetCustCodes().get(0)));
        flightDataImportService.importSplitFlightTraveler(travelerSegment);
        log.info("旅客拆分数据消费完成，dateTime= {}", getNowLocalDateTime());
    }

    private String getNowLocalDateTime() {
        return DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN);
    }

}
