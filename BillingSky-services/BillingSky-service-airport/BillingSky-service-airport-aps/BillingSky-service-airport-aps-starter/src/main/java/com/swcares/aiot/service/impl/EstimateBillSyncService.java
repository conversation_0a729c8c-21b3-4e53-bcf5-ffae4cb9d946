package com.swcares.aiot.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aiot.ComplexMessage;
import com.swcares.aiot.core.entity.BillFileAttachment;
import com.swcares.aiot.core.entity.EstimateBill;
import com.swcares.aiot.core.entity.EstimateBillProve;
import com.swcares.aiot.core.service.IBillFileAttachmentService;
import com.swcares.aiot.core.service.IEstimateBillProveService;
import com.swcares.aiot.core.service.IEstimateBillService;
import com.swcares.aiot.dto.EstimateBillDTO;
import com.swcares.aiot.synergy.annotation.DataType;
import com.swcares.aiot.synergy.service.IDataSyncService;
import com.swcares.aiot.vo.BillFileAttachmentVo;
import com.swcares.aiot.vo.EstimateBillProveVo;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@DataType("ESTIMATE_BILL")
@Slf4j
@Service
public class EstimateBillSyncService implements IDataSyncService {

    @Resource
    private IEstimateBillService iEstimateBillService;

    @Resource
    private IEstimateBillProveService iEstimateBillProveService;

    @Resource
    private IBillFileAttachmentService iBillFileAttachmentService;

    @Override
    public Long getTenantId(String tenantCode) {
        return ConfigUtil.getLong("signature_config", tenantCode.toUpperCase(), GlobalConstants.TENANT_DEFAULT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncData(ComplexMessage message) {
        EstimateBillDTO estimateBillDTO = JSON.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), EstimateBillDTO.class);
        log.info("收到返还账单信息：{}", estimateBillDTO);
        if (ObjectUtil.isEmpty(estimateBillDTO)) {
            return;
        }
        // 保存或更新账单
        EstimateBill estimateBill = new EstimateBill();
        BeanUtil.copyProperties(estimateBillDTO, estimateBill);
        EstimateBill estimateBill1 = iEstimateBillService.lambdaQuery().eq(EstimateBill::getId, estimateBillDTO.getId()).one();
        if (ObjectUtil.isNotEmpty(estimateBill1)) {
            iEstimateBillService.updateById(estimateBill);
        } else {
            iEstimateBillService.save(estimateBill);
        }
        // 存证记录
        List<EstimateBillProveVo> estimateBillProveVoList = estimateBillDTO.getEstimateBillProveVoList();
        if (CollUtil.isNotEmpty(estimateBillProveVoList)) {
            // 处理操作记录中的附件
            List<BillFileAttachment> billFileAttachments = new ArrayList<>();
            List<EstimateBillProve> estimateBillProves = estimateBillProveVoList.stream().map(estimateBillProveVo -> {
                EstimateBillProve estimateBillProve = BeanUtil.copyProperties(estimateBillProveVo, EstimateBillProve.class);
                List<BillFileAttachment> billFileAttachmentList = estimateBillProveVo.getFileList();
                if (!billFileAttachmentList.isEmpty()) {
                    billFileAttachments.addAll(billFileAttachmentList);
                }
                return estimateBillProve;
            }).collect(Collectors.toList());
            iEstimateBillProveService.saveOrUpdateBatch(estimateBillProves);
            if (!billFileAttachments.isEmpty()) {
                iBillFileAttachmentService.saveOrUpdateBatch(billFileAttachments);
            }
        }
        // 发票
        List<BillFileAttachmentVo> invoiceList = estimateBillDTO.getInvoiceList();
        if (CollUtil.isNotEmpty(invoiceList)) {
            List<BillFileAttachment> billFileAttachments = invoiceList.stream().map(invoice -> BeanUtil.copyProperties(invoice, BillFileAttachment.class)).collect(Collectors.toList());
            iBillFileAttachmentService.saveOrUpdateBatch(billFileAttachments);
        }
    }

}
