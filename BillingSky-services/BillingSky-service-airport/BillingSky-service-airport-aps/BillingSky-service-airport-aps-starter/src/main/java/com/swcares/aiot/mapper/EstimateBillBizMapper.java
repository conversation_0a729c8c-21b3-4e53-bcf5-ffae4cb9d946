package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.entity.EstimateBill;
import com.swcares.aiot.core.form.EstimateBillPageForm;
import com.swcares.aiot.vo.EstimateBillPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 离港返还账单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
public interface EstimateBillBizMapper extends BaseMapper<EstimateBill> {

    IPage<EstimateBillPageVo> page(@Param("form") EstimateBillPageForm form, Page<EstimateBill> page);

}
