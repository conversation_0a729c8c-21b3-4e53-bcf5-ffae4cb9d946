package com.swcares.aiot.core.easyexcel;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title ：SkipHeaderRowColumnWidthStyleStrategy <br>
 * Package ：com.swcares.aiot.core.easyexcel <br>
 * Copyright 2025 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : easyExcel自动列宽，跳过指定行数的表头 <br>
 *
 * <AUTHOR> <br>
 * date 2025年 05月12日 12:14 <br>
 * @version v1.0 <br>
 */
public class SkipHeaderRowColumnWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {
    private static final int MAX_COLUMN_WIDTH = 255;
    private final Integer skipHeaderRow ;
    private final Map<Integer, Map<Integer, Integer>> cache = new HashMap<>(8);

    public SkipHeaderRowColumnWidthStyleStrategy(Integer skipHeaderRowNum){
        this.skipHeaderRow = skipHeaderRowNum;
    }
    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cellDataList, Cell cell, Head head,
                                  Integer relativeRowIndex, Boolean isHead) {
        boolean needSetWidth = (isHead && relativeRowIndex >= skipHeaderRow)
                        || (!isHead && !CollectionUtils.isEmpty(cellDataList));
        if (!needSetWidth) {
            return;
        }
        Map<Integer, Integer> maxColumnWidthMap = cache.computeIfAbsent(writeSheetHolder.getSheetNo(), k -> new HashMap<>(16));
        Integer columnWidth = dataLength(cellDataList, cell, isHead);
        if (columnWidth < 0) {
            return;
        }
        if (columnWidth > MAX_COLUMN_WIDTH) {
            columnWidth = MAX_COLUMN_WIDTH;
        }
        Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
        if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
            maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
        }
    }

    private Integer dataLength(List<CellData> cellDataList, Cell cell, Boolean isHead) {
        if (Boolean.TRUE.equals(isHead)) {
            //这里是发现
            return calculateColumnWidth(cell.getStringCellValue());
        }
        CellData cellData = cellDataList.get(0);
        CellDataTypeEnum type = cellData.getType();
        if (type == null) {
            return -1;
        }
        switch (type) {
            case STRING:
                return cellData.getStringValue().getBytes().length;
            case BOOLEAN:
                return cellData.getBooleanValue().toString().getBytes().length;
            case NUMBER:
                return cellData.getNumberValue().toString().getBytes().length;
            default:
                return -1;
        }
    }

    private int calculateColumnWidth(String cellValue) {
        // 1. 定义不同字符类型的宽度系数（经验值）
        final double CHINESE_WIDTH = 3;
        final double DIGIT_WIDTH = 1.5;

        // 2. 遍历字符计算总宽度
        double totalWidth = 0;
        for (char c : cellValue.toCharArray()) {
            if (isChinese(c)) {
                totalWidth += CHINESE_WIDTH;
            } else {
                totalWidth += DIGIT_WIDTH;
            }
        }

        // 3. 添加余量并转换为Excel列宽单位
        return (int) (totalWidth * 1.2 + 1); // 1.2倍余量，+1防止截断
    }

    /**
     * 判断是否为中文字符
     */
    private boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION;
    }
}
