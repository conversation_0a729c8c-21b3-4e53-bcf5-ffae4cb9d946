package com.swcares.aiot.core.liteflow.cmp;

import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TServiceRecord;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.service.ITServiceRecordService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.CmpGetServiceRecordMap
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/12 10:38
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcGetServiceRecordMap", name = "组件-对账通机场端-计算模块-获取业务保障信息")
public class CmpCalcGetServiceRecordMap extends NodeComponent {
    @Resource
    private ITServiceRecordService serviceRecordService;

    @Override
    public void process() {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        Map<String, List<FlightInfoMb>> calcFlightInfoMbMap = ctxCalc.getCalcFlightInfoMbMap();
        //获取业务保障数据
        Map<String, Map<String, List<TServiceRecord>>> serviceRecordMap=new HashMap<>();
        //按照航司遍历航班
        for(Map.Entry<String, List<FlightInfoMb>> entry : calcFlightInfoMbMap.entrySet()) {
            List<FlightInfoMb> flightInfoMbList = entry.getValue();
            List<String> flightIdList = flightInfoMbList.stream().map(FlightInfoMb::getId).collect(Collectors.toList());
            List<TServiceRecord> serviceRecordList = serviceRecordService.lambdaQuery()
                    .eq(TServiceRecord::getInvalid, "1")
                    .in(TServiceRecord::getFlightId, flightIdList)
                    .list();
            for(TServiceRecord serviceRecord : serviceRecordList) {
                Map<String, List<TServiceRecord>> flightIdMap = serviceRecordMap.getOrDefault(serviceRecord.getFlightId(), new HashMap<>());
                List<TServiceRecord> serivceCodeList = flightIdMap.getOrDefault(serviceRecord.getServiceCode(), new ArrayList<>());
                serivceCodeList.add(serviceRecord);
                flightIdMap.put(serviceRecord.getServiceCode(), serivceCodeList);
                serviceRecordMap.put(serviceRecord.getFlightId(), flightIdMap);
            }
        }
        ctxCalc.setServiceRecordMap(serviceRecordMap);
    }
}
