package com.swcares.aiot.service;


/**
 * ClassName：com.swcares.service.MqService
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/3/23 9:19
 * @version v1.0
 */
public interface MqService<T> {
    void send(T message,String keyName);
    /**
     * Title: sendReCaleDat<br>
     * Author: 刘志恒<br>
     * Description: 重新结算完后，将中间中间表和账单表数据推送到mq<br>
     * Date:  2022/3/24 9:23 <br>
     */
    void sendReCaleDat();
    void addfee(String feeId);
    void updatefee(String feeId);
    void deletefee(String feeId);
}
