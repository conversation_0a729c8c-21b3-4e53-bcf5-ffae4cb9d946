package com.swcares.aiot.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.swcares.aiot.client.IErMonthlyBillBizClient;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.model.dto.ErAutoConfirmDto;
import com.swcares.aiot.core.model.dto.ErBillFulfillAndConfirmDto;
import com.swcares.aiot.core.model.dto.ErMonthlyBillPageDto;
import com.swcares.aiot.core.model.dto.ErMonthlyObjectionDto;
import com.swcares.aiot.core.model.vo.*;
import com.swcares.aiot.service.MonthlyBillService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.sys.util.ConfigUtil;
import com.worm.hutool.HttpUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.impl.MonthlyBillServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/28 11:30
 * @version v1.0
 */
@Service
public class MonthlyBillServiceImpl implements MonthlyBillService {

    @Resource
    private IErMonthlyBillBizClient monthlyBillBizClient;

    @Override
    public PagedResult<List<ErMonthlyBillPageVo>> page(ErMonthlyBillPageDto monthlyBillPageDto) {
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
        monthlyBillPageDto.setAirportCode(airportCode);
        PagedResult<List<ErMonthlyBillPageVo>> listPagedResult = monthlyBillBizClient.pageErMonthlyBill(monthlyBillPageDto);
        if(listPagedResult!=null && listPagedResult.getData()!=null && !listPagedResult.getData().isEmpty()) {
            listPagedResult.getData().forEach(monthlyBillPageVo -> {
                monthlyBillPageVo.setBlsCheckinCnt(null);
                monthlyBillPageVo.setBlsFlightCnt(null);
            });
        }
        return listPagedResult;
    }

    @Override
    public BaseResult<ErMonthlyBillAccVo> count(ErMonthlyBillPageDto monthlyBillPageDto) {
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, TenantHolder.getTenant());
        monthlyBillPageDto.setAirportCode(airportCode);
        BaseResult<ErMonthlyBillAccVo> result = monthlyBillBizClient.accErMonthlyBill(monthlyBillPageDto);
        if (result != null && result.getData() != null) {
            result.getData().setBlsCheckinCnt(null);
            result.getData().setBlsFlightCnt(null);
        }
        return result;
    }

    @Override
    public BaseResult<Boolean> confirmId(Long id) {
        return monthlyBillBizClient.confirmAirportMonthlyBill(id);
    }

    @Override
    public BaseResult<Boolean> switchMonthlyAutoConfirm(ErAutoConfirmDto erAutoConfirmDto) {
        return monthlyBillBizClient.switchMonthlyAutoConfirm(erAutoConfirmDto);
    }

    @Override
    public BaseResult<Boolean> monthlyAutoConfirmQuery(String airportCode) {
        return monthlyBillBizClient.monthlyAutoConfirmQuery(airportCode);
    }

    @Override
    public BaseResult<List<ErMonthlyBillHistoryVo>> listErMonthlyBillObjectionHistoryVo(String id) {
        return monthlyBillBizClient.listErMonthlyBillObjectionHistoryVo(id);
    }

    @Override
    public BaseResult<Boolean> objection(ErMonthlyObjectionDto erMonthlyObjectionDto){
        return monthlyBillBizClient.objection(erMonthlyObjectionDto);
    }

    @Override
    public BaseResult<ErBillingBillPageVo> getBillingBillHis( String billingBillId){
        return monthlyBillBizClient.getBillingBillHis(billingBillId);
    }


    @Override
    public BaseResult<ErBillFulfillAndConfirmVo> confirmPay(ErBillFulfillAndConfirmDto erBillFulfillAndConfirmDto){
        return monthlyBillBizClient.confirmPay(erBillFulfillAndConfirmDto);
    }

    @Override
    public void downloadErMonthlyBillData(ErMonthlyBillPageDto erMonthlyBillPageDto, HttpServletResponse response) throws IOException {
        List<ErMonthlyBillDownloadVo> erMonthlyBillDownloadVoList= monthlyBillBizClient.downloadErMonthlyBillData(erMonthlyBillPageDto);
        String ymd = LocalDateTimeUtil.format(LocalDate.now(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 设置响应头
        String fileName = URLEncoder.encode(CharSequenceUtil.format("每月预估账单-{}.xlsx", ymd), CharsetUtil.UTF_8);
        response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + fileName);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        // 使用 EasyExcel 写入数据
        EasyExcelFactory.write(response.getOutputStream(), ErMonthlyBillDownloadVo.class).sheet("每月预估账单").doWrite(erMonthlyBillDownloadVoList);
    }

    @Override
    public BaseResult<String> uploadFile(MultipartFile file) {
        return monthlyBillBizClient.uploadFile(file);
    }
}
