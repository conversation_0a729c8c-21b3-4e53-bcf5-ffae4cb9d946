package com.swcares.aiot.service;

import com.swcares.baseframe.common.security.LoginUserDetails;

import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.service.ReCalcProcessService
 * Description：重新结算流程service
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/11/25 9:54
 * @version v1.0
 */
public interface ReCalcProcessService {
    void reCalc(String airportCode, String airlineCode, List<String> flightNoList, List<String> flightIdList,
                List<String> formulaIdList, Date startDate, Date endDate, List<String> feeCodeList,
                LoginUserDetails userDetails, Integer dateType);


}
