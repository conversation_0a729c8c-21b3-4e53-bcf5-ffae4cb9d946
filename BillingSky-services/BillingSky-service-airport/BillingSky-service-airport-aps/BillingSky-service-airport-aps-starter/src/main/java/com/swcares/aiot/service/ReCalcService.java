package com.swcares.aiot.service;


import com.swcares.aiot.core.model.entity.CargoFreeAirline;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.form.ReCalcForm;

import java.util.List;

/**
 *
 * ClassName：ReCalcService <br>
 * Description：重新计算service接口<br>
 * Copyright © 2020/6/17 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020/09/02 14:30<br>
 * @version v1.0 <br>
 */
public interface ReCalcService {
    /**
     * Title: execute<br>
     * Author: liyazhou<br>
     * Description: 根据航班号重新计算<br>
     * Date:  14:30 <br>
     * @param form :
     * @param user :
     * return: ResultBuilder
     */
    void execute(ReCalcForm form, LoginUserDetails user, Long tenantId);


    /**
     * Title: isBilling<br>
     * Author: 刘志恒<br>
     * Description: 是否正在结算<br>
     * Date:  2021/11/22 9:37 <br>
     */
    boolean isBilling(String airportCode);


    /**
     * Title: addCargoNotFreeAirline<br>
     * Author: 刘志恒<br>
     * Description: 添加货邮为零不收货邮费的航司<br>
     * Date:  2022/1/13 9:48 <br>

     */
    Boolean addCargoNotFreeAirline(String airportCode,String airlineCodes,LoginUserDetails user);

    /**
     * Title: getCargoNotFreeAirline<br>
     * Author: 刘志恒<br>
     * Description: 获取货邮为零不收货邮费的航司<br>
     * Date:  2022/1/13 9:48 <br>

     */
    List<CargoFreeAirline> getCargoNotFreeAirline(String airportCode);

    /**
     * Title: deleteCargoNotFreeAirline<br>
     * Author: 刘志恒<br>
     * Description: 删除货邮为零不收货邮费的航司<br>
     * Date:  2022/1/13 9:48 <br>

     */
    Boolean deleteCargoNotFreeAirline(String ids);

    /**
     * Title: cancelReCalcByVariable<br>
     * Author: 刘志恒<br>
     * Description: 保障数据页面取消确认的取消结算<br>
     * Date:  2022/10/31 16:55 <br>

     */
    Boolean cancelReCalcByVariable(String airportCode,String flightId);

    /**
     * Title: cancelReCalcByFlight<br>
     * Author: 刘志恒<br>
     * Description: 航班数据页面的取消确认的取消结算<br>
     * Date:  2022/10/31 16:55 <br>

     */
    boolean cancelReCalcByFlight(String airportCode,String flightId);
    /**
     * Title: cancelReCalc<br>
     * Author: 刘志恒<br>
     * Description: 取消结算<br>
     * Date:  2022/10/31 14:53 <br>

     */
    boolean cancelReCalc(String airportCode,String flightId,String formulaType);
}
