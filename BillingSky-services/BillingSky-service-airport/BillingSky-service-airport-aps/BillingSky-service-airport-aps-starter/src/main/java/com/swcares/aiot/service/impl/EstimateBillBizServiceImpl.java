package com.swcares.aiot.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.common.constant.EstimateBillConstants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.EasyExcelUtil;
import com.swcares.aiot.core.common.util.SsssUtil;
import com.swcares.aiot.core.entity.*;
import com.swcares.aiot.core.enums.EnumOperateType;
import com.swcares.aiot.core.enums.FileBusinessTypeEnum;
import com.swcares.aiot.core.enums.HostAviationEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.EstimateBillConfirmForm;
import com.swcares.aiot.core.form.EstimateBillPageForm;
import com.swcares.aiot.core.model.dto.HostAviationDownloadDto;
import com.swcares.aiot.core.model.dto.RaiseAnObjectionDto;
import com.swcares.aiot.core.service.*;
import com.swcares.aiot.mapper.EstimateBillBizMapper;
import com.swcares.aiot.service.EstimateBillBizService;
import com.swcares.aiot.service.IFileAttachmentBizService;
import com.swcares.aiot.statemachine.StatemachineTemplate;
import com.swcares.aiot.statemachine.biz.events.EnumDepartureRebateChangeEvent;
import com.swcares.aiot.vo.*;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.service.impl.EstimateBillBizServiceImpl
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/7/16
 * @version v1.0
 */
@Service
@Slf4j
public class EstimateBillBizServiceImpl implements EstimateBillBizService {

    @Resource
    private IEstimateBillService iEstimateBillService;
    @Resource
    private StatemachineTemplate<EstimateBill> statemachineTemplate;
    @Resource
    private SsssUtil ssssUtil;

    @Resource
    private IEstimateBillProveService iEstimateBillProveService;

    @Resource
    private IHostAviationService iHostAviationService;

    @Resource
    private INotHostAviationService iNotHostAviationService;

    @Resource
    private EstimateBillBizMapper estimateBillBizMapper;

    @Resource
    private IBillFileAttachmentService iBillFileAttachmentService;

    @Resource
    private IFileAttachmentBizService iFileAttachmentBizService;

    /**
     * 机场待确认、待机场异议处理
     */
    protected static final List<Integer> STATUS_LIST = Arrays.asList(3, 8);

    @Override
    public IPage<EstimateBillPageVo> page(EstimateBillPageForm form) {
        IPage<EstimateBillPageVo> page = estimateBillBizMapper.page(form, new Page<>(form.getPage(), form.getLimit()));
        if (ObjectUtil.isNotEmpty(page) && CollUtil.isNotEmpty(page.getRecords())) {
            List<EstimateBillPageVo> records = page.getRecords();
            List<Long> estimateBillIds = records.stream().map(EstimateBillPageVo::getId).collect(Collectors.toList());
            // 查询发票
            List<BillFileAttachment> fileAttachments = iBillFileAttachmentService.lambdaQuery().in(BillFileAttachment::getTableId, estimateBillIds).list();
            if (CollUtil.isNotEmpty(fileAttachments)) {
                Map<Long, List<BillFileAttachment>> fileAttachmentsMap = fileAttachments.stream().collect(Collectors.groupingBy(BillFileAttachment::getTableId));
                records.forEach(bill -> {
                    if (fileAttachmentsMap.containsKey(bill.getId())) {
                        List<BillFileAttachment> billFileAttachments = fileAttachmentsMap.get(bill.getId());
                        bill.getInvoiceList().addAll(billFileAttachments.stream().map(billFileAttachment -> {
                            EstimateBillPageVo.InvoiceVO invoiceVO = new EstimateBillPageVo.InvoiceVO();
                            invoiceVO.setInvoice(billFileAttachment.getFileKey());
                            invoiceVO.setInvoiceId(billFileAttachment.getFileKey());
                            invoiceVO.setInvoiceName(billFileAttachment.getOriginalName());
                            return invoiceVO;
                        }).collect(Collectors.toList()));
                    }
                });
            }
        }
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirm(EstimateBillConfirmForm form) {
        // 查询是否可以操作该数据
        EstimateBill estimateBill = iEstimateBillService.lambdaQuery().eq(EstimateBill::getId, form.getId()).one();
        if (ObjectUtil.isEmpty(estimateBill)) {
            throw new BusinessException(10001);
        }
        // 只有机场待确认、待机场异议处理
        if (ObjectUtil.isEmpty(estimateBill.getStatus()) || !STATUS_LIST.contains(estimateBill.getStatus())) {
            throw new BusinessException(10002);
        }
        // 判断是否上传了发票
        List<String> fileKeys = form.getFileKeys().stream().filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        if (fileKeys.isEmpty()) {
            throw new BusinessException(10107);
        }
        // 上传文件不能超过20个
        if (fileKeys.size() > 20) {
            throw new BusinessException(10108);
        }
        EstimateBill bill = BeanUtil.copyProperties(form, EstimateBill.class);
        bill.setStatus(EstimateBillConstants.AWAITING_BRANCH_INVOICE_REVIEW);
        bill.setOperateLastTime(LocalDateTime.now());
        iEstimateBillService.updateById(bill);
        // 记录离港返还账单存证
        EstimateBillProve estimateBillProve = new EstimateBillProve();
        estimateBillProve.setEstimateBillId(estimateBill.getId());
        estimateBillProve.setType(EstimateBillConstants.INVOICE);
        estimateBillProve.setOperateType(EnumOperateType.AIRPORT_UPLOAD_INVOICE.getCode() + "");
        // 保存账单存证
        iEstimateBillProveService.save(estimateBillProve);
        // 更新上传发票
        iFileAttachmentBizService.assFileByFileKeyList(fileKeys, FileBusinessTypeEnum.INVOICE, bill.getId());
        // 离港返利账单变更
        statemachineTemplate.updateDepartureRebateEvent(iEstimateBillService, EstimateBill::getStatus, EstimateBill::getId, String.valueOf(form.getId()), EnumDepartureRebateChangeEvent.AIRPORT_CONFIRMATION);
        // 待分支发票审核
        sendBranch(bill.getId(), estimateBillProve, fileKeys);
    }

    @Override
    public List<EstimateBillProveRecordVo> getEstimateBillProveList(String estimateBillId) {
        List<EstimateBillProveRecordVo> estimateBillProveRecordVos = new ArrayList<>();
        // 查询主账单数据
        EstimateBill estimateBill = iEstimateBillService.getById(estimateBillId);
        if (ObjectUtil.isEmpty(estimateBill)) {
            return Collections.emptyList();
        }
        // 主键id或条件查询存证数据
        List<EstimateBillProve> estimateBillProves = iEstimateBillProveService.lambdaQuery()
                .eq(EstimateBillProve::getDeleted, Boolean.FALSE)
                .eq(EstimateBillProve::getEstimateBillId, estimateBillId)
                .orderByDesc(EstimateBillProve::getCreatedTime).list();
        // 查询操作记录
        if (CollUtil.isNotEmpty(estimateBillProves)) {
            List<Long> estimateBillProveIds = estimateBillProves.stream().map(EstimateBillProve::getId).collect(Collectors.toList());
            // 查询操作文件记录
            List<BillFileAttachment> fileAttachments = iBillFileAttachmentService.lambdaQuery().in(BillFileAttachment::getTableId, estimateBillProveIds).list();
            Map<Long, List<BillFileAttachment>> fileAttachmentsMap = fileAttachments.stream().collect(Collectors.groupingBy(BillFileAttachment::getTableId));
            estimateBillProveRecordVos.addAll(estimateBillProves.stream().map(estimateBillProve -> {
                EstimateBillProveRecordVo estimateBillProveRecordVo = BeanUtil.copyProperties(estimateBillProve, EstimateBillProveRecordVo.class);
                if (fileAttachmentsMap.containsKey(estimateBillProve.getId())) {
                    List<BillFileAttachment> billFileAttachments = fileAttachmentsMap.get(estimateBillProve.getId());
                    // 账单分组
                    Map<String, List<BillFileAttachment>> billFileAttachmentsMap = billFileAttachments.stream().collect(Collectors.groupingBy(k -> CharSequenceUtil.format("{}_{}", k.getTableId(), k.getFileKey())));
                    List<EstimateBillProveFileVo> estimateBillProveFileVoList = billFileAttachmentsMap.keySet().stream().map(key -> {
                        // 防止文件重复
                        BillFileAttachment billFileAttachment = billFileAttachmentsMap.get(key).get(0);
                        EstimateBillProveFileVo estimateBillProveFileVo = new EstimateBillProveFileVo();
                        estimateBillProveFileVo.setFileKey(billFileAttachment.getFileKey());
                        estimateBillProveFileVo.setFileName(billFileAttachment.getOriginalName());
                        return estimateBillProveFileVo;
                    }).collect(Collectors.toList());
                    estimateBillProveRecordVo.getEstimateBillProveFileVos().addAll(estimateBillProveFileVoList);
                }
                return estimateBillProveRecordVo;
            }).collect(Collectors.toList()));
        }
        // 过程记录增加明细账单附件
        saveDetailedBillFile(estimateBillProveRecordVos);
        return estimateBillProveRecordVos;
    }

    @Override
    public void hostAviationFileDownload(HostAviationDownloadDto dto, HttpServletResponse response) {
        Map<String, Object> data = this.hostAviationFilePreview(dto);
        // 表名
        String hostManyType = Objects.equals(HostAviationEnum.HOST_MANY_TYPE.getValue(), dto.getDataType()) ? "非HOST航-多主机" : "HOST航信息表";
        String hostAppType = Objects.equals(HostAviationEnum.HOST_APP_TYPE.getValue(), dto.getDataType()) ? "非HOST航-app" : hostManyType;
        String fileName = CharSequenceUtil.format("{}{}", hostAppType, DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN));
        String hostAviation = Objects.equals(HostAviationEnum.HOST_AVIATION.getValue(), dto.getDataType()) ? "host_export_template.xlsx" : "non_host_export_template.xlsx";
        EasyExcelUtil.responseSingleSheetExcel(response, fileName, hostAviation, "sheet1", data);
    }

    @Override
    public Map<String, Object> hostAviationFilePreview(HostAviationDownloadDto dto) {
        Map<String, Object> data = new HashMap<>();
        // host航下载
        if (Objects.equals(dto.getDataType(), HostAviationEnum.HOST_AVIATION.getValue())) {
            List<HostAviation> vos = iHostAviationService.lambdaQuery().eq(HostAviation::getEstimateBillProveId, dto.getEstimateBillProveId()).list();
            if (CollUtil.isEmpty(vos)) {
                return data;
            }
            // 添加合计行
            HostAviation sum = new HostAviation();
            sum.setBillMonth("合计");
            sum.setHostAviationFeeTotal(String.valueOf(vos.stream().filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getHostAviationFeeTotal())).mapToDouble(v -> Double.parseDouble(v.getHostAviationFeeTotal())).sum()));
            vos.add(sum);

            data.put("billNumber", vos.get(0).getBillNumber());
            data.put("customerName", vos.get(0).getCustomerName());
            data.put("billStatus", vos.get(0).getBillStatus());
            data.put("data", vos);
        } else {
            data.put("type", Objects.equals(dto.getDataType(), HostAviationEnum.HOST_APP_TYPE.getValue()) ? "app" : "多主机");
            List<NotHostAviation> vos = iNotHostAviationService.lambdaQuery()
                    .eq(NotHostAviation::getEstimateBillProveId, dto.getEstimateBillProveId())
                    .eq(NotHostAviation::getDataType, dto.getDataType())
                    .list();
            if (CollUtil.isEmpty(vos)) {
                return data;
            }
            // 添加合计行
            NotHostAviation sum = new NotHostAviation();
            sum.setAirportCode("合计");
            sum.setByAirTotal(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getByAirTotal())).mapToDouble(v -> Double.parseDouble(v.getByAirTotal())).sum()));
            sum.setRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getRefundMoney())).sum()));
            sum.setJanuaryRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getJanuaryRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getJanuaryRefundMoney())).sum()));
            sum.setFebruaryRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getFebruaryRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getFebruaryRefundMoney())).sum()));
            sum.setMarchRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getMarchRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getMarchRefundMoney())).sum()));
            sum.setAprilRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getAprilRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getAprilRefundMoney())).sum()));
            sum.setMayRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getMayRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getMayRefundMoney())).sum()));
            sum.setJuneRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getJuneRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getJuneRefundMoney())).sum()));
            sum.setJulyRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getJulyRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getJulyRefundMoney())).sum()));
            sum.setAugustRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getAugustRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getAugustRefundMoney())).sum()));
            sum.setSeptemberRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getSeptemberRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getSeptemberRefundMoney())).sum()));
            sum.setOctoberRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getOctoberRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getOctoberRefundMoney())).sum()));
            sum.setNovemberRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getNovemberRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getNovemberRefundMoney())).sum()));
            sum.setDecemberRefundMoney(String.valueOf(vos.stream().filter(v -> StringUtils.isNotBlank(v.getDecemberRefundMoney())).mapToDouble(v -> Double.parseDouble(v.getDecemberRefundMoney())).sum()));
            vos.add(sum);

            data.put("year", vos.get(0).getYear());
            data.put("data", vos);
        }
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void raiseAnObjection(RaiseAnObjectionDto dto) {
        // 查询是否可以操作该数据
        EstimateBill estimateBill = iEstimateBillService.lambdaQuery().eq(EstimateBill::getId, dto.getEstimateBillId()).one();
        if (ObjectUtil.isEmpty(estimateBill)) {
            throw new BusinessException(10001);
        }
        // 记录离港返还账单存证
        EstimateBillProve estimateBillProve = new EstimateBillProve();
        BeanUtil.copyProperties(dto, estimateBillProve);
        estimateBillProve.setType(EstimateBillConstants.OTHER);
        estimateBillProve.setOperateType(EnumOperateType.AIRPORT_DISAGREEMENT.getCode() + "");
        // 保存账单存证
        iEstimateBillProveService.save(estimateBillProve);
        // 提出异议，待分支异议处理
        iEstimateBillService.lambdaUpdate().set(EstimateBill::getStatus, EstimateBillConstants.PENDING_BRANCH_OBJECTION_PROCESSING)
                .set(EstimateBill::getOperateLastTime, LocalDateTime.now())
                .eq(EstimateBill::getId, dto.getEstimateBillId())
                .update();
        // 去除空
        List<String> fileKeys = dto.getFileKeys().stream().filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        if (!fileKeys.isEmpty()) {
            // 上传文件不能超过20个
            if (fileKeys.size() > 20) {
                throw new BusinessException(10108);
            }
            iFileAttachmentBizService.assFileByFileKeyList(fileKeys, FileBusinessTypeEnum.ATTACHMENT, estimateBillProve.getId());
        }
        // 提异议通知给机场
        sendBranch(estimateBillProve.getEstimateBillId(), estimateBillProve, fileKeys);
    }

    /**
     * 账单确认、提异议、账单操作记录通知给分支
     *
     * @param estimateBillId :
     * @param estimateBillProve :
     * @param fileKeys :
     */
    private void sendBranch(Long estimateBillId, EstimateBillProve estimateBillProve, List<String> fileKeys) {
        //查询离岗返还账单
        EstimateBill estimateBill = iEstimateBillService.getById(estimateBillId);
        if (ObjectUtil.isNotEmpty(estimateBill)) {
            String keyName = ConfigUtil.getString(EstimateBillConstants.SIGNATURE_CONFIG, EstimateBillConstants.TRAVELSKY_CODE, GlobalConstants.TENANT_DEFAULT);
            if (StringUtils.isBlank(keyName)) {
                throw new GenericException(BusinessMessageEnum.NOT_TRAVELSKY_CODE_ERROR.getCode(), BusinessMessageEnum.NOT_TRAVELSKY_CODE_ERROR.getCode());
            }
            // 账单确认、提异议通知给分支
            EstimateBillVO estimateBillVO = new EstimateBillVO();
            BeanUtil.copyProperties(estimateBill, estimateBillVO);
            // 账单操作记录
            EstimateBillProveVo estimateBillProveVo = new EstimateBillProveVo();
            BeanUtil.copyProperties(estimateBillProve, estimateBillProveVo);
            estimateBillVO.getEstimateBillProveVoList().add(estimateBillProveVo);
            // 查询发票
            if (!fileKeys.isEmpty()) {
                List<BillFileAttachment> fileAttachments = iBillFileAttachmentService.lambdaQuery().in(BillFileAttachment::getFileKey, fileKeys).list();
                if (CollUtil.isNotEmpty(fileAttachments)) {
                    estimateBillVO.getInvoiceList().addAll(fileAttachments);
                }
            }
            ssssUtil.send("ESTIMATE_BILL_CONFIRM", estimateBill.getAirportCode(), Collections.singletonList(keyName), estimateBillVO);
        }
    }

    /**
     * Title：saveDetailedBillFile
     * Description：过程记录增加明细账单附件
     * author：李军呈
     * date： 2024/10/31 13:41
     * @param estimateBillProveVoList 过程记录集合
     */
    private void saveDetailedBillFile(List<EstimateBillProveRecordVo> estimateBillProveVoList) {
        estimateBillProveVoList.forEach(pro -> {
            if (HostAviationEnum.HOST_AVIATION.getValue().equals(pro.getType())) {
                // 明细账单附件查询
                List<HostAviation> hostAviationList = iHostAviationService.lambdaQuery()
                        .eq(HostAviation::getEstimateBillProveId, pro.getId())
                        .eq(HostAviation::getDeleted, Boolean.FALSE).list();
                // 获取host航导入批次号
                if (CollUtil.isNotEmpty(hostAviationList)) {
                    List<BillFileAttachment> fileList = iFileAttachmentBizService.queryFileList(FileBusinessTypeEnum.ATTACHMENT_HOST, Long.valueOf(hostAviationList.get(0).getBatchNumber()));
                    if (CollUtil.isNotEmpty(fileList)) {
                        EstimateBillProveFileVo estimateBillProveFileVo = new EstimateBillProveFileVo();
                        estimateBillProveFileVo.setFileKey(fileList.get(0).getFileKey());
                        estimateBillProveFileVo.setFileName(fileList.get(0).getOriginalName());
                        pro.setEstimateBillProveFileVos(Collections.singletonList(estimateBillProveFileVo));
                    }
                }
            }
            // hostApp file附件添加
            setHostAppFile(pro);
            // host多主机 file附件添加
            setHostManyFile(pro);
        });
    }

    private void setHostAppFile(EstimateBillProveRecordVo estimateBillProveRecordVo) {
        if (HostAviationEnum.HOST_APP_TYPE.getValue().equals(estimateBillProveRecordVo.getType())) {
            List<NotHostAviation> notHostList = iNotHostAviationService.lambdaQuery()
                    .eq(NotHostAviation::getEstimateBillProveId, estimateBillProveRecordVo.getId())
                    .eq(NotHostAviation::getDataType, HostAviationEnum.HOST_APP_TYPE.getValue())
                    .eq(NotHostAviation::getDeleted, Boolean.FALSE).list();
            if (CollUtil.isNotEmpty(notHostList)) {
                List<BillFileAttachment> fileList = iFileAttachmentBizService.queryFileList(FileBusinessTypeEnum.ATTACHMENT_HOST_APP, Long.valueOf(notHostList.get(0).getBatchNumber()));
                if (CollUtil.isNotEmpty(fileList)) {
                    EstimateBillProveFileVo estimateBillProveFileVo = new EstimateBillProveFileVo();
                    estimateBillProveFileVo.setFileKey(fileList.get(0).getFileKey());
                    estimateBillProveFileVo.setFileName(fileList.get(0).getOriginalName());
                    estimateBillProveRecordVo.setEstimateBillProveFileVos(Collections.singletonList(estimateBillProveFileVo));
                }
            }
        }
    }

    private void setHostManyFile(EstimateBillProveRecordVo estimateBillProveRecordVo) {
        if (HostAviationEnum.HOST_MANY_TYPE.getValue().equals(estimateBillProveRecordVo.getType())) {
            List<NotHostAviation> notHostList = iNotHostAviationService.lambdaQuery()
                    .eq(NotHostAviation::getEstimateBillProveId, estimateBillProveRecordVo.getId())
                    .eq(NotHostAviation::getDataType, HostAviationEnum.HOST_MANY_TYPE.getValue())
                    .eq(NotHostAviation::getDeleted, Boolean.FALSE).list();
            if (CollUtil.isNotEmpty(notHostList)) {
                List<BillFileAttachment> fileList = iFileAttachmentBizService.queryFileList(FileBusinessTypeEnum.ATTACHMENT_HOST_MANY, Long.valueOf(notHostList.get(0).getBatchNumber()));
                if (CollUtil.isNotEmpty(fileList)) {
                    EstimateBillProveFileVo estimateBillProveFileVo = new EstimateBillProveFileVo();
                    estimateBillProveFileVo.setFileKey(fileList.get(0).getFileKey());
                    estimateBillProveFileVo.setFileName(fileList.get(0).getOriginalName());
                    estimateBillProveRecordVo.setEstimateBillProveFileVos(Collections.singletonList(estimateBillProveFileVo));
                }
            }
        }
    }

}
