package com.swcares.aiot.service;

import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.form.SubsidyFlightForm;

/**
 * ClassName：com.swcares.service.SubsidySettlementService
 * Description：航线补贴结算service
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/18 9:35
 * @version v1.0
 */
public interface SubsidySettlementService {

    boolean check(SubsidyFlightForm form);

    boolean settlement(SubsidyFlightForm form, LoginUserDetails user);

}
