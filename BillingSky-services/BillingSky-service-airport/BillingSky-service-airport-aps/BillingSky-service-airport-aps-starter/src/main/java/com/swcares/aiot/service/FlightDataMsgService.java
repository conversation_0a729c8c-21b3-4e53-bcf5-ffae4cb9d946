package com.swcares.aiot.service;

import java.util.List;

/**
 * ClassName：com.swcares.service.FlightDataMsgService
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/2/27 9:42
 * @version v1.0
 */
public interface FlightDataMsgService {

    void saveFlightClassMsg(Object obj, String flightInfoFlag, String airportCode);

    void saveServiceClassMsg(Object obj, String flightInfoFlag, String airportCode);

    void getFlightClassMsg(String flightInfoFlag, String airportCode);

    void getServiceClassMsg(String flightInfoFlag, String airportCode);


}
