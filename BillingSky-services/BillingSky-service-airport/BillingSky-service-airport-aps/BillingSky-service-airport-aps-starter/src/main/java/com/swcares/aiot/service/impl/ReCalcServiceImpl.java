package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.ExcelFlightUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.ReCalcForm;
import com.swcares.aiot.core.model.entity.CargoFreeAirline;
import com.swcares.aiot.core.model.entity.SettlementStatus;
import com.swcares.aiot.dao.CargoFreeAirlineDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.aiot.service.ReCalcProcessService;
import com.swcares.aiot.service.ReCalcService;
import com.swcares.aiot.service.SettlementStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * ClassName：ReCalcServiceImpl <br>
 * Description：重新计算实现类<br>
 * Copyright © 2020/6/17 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/09/02 14:00<br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ReCalcServiceImpl implements ReCalcService {

    // 不同机场是否正在结算标志位Map
    private static final Map<String, Boolean> IS_BILLING_MAP = new ConcurrentHashMap<>();
    // 计算数据id对应状态set
    private static final Set<String> RE_CALC_SET = new ConcurrentHashSet<>();
    @Resource
    private SettlementStatusService settlementStatusService;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private ReCalcProcessService reCalcProcessService;
    @Resource
    private RedissonClient redissonClient;

    @Override
    @Async
    public void execute(ReCalcForm form, LoginUserDetails user, Long tenantId) {
        LocalDateTime calcStartTime = LocalDateTime.now();
        log.debug("++++++++++++++++++++++++++结算接口开始时间为：{}", LocalDateTimeUtil.formatNormal(calcStartTime));
        TenantHolder.setTenant(tenantId);
        String airportCode = form.getAirportCode();
        String airlineCode = Strings.isBlank(form.getAirlineCode()) ? "ALL" : form.getAirlineCode();
        List<String> flightNoList = Strings.isBlank(form.getFlightNo()) ? Collections.emptyList() : Arrays.asList(form.getFlightNo().split(","));
        // 单个航班的id
        List<String> flightIdList = Strings.isBlank(form.getFlightId()) ? Collections.emptyList() : Arrays.asList(form.getFlightId().split(","));
        List<String> feeCodeList = Strings.isBlank(form.getFeeCodes()) ? Collections.emptyList() : Arrays.asList(form.getFeeCodes().split(","));
        List<String> formulaIdList = Strings.isBlank(form.getFormulaId()) ? Collections.emptyList() : Arrays.asList(form.getFormulaId().split(","));
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        Integer dateType = form.getDateType();
        String formulaType = form.getFormulaType();

        if (startDate != null && endDate != null && startDate.after(endDate)) {
            throw new GenericException(BusinessMessageEnum.SETTLEMENT_DATE_ERROR.getCode(), BusinessMessageEnum.SETTLEMENT_DATE_ERROR.getMsg());
        }

        // 传入航班id对应等待中的结算记录id
        if (inspectExecute(flightIdList, formulaType)) {
            return;
        }
        // 重新结算redis锁的key
        String redisLockKey = "BillingSky:airport:aps:lock:calc:" + TenantHolder.getTenant();
        // 新增结算操作记录
        String ssId = checkError(form, user);
        boolean success = false;
        RLock lock = null;
        try {
            lock = redissonClient.getLock(redisLockKey);
            lock.lock(2L, TimeUnit.HOURS);
            // 如果当前线程获取到锁时，flightid在提前结束set中，则直接返回
            SettlementStatus ss = settlementStatusService.findById(ssId);
            if (!"2".equals(ss.getStatus())) {
                return;
            }
            // 将map中该机场正在执行标志位置为true
            IS_BILLING_MAP.put(airportCode, true);
            // 将操作记录设置为结算中
            settlementStatusService.changeStatus(ssId, "0");
            // set中添加结算数据id，用于结束新建线程
            RE_CALC_SET.add(ssId);
            // 开启线程，定时30分钟后查看当前执行结算操作是否还在”正在执行“状态，如果是则修改状态为执行失败，并将标志位置为false
            executeBiz0(tenantId, ssId, airportCode);
            // 调用重新结算流程
            reCalcProcessService.reCalc(airportCode, airlineCode, flightNoList, flightIdList, formulaIdList, startDate, endDate, feeCodeList, user, dateType);
            success = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new GenericException(BusinessMessageEnum.ReSETTLE_KJB_FAILURE.getCode(), BusinessMessageEnum.ReSETTLE_KJB_FAILURE.getMsg());
        } finally {
            releaseResource(lock, ssId, airportCode, success);
            printExecuteTime(calcStartTime);
        }
    }

    private void releaseResource(RLock lock, String ssId, String airportCode, boolean success) {
        if (!Objects.isNull(lock)) {
            lock.unlock();
        }
        // set中删掉结算数据id
        if (ssId != null) {
            RE_CALC_SET.remove(ssId);
        }
        // 将map中该机场正在执行标志位置为false
        IS_BILLING_MAP.put(airportCode, false);
        if (ssId != null) {
            settlementStatusService.changeStatus(ssId, success ? "1" : "-1");
        }
        TenantHolder.clear();
    }

    private static void printExecuteTime(LocalDateTime calcStartTime) {
        if (log.isDebugEnabled()) {
            LocalDateTime calcEndTime = LocalDateTime.now();
            Duration duration = Duration.between(calcStartTime, calcEndTime);
            log.debug("++++++++++++++++++++++++++结算接口结束结束时间为：{}, 结算接口本次用时: {} 秒", LocalDateTimeUtil.formatNormal(calcEndTime), duration.getSeconds());
        }
    }

    private boolean inspectExecute(List<String> flightIdList, String formulaType) {
        if (CollectionUtils.isNotEmpty(flightIdList) && Strings.isNotBlank(formulaType)) {
            // flightId需要单个传进来，否则没办法在批量确认后取消单个
            List<String> settlementStatusId = settlementStatusService.getSettlementIdListByFlightId(flightIdList, formulaType);
            // 标识当前id已有等待中的结算操作
            return !settlementStatusId.isEmpty();
        }
        return false;
    }

    private void executeBiz0(Long tenantId, String ssId, String airportCode) {
        ThreadUtil.execute(() -> {
            try {
                TenantHolder.setTenant(tenantId);
                for (int i = 0; i < 30; i++) {
                    // 如果set没有此id，则结束线程
                    if (!RE_CALC_SET.contains(ssId)) {
                        log.info("=======================结算接口监控线程提前结束");
                        return;
                    }
                    Thread.sleep(60000L);
                }
            } catch (InterruptedException e) {
                log.error("结算接口监控线程异常.", e);
                Thread.currentThread().interrupt();
            } finally {
                SettlementStatus newSs = settlementStatusService.findById(ssId);
                if ("0".equals(newSs.getStatus())) {
                    settlementStatusService.changeStatus(ssId, "-1");
                    // 将map中该机场正在执行标志位置为false
                    IS_BILLING_MAP.put(airportCode, false);
                }
                TenantHolder.clear();
            }
        });
    }

    @Override
    public boolean isBilling(String airportCode) {
        return IS_BILLING_MAP.getOrDefault(airportCode, false);
    }


    @Resource
    private CargoFreeAirlineDao cargoFreeAirlineDao;


    @Override
    public Boolean addCargoNotFreeAirline(String airportCode, String airlineCodes,
                                          LoginUserDetails user) {
        String[] acs = airlineCodes.split(",");

        for (String ac : acs) {
            CargoFreeAirline cfa = new CargoFreeAirline(airportCode, ac);
            cfa.setCreateTime(new Date());
            cfa.setCreateBy(user.getUsername());
            cargoFreeAirlineDao.save(cfa);
        }
        return true;
    }

    @Override
    public List<CargoFreeAirline> getCargoNotFreeAirline(String airportCode) {
        return cargoFreeAirlineDao.getCargoFreeAirlineByAirportCode(airportCode);
    }

    @Override
    public Boolean deleteCargoNotFreeAirline(String ids) {
        for (String id : ids.split(",")) {
            cargoFreeAirlineDao.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean cancelReCalcByVariable(String airportCode, String flightId) {
        return cancelReCalc(airportCode, flightId, CommonConstants.SETTLEMENT_VARIABLE_FORMULA);
    }

    @Override
    public boolean cancelReCalcByFlight(String airportCode, String flightId) {
        return cancelReCalc(airportCode, flightId, CommonConstants.SETTLEMENT_FLIGHT_FORMULA);
    }

    @Override
    public boolean cancelReCalc(String airportCode, String flightId, String formulaType) {
        // 逻辑删除结算操作记录
        Integer removeNum = settlementStatusService.cancelSettlementStatusByFlightId(flightId, formulaType);
        return removeNum != null && removeNum != 0;

    }

    /**
     * Title: 检查结算对象是否符合要求<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/24 16:10 <br>
     */
    private String checkError(ReCalcForm form, LoginUserDetails user) {
        String feeCodes = form.getFeeCodes();
        List<Object[]> lsit;
        List<Object[]> errorList = new ArrayList<>();
        if (Strings.isNotBlank(feeCodes)) {
            String[] feeArr = feeCodes.split(",");
            handleErrorMsg(feeArr, form, errorList);
        } else {
            String airportCode = form.getAirportCode();
            String airlineCode = form.getAirlineCode();
            String flightNo = form.getFlightNo();
            String flightId = form.getFlightId();
            String formulaId = form.getFormulaId();
            Date startDate = form.getStartDate();
            Date endDate = form.getEndDate();
            lsit = flightInfoDao.listFlightInfoByCondition(startDate, endDate, airportCode,
                    airlineCode, formulaId, feeCodes, flightId, flightNo);
            handleErrorMsg(form, lsit, errorList, flightNo);
        }
        Workbook wb = null;
        String errorFileName = null;
        if (!errorList.isEmpty()) {
            // 如果存在错误数据，则将隐藏的记录显示出来
            form.setSuccessHide(false);
            List<String> titleList = Arrays.asList("航班日期", "航班号", "起降标识", "结算项目", "失败原因");
            wb = ExcelFlightUtils.getWorkbookByObject(titleList, errorList, 4);
            errorFileName = "重新结算";
        }
        return settlementStatusService.insertSettlementStatus(form, user, wb, errorFileName);
    }

    private void handleErrorMsg(String[] feeArr, ReCalcForm form, List<Object[]> errorList) {
        String airportCode = form.getAirportCode();
        String airlineCode = form.getAirlineCode();
        String flightNo = form.getFlightNo();
        String flightId = form.getFlightId();
        String formulaId = form.getFormulaId();
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        List<Object[]> lsit;
        for (String fc : feeArr) {
            lsit = flightInfoDao.listFlightInfoByCondition(startDate, endDate, airportCode,
                    airlineCode, formulaId, fc, flightId, flightNo);
            for (Object[] objs : lsit) {
                String errorMsg = getErrorMsg(objs);
                if (errorMsg == null) continue;
                Object[] error = new Object[5];
                error[0] = objs[1];
                error[1] = objs[2];
                error[2] = objs[3];
                if (objs[12] == null) {
                    // 如果formulaId为空，则是全部，否则查询出对应公式
                    error[3] = fc;
                } else {
                    error[3] = objs[12];
                }
                error[4] = errorMsg;
                errorList.add(error);
            }
        }
    }

    @Nullable
    private static String getErrorMsg(Object[] objs) {
        String errorMsg;
        // 航班未确认
        if ((objs[5] == null || objs[5].equals(0)) && objs[11] != null && ("2".equals("" + objs[11]) || "4".equals("" + objs[11]))) {
            errorMsg = "航班数据未确认";
            // 保障数据未确认
        } else if ((objs[6] == null || objs[6].equals(0)) && objs[11] != null && ("3".equals("" + objs[11]) || "4".equals("" + objs[11]))) {
            errorMsg = "保障数据未确认";
            // 飞机信息缺失
        } else if (objs[7] == null) {
            errorMsg = "飞机信息缺失";
            // 航司信息缺失
        } else if (objs[8] == null) {
            errorMsg = "航司信息缺失";
            // 费用信息缺失
        } else if (objs[9] == null) {
            errorMsg = "费用信息缺失";
            // 公式信息缺失
        } else if (objs[10] == null) {
            errorMsg = "公式信息缺失";
        } else {
            return null;
        }
        return errorMsg;
    }

    private void handleErrorMsg(ReCalcForm form, List<Object[]> lsit, List<Object[]> errorList, String flightNo) {
        for (Object[] objs : lsit) {
            String errorMsg;
            errorMsg = getString(objs);
            if (errorMsg == null) continue;
            Object[] error = new Object[5];
            error[0] = objs[1];
            error[1] = objs[2];
            error[2] = objs[3];
            // 如果formulaId为空，则是全部，否则查询出对应公式
            if (objs[12] == null) {
                error[3] = "全部";
            } else {
                error[3] = objs[12];
            }
            error[4] = errorMsg;
            errorList.add(error);
            // 当航班数据确认 或 业务保障数据确认而重新结算时，自动填充航班号
            if (Strings.isNotBlank(form.getFlightId()) && Strings.isBlank(flightNo)) {
                form.setFlightNo("" + objs[2]);
            }
        }
    }

    @Nullable
    private static String getString(Object[] objs) {
        String errorMsg;
        // 航班未确认
        if ((objs[5] == null || "0".equals("" + objs[5])) && objs[11] != null && ("2".equals("" + objs[11]) || "4".equals("" + objs[11]))) {
            errorMsg = "航班数据未确认";
            // 保障数据未确认
        } else if ((objs[6] == null || "0".equals("" + objs[6])) && objs[11] != null && ("3".equals("" + objs[11]) || "4".equals("" + objs[11]))) {
            errorMsg = "保障数据未确认";
            // 飞机信息缺失
        } else if (objs[7] == null) {
            errorMsg = "飞机信息缺失";
            // 航司信息缺失
        } else if (objs[8] == null) {
            errorMsg = "航司信息缺失";
            // 费用信息缺失
        } else if (objs[9] == null) {
            errorMsg = "费用信息缺失";
            // 公式信息缺失
        } else if (objs[10] == null) {
            errorMsg = "公式信息缺失";
        } else {
            return null;
        }
        return errorMsg;
    }
}
