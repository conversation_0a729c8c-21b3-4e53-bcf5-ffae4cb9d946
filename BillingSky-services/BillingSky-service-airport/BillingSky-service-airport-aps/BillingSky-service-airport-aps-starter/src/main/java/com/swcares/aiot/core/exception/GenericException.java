package com.swcares.aiot.core.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * ClassName：GenericException <br>
 * Description： 自定义系统异常 <br>
 *
 * <AUTHOR> <br>
 * date 2020/5/21 15:43<br>
 * @version v1.0 <br>
 */
@Getter
public class GenericException extends RuntimeException {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;
    /**
     * 错误代码
     * -- GETTER --
     * 功能描述：getCode()
     * -- SETTER --
     *  功能描述：setCode()
     *

     */
    @Setter
    private Integer code;

    /**
     * 参数用来补充说明异常消息，如需提示用户在某IP处登录可以设置消息
     * -- GETTER --
     * 功能描述：getParams()
     */
    private String params;

    /**
     * 实际数据
     * -- GETTER --
     * 功能描述：getData()
     * -- SETTER --
     *  功能描述：setData()
     *

     */
    @Setter
    private Object data;

    /**
     * 创建一个新的实例 GenericException.
     *
     * @param code Integer
     */
    public GenericException(Integer code) {
        this.code = code;
    }

    /**
     * 创建一个新的实例 GenericException.
     *
     * @param params String
     */
    public GenericException(String params) {
        this.params = params;
    }

    /**
     * 创建一个新的实例 GenericException.
     *
     * @param code Integer
     * @param data Object
     */
    public GenericException(Integer code, Object data) {
        this.code = code;
        this.data = data;
    }

    /**
     * 创建一个新的实例 GenericException.
     *
     * @param code   Integer
     * @param params String
     */
    public GenericException(Integer code, String params) {
        this.code = code;
        this.params = params;
    }

    /**
     * 创建一个新的实例 GenericException.
     *
     * @param code   Integer
     * @param data   Object
     * @param params :
     */
    public GenericException(Integer code, Object data, String params) {
        this.code = code;
        this.data = data;
        this.params = params;
    }

}
