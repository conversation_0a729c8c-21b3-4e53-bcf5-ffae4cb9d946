package com.swcares.aiot.service;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IFlightInfoManageBizService {
    /**
     * 所有航司
     * @return 航司代码
     */
    List<String> retrieveAirlineCodes();

    /**
     * 获取机场名称
     * @param allAirlineCodes 机场代码
     * @param expenseAirlineCodes 机场代码
     * @return 机场名称
     */
    List<String> getAirlineNames(List<String> allAirlineCodes, List<String> expenseAirlineCodes);

}
