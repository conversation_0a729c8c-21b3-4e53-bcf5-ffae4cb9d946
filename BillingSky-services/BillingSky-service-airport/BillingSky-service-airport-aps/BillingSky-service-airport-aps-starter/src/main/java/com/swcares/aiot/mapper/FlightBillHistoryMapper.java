package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aiot.core.entity.TFlightBillHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 航班账单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
public interface FlightBillHistoryMapper extends BaseMapper<TFlightBillHistory> {


    void copyBillHistory(@Param("billIds") List<String> billIds, @Param("name") String name, @Param("operation") String operation);

    List<TFlightBillHistory> getTFlightBillHistoryByFlightBillIds(@Param("flightBillIds") List<String> flightBillIds, @Param("operation") String operation);

}
