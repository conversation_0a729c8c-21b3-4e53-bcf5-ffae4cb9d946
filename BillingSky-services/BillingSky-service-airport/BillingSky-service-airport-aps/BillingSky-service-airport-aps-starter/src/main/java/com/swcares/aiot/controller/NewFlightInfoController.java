package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.cons.ConsAirportAps;
import com.swcares.aiot.core.model.dto.FlightInfoConfirmDto;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.service.CalcService;
import com.swcares.aiot.service.IFlightFeeBillBizService;
import com.swcares.aiot.service.NewFlightInfoService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.NewFlightInfoController
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/7 15:41
 * @version v1.0
 */
@RestController
@RequestMapping("/api/NewFlightInfoController")
@Api(value = "NewFlightInfoController", tags = {"新航班信息接口"})
@ApiVersion(value = ConsAirportAps.API_MODULE_NAME)
public class NewFlightInfoController {

    @Resource
    private NewFlightInfoService newFlightInfoService;

    @Resource
    private CalcService calcService;

    @ApiOperation("确认航班信息")
    @PostMapping(value = "/confirmFlightInfo")
    public BaseResult<Object> confirmFlightInfo(@RequestBody @Validated FlightInfoConfirmDto dto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        List<String> idList = newFlightInfoService.confirmFlightInfo(dto);
        //确认后重新结算
        if (CollectionUtils.isNotEmpty(idList)) {
            ReCalcDto reCalcDto=new ReCalcDto().setFlightId(idList).setAirportCode(dto.getAirportCode()).setSuccessHide(Boolean.TRUE);
            calcService.execute(reCalcDto, user, TenantHolder.getTenant());
        }
        return BaseResult.ok();
    }
    @ApiOperation("取消航班信息确认")
    @PostMapping(value = "/cancelConfirmFlightInfo")
    public BaseResult<Object> cancelConfirmFlightInfo( String flightIds) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        newFlightInfoService.cancelConfirmFlightInfo(flightIds, user);
        return BaseResult.ok();
    }
    @ApiOperation("确认航班业务保障数据")
    @PostMapping(value = "/confirmFlightBusinessInfo")
    public BaseResult<Object> confirmFlightBusinessInfo(@RequestBody @Validated FlightInfoConfirmDto dto) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        List<String> idList = newFlightInfoService.confirmFlightBusinessInfo(dto);
        //确认后重新结算
        if (CollectionUtils.isNotEmpty(idList)) {
            ReCalcDto reCalcDto=new ReCalcDto().setFlightId(idList).setAirportCode(dto.getAirportCode()).setSuccessHide(Boolean.TRUE);
            calcService.execute(reCalcDto, user, TenantHolder.getTenant());
        }
        return BaseResult.ok();
    }
    @ApiOperation("取消确认航班业务保障数据")
    @PostMapping(value = "/cancelConfirmFlightBusinessInfo")
    public BaseResult<Object> cancelConfirmFlightBusinessInfo(String flightIds) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
         newFlightInfoService.cancelConfirmFlightBusinessInfo(flightIds,user);
        return BaseResult.ok();
    }

}
