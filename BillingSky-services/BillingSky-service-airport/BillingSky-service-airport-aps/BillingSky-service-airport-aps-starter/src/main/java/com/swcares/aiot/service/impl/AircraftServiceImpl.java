package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.ExcelFlightUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.entity.AircraftAirlineInfoVo;
import com.swcares.aiot.core.entity.AircraftInfoVo;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.AircraftForm;
import com.swcares.aiot.core.form.ReCalcForm;
import com.swcares.aiot.core.model.dto.ImportAircraftErrorDto;
import com.swcares.aiot.core.model.entity.AircraftInfo;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.AircraftDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.aiot.mapper.AircraftMapper;
import com.swcares.aiot.service.AircraftService;
import com.swcares.aiot.service.ReCalcService;
import com.swcares.aiot.service.UploadStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * ClassName：AircraftServiceImpl <br> Description：(AircraftService的实现类)<br> Copyright © 2020-9-29
 * xnky.travelsky.net Inc. All rights reserved. <br> Company：Aviation Cares Of Southwest Chen Du LTD
 * <br>
 *
 * <AUTHOR> <br> date 2020-9-29 14:24<br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class AircraftServiceImpl implements AircraftService {
    private static final String ERROR_MSG = "出现业务异常";
    public static final String TEXT_ERROR = "文本格式不正确";
    public static final String DATE_ERROR = "结束时间不能小于开始时间";
    public static final String DATE_FORMAT_ERROR = "开始时间/结束时间 格式不正确";
    public static final String DATE_OVERLAP_ERROR = "同一机号开始时间结束时间有重叠";
    private static final String MODIFIED_BY_IMPORT_DATA_REPEAT = "导入数据重叠";

    @Resource
    private AircraftDao aircraftDao;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private ReCalcService reCalcService;
    @Resource
    private UploadStatusService uploadStatusService;
    private static final Map<String, Lock> AIRPORT_LOCK_MAP = new ConcurrentHashMap<>();
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private AircraftMapper aircraftMapper;

    @Override
    public ResultBuilder<AircraftInfo> getAircraftInfoById(String aircraftId) {
        return new ResultBuilder.Builder<AircraftInfo>().data(aircraftDao.getAircraftInfoById(aircraftId)).builder();
    }

    @Transactional
    @Override
    public ResultBuilder<Object> saveAircraft(AircraftForm aircraftForm, LoginUserDetails user) {
        AircraftInfo aircraftInfo = new AircraftInfo();
        List<AircraftInfo> dbAircraftInfoList = aircraftDao.findByRegNo(aircraftForm.getRegNo());
        if (!dbAircraftInfoList.isEmpty()) {
            for (AircraftInfo info : dbAircraftInfoList) {
                // 判断时间是否重叠
                if (info.getEndDate().after(aircraftForm.getStartDate())
                        && info.getStartDate().before(aircraftForm.getEndDate())) {
                    log.error(BusinessMessageEnum.SAVE_AIRCRAFT_ERROR.getMsg());
                    throw new GenericException(BusinessMessageEnum.SAVE_AIRCRAFT_ERROR.getCode(),
                            BusinessMessageEnum.SAVE_AIRCRAFT_ERROR.getMsg());
                }
            }
        }
        aircraftInfo.setAirlineCode(aircraftForm.getAirlineCode());
        aircraftInfo.setAirlineShortName(aircraftForm.getAirlineShortName());
        aircraftInfo.setSettleCode(aircraftForm.getSettleCode());
        aircraftInfo.setRegNo(aircraftForm.getRegNo());
        aircraftInfo.setAirplaneModel(aircraftForm.getAirplaneModel());
        aircraftInfo.setMaxTakeoffWeight(aircraftForm.getMaxTakeoffWeight());
        aircraftInfo.setMaxSeat(aircraftForm.getMaxSeat());
        aircraftInfo.setMaxPayload(aircraftForm.getMaxPayload());
        aircraftInfo.setAirplaneType(aircraftForm.getAirplaneType());
        aircraftInfo.setAirplaneAtt(aircraftForm.getAirplaneAtt());
        aircraftInfo.setStartDate(aircraftForm.getStartDate());
        aircraftInfo.setEndDate(aircraftForm.getEndDate());
        aircraftInfo.setCreateBy(user.getUsername());
        aircraftInfo.setCreateTime(new Date());
        aircraftInfo.setModifiedBy(user.getUsername());
        aircraftInfo.setModifiedTime(new Date());
        aircraftDao.save(aircraftInfo);

        executeReCalc(aircraftForm,user);
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<Object> pageAircraftByCondition(String airlineCode, String airlineShortName, String regNo, PageParam pageParam) {
        Pageable pageable = PageRequest.of(pageParam.getPage() - 1, pageParam.getLimit());
        if (CharSequenceUtil.isBlank(airlineCode)) {
            airlineCode = null;
        }
        if (CharSequenceUtil.isBlank(airlineShortName)) {
            airlineShortName = null;
        }
        if (CharSequenceUtil.isBlank(regNo)) {
            regNo = null;
        }
        return new ResultBuilder.Builder<>().data(aircraftDao.pageAircraftByCondition(airlineCode, airlineShortName, regNo, pageable)).builder();
    }

    @Override
    public ResultBuilder<Object> deleteAircraftByCondition(String airlineCode, String airlineShortName, String regNo, String ids,LoginUserDetails user) {

        String[] random = {"random"};
        aircraftDao.deleteAircraftByCondition(airlineCode, airlineShortName, regNo, ids == null ? Arrays.asList(random) :
                Arrays.asList(ids.split(",")),user.getUsername(),new Date());

        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional
    public ResultBuilder<Object> updateAircraft(AircraftForm aircraftForm, LoginUserDetails user) {
        AircraftInfo aircraftInfo = aircraftDao.findByAircraftId(aircraftForm.getAircraftId());
        List<AircraftInfo> dbAircraftInfoList = aircraftDao.findByRegNo(aircraftForm.getRegNo());
        if (!dbAircraftInfoList.isEmpty()) {
            for (AircraftInfo info : dbAircraftInfoList) {
                // 判断时间是否重叠
                if (info.getEndDate().after(aircraftForm.getStartDate())
                        && info.getStartDate().before(aircraftForm.getEndDate())
                        && !info.getId().equals(aircraftForm.getAircraftId())) {
                    log.error(BusinessMessageEnum.UPDATE_AIRCRAFT_ERROR.getMsg());
                    throw new GenericException(BusinessMessageEnum.UPDATE_AIRCRAFT_ERROR.getCode(),
                            BusinessMessageEnum.UPDATE_AIRCRAFT_ERROR.getMsg());
                }
            }
        }
        try {
            BeanUtils.copyProperties(aircraftInfo, aircraftForm);

            aircraftInfo.setAirlineCode(aircraftForm.getAirlineCode());
            aircraftInfo.setModifiedBy(user.getUsername());
            aircraftInfo.setModifiedTime(new Date());
            aircraftDao.save(aircraftInfo);

            // 重新结算
            executeReCalc(aircraftForm, user);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(ERROR_MSG, e);
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<Object> importAircraft(MultipartFile file, String airportCode, Date settlementStartDate, Date settlementEndDate, LoginUserDetails user) {
        List<String> newRegNo = new ArrayList<>();
        boolean uploadOver = false;
        Lock airportLock;
        // 判断当前机场的锁
        synchronized (this) {
            airportLock = AIRPORT_LOCK_MAP.computeIfAbsent(airportCode, k -> new ReentrantLock());
        }
        // 新建上传信息消息
        String uploadId = uploadStatusService.newUpload("飞机信息表上传", airportCode, user);
        Object[] header = FileUtils.getExcelHeader(file);
        checkHeader(header, uploadId);
        // 根据机场获取锁
        airportLock.lock();
        DefaultTransactionDefinition transDefinition = new DefaultTransactionDefinition();
        transDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transStatus = transactionManager.getTransaction(transDefinition);
        try {
            // 上传消息更改状态为进行中
            uploadStatusService.startUpload(uploadId);
            List<Object[]> list = FileUtils.importExcel(file);
            List<AircraftInfo> aircraftInfos = new ArrayList<>();

            List<Object[]> errorList = new ArrayList<>();
            // 获取数据库所有信息
            List<AircraftInfo> aircraftInfoAll = aircraftDao.findAll();
            // 新导入飞机信息，用于检查传入飞机信息是否有时间重叠的错误
            Map<String, List<AircraftInfo>> newAircraftInfoMap = new HashMap<>();
            // 数据库中的旧飞机信息数据，按照机号分装到map中
            Map<String, List<AircraftInfo>> aircraftInfoAllMap = getAircraftInfoAllMap(aircraftInfoAll);
            // 数据库中需要删除的就飞机信息数据
            Map<String, List<AircraftInfo>> deleteAircraftInfoMap = new HashMap<>();
            for (Object[] objects : list) {
                if (objects[0] == null) {
                    continue;
                }
                getSaveList(objects, errorList, newAircraftInfoMap, deleteAircraftInfoMap, aircraftInfoAllMap, user.getUsername());
            }
            //保存到数据库
            saveAndDeleteAircraftInfo(deleteAircraftInfoMap, newAircraftInfoMap, aircraftInfos, newRegNo,user);
            // 生成错误数据文件workbook
            Workbook errorWorkbook = ExcelFlightUtils.getAircaraftErrorWorkbook(errorList);

            // 将上传消息状态更改为完成
            if (Strings.isNotBlank(uploadId)) {
                uploadStatusService.uploadSuccess(uploadId, "飞机信息导入错误数据文件", errorWorkbook);
                uploadOver = true;
            }
            transactionManager.commit(transStatus);
        } catch (Exception e) {
            log.error("导入飞机信息异常:", e);
            transactionManager.rollback(transStatus);
            throw new GenericException(BusinessMessageEnum.DATA_IMPORT_ERROR.getCode(),
                    BusinessMessageEnum.DATA_IMPORT_ERROR.getMsg());
        } finally {
            airportLock.unlock();
            if (Strings.isNotBlank(uploadId) && !uploadOver) {
                uploadStatusService.uploadFail(uploadId, "执行异常");
            }
        }
        doReCalc(airportCode, settlementStartDate, settlementEndDate, user, newRegNo);
        return new ResultBuilder.Builder<>().builder();
    }

    /**
     * Title: saveAndDeleteAircraftInfo
     *
     * @param deleteAircraftInfoMap ：
     * @param newAircraftInfoMap ：
     * @param aircraftInfos ：
     * @param newRegNo ：
     * Description: 保存新飞机信息，并删除需要删除的飞机信息
     * data:
     * Author: liuzhiheng
     * Date : 2024-10-21 11:11:30
     */
    private void saveAndDeleteAircraftInfo(Map<String, List<AircraftInfo>> deleteAircraftInfoMap, Map<String, List<AircraftInfo>> newAircraftInfoMap,
                                           List<AircraftInfo> aircraftInfos, List<String> newRegNo,LoginUserDetails user) {
        List<AircraftInfo> deleteAllList = new ArrayList<>();
        for (Map.Entry<String, List<AircraftInfo>> entry : deleteAircraftInfoMap.entrySet()) {
            deleteAllList.addAll(entry.getValue());
        }
        if (!deleteAllList.isEmpty()) {
            aircraftDao.deleteAircraftByIds(deleteAllList.stream().map(AircraftInfo::getId).collect(Collectors.toList()),user.getUsername(), new Date());
        }
        aircraftDao.flush();
        for (Map.Entry<String, List<AircraftInfo>> entry : newAircraftInfoMap.entrySet()) {
            for (AircraftInfo addAi : entry.getValue()) {
                if ("1".equals(addAi.getInvalid())) {
                    aircraftInfos.addAll(entry.getValue());
                }
            }
        }
        aircraftDao.saveAll(aircraftInfos);
        aircraftDao.flush();
        for (AircraftInfo ai : aircraftInfos) {
            newRegNo.add(ai.getRegNo());
        }
    }

    /**
     * Title: getSaveList
     *
     * @param objects ：
     * @param errorList ：
     * @param newAircraftInfoMap ：
     * @param deleteAircraftInfoMap ：
     * @param aircraftInfoAllMap ：
     * @param username
     * Description: 生成需要保存的飞机数据list
     * data:
     * Author: liuzhiheng
     * Date: 2024-10-21 11:08:29
     */
    private void getSaveList(Object[] objects, List<Object[]> errorList, Map<String, List<AircraftInfo>> newAircraftInfoMap,
                             Map<String, List<AircraftInfo>> deleteAircraftInfoMap, Map<String, List<AircraftInfo>> aircraftInfoAllMap,
                             String username) {
        AircraftInfo aircraftInfo = new AircraftInfo();
        if ("null".equals(objects[0])) {
            return;
        }
        ImportAircraftErrorDto importAircraftErrorDto = new ImportAircraftErrorDto();

        String regNo = setReg(objects, aircraftInfo, importAircraftErrorDto);
        setAirplaneModel(objects, aircraftInfo, importAircraftErrorDto);
        setMaxTakeoffWeight(objects, aircraftInfo, importAircraftErrorDto);
        setMaxPayload(objects, aircraftInfo, importAircraftErrorDto);
        setMaxSeat(objects, aircraftInfo, importAircraftErrorDto);
        setAirplaneType(objects, aircraftInfo, importAircraftErrorDto);
        setAirplaneAtt(objects, aircraftInfo, importAircraftErrorDto);
        setSettleCode(objects, aircraftInfo, importAircraftErrorDto);
        setAirlineCode(objects, aircraftInfo, importAircraftErrorDto);
        setAirlineShortName(objects, aircraftInfo, importAircraftErrorDto);
        setStartDateAndEndDate(objects, aircraftInfo, importAircraftErrorDto);

        // 将结束时间早于开始时间的飞机信息放进错误信息表中，并跳过
        if (!importAircraftErrorDto.isError() && (aircraftInfo.getStartDate() == null || aircraftInfo.getEndDate() == null || (aircraftInfo.getStartDate().after(aircraftInfo.getEndDate())))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.START_DATE.getValue());
            importAircraftErrorDto.setErrorText(DATE_ERROR);
            importAircraftErrorDto.setError(true);
        }

        if (importAircraftErrorDto.isError()) {
            Object[] obj = new Object[3];
            obj[0] = importAircraftErrorDto.getErrorTitle();
            obj[1] = aircraftInfo;
            obj[2] = importAircraftErrorDto.getErrorText();
            errorList.add(obj);
            return;
        }
        // 是否添加到数据库的新数据
        boolean isNew = true;
        // 判断导入的新数据比较是否有日期重叠
        List<AircraftInfo> newAircraftList = newAircraftInfoMap.getOrDefault(aircraftInfo.getRegNo(), new ArrayList<>());
        for (AircraftInfo ai : newAircraftList) {
            isNew = oldDataOverlap(ai, aircraftInfo, errorList, deleteAircraftInfoMap, isNew, regNo);
        }
        // 如果跟一同导入的新数据有时间重叠，则将本条数据添加到错误数据中，并跳过
        isNew = newDataOverlap(aircraftInfo, errorList, isNew, aircraftInfoAllMap, regNo, deleteAircraftInfoMap);
        // 完全新增 的数据,否则为老数据一致的数据，则只进行上面的判断，不新增
        if (!isNew) {
            aircraftInfo.setInvalid("0");
        }
        aircraftInfo.setCreateBy(username);
        aircraftInfo.setCreateTime(new Date());
        aircraftInfo.setModifiedBy(username);
        aircraftInfo.setModifiedTime(new Date());

        newAircraftList.add(aircraftInfo);
        newAircraftInfoMap.put(aircraftInfo.getRegNo(), newAircraftList);
    }

    private boolean newDataOverlap(AircraftInfo aircraftInfo, List<Object[]> errorList, boolean isNew, Map<String, List<AircraftInfo>> aircraftInfoAllMap,
                                   String regNo, Map<String, List<AircraftInfo>> deleteAircraftInfoMap) {
        if (!isNew) {
            addErrorList(aircraftInfo,errorList);
        } else {

            // 与数据库中的数据比较是否数据一致
            if (aircraftInfoAllMap.containsKey(regNo)) {
                List<AircraftInfo> aiList = aircraftInfoAllMap.get(regNo);
                isNew = isOverlap(aiList, regNo, aircraftInfo, deleteAircraftInfoMap);
                aircraftInfoAllMap.put(aircraftInfo.getRegNo(), aiList);
            }
        }
        return isNew;
    }

    private boolean isOverlap(List<AircraftInfo> aiList, String regNo, AircraftInfo aircraftInfo, Map<String, List<AircraftInfo>> deleteAircraftInfoMap) {
        boolean isNew=true;
        Iterator<AircraftInfo> it = aiList.iterator();
        while (it.hasNext()) {
            AircraftInfo info = it.next();
            if (regNo.equals(info.getRegNo())) {
                // 机号相同，内容不同
                if (!aircraftInfo.getMaxTakeoffWeight()
                        .equals(info.getMaxTakeoffWeight())
                        || !aircraftInfo.getMaxPayload()
                        .equals(info.getMaxPayload())
                        || !aircraftInfo.getMaxSeat().equals(info.getMaxSeat())
                        || !aircraftInfo.getAirplaneModel()
                        .equals(info.getAirplaneModel())
                        || !aircraftInfo.getAirplaneAtt()
                        .equals(info.getAirplaneAtt())
                        || !aircraftInfo.getAirplaneType()
                        .equals(info.getAirplaneType())
                        || !aircraftInfo.getAirlineCode()
                        .equals(info.getAirlineCode())
                        || !aircraftInfo.getAirlineShortName()
                        .equals(info.getAirlineShortName())
                        || !aircraftInfo.getSettleCode()
                        .equals(info.getSettleCode())
                        || !aircraftInfo.getStartDate().equals(info.getStartDate())
                        || !aircraftInfo.getEndDate().equals(info.getEndDate())) {

                    // 判断时间上是否有交集
                    if (aircraftInfo.getStartDate().compareTo(info.getEndDate()) <= 0
                            && aircraftInfo.getEndDate()
                            .compareTo(info.getStartDate()) >= 0) {
                        // 从list中和数据库中删除
                        it.remove();
                        List<AircraftInfo> deleteList = deleteAircraftInfoMap
                                .getOrDefault(regNo, new ArrayList<>());
                        deleteList.add(info);
                        deleteAircraftInfoMap.put(regNo, deleteList);
                    }
                } else {// 如果新老数据完全一致,则将老数据从list中删除，数据库不新增也不删除
                    isNew = false;
                }
            }
        }
        return isNew;
    }

    private boolean oldDataOverlap(AircraftInfo ai, AircraftInfo aircraftInfo, List<Object[]> errorList,
                                   Map<String, List<AircraftInfo>> deleteAircraftInfoMap, boolean isNew, String regNo) {
        // 如果导入数据中相同机号的飞机信息时间上有重叠，则放入错误数据list中
        if (aircraftInfo.getStartDate().compareTo(ai.getEndDate()) <= 0
                && aircraftInfo.getEndDate().compareTo(ai.getStartDate()) >= 0) {
            isNew = false;
            //如果当前ai飞机数据为正常数据，或者是下面因为与老数据相同而被逻辑删除的数据，则需要添加到错误数据文件中
            if ("1".equals(ai.getInvalid()) || ("0".equals(ai.getInvalid()) && (ai.getModifiedBy() == null || !MODIFIED_BY_IMPORT_DATA_REPEAT.equals(ai.getModifiedBy())))) {
                addErrorList(ai,errorList);
                //需要判断已经在删除列表中的数据是否有与这条数据相关的数据，有则取消删除
                List<AircraftInfo> deleteList = deleteAircraftInfoMap.get(regNo);
                if (deleteList != null && !deleteList.isEmpty()) {
                    deleteList.removeIf(deleteAircraft -> deleteAircraft.getStartDate().before(ai.getEndDate()) && deleteAircraft.getEndDate().after(ai.getStartDate()));
                    deleteAircraftInfoMap.put(regNo, deleteList);
                }
            }
        }
        return isNew;
    }

    private String setReg(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 机号，必填，文本类型，五个字符以内，只支持大写字母和数字
        String regNo = (String) objects[0];
        if (Strings.isBlank(regNo) || !regNo.matches("[A-Z0-9]{1,7}")) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.REG_NO.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setRegNo(regNo);
        return regNo;
    }

    private void setAirplaneModel(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 机型，必填，文本类型，20个字符以内，只支持大写字母、-、数字
        String airplaneModel = (String) objects[1];
        if (!importAircraftErrorDto.isError() && (Strings.isBlank(airplaneModel)
                || !airplaneModel.matches("[A-Z0-9-]{1,20}"))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.AIRPLANE_MODEL.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setAirplaneModel(airplaneModel);
    }

    private void setMaxTakeoffWeight(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 最大起飞重量，必填，数值，范围0至999
        Integer maxTakeoffWeight = (Integer) objects[2];
        if (!importAircraftErrorDto.isError() && (maxTakeoffWeight == null || maxTakeoffWeight < 0
                || maxTakeoffWeight > 999)) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.MAX_TAKEOFF_WEIGHT.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setMaxTakeoffWeight(maxTakeoffWeight);
    }

    private void setMaxPayload(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 最大业载，必填，数值，范围0至999
        Integer maxPayload = (Integer) objects[3];
        if (!importAircraftErrorDto.isError() && (maxPayload == null || maxPayload < 0 || maxPayload > 999)) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.MAX_PAYLOAD.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setMaxPayload(maxPayload);
    }


    private void setMaxSeat(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 最大座位数，必填，数值，范围0至999
        Integer maxSeat = (Integer) objects[4];
        if (!importAircraftErrorDto.isError() && (maxSeat == null || maxSeat < 0 || maxSeat > 999)) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.MAX_SEAT.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setMaxSeat(maxSeat);
    }

    private void setAirplaneType(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 宽/窄体,必填，文本，两位中文
        String airplaneType = (String) objects[5];
        if (!importAircraftErrorDto.isError() && (Strings.isBlank(airplaneType) || (!"宽体".equals(airplaneType) && !"窄体".equals(airplaneType)
                && !"宽体机".equals(airplaneType) && !"窄体机".equals(airplaneType) && !"普通".equals(airplaneType)))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.AIRPLANE_TYPE.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        if (Strings.isNotBlank(airplaneType) && "宽体".equals(airplaneType)) {
            airplaneType = "宽体机";
        } else if (Strings.isNotBlank(airplaneType) && ("窄体".equals(airplaneType) || "普通".equals(airplaneType))) {
            airplaneType = "窄体机";
        }
        aircraftInfo.setAirplaneType(airplaneType);
    }

    private void setAirplaneAtt(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 飞机属性：客机
        String airplaneAtt = (String) objects[6];
        if (!importAircraftErrorDto.isError() && (Strings.isBlank(airplaneAtt) || (!"客机".equals(airplaneAtt) && !"货机".equals(airplaneAtt) && !"公务".equals(airplaneAtt)))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.AIRPLANE_ATT.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setAirplaneAtt(airplaneAtt);
    }

    private void setSettleCode(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 航空公司结算代码,必填，文本，三位字符，大写字母和数字
        String settleCode = (String) objects[7];
        if (!importAircraftErrorDto.isError() && (Strings.isBlank(settleCode) || !settleCode.matches("[A-Z0-9]{3}"))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.SETTLE_CODE.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setSettleCode(settleCode);
    }

    private void setAirlineCode(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 航司二字码，必填，文本，两位字符，大写字母加数字
        String airlineCode = (String) objects[8];
        if (!importAircraftErrorDto.isError() && (Strings.isBlank(airlineCode) || !airlineCode.matches("[A-Z0-9]{8}"))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.AIRPLANE_CODE.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setAirlineCode(airlineCode);
    }

    private void setAirlineShortName(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        // 航司简称，必填，文本，十位字符以内，中文
        String airlineShortName = (String) objects[9];
        if (!importAircraftErrorDto.isError() && (Strings.isBlank(airlineShortName) || !airlineShortName.matches("^[\\u4e00-\\u9fa5]{1,10}$"))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.AIRPLANE_NAME.getValue());
            importAircraftErrorDto.setErrorText(TEXT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setAirlineShortName(airlineShortName);
    }

    private void setStartDateAndEndDate(Object[] objects, AircraftInfo aircraftInfo, ImportAircraftErrorDto importAircraftErrorDto) {
        String startDate;
        String endDate;
        if (objects.length == 12) {
            startDate = objects[10] == null ? "" : "" + objects[10];
            endDate = objects[11] == null ? "" : "" + objects[11];
        } else {
            aircraftInfo.setRemark(objects[10] == null ? "" : "" + objects[10]);
            startDate = objects[11] == null ? "" : "" + objects[11];
            endDate = objects[12] == null ? "" : "" + objects[12];
        }

        if (!importAircraftErrorDto.isError() && (!DateUtils.isValidDate(startDate) || !DateUtils.isValidDate(endDate))) {
            importAircraftErrorDto.setErrorTitle(Constants.AircraftEnum.START_DATE.getValue());
            importAircraftErrorDto.setErrorText(DATE_FORMAT_ERROR);
            importAircraftErrorDto.setError(true);
        }
        aircraftInfo.setStartDate(DateUtils.parseDate(startDate));
        aircraftInfo.setEndDate(DateUtils.parseDate(endDate));
    }

    /**
     * Title: getAircraftInfoAllMap
     *
     * @param aircraftInfoAll ：
     * Description: 获取全部飞机信息的map
     * data:
     * return: java.util.Map<java.lang.String, java.util.List < com.swcares.aiot.core.model.entity.AircraftInfo>>
     * Author: liuzhiheng
     * Date: 2024-10-21 10:25:45
     */
    private Map<String, List<AircraftInfo>> getAircraftInfoAllMap(List<AircraftInfo> aircraftInfoAll) {
        // 数据库中的旧飞机信息数据，按照机号分装到map中
        Map<String, List<AircraftInfo>> aircraftInfoAllMap = new HashMap<>();
        for (AircraftInfo ai : aircraftInfoAll) {
            if ("0".equals(ai.getInvalid())) {
                continue;
            }
            List<AircraftInfo> aiList =
                    aircraftInfoAllMap.getOrDefault(ai.getRegNo(), new ArrayList<>());
            aiList.add(ai);
            aircraftInfoAllMap.put(ai.getRegNo(), aiList);
        }
        return aircraftInfoAllMap;
    }

    /**
     * Title:checkHeader
     *
     * @param header ：
     * @param uploadId ：
     * Description: 检查excel文件字段标题
     * data:
     * Author: liuzhiheng
     * Date: 2024-10-21 09:55:18
     */
    private void checkHeader(Object[] header, String uploadId) {
        try {
            if (!header[0].equals(Constants.AircraftEnum.REG_NO.getValue())
                    && !header[1].equals(Constants.AircraftEnum.AIRPLANE_MODEL.getValue())
                    && !header[2].equals(Constants.AircraftEnum.MAX_TAKEOFF_WEIGHT.getValue())
                    && !header[3].equals(Constants.AircraftEnum.MAX_PAYLOAD.getValue())
                    && !header[4].equals(Constants.AircraftEnum.MAX_SEAT.getValue())
                    && !header[5].equals(Constants.AircraftEnum.AIRPLANE_TYPE.getValue())
                    && !header[6].equals(Constants.AircraftEnum.AIRPLANE_ATT.getValue())
                    && !header[7].equals(Constants.AircraftEnum.SETTLE_CODE.getValue())
                    && !header[8].equals(Constants.AircraftEnum.AIRPLANE_CODE.getValue())
                    && !header[9].equals(Constants.AircraftEnum.AIRPLANE_NAME.getValue())
                    && !header[11].equals(Constants.AircraftEnum.START_DATE.getValue())
                    && !header[12].equals(Constants.AircraftEnum.END_DATE.getValue())) {
                throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(),
                        BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
            }
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
            if (Strings.isNotBlank(uploadId)) {
                uploadStatusService.uploadFail(uploadId, "上传文件表头不符合系统要求，上传失败！");
            }
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(),
                    BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }
    }

    /**
     * Title: doReCalc
     *
     * @param airportCode ：
     * @param settlementStartDate ：
     * @param settlementEndDate ：
     * @param user ：
     * @param newRegNo ：
     * Description: 导入飞机信息后，重新结算账单
     * data:
     * Author: liuzhiheng
     * Date: 2024-10-21 09:44:40
     */
    private void doReCalc(String airportCode, Date settlementStartDate, Date settlementEndDate, LoginUserDetails user, List<String> newRegNo) {
        if (settlementStartDate != null && settlementEndDate != null && !newRegNo.isEmpty()) {
            List<String[]> flightIdList= flightInfoDao.getFlightInfoByRegNo(newRegNo, airportCode);
            StringBuilder flightIdBuilder = new StringBuilder();
            StringBuilder flightNoBuilder = new StringBuilder();
            for(String[] flightId : flightIdList) {
                flightIdBuilder.append(flightId[0]);
                flightIdBuilder.append(",");
                if(flightNoBuilder.indexOf(flightId[1]) == -1) {
                    flightNoBuilder.append(flightId[1]);
                    flightNoBuilder.append(",");
                }
            }
            String flightIds = flightIdBuilder.toString();
            String flightNos = flightNoBuilder.toString();
            if (Strings.isNotBlank(flightIds)) {
                ReCalcForm form = new ReCalcForm();
                form.setStartDate(settlementStartDate);
                form.setEndDate(settlementEndDate);
                form.setAirportCode(airportCode);
                form.setFlightId(flightIds);
                form.setFlightNo(flightNos);
                form.setDateType(1);
                reCalcService.execute(form, user, TenantHolder.getTenant());
            }
        }
    }


    @Override
    public ResultBuilder<Object> doReSettlementByFlightDate(String airportCode, Date startDate,
                                                       Date endDate, LoginUserDetails user) {

        List<FlightInfo> flightInfos =
                flightInfoDao.listFightInfoByFlightDate(airportCode, startDate, endDate);
        List<String> flightNoList = new ArrayList<>();
        for (FlightInfo flight : flightInfos) {
            flightNoList.add(flight.getFlightNo());
        }
        for (String flightNo : flightNoList) {
            ReCalcForm form = new ReCalcForm();
            form.setAirportCode(airportCode);
            form.setFlightNo(flightNo);
            form.setStartDate(startDate);
            form.setEndDate(endDate);
            form.setDateType(1);
            reCalcService.execute(form, user, TenantHolder.getTenant());
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional
    public ResultBuilder<Object> delAircraftInfo(String aircraftIds, LoginUserDetails user) {
        String[] ids = aircraftIds.split(",");
        for (String aircraftId : ids) {
            aircraftDao.delAircraftInfo(aircraftId, user.getUsername(), new Date());
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<Object> findByRegnoLike(String regNo) {
        List<AircraftInfo> listAircraftInfo = aircraftDao.findByRegnoLike(regNo);
        List<String> aircraftInfos = new ArrayList<>();
        for (AircraftInfo air : listAircraftInfo) {
            String info = air.getRegNo() + "->" + DateUtils.format(air.getStartDate()) + "-" + DateUtils.format(air.getEndDate());
            aircraftInfos.add(info);
        }
        return new ResultBuilder.Builder<>().data(aircraftInfos).builder();
    }

    @Override
    public ResultBuilder<Object> findDistinctRegno(String regNo) {
        return new ResultBuilder.Builder<>().data(aircraftDao.findDistinctRegno(regNo)).builder();
    }

    @Override
    public ResultBuilder<Object> findInvalidRegon() {
        String now = DateUtils.format(new Date());
        String startDate = DateUtils.getEfficientDate();
        return new ResultBuilder.Builder<>().data(aircraftDao.findInvalidRegon(startDate, now)).builder();
    }

    @Override
    public ResultBuilder<Object> findAirplaneModelList(String airplaneModel) {
        return new ResultBuilder.Builder<>().data(aircraftDao.findAirplaneModelList(airplaneModel)).builder();
    }

    @Override
    public List<AircraftAirlineInfoVo> listAirlineInfoByRegno(String regNo, LocalDate effectiveDate) {
        return aircraftMapper.listAirlineInfoByRegno(regNo, effectiveDate);
    }

    @Override
    public List<AircraftAirlineInfoVo> listAirlineInfoByAirlineShortName(String airlineShortName) {
        return aircraftMapper.listAirlineInfoByAirlineShortName(airlineShortName);
    }

    @Override
    public List<AircraftInfoVo> getAirraftInfoByUpdateTime(LocalDateTime updateTime) {
        List<AircraftInfo> aircraftInfoList = aircraftDao.getAirraftInfoByUpdateTime(updateTime);
        List<AircraftInfoVo> aircraftInfoVoList = new ArrayList<>();
        for (AircraftInfo aircraftInfo : aircraftInfoList) {
            try {
                AircraftInfoVo aircraftInfoVo = new AircraftInfoVo();
                BeanUtils.copyProperties(aircraftInfoVo, aircraftInfo);
                aircraftInfoVoList.add(aircraftInfoVo);
            } catch (IllegalAccessException | InvocationTargetException e) {
                throw new GenericException(BusinessMessageEnum.GET_AIRRAFT_INFO_ERROR.getCode(), BusinessMessageEnum.GET_AIRRAFT_INFO_ERROR.getMsg());
            }
        }
        return aircraftInfoVoList;
    }

    private void executeReCalc(AircraftForm aircraftForm, LoginUserDetails user){
        if (Strings.isNotBlank(aircraftForm.getAirportCode())
                && aircraftForm.getSettlementStartDate() != null
                && aircraftForm.getSettlementEndDate() != null) {
            doReCalc(aircraftForm.getAirportCode(),aircraftForm.getSettlementStartDate()
                    ,aircraftForm.getSettlementEndDate(),user,Collections.singletonList(aircraftForm.getRegNo()));
        }
    }


    private void addErrorList(AircraftInfo aircraftInfo, List<Object[]> errorList){
        aircraftInfo.setModifiedBy(MODIFIED_BY_IMPORT_DATA_REPEAT);
        aircraftInfo.setInvalid("0");
        Object[] obj = new Object[3];
        obj[0] = Constants.AircraftEnum.START_DATE.getValue();
        obj[1] = aircraftInfo;
        obj[2] = DATE_OVERLAP_ERROR;
        errorList.add(obj);
    }
}
