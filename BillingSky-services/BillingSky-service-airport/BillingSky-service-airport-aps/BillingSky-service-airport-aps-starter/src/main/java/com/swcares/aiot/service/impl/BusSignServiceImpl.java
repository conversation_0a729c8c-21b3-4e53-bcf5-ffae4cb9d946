package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.util.*;
import com.swcares.aiot.core.entity.TBusSign;
import com.swcares.aiot.core.form.BugSignSearchForm;
import com.swcares.aiot.core.form.BusServiceRecordForm;
import com.swcares.aiot.core.form.SubmitForm;
import com.swcares.aiot.core.model.dto.SubmitBusDataDto;
import com.swcares.aiot.core.model.entity.BillItemBus;
import com.swcares.aiot.core.model.entity.BusSign;
import com.swcares.aiot.core.model.entity.BusSignHistory;
import com.swcares.aiot.core.model.entity.BusSignServiceRecord;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.model.vo.BillItemBusVo;
import com.swcares.aiot.core.model.vo.SubmitVo;
import com.swcares.aiot.core.service.ITBusSignService;
import com.swcares.aiot.dao.BillItemBusDao;
import com.swcares.aiot.dao.BusSignDao;
import com.swcares.aiot.dao.BusSignHistoryDao;
import com.swcares.aiot.dao.BusSignServiceRecordDao;
import com.swcares.aiot.mapper.BusSignMapper;
import com.swcares.aiot.service.BusSignService;
import com.swcares.aiot.statemachine.StatemachineTemplate;
import com.swcares.aiot.statemachine.biz.events.EnumBillStatusChangeEvent;
import com.swcares.aiot.statemachine.biz.events.EnumRevocationStatusChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.JavaType;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.service.impl.BusSignServiceImpl
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/11/15 9:04
 * @version v1.0
 */
@Service
@Slf4j
public class BusSignServiceImpl implements BusSignService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private BusSignDao busSignDao;
    @Resource
    private BusSignHistoryDao busSignHistoryDao;
    @Resource
    private BusSignServiceRecordDao busSignServiceRecordDao;
    @Resource
    private BillItemBusDao billItemBusDao;
    @Resource
    private SsssUtil ssssUtil;

    @Resource
    private StatemachineTemplate<TBusSign> statemachineTemplate;

    @Resource
    private ITBusSignService itBusSignService;

    @Resource
    private BusSignMapper bsm;

    @Override
    public ResultBuilder<Object[]> pageBusSign(BugSignSearchForm busSignSearchForm) {
        StringBuilder conditionSql = new StringBuilder();
        String airportCode = busSignSearchForm.getAirportCode();
        String flightSegment = busSignSearchForm.getFlightSegment();
        Date startDate = busSignSearchForm.getStartDate();
        Date endDate = busSignSearchForm.getEndDate();
        Date createStratDate = busSignSearchForm.getCreateStartDate();
        Date createEndDate = busSignSearchForm.getCreateEndDate();
        String airlineCode = CharSequenceUtil.isBlank(busSignSearchForm.getAirlineCode()) ? null
                : busSignSearchForm.getAirlineCode();
        String flightNo = CharSequenceUtil.isBlank(busSignSearchForm.getFlightNo()) ? null
                : busSignSearchForm.getFlightNo();
        String submit = busSignSearchForm.getSubmit();
        Integer revocation = busSignSearchForm.getRevocation();
        int count = 0;
        List<BusServiceRecordForm> conditionList = busSignSearchForm.getBusServiceRecordList();
        if (conditionList != null && !conditionList.isEmpty()) {
            count = conditionList.size();
            conditionSql = new StringBuilder(" and ( ");
            for (BusServiceRecordForm bsr : conditionList) {
                conditionSql.append(" (item_name= '").append(bsr.getServiceName()).append("' ");
                List<String> valuesList = bsr.getServiceValue();
                StringBuilder values = new StringBuilder();
                for (String v : valuesList) {
                    values.append("'").append(v).append("',");
                }
                if (values.toString().endsWith(",")) {
                    values = new StringBuilder(values.substring(0, values.length() - 1));
                }

                conditionSql.append(" and item_value in (").append(values).append(")) or");
            }
            conditionSql = new StringBuilder(conditionSql.substring(0, conditionSql.length() - 2));
            conditionSql.append(") ");
        }
        Pageable pageable = PageRequest.of(busSignSearchForm.getPage() - 1, busSignSearchForm.getLimit());
        Page<BusSign> busSignPage;
        if (count > 0) {
            String sql = getBusSignIdByCondition(conditionSql.toString(), count, airportCode);
            busSignPage = busSignDao.pageBusSignBySignIdS(sql, airportCode, startDate,
                    endDate, airlineCode, flightNo, createStratDate, createEndDate, flightSegment, submit, revocation, pageable);
        } else {
            busSignPage = busSignDao.pageBusSignByCondition(airportCode, startDate, endDate,
                    airlineCode, flightNo, createStratDate, createEndDate, flightSegment, submit, revocation, pageable);
        }
        List<String> itemNameList = billItemBusDao.getItemNameList(busSignSearchForm.getAirportCode());
        List<BusSignServiceRecord> serviceList = busSignServiceRecordDao.getBusSignServiceRecordListBySignIds(busSignPage.getContent()
                .stream().map(BusSign::getSignId).collect(Collectors.toList()));
        Map<String, String> serviceRecordMap = getServiceRecordMap(serviceList);
        List<String> title = new ArrayList<>();
        title.add("id");
        title.add("signId");
        title.add("单据日期");
        title.add("航班日期");
        title.add("航班号");
        title.add("航段");
        title.add("航空公司");
        title.addAll(itemNameList);
        title.add("车牌号");
        title.add("备注");
        title.add("单价");
        title.add("费用总计");

        Page<Map<String, String>> resPage = new PageImpl<>(getDataList(busSignPage, itemNameList, serviceRecordMap), pageable, busSignPage.getTotalElements());
        Object[] res = new Object[2];
        res[0] = title;
        res[1] = resPage;
        return new ResultBuilder.Builder<Object[]>().data(res).builder();
    }

    private Map<String, String> getServiceRecordMap(List<BusSignServiceRecord> serviceList) {
        Map<String, String> serviceRecordMap = new HashMap<>();
        for (BusSignServiceRecord bssr : serviceList) {
            serviceRecordMap.put(bssr.getBillSignId() + "-" + bssr.getItemName(),
                    bssr.getItemValue());
        }
        return serviceRecordMap;
    }

    private List<Map<String, String>> getDataList(Page<BusSign> busSignPage, List<String> itemNameList, Map<String, String> serviceRecordMap) {
        List<Map<String, String>> dataList = new ArrayList<>();
        for (BusSign bs : busSignPage.getContent()) {
            Map<String, String> data = new LinkedHashMap<>();
            putBaseInfo(data, bs);
            for (String itemName : itemNameList) {
                data.put(itemName,
                        serviceRecordMap.getOrDefault(bs.getSignId() + "-" + itemName, ""));
            }
            data.put("车牌号", bs.getLicensePlateNumber() == null ? "" : bs.getLicensePlateNumber());
            data.put("签单备注", bs.getRemark() == null ? "" : bs.getRemark());
            data.put("单价", bs.getUnitPrice() == null ? "0" : bs.getUnitPrice().toString());
            data.put("费用总计",
                    bs.getSettlementAmount() == null ? "0" : bs.getSettlementAmount().toString());
            data.put("账单状态", bs.getSubmit());
            data.put("备注", bs.getFeedback() == null ? "" : bs.getFeedback());
            data.put("撤销状态", bs.getRevocation() == null ? "" : bs.getRevocation().toString());
            dataList.add(data);
        }
        return dataList;
    }

    private void putBaseInfo(Map<String, String> data, BusSign bs) {
        data.put("id", bs.getId());
        data.put("signId", String.valueOf(bs.getSignId()));
        data.put("单据日期", bs.getSignCreatedTime() == null ? ""
                : DateUtils.format(bs.getSignCreatedTime()));
        data.put("航班日期",
                bs.getFlightDate() == null ? "" : DateUtils.format(bs.getFlightDate()));
        data.put("航班号", bs.getFlightNo() == null ? "" : bs.getFlightNo());
        data.put("航段", bs.getFlightSegment() == null ? "" : bs.getFlightSegment());
        data.put("航空公司", bs.getAirlineShortName() == null ? "" : bs.getAirlineShortName());
    }

    private String getBusSignIdByCondition(String condition, Integer count, String airportCode) {
        return " EXISTS (select 1 from  bill_sign_id from t_bus_sign_service where bill_sign_id = t.sign_id and " +
                "airport_code='" + airportCode + "' and invalid='1' " + condition
                + "  GROUP BY bill_sign_id HAVING count(1)>=" + count + " ) ";
    }

    @Override
    public void exportBusSign(BugSignSearchForm busSignSearchForm, HttpServletResponse res) {
        StringBuilder conditionSql = new StringBuilder();
        String airportCode = busSignSearchForm.getAirportCode();
        String flightSegment = busSignSearchForm.getFlightSegment();
        Date startDate = busSignSearchForm.getStartDate();
        Date endDate = busSignSearchForm.getEndDate();
        Date createStratDate = busSignSearchForm.getCreateStartDate();
        Date createEndDate = busSignSearchForm.getCreateEndDate();
        String airlineCode = busSignSearchForm.getAirlineCode();
        String flightNo = busSignSearchForm.getFlightNo();
        String submit = busSignSearchForm.getSubmit();
        Integer revocation = busSignSearchForm.getRevocation();

        Result result = getResult(busSignSearchForm, conditionSql);
        List<BusSign> busSignList;
        if (result.count > 0) {
            String sqlStr = getBusSignIdByCondition(result.conditionSql.toString(), result.count, airportCode);
            busSignList = busSignDao.listBusSignBySignIdS(sqlStr, airportCode, startDate, endDate, airlineCode, flightNo, createStratDate, createEndDate, flightSegment, submit, revocation);
        } else {
            busSignList = busSignDao.listBusSignByCondition(airportCode, startDate, endDate,
                    airlineCode, flightNo, createStratDate, createEndDate, flightSegment, submit, revocation);
        }
        List<String> itemNameList = billItemBusDao.getItemNameList(busSignSearchForm.getAirportCode());
        List<BusSignServiceRecord> serviceList = busSignServiceRecordDao.getBusSignServiceRecordListBySignIds(busSignList.stream().map(BusSign::getSignId).collect(Collectors.toList()));
        Map<String, String> serviceRecordMap = new HashMap<>();
        for (BusSignServiceRecord bssr : serviceList) {
            serviceRecordMap.put(bssr.getBillSignId() + "-" + bssr.getItemName(), bssr.getItemValue());
        }
        List<String> title = new ArrayList<>();
        title.add("单据日期");
        title.add("航班日期");
        title.add("航班号");
        title.add("航段");
        title.add("航空公司");
        title.addAll(itemNameList);
        title.add("车牌号");
        title.add("备注");
        title.add("单价");
        title.add("费用总计");
        List<List<String>> dataList = getDataList(busSignList, itemNameList, serviceRecordMap);
        Workbook workbook = ExcelBusUtils.getWorkbook(title, dataList);
        String fileName = "车辆费用账单导出.xlsx";
        FileUtils.exportToExcel(workbook, fileName, res);
    }

    private static @NotNull List<List<String>> getDataList(List<BusSign> busSignList, List<String> itemNameList, Map<String, String> serviceRecordMap) {
        List<List<String>> dataList = new ArrayList<>();
        for (BusSign bs : busSignList) {
            List<String> data = getData(bs, itemNameList, serviceRecordMap);
            dataList.add(data);
        }
        return dataList;
    }

    private static @NotNull List<String> getData(BusSign bs, List<String> itemNameList, Map<String, String> serviceRecordMap) {
        List<String> data = new ArrayList<>();
        data.add(bs.getSignCreatedTime() == null ? "" : DateUtils.format(bs.getSignCreatedTime()));
        data.add(bs.getFlightDate() == null ? "" : DateUtils.format(bs.getFlightDate()));
        data.add(bs.getFlightNo() == null ? "" : bs.getFlightNo());
        data.add(bs.getFlightSegment() == null ? "" : bs.getFlightSegment());
        data.add(bs.getAirlineShortName() == null ? "" : bs.getAirlineShortName());
        for (String itemName : itemNameList) {
            data.add(serviceRecordMap.getOrDefault(bs.getSignId() + "-" + itemName, ""));
        }
        data.add(bs.getLicensePlateNumber() == null ? "" : bs.getLicensePlateNumber());
        data.add(bs.getRemark() == null ? "" : bs.getRemark());
        data.add(bs.getUnitPrice() == null ? "0" : bs.getUnitPrice().toString());
        data.add(bs.getSettlementAmount() == null ? "0" : bs.getSettlementAmount().toString());
        return data;
    }

    private static @NotNull Result getResult(BugSignSearchForm busSignSearchForm, StringBuilder conditionSql) {
        List<BusServiceRecordForm> conditionList = busSignSearchForm.getBusServiceRecordList();
        int count = 0;
        if (conditionList != null && !conditionList.isEmpty()) {
            count = conditionList.size();
            conditionSql = new StringBuilder(" and ( ");
            for (BusServiceRecordForm bsr : conditionList) {
                conditionSql.append(" (item_name= '").append(bsr.getServiceName()).append("' ");
                List<String> valuesList = bsr.getServiceValue();
                StringBuilder values = new StringBuilder();
                for (String v : valuesList) {
                    values.append("'").append(v).append("',");
                }
                if (values.toString().endsWith(",")) {
                    values = new StringBuilder(values.substring(0, values.length() - 1));
                }
                conditionSql.append(" and item_value in (").append(values).append(")) or");
            }
            conditionSql = new StringBuilder(conditionSql.substring(0, conditionSql.length() - 2));
            conditionSql.append(") ");
        }
        return new Result(conditionSql, count);
    }

    private static class Result {
        public final StringBuilder conditionSql;
        public final int count;

        public Result(StringBuilder conditionSql, int count) {
            this.conditionSql = conditionSql;
            this.count = count;
        }
    }

    @Override
    public ResultBuilder<List<BillItemBusVo>> getQueryCondition(String airpotCode) {
        List<BillItemBus> bibList = billItemBusDao.getItemList(airpotCode);
        List<Object[]> itemList = busSignServiceRecordDao.getItemNameAndValue(airpotCode);
        Map<String, List<String>> itemMap = new HashMap<>();
        for (Object[] objs : itemList) {
            List<String> valueList = itemMap.getOrDefault("" + objs[0], new ArrayList<>());
            valueList.add("" + objs[1]);
            itemMap.put("" + objs[0], valueList);
        }

        List<BillItemBusVo> bibVoList = new ArrayList<>();
        for (BillItemBus bib : bibList) {
            BillItemBusVo bibVo = new BillItemBusVo();
            bibVo.setItemName(bib.getItemName());
            bibVo.setSelectList(itemMap.get(bib.getItemName()));
            if (bibVo.getSelectList() == null) {
                bibVo.setSelectList(new ArrayList<>());
            }
            bibVoList.add(bibVo);
        }
        return new ResultBuilder.Builder<List<BillItemBusVo>>().data(bibVoList).builder();
    }

    @Transactional
    @Override
    public ResultBuilder<?> submit(SubmitForm form) {
        bsm.updateBusSignSettleCode(form.getAirportCode(), form.getStartDate(), form.getEndDate());
        List<BusSign> busSignList = busSignDao.getBusSignByCondition(form.getAirportCode(), form.getAirlineCode(),
                form.getStartDate(), form.getEndDate());
        int fail = 0;
        int success = 0;
        SubmitVo resVo = new SubmitVo();
        if (busSignList.isEmpty()) {
            return new ResultBuilder.Builder<>().data(resVo).builder();
        }
        List<SubmitBusDataDto> submitBusDatas = new ArrayList<>(busSignList.size());
        SubmitBusDataDto bsd;
        BusSign send;
        for (BusSign each : busSignList) {
            send = new BusSign();
            BeanUtils.copyProperties(each, send);
            send.setModifiedBy(form.getAirportCode());
            send.setModifiedTime(new Date());
            bsd = new SubmitBusDataDto();
            bsd.setSettleCode(each.getSettleCode());
            bsd.setBusSignData(send);
            bsd.setItemData(billItemBusDao.findAll());
            bsd.setServiceData(busSignServiceRecordDao.getBusSignServiceRecordListBySignIds(Collections.singletonList(each.getSignId())));
            bsd.setOperation(BillOperation.SUBMIT);
            submitBusDatas.add(bsd);
        }
        Map<String, List<SubmitBusDataDto>> busSignCodeMap = submitBusDatas.stream().collect(Collectors.groupingBy(SubmitBusDataDto::getSettleCode));
        // 调用发送消息到航司端接口
        // 更新账单状态，记录历史
        BusSign busSign;
        BusSignHistory bsh;
        for (Map.Entry<String, List<SubmitBusDataDto>> keyValue : busSignCodeMap.entrySet()) {
            try {
                ssssUtil.send("BUS_BILL", form.getAirportCode(), Collections.singletonList(keyValue.getKey()), keyValue.getValue());
                success += keyValue.getValue().size();

                TBusSign tmp;
                for (SubmitBusDataDto each : keyValue.getValue()) {
                    busSign = each.getBusSignData();
                    tmp = new TBusSign();
                    BeanUtils.copyProperties(busSign, tmp);
                    tmp.setModifiedBy(AuthenticationUtils.getCurrentUser().getUsername());
                    itBusSignService.saveOrUpdate(tmp);
                    statemachineTemplate.updateBillStatusEvent(itBusSignService, TBusSign::getSubmit, TBusSign::getId, busSign.getId(), EnumBillStatusChangeEvent.AIRPORT_SUBMIT_RECONCILIATION);
                    statemachineTemplate.updateRevocationStatusEvent(itBusSignService, TBusSign::getRevocation, TBusSign::getId, busSign.getId(), EnumRevocationStatusChangeEvent.AIRPORT_SUBMIT_RECONCILIATION);

                    bsh = new BusSignHistory();
                    tmp = itBusSignService.getById(busSign.getId());
                    BeanUtils.copyProperties(tmp, bsh);
                    bsh.setFlightDate(Date.from(tmp.getFlightDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
                    bsh.setSignCreatedTime(Date.from(tmp.getSignCreatedTime().atZone(ZoneId.systemDefault()).toInstant()));
                    bsh.setId(null);
                    bsh.setCreateTime(new Date());
                    bsh.setCreateBy(AuthenticationUtils.getCurrentUser().getUsername());
                    bsh.setOperation(BillOperation.SUBMIT.getCode());
                    submitBiz(form, each, bsh);
                    busSignHistoryDao.save(bsh);
                }
            } catch (Exception e) {
                log.error("出现业务异常,param = {}", form, e);
                fail += keyValue.getValue().size();
            }
        }

        // 添加更新车辆签单状态
        resVo.setSuccess(success);
        resVo.setFail(fail);
        return new ResultBuilder.Builder<>().data(resVo).builder();
    }

    private void submitBiz(SubmitForm form, SubmitBusDataDto each, BusSignHistory bsh) {
        try {
            bsh.setServiceRecordsString(objectMapper.writeValueAsString(each.getServiceData()));
        } catch (IOException e) {
            log.error("车辆签单历史保存服务项出错：param ={}", form, e);
        }
    }

    @Override
    public List<BusSignHistory> busSignhistoryList(Long signId, int page) {
        int number = page * 10;
        List<BusSignHistory> result = busSignHistoryDao.getBusSignHistoryBySignId(signId, number);
        JavaType historyType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, BusSignServiceRecord.class);
        result.forEach(each -> {
            try {
                if (each.getServiceRecordsString() != null) {
                    each.setServiceRecords(objectMapper.readValue(each.getServiceRecordsString(), historyType));
                }
            } catch (IOException e) {
                log.error("车辆签单历史转换服务项出错：{}", e.getMessage());
            }
        });

        return result;
    }

    @Override
    public Integer busSignhistoryTotal(Long signId) {
        return busSignHistoryDao.getBusSignhistoryTotal(signId);
    }

    @Transactional
    @Override
    public void changeBusSignStatus(String id, String operation, String reason) {
        List<SubmitBusDataDto> submitBusDatas = new ArrayList<>(1);
        TBusSign tmp = itBusSignService.getById(id);
        tmp.setFeedback(reason);
        tmp.setModifiedTime(LocalDateTime.now());
        tmp.setModifiedBy(tmp.getAirportCode());
        BusSign busSign = new BusSign();
        BeanUtils.copyProperties(tmp, busSign);
        busSign.setFlightDate(Date.from(tmp.getFlightDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
        busSign.setSignCreatedTime(Date.from(tmp.getSignCreatedTime().atZone(ZoneId.systemDefault()).toInstant()));
        SubmitBusDataDto bsd = new SubmitBusDataDto();
        if (BillOperation.REJECT_HANDLE.getCode().equals(operation)) {
            bsd.setOperation(BillOperation.REJECT_HANDLE);
        } else if (BillOperation.APPLY_CANCEL.getCode().equals(operation)) {
            bsd.setOperation(BillOperation.APPLY_CANCEL);
        }
        bsd.setItemData(billItemBusDao.findAll());
        bsd.setServiceData(busSignServiceRecordDao.getBusSignServiceRecordListBySignIds(Collections.singletonList(busSign.getSignId())));
        bsd.setSettleCode(busSign.getSettleCode());
        bsd.setBusSignData(busSign);
        submitBusDatas.add(bsd);
        ssssUtil.send("BUS_BILL", busSign.getAirportCode(), Collections.singletonList(busSign.getSettleCode()), submitBusDatas);

        tmp.setModifiedBy(AuthenticationUtils.getCurrentUser().getUsername());
        itBusSignService.saveOrUpdate(tmp);
        if (BillOperation.REJECT_HANDLE.getCode().equals(operation)) {
            statemachineTemplate.updateBillStatusEvent(itBusSignService, TBusSign::getSubmit, TBusSign::getId, tmp.getId(), EnumBillStatusChangeEvent.AIRPORT_REFUSAL_HANDLE);
        } else if (BillOperation.APPLY_CANCEL.getCode().equals(operation)) {
            statemachineTemplate.updateRevocationStatusEvent(itBusSignService, TBusSign::getRevocation, TBusSign::getId, tmp.getId(), EnumRevocationStatusChangeEvent.AIRPORT_APPLY_REVOCATION);
        }

        BusSignHistory bsh = new BusSignHistory();
        tmp = itBusSignService.getById(tmp.getId());
        BeanUtils.copyProperties(tmp, bsh);
        bsh.setFlightDate(Date.from(tmp.getFlightDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
        bsh.setSignCreatedTime(Date.from(tmp.getSignCreatedTime().atZone(ZoneId.systemDefault()).toInstant()));
        bsh.setId(null);
        bsh.setCreateTime(new Date());
        bsh.setCreateBy(AuthenticationUtils.getCurrentUser().getUsername());
        bsh.setOperation(operation);
        try {
            bsh.setServiceRecordsString(objectMapper.writeValueAsString(bsd.getServiceData()));
        } catch (IOException e) {
            log.error("车辆签单历史保存服务项出错：{}", e.getMessage());
        }
        busSignHistoryDao.save(bsh);
    }

}
