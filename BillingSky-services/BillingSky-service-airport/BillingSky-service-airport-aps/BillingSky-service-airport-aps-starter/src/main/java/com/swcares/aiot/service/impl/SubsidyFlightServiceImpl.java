package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.ExcelFlightUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.ObjectUtil;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.SubsidyFlightDetailDataForm;
import com.swcares.aiot.core.form.SubsidyFlightForm;
import com.swcares.aiot.core.form.SubsidySavePassengerTicketForm;
import com.swcares.aiot.core.form.SubsidyUpdatePassengerTicketForm;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.*;
import com.swcares.aiot.service.SubsidyFlightService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.service.impl.SubsidyFlightServiceImpl
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/15 9:47
 * @version v1.0
 */
@Service
public class SubsidyFlightServiceImpl implements SubsidyFlightService {
    @Resource
    private FlightLineInfoDao flightLineInfoDao;
    @Resource
    private SubsidyParamDao subsidyParamDao;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private SubsidyFormulaDao subsidyFormulaDao;
    @Resource
    private SubsidyFlightParamJoinDao subsidyFlightParamJoinDao;
    @Resource
    private AircraftDao aircraftDao;
    private static final String OIL_EXPENSE = "燃油附加费";

    @Override
    public Object[] pageFlightLineData(SubsidyFlightForm form, PageParam pageParam) {
        String airline = form.getAirline();
        String flightLine = form.getFlightLine();
        String airportCode = form.getAirportCode();
        String airlineShortName = "";
        //如果航司包含中文
        if (ObjectUtil.checkStringContainChinese(airline)) {
            airlineShortName = airline;
            airline = "";
        }
        StringBuilder reFlightLine = getReFlightLine(flightLine);
        String flightLineCn = "";
        String reFlightLineCn = "";
        if (ObjectUtil.checkStringContainChinese(flightLine)) {
            flightLineCn = flightLine;
            flightLine = "";
            reFlightLineCn = reFlightLine.toString();
            reFlightLine = new StringBuilder();
        }
        List<Object[]> flightLineList = flightLineInfoDao.getFlightLineInfoByStartEndDate(form.getStartDate(), form.getEndDate(),
                airline, airlineShortName, flightLine, reFlightLine.toString(), flightLineCn, reFlightLineCn);
        // 将查出来的航线数据按照分页需求分页
        Pager<Object[]> pagerList = new Pager<>(pageParam.getPage(), pageParam.getLimit(), flightLineList);
        // 航班非手动填入参数
        List<SubsidyParam> flightParamList = new ArrayList<>();
        // 航班手动填入参数
        List<SubsidyParam> flightManualParamList = new ArrayList<>();
        // 根据航线查询机场下的航班参数
        List<SubsidyParam> paramList = subsidyParamDao.getFlightSubsidyParamByAirportCode(airportCode);

        List<Object[]> resList = new ArrayList<>();
        List<String> titleList = new ArrayList<>();
        titleList.add("id");
        titleList.add("日期");
        titleList.add("航司");
        titleList.add("航线");
        paramListToForEach(paramList, flightManualParamList, flightParamList);
        // 根据航班参数，获取返回对象大小
        int resSize = 4 + flightManualParamList.size() + flightParamList.size();
        // 下标表map
        Map<String, Integer> indexMap = getIndexMap(flightParamList, flightManualParamList, titleList);
        // 获取这段时间内所有航司的班次和旅客数之和
        List<Object[]> flightCountAndPsg = flightInfoDao.getFlightInfoCountByTime(form.getStartDate(), form.getEndDate(), airline, flightLine, reFlightLine.toString(), form.getAirportCode());
        // 航班非手动录入数据Map
        Map<String, Object[]> flightCountAndPsgMap = getFlightCountAndPsgMap(flightCountAndPsg);

        for (Object[] fl : pagerList.getList()) {
            FlightLineDataDto flightLineDataDto = getFlightLineDataDto(form, fl, flightCountAndPsgMap);
            if (flightLineDataDto == null) {
                continue;
            }
            Object[] resObj = getResObj(form, fl, resSize, flightLineDataDto, flightParamList, indexMap);
            resList.add(resObj);
        }
        pagerList.setList(resList);

        Object[] res = new Object[2];
        res[0] = titleList;
        res[1] = pagerList;
        return res;
    }

    private Object[] getResObj(SubsidyFlightForm form, Object[] fl, int resSize, FlightLineDataDto flightLineDataDto, List<SubsidyParam> flightParamList, Map<String, Integer> indexMap) {
        Object[] resObj = new Object[resSize];
        resObj[0] = fl[0];
        // 设置日期参数
        resObj[1] = DateUtils.format(flightLineDataDto.formulaStartDate) + " 至 " + DateUtils.format(flightLineDataDto.formulaEndDate);
        // 设置航司简称
        resObj[2] = fl[2];
        // 设置中文航线
        resObj[3] = fl[3];
        // 遍历解析非手动录入参数
        flightParamList.forEach(flightParam -> {
            if ("班次".equals(flightParam.getParamName()) && indexMap.containsKey("班次")) {
                resObj[indexMap.get("班次")] = flightLineDataDto.objs[2];
            } else if ("旅客数".equals(flightParam.getParamName()) && indexMap.containsKey("旅客数")) {
                resObj[indexMap.get("旅客数")] = getPassengerNum(flightLineDataDto);
            }
        });

        List<Object[]> paramValueList = getParamValueList(form, fl, flightLineDataDto);
        for (Object[] paramValue : paramValueList) {
            String paramName = (String) paramValue[0];
            Integer paramIndex = indexMap.get(paramName);
            if (paramIndex != null) {
                if ("轮挡时间".equals(paramName)) {
                    resObj[paramIndex] = String.format("%.2f", Double.parseDouble("" + paramValue[1]) / Double.parseDouble("" + flightLineDataDto.objs[2]));
                } else {
                    resObj[paramIndex] = paramValue[1];
                }
            }
        }
        // 遍历处理为空的数据
        for (int i = 4; i < resSize; i++) {
            if (resObj[i] == null) {
                resObj[i] = "0";
            }
        }
        return resObj;
    }

    private static @NotNull Object getPassengerNum(FlightLineDataDto flightLineDataDto) {
        return flightLineDataDto.objs[3] == null ? 0 : flightLineDataDto.objs[3];
    }

    private List<Object[]> getParamValueList(SubsidyFlightForm form, Object[] fl, FlightLineDataDto flightLineDataDto) {
        String flReFlightLine = getFlReFlightLine((fl[4]).toString());
        // 查询该航司航线下所有手动录入参数
        return subsidyParamDao.getSubsidyParamCountByDate(flightLineDataDto.formulaStartDate, flightLineDataDto.formulaEndDate, (fl[1]).toString(), (fl[4]).toString(), flReFlightLine, form.getAirportCode());
    }

    private static @NotNull Map<String, Object[]> getFlightCountAndPsgMap(List<Object[]> flightCountAndPsg) {
        Map<String, Object[]> flightCountAndPsgMap = new HashMap<>();
        for (Object[] objs : flightCountAndPsg) {
            flightCountAndPsgMap.put(objs[0] + "_" + objs[1], objs);
        }
        return flightCountAndPsgMap;
    }

    private @Nullable FlightLineDataDto getFlightLineDataDto(SubsidyFlightForm form, Object[] fl, Map<String, Object[]> flightCountAndPsgMap) {
        Date formulaStartDate = form.getStartDate();
        Date formulaEndDate = form.getEndDate();
        Object[] objs;
        // 判断是否有航线公式开始结束时间介于查询时间之间
        if (((Date) fl[5]).after(form.getStartDate()) || ((Date) fl[6]).before(form.getEndDate())) {
            formulaStartDate = DateUtils.laterTime(formulaStartDate, (Date) fl[5]);
            formulaEndDate = DateUtils.earlyTime(formulaEndDate, (Date) fl[6]);

            // 重新在新的限定时间内查询非手动录入参数
            List<Object[]> newParams = flightInfoDao.getFlightInfoCountByTime(formulaStartDate, formulaEndDate, (fl[1]).toString(), (fl[4]).toString(), getFlReFlightLine((fl[4]).toString()), form.getAirportCode());
            if (newParams.isEmpty()) {
                return null;
            } else {
                objs = getObjs(newParams);
            }
        } else {
            // 获取 航司_航线 下在输入时间段内的非手动录入航班参数
            objs = flightCountAndPsgMap.get((fl[1]).toString() + "_" + (fl[4]).toString());
            if (objs == null) {
                return null;
            }
            Object[] reObjs = flightCountAndPsgMap.get((fl[1]).toString() + "_" + getFlReFlightLine((fl[4]).toString()));
            objs[2] = (objs[2] == null ? BigInteger.valueOf(0) : (BigInteger) objs[2]).add((BigInteger) reObjs[2]);
            objs[3] = (objs[3] == null ? BigDecimal.valueOf(0) : (BigDecimal) objs[3]).add((BigDecimal) reObjs[3]);
        }
        return new FlightLineDataDto(formulaStartDate, formulaEndDate, objs);
    }

    private static class FlightLineDataDto {
        public final Date formulaStartDate;
        public final Date formulaEndDate;
        public final Object[] objs;

        public FlightLineDataDto(Date formulaStartDate, Date formulaEndDate, Object[] objs) {
            this.formulaStartDate = formulaStartDate;
            this.formulaEndDate = formulaEndDate;
            this.objs = objs;
        }
    }

    private void paramListToForEach(List<SubsidyParam> paramList, List<SubsidyParam> flightManualParamList, List<SubsidyParam> flightParamList) {
        paramList.forEach(param -> {
            // 将该机场下的航班参数，分别填入手动填入参数列表和非手动填入参数列表
            if ("1".equals(param.getParamType())) {
                flightManualParamList.add(param);
            } else if ("4".equals(param.getParamType()) && !"旅客均值".equals(param.getParamName())) {
                flightParamList.add(param);
            }
        });
    }

    private Map<String, Integer> getIndexMap(List<SubsidyParam> flightParamList, List<SubsidyParam> flightManualParamList, List<String> titleList) {
        // 下标表map
        Map<String, Integer> indexMap = new HashMap<>();
        int index = 4;
        for (SubsidyParam sp : flightParamList) {
            indexMap.put(sp.getParamName(), index++);
            titleList.add(sp.getParamName());
        }
        for (SubsidyParam sp : flightManualParamList) {
            indexMap.put(sp.getParamName(), index++);
            titleList.add(sp.getParamName());
        }
        return indexMap;
    }

    private Object[] getObjs(List<Object[]> newParams) {
        Object[] objs = newParams.get(0);
        if (newParams.size() > 1) {
            objs[2] = (objs[2] == null ? BigInteger.ZERO : (BigInteger) objs[2]).add(newParams.get(1)[2] == null ? BigInteger.ZERO : (BigInteger) newParams.get(1)[2]);
            objs[3] = (objs[3] == null ? BigDecimal.ZERO : (BigDecimal) objs[3]).add(newParams.get(1)[3] == null ? BigDecimal.ZERO : (BigDecimal) newParams.get(1)[3]);
        }
        return objs;
    }

    private String getFlReFlightLine(String flFlightLine) {
        StringBuilder flReFlightLine = new StringBuilder();
        if (CharSequenceUtil.isNotBlank(flFlightLine)) {
            // 获取相反航线
            String[] flArr = flFlightLine.split("-");
            for (int i = 0; i < flArr.length; i++) {
                flReFlightLine.append(flArr[flArr.length - 1 - i]);
                if (i != flArr.length - 1) {
                    flReFlightLine.append("-");
                }
            }
        }
        return flReFlightLine.toString();
    }

    private StringBuilder getReFlightLine(String flightLine) {
        StringBuilder reFlightLine = new StringBuilder();
        if (CharSequenceUtil.isNotBlank(flightLine)) {
            // 获取相反航线
            String[] flArr = flightLine.split("-");
            for (int i = 0; i < flArr.length; i++) {
                reFlightLine.append(flArr[flArr.length - 1 - i]);
                if (i != flArr.length - 1) {
                    reFlightLine.append("-");
                }
            }
        }
        return reFlightLine;
    }

    @Override
    public Object[] pageFlightLineDetailData(SubsidyFlightDetailDataForm form) {
        String formulaId = form.getFlightLineId();
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        SubsidyFormula sf = subsidyFormulaDao.getFormulaById(formulaId);
        if (sf == null) {
            throw new GenericException(BusinessMessageEnum.SUBSIDY_BILL_LINE_NULL_ERROR.getCode(), BusinessMessageEnum.SUBSIDY_BILL_LINE_NULL_ERROR.getMsg());
        }

        Date formulaStartDate = startDate;
        Date formulaEndDate = endDate;
        if (sf.getStartDate().after(startDate) || sf.getEndDate().before(endDate)) {
            formulaStartDate = DateUtils.laterTime(formulaStartDate, sf.getStartDate());
            formulaEndDate = DateUtils.earlyTime(formulaEndDate, sf.getEndDate());
        }
        Set<String> amSet = getAmSet(sf);
        FlightLineInfo fl = getFlightLineInfo(sf);
        String airportCode = fl.getAirportCode();
        String reFlightLine = getReFlightLine(fl);
        List<FlightInfo> fiList = flightInfoDao.getFlightInfoByTime(formulaStartDate, formulaEndDate, fl.getAirlineCode(), fl.getFlightLine(), reFlightLine, airportCode);

        // 构建对象
        FlightParamManual flightParamManual = getFlightParamManual(airportCode);
        // 构建对象
        TitleIndex titleIndex = getTitleIndex(flightParamManual.flightParamList, flightParamManual.flightManualParamList);

        // 根据航班id查询所有参数关联表信息,并将数据装进map中
        List<SubsidyFlightParamJoin> spList = subsidyFlightParamJoinDao.getSubsidyParamByDate(formulaStartDate, formulaEndDate, fl.getAirlineCode(), fl.getFlightLine(), reFlightLine, airportCode);
        Map<String, Map<String, String>> spMap = new HashMap<>();
        spList.forEach(sp -> {
            Map<String, String> paramMap = spMap.getOrDefault(sp.getFlightId(), new HashMap<>());
            paramMap.put(sp.getParamName(), sp.getParamValue());
            spMap.put(sp.getFlightId(), paramMap);
        });


        List<Object[]> resList = new ArrayList<>();
        for (FlightInfo fi : fiList) {
            Object[] fiObj = new Object[flightParamManual.resSize];
            fiObj[0] = fi.getId();
            fiObj[1] = DateUtils.format(fi.getFlightDate());
            fiObj[2] = fi.getFlightNo();
            fiObj[3] = fi.getFlightSegment();
            String regNo = fi.getRegNo();
            String flightModel = getFlightModel(fi, regNo);
            fiObj[4] = flightModel;
            boolean regErrorFlag = (!"*".equals(sf.getApplicableModels())) && !amSet.contains(flightModel);
            fiObj[5] = regErrorFlag;

            handleFlightParamList(flightParamManual, titleIndex, fi, fiObj);

            Map<String, String> fiParamMap = spMap.getOrDefault(fi.getId(), new HashMap<>());
            flightParamManual.flightManualParamList.forEach(flightManualParam -> fiObj[titleIndex.indexMap.get(flightManualParam.getParamName())] = fiParamMap.get(flightManualParam.getParamName()));

            for (int i = 6; i < fiObj.length; i++) {
                if (fiObj[i] == null) {
                    fiObj[i] = "0";
                }
            }
            resList.add(fiObj);
        }

        Object[] resObjs = new Object[2];
        resObjs[0] = titleIndex.titleList;
        resObjs[1] = resList;
        return resObjs;
    }

    private void handleFlightParamList(FlightParamManual flightParamManual, TitleIndex titleIndex, FlightInfo fi, Object[] fiObj) {
        // 遍历解析非手动录入参数
        flightParamManual.flightParamList.forEach(flightParam -> {
            if ("班次".equals(flightParam.getParamName()) && titleIndex.indexMap.containsKey("班次")) {
                fiObj[titleIndex.indexMap.get("班次")] = 1;
            } else if ("旅客数".equals(flightParam.getParamName()) && titleIndex.indexMap.containsKey("旅客数")) {
                int passengerNum = getPassengerNum(fi);
                fiObj[titleIndex.indexMap.get("旅客数")] = fi.getPsgNumber() == null ? 0 : passengerNum;
            }
        });
    }

    private @NotNull FlightParamManual getFlightParamManual(String airportCode) {
        // 航班非手动填入参数
        List<SubsidyParam> flightParamList = new ArrayList<>();
        // 航班手动填入参数
        List<SubsidyParam> flightManualParamList = new ArrayList<>();
        // 根据航线查询机场下的航班参数
        List<SubsidyParam> paramList = subsidyParamDao.getFlightSubsidyParamByAirportCode(airportCode);
        // 根据航班参数，获取返回对象大小
        int resSize = 6 + paramList.size();
        paramList.forEach(param -> {
            // 将该机场下的航班参数，分别填入手动填入参数列表和非手动填入参数列表
            if ("1".equals(param.getParamType())) {
                flightManualParamList.add(param);
            } else if ("4".equals(param.getParamType())) {
                flightParamList.add(param);
            }
        });
        return new FlightParamManual(flightParamList, flightManualParamList, resSize);
    }

    private static class FlightParamManual {
        public final List<SubsidyParam> flightParamList;
        public final List<SubsidyParam> flightManualParamList;
        public final int resSize;

        public FlightParamManual(List<SubsidyParam> flightParamList, List<SubsidyParam> flightManualParamList, int resSize) {
            this.flightParamList = flightParamList;
            this.flightManualParamList = flightManualParamList;
            this.resSize = resSize;
        }
    }

    private static @NotNull TitleIndex getTitleIndex(List<SubsidyParam> flightParamList, List<SubsidyParam> flightManualParamList) {
        List<String> titleList = new ArrayList<>();
        titleList.add("id");
        titleList.add("日期");
        titleList.add("航班号");
        titleList.add("航段");
        titleList.add("机型");
        // 机号不匹配，true为不匹配
        titleList.add("regError");
        // 下标表map
        Map<String, Integer> indexMap = new HashMap<>();
        int index = 6;
        for (SubsidyParam sp : flightParamList) {
            indexMap.put(sp.getParamName(), index++);
            titleList.add(sp.getParamName());
        }
        for (SubsidyParam sp : flightManualParamList) {
            indexMap.put(sp.getParamName(), index++);
            titleList.add(sp.getParamName());
        }
        return new TitleIndex(titleList, indexMap);
    }

    private static class TitleIndex {
        public final List<String> titleList;
        public final Map<String, Integer> indexMap;

        public TitleIndex(List<String> titleList, Map<String, Integer> indexMap) {
            this.titleList = titleList;
            this.indexMap = indexMap;
        }
    }

    private static @NotNull String getReFlightLine(FlightLineInfo fl) {
        String[] flArr = fl.getFlightLine().split("-");
        StringBuilder reFlightLine = new StringBuilder();
        for (int i = 0; i < flArr.length; i++) {
            reFlightLine.append(flArr[flArr.length - 1 - i]);
            if (i != flArr.length - 1) {
                reFlightLine.append("-");
            }
        }
        return reFlightLine.toString();
    }

    private @NotNull FlightLineInfo getFlightLineInfo(SubsidyFormula sf) {
        FlightLineInfo fl = flightLineInfoDao.getFlightLineInfoById(sf.getFlightLineId());
        if (fl == null) {
            // 抛出航綫為空異常
            throw new GenericException(BusinessMessageEnum.SUBSIDY_BILL_LINE_NULL_ERROR.getCode(), BusinessMessageEnum.SUBSIDY_BILL_LINE_NULL_ERROR.getMsg());
        }
        return fl;
    }

    private static @NotNull Set<String> getAmSet(SubsidyFormula sf) {
        Set<String> amSet = new HashSet<>();
        // 解析机型
        if (!"*".equals(sf.getApplicableModels())) {
            String[] applicableModels = sf.getApplicableModels().split(",");
            amSet = Arrays.stream(applicableModels).collect(Collectors.toSet());
        }
        return amSet;
    }

    private int getPassengerNum(FlightInfo fi) {
        int adultNumber = fi.getTransitAdultNumber() == null ? 0 : fi.getTransitAdultNumber();
        int childNumber = fi.getTransitChildNumber() == null ? 0 : fi.getTransitChildNumber();
        int infantNumber = fi.getTransitInfantNumber() == null ? 0 : fi.getTransitInfantNumber();
        return fi.getPsgNumber() + adultNumber + childNumber + infantNumber;
    }

    private String getFlightModel(FlightInfo fi, String regNo) {
        String flightModel;
        // 根据机号查询机型
        List<AircraftInfo> aiList = aircraftDao.findByRegNo(regNo);
        if (aiList.isEmpty()) {
            flightModel = fi.getFlightModel();
        } else {
            flightModel = aiList.get(0).getAirplaneModel();
        }
        return flightModel;
    }


    @Override
    public boolean updateFlightParam(String flightId, String paramName, String value,
                                     String airportCode) {
        SubsidyParam subsidyParam = subsidyParamDao.getSubsidyParamByName(paramName, airportCode);
        // 校验数值格式
        if ("1".equals(subsidyParam.getNumType())) {
            if (isPositiveInteger(value)) {
                // 抛出参数类型为正整数，但录入实际参数值不匹配
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR.getMsg());
            }
        } else if ("2".equals(subsidyParam.getNumType())) {
            if (!isInteger(value)) {
                // 抛出参数类型为整数，但录入实际参数值不匹配
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_INTEGER_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_INTEGER_ERROR.getMsg());
            }
        } else if ("3".equals(subsidyParam.getNumType()) && isDouble(value)) {
            // 抛出参数类型为浮点数，但录入实际参数值不匹配
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getMsg());
        }

        // 根据flightId和参数id查询关联表数据
        SubsidyFlightParamJoin sfpj = subsidyFlightParamJoinDao
                .getSubsidyFlightParamJoinByFlightIdAndParamId(flightId, subsidyParam.getId());
        if (sfpj == null) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_FLIGHT_PARAM_NULL_ERROR.getCode(),
                    paramName + BusinessMessageEnum.SUBSIDY_FLIGHT_PARAM_NULL_ERROR.getMsg());
        }
        // 修改原来的参数时间参数值
        sfpj.setParamValue(value);
        subsidyFlightParamJoinDao.save(sfpj);
        return true;
    }


    @Override
    public Object[] pagePassengerTicket(SubsidyFlightForm form, PageParam pageParam) {
        String airportCode = form.getAirportCode();
        List<FlightInfo> fiList = flightInfoDao.getFlightInfoByLineTime(form.getStartDate(),
                form.getEndDate(), form.getAirline(), form.getFlightLine(), airportCode);
        // 将查出来的航班数据按照分页需求分页
        Pager<FlightInfo> pagerList = new Pager<>(pageParam.getPage(), pageParam.getLimit(), fiList);
        Object[] res = getPassengerRes(pagerList.getList(), airportCode);
        pagerList.setList((List) res[1]);
        res[1] = pagerList;
        return res;

    }


    @Override
    public Object[] countPagePassengerTicket(SubsidyFlightForm form) {
        String airportCode = form.getAirportCode();
        List<FlightInfo> fiList = flightInfoDao.getFlightInfoByLineTime(form.getStartDate(),
                form.getEndDate(), form.getAirline(), form.getFlightLine(), airportCode);
        Object[] res = getPassengerRes(fiList, airportCode);
        List<String> title = (List<String>) res[0];
        List<Object[]> dataList = ((List) res[1]);

        Object[] countData = new Object[title.size()];
        for (Object[] objs : dataList) {
            for (int i = 5; i < title.size(); i++) {
                countData[i] = Double.parseDouble((String) objs[i])
                        + (countData[i] == null ? 0.0 : (double) countData[i]);
            }
        }
        res[1] = countData;
        return res;
    }

    private Object[] getPassengerRes(List<FlightInfo> flightInfos, String airportCode) {

        List<String> titleList = new ArrayList<>();
        titleList.add("id");
        titleList.add("日期");
        titleList.add("航班号");
        titleList.add("航线");
        titleList.add("航段");
        // 根据航线查询机场下的航班参数
        List<SubsidyParam> paramList = subsidyParamDao.getFlightSubsidyParamByAirportCode(airportCode);

        Set<String> titleSet = new HashSet<>();
        for (SubsidyParam sp : paramList) {
            titleSet.add(sp.getParamName());
        }
        for (String title : CommonConstants.SUBSIDY_PASSENGER_TICKET_TITLE) {
            if (titleSet.contains(title)) {
                titleList.add(title);
            }
        }
        List<Object[]> resList = getObjects(flightInfos, titleList);
        Object[] res = new Object[2];
        res[0] = titleList;
        res[1] = resList;

        return res;
    }

    private @NotNull List<Object[]> getObjects(List<FlightInfo> flightInfos, List<String> titleList) {
        List<Object[]> resList = new ArrayList<>();
        for (FlightInfo fi : flightInfos) {
            Object[] resObj = new Object[titleList.size()];
            for (int i = 0; i < titleList.size(); i++) {
                switch (titleList.get(i)) {
                    case "id":
                        resObj[i] = fi.getId();
                        break;
                    case "日期":
                        resObj[i] = DateUtils.format(fi.getFlightDate());
                        break;
                    case "航班号":
                        resObj[i] = fi.getFlightNo();
                        break;
                    case "航线":
                        resObj[i] = fi.getFlightLine();
                        break;
                    case "航段":
                        resObj[i] = fi.getFlightSegment();
                        break;
                    default:
                        SubsidyFlightParamJoin sfpj = subsidyFlightParamJoinDao
                                .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(),
                                        titleList.get(i));
                        if (sfpj == null || sfpj.getParamValue() == null) {
                            resObj[i] = 0.0;
                        } else {
                            resObj[i] = Double.parseDouble(sfpj.getParamValue());
                        }
                        break;
                }
            }
            resList.add(resObj);
        }
        return resList;
    }

    @Override
    public boolean savePassengerTicket(SubsidySavePassengerTicketForm form, LoginUserDetails user) {
        String flightSegment = form.getFlightSegment();
        String flightLine = form.getFlightLine();
        String airportCode = form.getAirportCode();

        if (!flightLine.contains(flightSegment)) {
            throw createGenericException(BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_LINE_ERROR);
        }
        // 根据航班信息获取航班
        List<FlightInfo> fiList = flightInfoDao.listFlightInfoByCondition2(form.getFlightNo(), form.getFlightDate(), flightSegment, airportCode);
        // 如果航班为空则，提醒用户去新建航班
        if (fiList.isEmpty()) {
            throw createGenericException(BusinessMessageEnum.SUBSIDY_FLIGHT_NOT_FOUND);
        }

        FlightInfo fi = fiList.get(0);
        // 判断客票、燃油表航班是否已存在
        List<SubsidyFlightParamJoin> sfpjList = getSubsidyFlightParamJoins(fi);

        Date now = new Date();
        for (SubsidyFlightParamJoin sfpj : sfpjList) {
            // 不存在则新建
            if (sfpj.getId() == null) {
                String paramName = sfpj.getParamName();
                SubsidyParam sp = subsidyParamDao.getSubsidyParamByName(paramName, airportCode);
                String paramValue = null;
                boolean isParamValueValid = false;
                if ("客票收入".equals(paramName)) {
                    isParamValueValid = isDouble(form.getPassengerTicket());
                    paramValue = form.getPassengerTicket();
                } else if (OIL_EXPENSE.equals(paramName)) {
                    isParamValueValid = isValid(form);
                    paramValue = getValue(form);
                } else if ("免票张数".equals(paramName)) {
                    isParamValueValid = isParamValueValid(form);
                    paramValue = getParamValue(form);
                }

                if (isParamValueValid) {
                    throw createGenericException(paramName);
                }

                sfpj.setParamValue(paramValue);
                sfpj.setFlightId(fi.getId());
                sfpj.setParamBelong(sp.getParamBelong());
                sfpj.setNumType(sp.getNumType());
                sfpj.setParamId(sp.getId());
                sfpj.setParamName(sp.getParamName());
                sfpj.setParamType(sp.getParamType());
                sfpj.setUnit(sp.getUnit());
                sfpj.setAirportCode(airportCode);
                sfpj.setCreateBy(user.getUsername());
                sfpj.setCreateTime(now);
                sfpj.setModifiedBy(user.getUsername());
                sfpj.setModifiedTime(now);
            } else {
                throw createGenericException(BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_EXIST_ERROR);
            }
        }

        subsidyFlightParamJoinDao.saveAll(sfpjList);
        return true;
    }

    private static String getValue(SubsidySavePassengerTicketForm form) {
        return Strings.isBlank(form.getFuelSurcharge()) ? "0.0" : form.getFuelSurcharge();
    }

    private boolean isValid(SubsidySavePassengerTicketForm form) {
        return !Strings.isBlank(form.getFuelSurcharge()) && isDouble(form.getFuelSurcharge());
    }

    private static String getParamValue(SubsidySavePassengerTicketForm form) {
        return Strings.isBlank(form.getFreeTickets()) ? "0" : form.getFreeTickets();
    }

    private boolean isParamValueValid(SubsidySavePassengerTicketForm form) {
        return !Strings.isBlank(form.getFreeTickets()) && isPositiveInteger(form.getFreeTickets());
    }

    private GenericException createGenericException(BusinessMessageEnum messageEnum) {
        return new GenericException(messageEnum.getCode(), messageEnum.getMsg());
    }

    private GenericException createGenericException(String paramName) {
        return new GenericException(BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getCode(), paramName + BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getMsg());
    }

    @Override
    public boolean updatePassengerTicket(SubsidyUpdatePassengerTicketForm form, LoginUserDetails user) {
        FlightInfo fi = getFlightInfo(flightInfoDao.getFlightInfoById(form.getFlightId()), BusinessMessageEnum.SUBSIDY_FLIGHT_NOT_FOUND, BusinessMessageEnum.SUBSIDY_FLIGHT_NOT_FOUND.getMsg());
        // 判断客票、燃油表航班是否已存在
        SubsidyFlightParamJoin ticket = subsidyFlightParamJoinDao.getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "客票收入");
        SubsidyFlightParamJoin fuelSurcharge = subsidyFlightParamJoinDao.getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), OIL_EXPENSE);
        SubsidyFlightParamJoin freeTicket = subsidyFlightParamJoinDao.getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "免票张数");
        List<SubsidyFlightParamJoin> sfpjList = Arrays.asList(ticket, fuelSurcharge, freeTicket);
        for (SubsidyFlightParamJoin sfpj : sfpjList) {
            if (sfpj != null) {
                handleSfpj(form, user, sfpj);
            } else {
                // 不存在则抛错提示不存在
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_NOT_EXIST_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_NOT_EXIST_ERROR.getMsg());
            }
        }
        subsidyFlightParamJoinDao.saveAll(sfpjList);
        return true;
    }

    private void handleSfpj(SubsidyUpdatePassengerTicketForm form, LoginUserDetails user, SubsidyFlightParamJoin sfpj) {
        if ("客票收入".equals(sfpj.getParamName())) {
            if (isDouble(form.getPassengerTicket())) {
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getCode(),
                        "客票收入" + BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR
                                .getMsg());
            }
            sfpj.setParamValue("" + Double.parseDouble(form.getPassengerTicket()));
        } else if (OIL_EXPENSE.equals(sfpj.getParamName())) {
            if ((!Strings.isBlank(form.getFuelSurcharge())) && isDouble(form.getFuelSurcharge())) {
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR.getCode(),
                        OIL_EXPENSE + BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_DOUBLE_ERROR
                                .getMsg());
            }
            sfpj.setParamValue(Strings.isBlank(form.getFuelSurcharge()) ? "0.0" : form.getFuelSurcharge());
        } else if ("免票张数".equals(sfpj.getParamName())) {
            if ((!Strings.isBlank(form.getFreeTickets())) && isPositiveInteger(form.getFreeTickets())) {
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR
                                .getCode(),
                        "免票张数" + BusinessMessageEnum.SUBSIDY_FORMULA_PARAM_POSITIVE_INTEGER_ERROR
                                .getMsg());
            }
            sfpj.setParamValue(Strings.isBlank(form.getFreeTickets()) ? "0" : form.getFreeTickets());
        }

        sfpj.setModifiedBy(user.getUsername());
        sfpj.setModifiedTime(new Date());
    }

    @Override
    public boolean deletePassengerTicket(String flightIds, LoginUserDetails user) {
        if (Strings.isBlank(flightIds)) {
            throw new GenericException(BusinessMessageEnum.SUBSIDY_FLIGHT_NOT_FOUND.getCode(),
                    BusinessMessageEnum.SUBSIDY_FLIGHT_NOT_FOUND.getMsg());
        }
        String[] arrFlightid = flightIds.split(",");
        for (String flightId : arrFlightid) {
            FlightInfo fi = getFlightInfo(flightInfoDao.getFlightInfoById(flightId), BusinessMessageEnum.SUBSIDY_FLIGHT_NOT_FOUND, BusinessMessageEnum.SUBSIDY_FLIGHT_NOT_FOUND.getMsg());
            // 判断客票、燃油表航班是否已存在
            SubsidyFlightParamJoin ticket = subsidyFlightParamJoinDao
                    .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "客票收入");
            SubsidyFlightParamJoin fuelSurcharge = subsidyFlightParamJoinDao
                    .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), OIL_EXPENSE);
            SubsidyFlightParamJoin freeTicket = subsidyFlightParamJoinDao
                    .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "免票张数");
            List<SubsidyFlightParamJoin> sfpjList = Arrays.asList(ticket, fuelSurcharge, freeTicket);
            List<SubsidyFlightParamJoin> deleteList = new ArrayList<>();

            for (SubsidyFlightParamJoin sfpj : sfpjList) {
                if (sfpj != null) {

                    sfpj.setInvalid("0");
                    sfpj.setModifiedBy(user.getUsername());
                    sfpj.setModifiedTime(new Date());
                    deleteList.add(sfpj);
                }
            }
            if (deleteList.isEmpty()) {
                // 不存在则抛错提示不存在
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_NOT_EXIST_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_NOT_EXIST_ERROR.getMsg());
            }
            subsidyFlightParamJoinDao.saveAll(deleteList);
        }
        return true;
    }

    @Override
    public void exportPassengerTicketStencil(HttpServletResponse res, LoginUserDetails user) {
        XSSFWorkbook book = ExcelFlightUtils.exportPassengerTicketStencil();
        String fileName = "客票收入导入模版.xlsx";
        FileUtils.exportToExcel(book, fileName, res);
    }

    @Override
    @Transactional
    public boolean importPassengerTicket(MultipartFile file, String airportCode, LoginUserDetails user, HttpServletRequest request) {
        List<Object[]> excelList = FileUtils.importExcel(file);
        Map<String, Map<String, Double>> flightPassengerTicketMap = new HashMap<>();
        for (Object[] objs : excelList) {
            if (objs.length < 5) {
                continue;
            }
            String flightKey = getFlightKey(airportCode, objs);
            Map<String, Double> valueMap = getValueMap(objs, flightPassengerTicketMap, flightKey);
            flightPassengerTicketMap.put(flightKey, valueMap);
        }

        for (Map.Entry<String, Map<String, Double>> entry : flightPassengerTicketMap.entrySet()) {
            String flightKey = entry.getKey();
            String[] flightInfo = flightKey.split("_");
            Date flightDate = DateUtils.parseDate(flightInfo[1]);
            String flightNo = flightInfo[0] + flightInfo[2];
            String fromAirportCode = flightInfo[3];
            String landFlag = getLandFlag(airportCode, fromAirportCode);
            Map<String, Double> valueMap = entry.getValue();
            //
            String key = flightKey + "航班" + BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_FLIGHT_ERROR.getMsg();
            FlightInfo flightInfoByCondition = flightInfoDao.getFlightInfoByCondition(flightNo, DateUtils.format(flightDate), landFlag, airportCode);
            FlightInfo fi = getFlightInfo(flightInfoByCondition, BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_FLIGHT_ERROR, key);
            //
            List<SubsidyFlightParamJoin> sfpjList = getSubsidyFlightParamJoins(fi);

            importPassengerTicketBiz(airportCode, user, sfpjList, fi, valueMap);
            subsidyFlightParamJoinDao.saveAll(sfpjList);
        }
        return true;
    }

    private void importPassengerTicketBiz(String airportCode, LoginUserDetails user, List<SubsidyFlightParamJoin> sfpjList, FlightInfo fi, Map<String, Double> valueMap) {
        for (SubsidyFlightParamJoin sfpj : sfpjList) {
            if (sfpj != null) {
                SubsidyParam sp = subsidyParamDao.getSubsidyParamByName(sfpj.getParamName(), fi.getAirportCode());
                if ("客票收入".equals(sfpj.getParamName())) {
                    sfpj.setParamValue(String.valueOf(valueMap.getOrDefault("客票收入", 0.0)));
                } else if (OIL_EXPENSE.equals(sfpj.getParamName())) {
                    sfpj.setParamValue(String.valueOf(valueMap.getOrDefault(OIL_EXPENSE, 0.0)));
                } else if ("免票张数".equals(sfpj.getParamName())) {
                    sfpj.setParamValue(String.valueOf(valueMap.getOrDefault("免票张数", 0.0)));
                }

                sfpj.setFlightId(fi.getId());
                sfpj.setParamBelong(sp.getParamBelong());
                sfpj.setNumType(sp.getNumType());
                sfpj.setParamId(sp.getId());
                sfpj.setParamName(sp.getParamName());
                sfpj.setParamType(sp.getParamType());
                sfpj.setUnit(sp.getUnit());
                sfpj.setAirportCode(airportCode);

                if (sfpj.getCreateTime() == null) {
                    sfpj.setCreateBy(user.getUsername());
                    sfpj.setCreateTime(new Date());
                }
                sfpj.setModifiedBy(user.getUsername());
                sfpj.setModifiedTime(new Date());
            } else {
                // 不存在则抛错提示不存在
                throw new GenericException(
                        BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_NOT_EXIST_ERROR.getCode(),
                        BusinessMessageEnum.SUBSIDY_PASSENGER_TICKET_NOT_EXIST_ERROR.getMsg());
            }
        }
    }

    private static @NotNull String getLandFlag(String airportCode, String fromAirportCode) {
        String landFlag;
        if (airportCode.equals(fromAirportCode)) {
            landFlag = "D";
        } else {
            landFlag = "A";
        }
        return landFlag;
    }

    private @NotNull FlightInfo getFlightInfo(FlightInfo flightInfoDao, BusinessMessageEnum subsidyImportPassengerTicketFlightError, String flightKey) {
        if (flightInfoDao == null) {
            // 未查到航班信息
            // 航班不存在
            throw new GenericException(
                    subsidyImportPassengerTicketFlightError.getCode(),
                    flightKey);
        }
        return flightInfoDao;
    }

    private @NotNull List<SubsidyFlightParamJoin> getSubsidyFlightParamJoins(FlightInfo fi) {
        SubsidyFlightParamJoin ticket = getFlightParamJoin(fi);
        SubsidyFlightParamJoin fuelSurcharge = getFuelSurcharge(fi);
        SubsidyFlightParamJoin freeTicket = getFreeTicket(fi);
        return Arrays.asList(ticket, fuelSurcharge, freeTicket);
    }

    private @NotNull SubsidyFlightParamJoin getFlightParamJoin(FlightInfo fi) {
        SubsidyFlightParamJoin ticket = subsidyFlightParamJoinDao.getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "客票收入");
        if (ticket == null) {
            ticket = new SubsidyFlightParamJoin();
            ticket.setParamName("客票收入");
        }
        return ticket;
    }

    private @NotNull SubsidyFlightParamJoin getFuelSurcharge(FlightInfo fi) {
        SubsidyFlightParamJoin fuelSurcharge = subsidyFlightParamJoinDao.getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), OIL_EXPENSE);
        if (fuelSurcharge == null) {
            fuelSurcharge = new SubsidyFlightParamJoin();
            fuelSurcharge.setParamName(OIL_EXPENSE);
        }
        return fuelSurcharge;
    }

    private @NotNull SubsidyFlightParamJoin getFreeTicket(FlightInfo fi) {
        SubsidyFlightParamJoin freeTicket = subsidyFlightParamJoinDao.getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "免票张数");
        if (freeTicket == null) {
            freeTicket = new SubsidyFlightParamJoin();
            freeTicket.setParamName("免票张数");
        }
        return freeTicket;
    }

    private @NotNull Map<String, Double> getValueMap(Object[] objs, Map<String, Map<String, Double>> flightPassengerTicketMap, String flightKey) {
        double passengerTicketValue = Double.parseDouble(objs[5] == null ? "0" : "" + objs[5]);
        Map<String, Double> valueMap = flightPassengerTicketMap.getOrDefault(flightKey, new HashMap<>());
        if (passengerTicketValue == 0) {
            Double value = valueMap.getOrDefault("免票张数", 0.0);
            value += 1;
            valueMap.put("免票张数", value);
        } else {
            Double value = valueMap.getOrDefault("客票收入", 0.0);
            value += passengerTicketValue;
            valueMap.put("客票收入", value);
        }
        // 燃油附加费
        if (objs.length > 6 && (!Strings.isBlank("" + objs[6])) && isDouble("" + objs[6])) {
            throw new GenericException(BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_DATA_ERROR.getCode(),
                    flightKey + "航班" + BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_DATA_ERROR.getMsg());
        }
        double objValue = Double.parseDouble(objs[6] == null ? "0.0" : "" + objs[6]);
        double fuelSurchargeValue = objs.length < 7 ? 0 : objValue;
        Double fuelValue = valueMap.getOrDefault(OIL_EXPENSE, 0.0);
        fuelValue += fuelSurchargeValue;
        valueMap.put(OIL_EXPENSE, fuelValue);
        return valueMap;
    }

    private @NotNull String getFlightKey(String airportCode, Object[] objs) {
        String airline = (String) objs[0];
        String flightDate = (String) objs[1];
        String flightNo = "" + objs[2];
        String fromAirportCode = (String) objs[3];
        String toAirportCode = (String) objs[4];

        String flightKey = airline + "_" + flightDate + "_" + flightNo + "_" + fromAirportCode + "_" + toAirportCode;
        if ((!airportCode.equals(fromAirportCode)) && !airportCode.equals(toAirportCode)) {
            // 起降标识不正确
            throw new GenericException(BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_FLIGHT_FLAG_ERROR.getCode(),
                    flightKey + "航班" + BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_FLIGHT_FLAG_ERROR.getMsg());
        }
        // 客票收入
        if (isDouble("" + objs[5])) {
            throw new GenericException(BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_DATA_ERROR.getCode(),
                    flightKey + "航班" + BusinessMessageEnum.SUBSIDY_IMPORT_PASSENGER_TICKET_DATA_ERROR.getMsg());
        }
        return flightKey;
    }

    @Override
    public void exportPassengerTicket(HttpServletResponse res, SubsidyFlightForm form) {
        String airportCode = form.getAirportCode();
        List<FlightInfo> fiList = flightInfoDao.getFlightInfoByLineTime(form.getStartDate(),
                form.getEndDate(), form.getAirline(), form.getFlightLine(), airportCode);
        Object[] objs = getPassengerRes(fiList, airportCode);

        List<String> title = (List<String>) objs[0];
        List<Object[]> dataList = (List<Object[]>) objs[1];

        Object[] count = new Object[title.size()];
        count[1] = "总计";
        if (!dataList.isEmpty()) {
            for (Object[] obj : dataList) {
                for (int i = 5; i < title.size(); i++) {
                    count[i] = (double) (count[i] == null ? 0.0 : count[i])
                            + Double.parseDouble("" + obj[i]);
                }
            }
        }
        String sheetName = "客票燃油数据";
        XSSFWorkbook book = ExcelFlightUtils.exportPassenger(title, dataList, count, sheetName);
        String fileName = "客票燃油数据.xlsx";
        FileUtils.exportToExcel(book, fileName, res);
    }

    @Override
    public void exportFlightLineDetailData(SubsidyFlightForm form, HttpServletResponse res) {
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        String airline = form.getAirline();
        String flightLine = form.getFlightLine();
        String airportCode = form.getAirportCode();
        String airlineShortName = "";
        //如果航司包含中文
        if (ObjectUtil.checkStringContainChinese(airline)) {
            airlineShortName = airline;
            airline = "";
        }
        StringBuilder reFlightLine = new StringBuilder();
        handleFlArr(flightLine, reFlightLine);
        String flightLineCn = "";
        String reFlightLineCn = "";
        if (ObjectUtil.checkStringContainChinese(flightLine)) {
            flightLineCn = flightLine;
            flightLine = "";
            reFlightLineCn = reFlightLine.toString();
            reFlightLine.setLength(0);
        }
        List<Object[]> flightLineList = flightLineInfoDao.getFlightLineInfoByStartEndDate(startDate, endDate, airline, airlineShortName, flightLine, reFlightLine.toString(), flightLineCn, reFlightLineCn);
        // 航班非手动填入参数
        List<SubsidyParam> flightParamList = new ArrayList<>();
        // 航班手动填入参数
        List<SubsidyParam> flightManualParamList = new ArrayList<>();
        // 根据航线查询机场下的航班参数
        List<SubsidyParam> paramList = subsidyParamDao.getFlightSubsidyParamByAirportCode(airportCode);
        // 返回结果集合
        List<Object[]> resList = new ArrayList<>(paramList.size());
        //
        TitleIndexDto titleIndexDto = getTitleIndexDto(paramList, flightManualParamList, flightParamList);
        // 合计数据
        Object[] count = new Object[titleIndexDto.titleList.size()];

        for (Object[] formulaData : flightLineList) {
            SubsidyFormula sf = subsidyFormulaDao.getFormulaById((String) formulaData[0]);
            if (sf == null) {
                continue;
            }
            Date formulaStartDate = startDate;
            Date formulaEndDate = endDate;
            if (sf.getStartDate().after(startDate) || sf.getEndDate().before(endDate)) {
                formulaStartDate = DateUtils.laterTime(formulaStartDate, sf.getStartDate());
                formulaEndDate = DateUtils.earlyTime(formulaEndDate, sf.getEndDate());
            }
            FlightLineInfo fl = getFlightLineInfo(sf);
            String reFlighrLine = getReFlightLine(fl);
            List<FlightInfo> fiList = flightInfoDao.getFlightInfoByTime(formulaStartDate, formulaEndDate, fl.getAirlineCode(), fl.getFlightLine(), reFlighrLine, airportCode);
            Map<String, Map<String, String>> spMap = getStringMap(formulaStartDate, formulaEndDate, fl, reFlighrLine, airportCode);
            for (FlightInfo fi : fiList) {
                Object[] fiObj = getFiObj(fi, titleIndexDto.resSize, flightParamList, titleIndexDto.indexMap, count, spMap, flightManualParamList);
                resList.add(fiObj);
            }
        }
        handleFixed(titleIndexDto.titleList.size(), titleIndexDto.titleList, count, titleIndexDto.indexMap);
        count[1] = "合计";
        String sheetName = "执飞明细数据";
        XSSFWorkbook book = ExcelFlightUtils.exportPassenger(titleIndexDto.titleList, resList, count, sheetName);
        String fileName = "执飞明细数据.xlsx";
        FileUtils.exportToExcel(book, fileName, res);
    }

    private static @NotNull TitleIndexDto getTitleIndexDto(List<SubsidyParam> paramList, List<SubsidyParam> flightManualParamList, List<SubsidyParam> flightParamList) {
        List<String> titleList = new ArrayList<>(paramList.size() + 5);
        titleList.add("id");
        titleList.add("日期");
        titleList.add("航班号");
        titleList.add("航段");
        titleList.add("机型");
        // 固定参数个数
        paramList.forEach(param -> {
            // 将该机场下的航班参数，分别填入手动填入参数列表和非手动填入参数列表
            if ("1".equals(param.getParamType())) {
                flightManualParamList.add(param);
            } else if ("4".equals(param.getParamType()) && !"旅客均值".equals(param.getParamName())) {
                flightParamList.add(param);
            }
        });
        // 根据航班参数，获取返回对象大小
        int resSize = titleList.size() + flightParamList.size() + flightManualParamList.size();
        // 下标表map
        Map<String, Integer> indexMap = new HashMap<>();
        int index = titleList.size();
        for (SubsidyParam sp : flightParamList) {
            indexMap.put(sp.getParamName(), index++);
            titleList.add(sp.getParamName());
        }
        for (SubsidyParam sp : flightManualParamList) {
            indexMap.put(sp.getParamName(), index++);
            titleList.add(sp.getParamName());
        }
        return new TitleIndexDto(titleList, resSize, indexMap);
    }

    private static class TitleIndexDto {
        public final List<String> titleList;
        public final int resSize;
        public final Map<String, Integer> indexMap;

        public TitleIndexDto(List<String> titleList, int resSize, Map<String, Integer> indexMap) {
            this.titleList = titleList;
            this.resSize = resSize;
            this.indexMap = indexMap;
        }
    }

    private Object[] getFiObj(FlightInfo fi, int resSize, List<SubsidyParam> flightParamList, Map<String, Integer> indexMap, Object[] count, Map<String, Map<String, String>> spMap, List<SubsidyParam> flightManualParamList) {
        Object[] fiObj = new Object[resSize];
        fiObj[0] = fi.getId();
        fiObj[1] = DateUtils.format(fi.getFlightDate());
        fiObj[2] = fi.getFlightNo();
        fiObj[3] = fi.getFlightSegment();
        String regNo = fi.getRegNo();
        String flightModel = getFlightModel(fi, regNo);
        fiObj[4] = flightModel;
        // 遍历解析非手动录入参数
        flightParamList.forEach(flightParam -> {
            if ("班次".equals(flightParam.getParamName()) && indexMap.containsKey("班次")) {
                int bcIndex = indexMap.get("班次");
                fiObj[bcIndex] = 1;
                count[bcIndex] = getAnInt(fiObj, bcIndex, count);
            } else if ("旅客数".equals(flightParam.getParamName()) && indexMap.containsKey("旅客数")) {
                handleLkIndex(fi, indexMap, fiObj, count);
            }
        });
        Map<String, String> fiParamMap = spMap.getOrDefault(fi.getId(), new HashMap<>());
        flightManualParamList.forEach(flightManualParam -> {
            String value = fiParamMap.get(flightManualParam.getParamName());
            int manualIndex = indexMap.get(flightManualParam.getParamName());
            if (value == null || value.isEmpty()) {
                value = "0";
            }
            if ("3".equals(flightManualParam.getNumType())) {
                fiObj[manualIndex] = String.format("%.2f", Double.parseDouble(value));
                count[manualIndex] = getCount(fiObj, manualIndex, count);
            } else {
                fiObj[manualIndex] = String.format("%.0f", Double.parseDouble(value));
                count[manualIndex] = getAnInt(fiObj, manualIndex, count);
            }

            if (fiObj[manualIndex] == null) {
                fiObj[manualIndex] = "0";
            }
        });
        return fiObj;
    }

    private static double getCount(Object[] fiObj, int manualIndex, Object[] count) {
        return Double.parseDouble("" + fiObj[manualIndex]) + (double) (count[manualIndex] == null ? 0.0 : count[manualIndex]);
    }

    private static int getAnInt(Object[] fiObj, int bcIndex, Object[] count) {
        return Integer.parseInt("" + fiObj[bcIndex]) + (int) (count[bcIndex] == null ? 0 : count[bcIndex]);
    }

    private Map<String, Map<String, String>> getStringMap(Date formulaStartDate, Date formulaEndDate, FlightLineInfo fl, String reFlightLine, String airportCode) {
        // 根据航班id查询所有参数关联表信息,并将数据装进map中
        List<SubsidyFlightParamJoin> spList = subsidyFlightParamJoinDao.getSubsidyParamByDate(
                formulaStartDate, formulaEndDate, fl.getAirlineCode(), fl.getFlightLine(),
                reFlightLine, airportCode);
        Map<String, Map<String, String>> spMap = new HashMap<>();
        spList.forEach(sp -> {
            Map<String, String> paramMap = spMap.getOrDefault(sp.getFlightId(), new HashMap<>());
            paramMap.put(sp.getParamName(), sp.getParamValue());
            spMap.put(sp.getFlightId(), paramMap);
        });
        return spMap;
    }

    private void handleLkIndex(FlightInfo fi, Map<String, Integer> indexMap, Object[] fiObj, Object[] count) {
        int lkIndex = indexMap.get("旅客数");
        int transitAdultNumber = fi.getTransitAdultNumber() == null ? 0 : fi.getTransitAdultNumber();
        int transitChildNumber = fi.getTransitChildNumber() == null ? 0 : fi.getTransitChildNumber();
        int transitInfantNumber = fi.getTransitInfantNumber() == null ? 0 : fi.getTransitInfantNumber();
        fiObj[lkIndex] = fi.getPsgNumber() == null ? 0 : fi.getPsgNumber()
                + (transitAdultNumber)
                + (transitChildNumber)
                + (transitInfantNumber);
        count[lkIndex] = getAnInt(fiObj, lkIndex, count);
    }

    private void handleFixed(int fixedParamSize, List<String> titleList, Object[] count, Map<String, Integer> indexMap) {
        for (int i = fixedParamSize; i < titleList.size(); i++) {
            if ("轮挡时间".equals(titleList.get(i))) {
                // 计算轮挡时间平均值
                count[i] = String.format("%.2f", Double.parseDouble("" + count[i])
                        / Double.parseDouble("" + count[indexMap.get("班次")]));
                break;
            }

        }
    }

    private void handleFlArr(String flightLine, StringBuilder reFlightLine) {
        if (CharSequenceUtil.isNotBlank(flightLine)) {
            // 获取相反航线
            String[] flArr = flightLine.split("-");
            for (int i = 0; i < flArr.length; i++) {
                reFlightLine.append(flArr[flArr.length - 1 - i]);
                if (i != flArr.length - 1) {
                    reFlightLine.append("-");
                }
            }
        }
    }


    // 判断正整数（int）
    private boolean isPositiveInteger(String str) {
        if (null == str || str.isEmpty()) {
            return true;
        }
        Pattern pattern = Pattern.compile("^[+]?[1-9]\\d*$");
        return !pattern.matcher(str).matches();
    }

    // 判断整数（int）
    private boolean isInteger(String str) {
        if (null == str || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-+]?\\d*$");
        return pattern.matcher(str).matches();
    }


    // 判断浮点数（double和float）
    private boolean isDouble(String str) {
        if (null == str || str.isEmpty()) {
            return true;
        }
        if (isInteger(str)) {
            return false;
        }
        // 之前这里正则表达式错误，现更正
        Pattern pattern = Pattern.compile("^[-+]?\\d*[.]\\d+$");
        return !pattern.matcher(str).matches();
    }
}
