package com.swcares.aiot.service;

import com.swcares.aiot.core.vo.SignBusDataSettlementVO;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.NewMqSignService
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/28 14:20
 * @version v1.0
 */
public interface NewMqSignService {

    void saveServiceRecord(List<SignBusDataSettlementVO> vo);

    void saveServiceRecordByCondition(List<SignBusDataSettlementVO> voList, String syncflightNos,
                                      Long syncItemId);
}
