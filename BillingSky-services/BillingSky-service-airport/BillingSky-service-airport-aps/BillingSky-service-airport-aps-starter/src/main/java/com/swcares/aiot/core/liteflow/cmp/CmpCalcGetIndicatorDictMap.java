package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.client.remote.IIndicatorBizRemoteClient;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.baseframe.common.base.BaseResult;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.CmpGetIndicatorDictMap
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/12 13:21
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcGetIndicatorDictMap", name = "组件-对账通机场端-计算模块-获取指标项字典")
public class CmpCalcGetIndicatorDictMap extends NodeComponent {

    @Resource
    private IIndicatorBizRemoteClient iIndicatorBizRemoteClient;

    @Override
    public void process()  {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        Map<String, IndAllIndicatorRetrVo> indicatorDictMap=new HashMap<>();
        BaseResult<List<IndAllIndicatorRetrVo>> indicatorDicRes = iIndicatorBizRemoteClient.list();
        if(indicatorDicRes!=null&&indicatorDicRes.getCode()==200&& CollUtil.isNotEmpty(indicatorDicRes.getData())){
            List<IndAllIndicatorRetrVo> indicatorDicList = indicatorDicRes.getData();
            for(IndAllIndicatorRetrVo indAllIndicatorRetrVo:indicatorDicList){
                indicatorDictMap.put(indAllIndicatorRetrVo.getCode(),indAllIndicatorRetrVo);
            }
        }
        ctxCalc.setIndicatorDictMap(indicatorDictMap);
    }
}
