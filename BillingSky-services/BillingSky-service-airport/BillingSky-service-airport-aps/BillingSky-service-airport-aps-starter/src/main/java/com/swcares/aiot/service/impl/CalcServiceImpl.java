package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.TenantConvertUtil;
import com.swcares.aiot.core.entity.CalcContract;
import com.swcares.aiot.core.enums.SettlementStatusEnum;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.dto.ReCalcErrorDto;
import com.swcares.aiot.core.model.entity.SettlementStatus;
import com.swcares.aiot.core.model.vo.ExpensesRetrVo;
import com.swcares.aiot.core.model.vo.ForExpensesRetrVo;
import com.swcares.aiot.mapper.ActuarialBizMapper;
import com.swcares.aiot.service.CalcService;
import com.swcares.aiot.service.IFormulaBizService;
import com.swcares.aiot.service.SettlementStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.CalcServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/25 13:19
 * @version v1.0
 */
@Service
@Slf4j
public class CalcServiceImpl implements CalcService {

    public static final String AIRPORT_KEY = "BillingSky:airport:aps:lock:new:calc:";
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private SettlementStatusService settlementStatusService;
    @Resource
    private FlowExecutor flowExecutor;
    @Resource
    private IFormulaBizService iFormulaBizService;
    @Resource
    private ActuarialBizMapper actuarialBizMapper;

    @Override
    @Async
    public void execute(ReCalcDto dto, LoginUserDetails user, Long tenantId) {
        TenantHolder.setTenant(tenantId);
        //机场三字码
        String airportCode = dto.getAirportCode();
        //生成结算记录
        SettlementStatus settlementStatus = settlementStatusService.newSettlementStatus(dto, user);
        //获取分布式锁
        RLock lock = redissonClient.getLock(AIRPORT_KEY + airportCode);
        //错误文件信息
        Map<String,ReCalcErrorDto> errorDtoMap = new HashMap<>();
        // 标记锁是否获取成功
        boolean acquired = false;
        try {
            //获取锁等待60分钟，持有锁30分钟
            acquired = lock.tryLock(3600, 1800, TimeUnit.SECONDS);
            if (acquired) {
                // 结算流程
                // 记录开始时间
                LocalDateTime start = LocalDateTime.now();
                log.info("开始计算引擎进行结算：{}", start);
                // 封装分页组件上下文
                CtxCalc ctxCalc = new CtxCalc().setDto(dto).setErrorDtoMap(errorDtoMap).setUser(user);
                // 执行流程
                LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("apsCalcBill", null, ctxCalc);
                //抛出liteflow中的异常
                boolean success = liteflowResponse.isSuccess();
                if (!success) {
                    Exception cause = liteflowResponse.getCause();
                    log.error("对账通机场端计算账单异常{0}", cause);
                    throw cause;
                }
                //修改结算记录状态为完成
                settlementStatus.setStatus(SettlementStatusEnum.COMPLETED.getStatus());
                // 记录结束时间
                LocalDateTime end = LocalDateTime.now();
                // 计算时间差
                Duration duration = Duration.between(start, end);
                long minutes = duration.toMinutes();        // 总分钟数
                long seconds = duration.minusMinutes(minutes).getSeconds(); // 剩余秒数
                // 结算流程
                log.info("计算引擎结算结束，方法执行用时:{}分{}秒", minutes, seconds);
            } else {
                log.error("结算账单获取锁失败，airportCode: {}", airportCode);
                log.info("计算引擎结算异常终止：{}", LocalDateTime.now());
            }
        } catch (InterruptedException e) {
            // 恢复线程中断状态
            Thread.currentThread().interrupt();
            log.error("结算账单锁等待被中断，airportCode: {}", airportCode, e);
        } catch (Exception e) {
            log.error("结算账单异常，airportCode: {}", airportCode, e);
        } finally {
            // 仅释放已获取的锁
            if (acquired) {
                lock.unlock();
            }
            try {
                List<ReCalcErrorDto> errorDtoList=new ArrayList<>(errorDtoMap.values());
                settlementStatusService.uploadErrorFile(settlementStatus, "重新结算", errorDtoList);
            } catch (Exception e) {
                log.error("上传错误文件异常");
            }
            //如果结算记录状态不是完成，则修改状态为失败
            if (!SettlementStatusEnum.COMPLETED.getStatus().equals(settlementStatus.getStatus())) {
                settlementStatus.setStatus(SettlementStatusEnum.FAIL.getStatus());
                log.error("结算账单记录状态失败，settlementStatus: {}", settlementStatus);
            }
            settlementStatusService.updateById(settlementStatus);
            TenantHolder.clear();
        }
    }

    @Override
    public List<ForExpensesRetrVo> retrForExpenses(ExpensesRetrVo dto) {
        if (CharSequenceUtil.isBlank(dto.getCustomerIataA())) {
            String tenantCode = TenantConvertUtil.getTenantCode(TenantHolder.getTenant());
            dto.setCustomerIataA(tenantCode);
        }
        log.info("查询虚拟合同:{}", dto);
        // 获取虚拟合同
        List<CalcContract> contracts = actuarialBizMapper.getContract(dto);
        return contracts.stream()
                .map(contract -> {
                    String contractId = contract.getId();
                    log.info("虚拟合同ID:{}", contractId);
                    return iFormulaBizService.retrForExpenses(contractId);
                }).flatMap(Collection::stream)
                .collect(Collectors.toList());


    }
}
