package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.model.entity.OperateRecord;

public interface OperateRecordService {
    /**
     * Title: addOperateRecord<br>
     * Author: lilong<br>
     * Description: (新增操作记录)<br>
     * Date:  9:55 <br>
     * @param operateRecord
     * return: com.swcares.exception.ResultBuilder
     */
    ResultBuilder addOperateRecord(OperateRecord operateRecord);
    /**
     * Title: listOperateRecordByOperateTypeAndOperateId<br>
     * Author: lilong<br>
     * Description: (根据操作对象和操作对象id查询操作记录)<br>
     * Date:  13:36 <br>
     * @param operateObject
     * @param operateId
     * return: com.swcares.exception.ResultBuilder
     */
     ResultBuilder listOperateRecordByOperateObjectAndOperateId(String operateObject, String operateId);
}
