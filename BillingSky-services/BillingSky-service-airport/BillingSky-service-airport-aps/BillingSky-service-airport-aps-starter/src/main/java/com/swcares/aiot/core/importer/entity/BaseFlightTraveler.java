package com.swcares.aiot.core.importer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.base.flight.api.entity.BaseFlightTraveler <br>
 * Description：航班旅客数据表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-07-19 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BaseFlightTraveler", description="航班旅客数据表")
public class BaseFlightTraveler extends BpBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "航班日期")
    private LocalDateTime flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "进出港标识;1：进港 0：出港")
    private String landFlag;

    @ApiModelProperty(value = "航段")
    private String flightSegment;

    @ApiModelProperty(value = "航班id")
    private Long baseFlightId;

    @ApiModelProperty(value = "商务舱人数")
    private Integer businessClassNum;

    @ApiModelProperty(value = "头等舱人数")
    private Integer firstClassNum;

    @ApiModelProperty(value = "经济舱人数")
    private Integer economyClassNum;

    @ApiModelProperty(value = "持外交护照旅客人数")
    private Integer diplomaticPassportTravelerNum;

    @ApiModelProperty(value = "持卡旅客人数")
    private Integer cardholdersTravelerNum;

    @ApiModelProperty(value = "持卡随行旅客人数")
    private Integer cardholdersAccompanyTravelerNum;

    @ApiModelProperty(value = "重要旅客人数")
    private Integer importantTravelerNum;

    @ApiModelProperty(value = "重要随行旅客人数")
    private Integer importantAccompanyTravelerNum;

    @ApiModelProperty(value = "本站成人数")
    private Integer adultNum;

    @ApiModelProperty(value = "本站儿童数")
    private Integer childNum;

    @ApiModelProperty(value = "本站婴儿数")
    private Integer infantNum;

    @ApiModelProperty(value = "过站成人数")
    private Integer transitAdultNum;

    @ApiModelProperty(value = "过站儿童数")
    private Integer transitChildNum;

    @ApiModelProperty(value = "过站婴儿数")
    private Integer transitInfantNum;

    @ApiModelProperty(value = "本站成人指令采集数据标识(1:一致,0:不一致)")
    private Boolean adultFlag;

    @ApiModelProperty(value = "本站儿童指令采集数据标识(1:一致,0:不一致)")
    private Boolean childFlag;

    @ApiModelProperty(value = "本站婴儿指令采集数据标识(1:一致,0:不一致)")
    private Boolean infantFlag;

    @ApiModelProperty(value = "过站成人指令采集数据标识(1:一致,0:不一致)")
    private Boolean transitAdultFlag;

    @ApiModelProperty(value = "过站儿童指令采集数据标识(1:一致,0:不一致)")
    private Boolean transitChildFlag;

    @ApiModelProperty(value = "过站婴儿指令采集数据标识(1:一致,0:不一致)")
    private Boolean transitInfantFlag;

    @ApiModelProperty(value = "客座率")
    private BigDecimal psgrPlf;

    @ApiModelProperty(value = "旅客吞吐量")
    private Integer travelerThroughput;

    @ApiModelProperty(value = "旅客总人数")
    private Integer travelerNum;

    @ApiModelProperty(value = "旅客总重量")
    private Integer psgrTotalKg;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;


}
