package com.swcares.aiot.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aiot.ComplexMessage;
import com.swcares.aiot.bell.BellTemplate;
import com.swcares.aiot.core.entity.TBusSign;
import com.swcares.aiot.core.model.entity.BusSign;
import com.swcares.aiot.core.model.entity.BusSignHistory;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.service.ITBusSignService;
import com.swcares.aiot.dao.BusSignDao;
import com.swcares.aiot.dao.BusSignHistoryDao;
import com.swcares.aiot.dao.BusSignServiceRecordDao;
import com.swcares.aiot.statemachine.StatemachineTemplate;
import com.swcares.aiot.statemachine.biz.events.EnumBillStatusChangeEvent;
import com.swcares.aiot.statemachine.biz.events.EnumRevocationStatusChangeEvent;
import com.swcares.aiot.synergy.annotation.DataType;
import com.swcares.aiot.synergy.service.IDataSyncService;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DataType("BUS_BILL")
public class BusBillCheckDataSyncService implements IDataSyncService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private BusSignDao busSignDao;

    @Resource
    private BusSignHistoryDao busSignHistoryDao;

    @Resource
    private BusSignServiceRecordDao busSignServiceRecordDao;

    @Resource
    private StatemachineTemplate<TBusSign> statemachineTemplate;

    @Resource
    private ITBusSignService itBusSignService;

    @Resource
    private BellTemplate bellTemplate;

    @Override
    public Long getTenantId(String tenantCode) {
        return ConfigUtil.getLong("signature_config", tenantCode.toUpperCase(), GlobalConstants.TENANT_DEFAULT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncData(ComplexMessage message) {
        BusSign dto = JSONObject.parseObject(JSONUtil.toJsonStr(message.getPayload().getData()), BusSign.class);
        log.info("收到账单反馈信息：{}", dto);
        if (dto == null) {
            return;
        }
        TBusSign bs = new TBusSign();
        BeanUtils.copyProperties(dto, bs);
        itBusSignService.saveOrUpdate(bs);
        String operation = "";
        switch (dto.getOperation()) {
//            case CHECK_BUS:
//                statemachineTemplate.updateAirportBillEvent(iTBusSignService, TBusSign::getSubmit, TBusSign::getId, dto.getId(), EnumAirportBillChangeEvent.AIRLINE_AUTOMATIC_RECONCILIATION_OK);
//                break;
            case CHECK_CONFIRM:
                operation = BillOperation.ACCEPT_FEEDBACK.getCode();
                statemachineTemplate.updateBillStatusEvent(itBusSignService, TBusSign::getSubmit, TBusSign::getId, dto.getId(), EnumBillStatusChangeEvent.AIRLINE_AUTOMATIC_RECONCILIATION_OK);
                break;
            case CHECK_DISPUTE:
                operation = BillOperation.ACCEPT_FEEDBACK.getCode();
                statemachineTemplate.updateBillStatusEvent(itBusSignService, TBusSign::getSubmit, TBusSign::getId, dto.getId(), EnumBillStatusChangeEvent.AIRLINE_CONTROVERSIAL);
                break;
            case AGREE_CANCEL:
                operation = BillOperation.AGREE_CANCEL.getCode();
                statemachineTemplate.updateBillStatusEvent(itBusSignService, TBusSign::getSubmit, TBusSign::getId, dto.getId(), EnumBillStatusChangeEvent.AIRLINE_AGREE_REVOKED);
                statemachineTemplate.updateRevocationStatusEvent(itBusSignService, TBusSign::getRevocation, TBusSign::getId, dto.getId(), EnumRevocationStatusChangeEvent.AIRLINE_AGREE_REVOCATION);
                break;
            case REJECT_CANCEL:
                operation = BillOperation.REJECT_CANCEL.getCode();
                statemachineTemplate.updateRevocationStatusEvent(itBusSignService, TBusSign::getRevocation, TBusSign::getId, dto.getId(), EnumRevocationStatusChangeEvent.AIRLINE_REJECT_REVOCATION);
                break;
        }

        BusSignHistory bsh = new BusSignHistory();
        bs = itBusSignService.getById(dto.getId());
        BeanUtils.copyProperties(bs, bsh);
        bsh.setFlightDate(Date.from(bs.getFlightDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
        bsh.setSignCreatedTime(Date.from(bs.getSignCreatedTime().atZone(ZoneId.systemDefault()).toInstant()));
        bsh.setId(null);
        bsh.setCreateTime(new Date());
        bsh.setCreateBy(dto.getSettleCode());
        bsh.setOperation(operation);
        try {
            List<Long> signIds = new ArrayList<>();
            signIds.add(dto.getSignId());
            bsh.setServiceRecordsString(objectMapper.writeValueAsString(busSignServiceRecordDao.getBusSignServiceRecordListBySignIds(signIds)));
        } catch (IOException e) {
            log.error("车辆签单历史保存服务项出错：{}", e.getMessage(), e);
        }
        busSignHistoryDao.save(bsh);
    }

}
