package com.swcares.aiot.core.mapstruct;

import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TFlightBill;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * ClassName：com.swcares.aiot.core.mapstruct.MsFlightBill
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/13 11:35
 * @version v1.0
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MsFlightBill {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "modifiedTime", ignore = true)
    @Mapping(target = "modifiedBy", ignore = true)
    TFlightBill infoToFlightBill(FlightInfoMb flightInfoMb);
}
