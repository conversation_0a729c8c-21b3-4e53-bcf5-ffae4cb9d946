package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.FormulaForm;
import com.swcares.aiot.core.form.UseFormulaForm;
import com.swcares.aiot.core.model.entity.FeeInfo;
import com.swcares.aiot.core.model.entity.FormulaInfo;
import com.swcares.aiot.core.model.entity.FunctionInfo;
import com.swcares.aiot.core.model.entity.RulesVariableRecord;
import com.swcares.aiot.core.model.vo.SafeguardTypeVo;
import com.swcares.baseframe.common.security.LoginUserDetails;

import java.util.List;

/*
 *
 * ClassName：FormulaService <br>
 * Description：公式的service层<br>
 * Copyright © 2020-6-8 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-6-8 15:05<br>
 * @version v1.0 <br>
 */
public interface FormulaService {
    /*
     * Title: saveFormula<br>
     * Author: 李龙<br>
     * Description: 新增公式<br>
     * Date:  15:11 <br>
     * @param formulaForm
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder<Object> saveFormula(FormulaForm formulaForm, LoginUserDetails user, String urlName);

    /*
     * Title: delFormulaById<br>
     * Author: 李龙<br>
     * Description: 根据公式id删除公式<br>
     * Date:  16:24 <br>
     * @param formulaId
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder<Object> delFormulaById(String formulaId, LoginUserDetails user, String urlName);

    /*
     * Title: getFormulaById<br>
     * Author: 李龙<br>
     * Description: 根据公式id查询公式<br>
     * Date:  16:39 <br>
     * @param formulaId
     * return: ResultBuilder
     */
    ResultBuilder<FormulaForm> getFormulaById(String formulaId);

    /*
     * Title: updateFormula<br>
     * Author: 李龙<br>
     * Description: 修改公式<br>
     * Date:  11:05 <br>
     * @param formulaForm
     * @param formulaId
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder<Object> updateFormula(FormulaForm formulaForm, LoginUserDetails user, String urlName);

    /*
     * Title: listFormulaByFeeId<br>
     * Author: 李龙<br>
     * Description: 根据费用id查询所属公式<br>
     * Date:  16:45 <br>
     * @param feeId
     * return: ResultBuilder
     */
    ResultBuilder<List<FormulaForm>> listFormulaByFeeId(String feeId);

    /*
     * Title: listVariableRulesByUnit<br>
     * Author: 李龙<br>
     * Description: 根据单位查询变量名<br>
     * Date:  14:37 <br>
     * @param variableUnit
     * return: ResultBuilder
     */
    ResultBuilder<List<RulesVariableRecord>> listVariableRulesByUnit(String variableUnit);

    /*
     * Title: useFormula<br>
     * Author: 李龙<br>
     * Description: (采用公式)<br>
     * Date:  14:47 <br>
     * @param useFormulaForm
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder<FormulaInfo> useFormula(UseFormulaForm useFormulaForm, LoginUserDetails user, String urlName);

    /*
     * Title: fastCopyFormula<br>
     * Author: 李龙<br>
     * Description: (快速复制航司下的所有公式)<br>
     * Date:  14:47 <br>
     * @param airlineId
     * @param useAirlineId
     * @param user
     * return: ResultBuilder
     */
    ResultBuilder<List<FeeInfo>> fastCopyFormula(String airlineId, String useAirlineId, LoginUserDetails user, String airportCode);

    /*
     * Title: findFormulaInfoByName<br>
     * Author: 李龙<br>
     * Description: 根据公式名称查询费用下的公式，用于判断公式名称的唯一性<br>
     * Date:  15:35 <br>
     * @param formulaName
     * @param feeId
     * return: ResultBuilder
     */
    ResultBuilder<String> findFormulaInfoByName(String formulaName, String feeId);

    /*
     * Title: listFunction<br>
     * Author: 李龙<br>
     * Description: 查询内置函数<br>
     * Date:  15:32 <br>
     * @param
     * return: ResultBuilder
     */
    ResultBuilder<List<FunctionInfo>> listFunction();

    /*
     * Title: cancelUseFormula<br>
     * Author: 李龙<br>
     * Description: (取消对公式的采用)<br>
     * Date:  15:06 <br>
     * @param formulaId
     * return: ResultBuilder
     */
    ResultBuilder<Object> cancelUseFormula(String formulaId, LoginUserDetails user, String urlName);

    /*
     * Title: getSumFormulaByAirline<br>
     * Author: 李龙<br>
     * Description: (根据航司id查询航司下的公式数量)<br>
     * Date:  10:42 <br>
     * @param airlineId
     * return: ResultBuilder
     */
    ResultBuilder<Integer> getCountFormulaByAirline(String airlineId);

    /**
     * Title: changeIsExpired
     * <p>
     * Description: 判断当前费用项是否存在公式过期
     * data:* @param feeInfo
     * Author: liuzhiheng
     * Date: 2024-10-10 09:55:29
     */
    void changeIsExpired(FeeInfo feeInfo);

    List<SafeguardTypeVo> getSafeguardType(String airportCode);
}
