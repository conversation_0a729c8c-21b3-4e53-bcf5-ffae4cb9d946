package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.model.dto.FlightIncomeFormDTO;
import com.swcares.aiot.core.model.vo.FlightIncomeFormVO;

import javax.servlet.http.HttpServletResponse;


/**
 * ClassName：FlightIncomeFormService
 * Description：机场收入报表 service接口
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/9/20 16:28
 * Version v1.0
 */
public interface FlightIncomeFormService {

    /**
     * Title：page
     * Description：机场收入报表分页
     * author：李军呈
     * date： 2024/9/20 16:29
     * @param dto 查询条件
     * @return IPage<FlightIncomeFormVO>
     */
    IPage<FlightIncomeFormVO> page(FlightIncomeFormDTO dto);

    /**
     * Title：incomeCount
     * Description：机场收入报表(收入合计)
     * author：李军呈
     * date： 2024/9/23 15:55
     * @param dto 查询入参
     * @return java.lang.String
     */
    String incomeCount(FlightIncomeFormDTO dto);

    /**
     * Title：exportFlightIncomeForm
     * Description：机场收入报表导出
     * author：李军呈
     * date： 2024/9/23 16:08
     * @param dto 查询入参
     * @param response 请求
     */
    void exportFlightIncomeForm(FlightIncomeFormDTO dto, HttpServletResponse response);
}
