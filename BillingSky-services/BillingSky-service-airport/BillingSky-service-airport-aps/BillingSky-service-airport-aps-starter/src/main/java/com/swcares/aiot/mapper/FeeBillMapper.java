package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.model.dto.FeeBillPagedDTO;
import com.swcares.aiot.core.model.vo.FeeBillCountVO;
import com.swcares.aiot.core.model.vo.FeeBillExportVo;
import com.swcares.aiot.core.model.vo.FeeBillVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.mapper.FeeBillMapper
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/2 10:19
 * @version v1.0
 */
@Mapper
public interface FeeBillMapper {

    IPage<FeeBillVO> pageFeeBillInfoByCondition(@Param("dto") FeeBillPagedDTO dto,@Param("dateType") Integer dateType,
                                                Page<FeeBillVO> page);

    FeeBillCountVO countTotal(@Param("dto") FeeBillPagedDTO dto, @Param("dateType")Integer dateType);

    List<FeeBillExportVo> listFeeBillInfoGroupByAirlineCode(@Param("dto") FeeBillPagedDTO dto, @Param("dateType")Integer dateType);

    List<FeeBillExportVo> listFeeBillInfoGroupBySettleCode(@Param("dto") FeeBillPagedDTO dto,
                                                  @Param("dateType")Integer dateType);
}
