package com.swcares.aiot.core.importer.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.baseframe.common.enums.DeletedEnum;
import com.swcares.aiot.core.importer.dao.ApsFlightInfoDao;
import com.swcares.aiot.core.importer.dao.ApsFlightInfoDaoCache;
import com.swcares.aiot.core.importer.dao.FlightInfoSegmentDao;
import com.swcares.aiot.core.importer.dao.FlightInfoSegmentDaoCache;
import com.swcares.aiot.core.importer.entity.*;
import com.swcares.aiot.core.importer.service.ImportService;
import com.swcares.aiot.core.importer.service.ImportToAirlineService;
import com.swcares.aiot.core.model.entity.AircraftInfo;
import com.swcares.aiot.core.model.entity.FlightInfo;
import com.swcares.aiot.dao.AircraftDao;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.dao.FlightBillDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.importer.service.ImportServiceImpl
 * Description：导入春秋航空航班数据serviceImpl
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/2/17 10:40
 * @version v1.0
 */
@Slf4j
@Service
public class ImportServiceImpl implements ImportService {
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private ApsFlightInfoDao apsFlightInfoDao;
    @Resource
    private ApsFlightInfoDaoCache apsFlightInfoDaoCache;
    @Resource
    private FlightInfoSegmentDao flightInfoSegmentDao;
    @Resource
    private FlightInfoSegmentDaoCache flightInfoSegmentDaoCache;
    @Resource
    private AircraftDao aircraftDao;
    @Resource
    private FeeDao feeDao;
    @Resource
    private FlightBillDao flightBillDao;
    @Resource
    private ImportToAirlineService importToAirlineService;


    @Override
    public void cacheFlightData(BaseFlightInfo bfi, ApsFlightInfo fi, String flightInfoFlag, String airportCode,
                                String flightFlag, ApsFlightInfo origin, Integer dataStatus) {
        String status = Strings.isBlank(bfi.getStatus()) ? "" : bfi.getStatus();
        // 判断航班是否逻辑删除,或者取消,或者备降时航线到港当前机场
        if (bfi.getDeleted().equals(DeletedEnum.DELETED) || bfi.getAcdmDeleted().equals(DeletedEnum.DELETED)
                || "can".equalsIgnoreCase(status)
                || ("alt".equalsIgnoreCase(status) && "A".equals(flightFlag)
                && (Strings.isNotBlank(bfi.getAirline())
                && !bfi.getAirline().contains(airportCode)))) {
            //删除航班
            fi.setInvalid("0");
            fi.setStayStartTime(null);
            fi.setStayEndTime(null);
        } else {
            setFlightInfo(bfi, fi, flightFlag, status);
        }

        ApsFlightInfoCache fiCache = apsFlightInfoDaoCache.getFlightInfoById(fi.getId());
        if (fiCache == null) {
            fiCache = new ApsFlightInfoCache();
            BeanUtils.copyProperties(fi, fiCache);
        }
        fiCache.setId(fi.getId());
        fiCache.setCreateBy(fi.getCreateBy());
        fiCache.setCreateTime(fi.getCreateTime());
        fiCache.setModifiedBy(fi.getModifiedBy());
        fiCache.setModifiedTime(fi.getModifiedTime());
        fiCache.setAirportCode(fi.getAirportCode());
        fiCache.setFlightDate(fi.getFlightDate());
        fiCache.setFlightTime(fi.getFlightTime());
        fiCache.setFlightNo(fi.getFlightNo());
        fiCache.setFlightLine(fi.getFlightLine());
        fiCache.setFlightSegment(fi.getFlightSegment());
        fiCache.setFlightType(fi.getFlightType());
        fiCache.setFlightLineType(StringUtil.isBlank(fi.getFlightLineType())?null:fi.getFlightLineType());
        fiCache.setFlightSegmentType(StringUtil.isBlank(fi.getFlightSegmentType())?null:fi.getFlightSegmentType());
        fiCache.setAirlineCode(fi.getAirlineCode());
        fiCache.setRegNo(fi.getRegNo());
        fiCache.setFlightModel(fi.getFlightModel());
        fiCache.setFromAirportCode(fi.getFromAirportCode());
        fiCache.setToAirportCode(fi.getToAirportCode());
        fiCache.setFlightStatus(fi.getFlightStatus());
        fiCache.setPreFlightId(fi.getPreFlightId());
        fiCache.setStayStartTime(fi.getStayStartTime());
        fiCache.setStayEndTime(fi.getStayEndTime());
        fiCache.setStayTime(fi.getStayTime());

        fiCache.setConnectFlightId(bfi.getConnectFlightId());
        apsFlightInfoDaoCache.saveAndFlush(fiCache);
        //只有数据状态是已确认的时候，才把状态修改为已修改
        if (dataStatus == 1) {
            setDataModified(fi, origin);
        }
    }

    private void setFlightInfo(BaseFlightInfo bfi, ApsFlightInfo fi, String flightFlag, String status) {
        setFlightLine(bfi, fi, flightFlag);
        setFlightTime(bfi, fi);
        //未删除航班
        fi.setInvalid("1");
        // 航线状态为空默认为国内
        fi.setFlightLineType(StringUtil.isBlank(bfi.getAirlineProperty()) ? null : bfi.getAirlineProperty());
        fi.setFlightSegment(bfi.getFlightSegment());
        fi.setFlightSegmentType(StringUtil.isBlank(bfi.getFlightSegmentProperty() ) ? null : bfi.getFlightSegmentProperty());

        fi.setFlightType(fi.getFlightSegmentType());
        fi.setAirlineCode(bfi.getAirlineCode());
        fi.setRegNo(bfi.getAircraftNo());
        fi.setFlightModel(bfi.getAircraftModel());
        fi.setFromAirportCode(bfi.getDepartureAirportCode());
        fi.setToAirportCode(bfi.getDestinationAirportCode());

        if ("alt".equalsIgnoreCase(status) && "A".equals(flightFlag)) {
            fi.setFlightStatus("alt");
        } else {
            fi.setFlightStatus(bfi.getPublishStatus());
        }
        fi.setTaskFlag(bfi.getTask());

        fi.setPreFlightId(
                bfi.getConnectFlightId() == null ? null : "" + bfi.getConnectFlightId());

        // 如果航班旅客数已同步，则根据机号计算出客座率
        if ((fi.getPlf() == null || fi.getPlf() == 0.0) && fi.getPsgNumber() != null) {
            getPlf(fi);
        }
    }

    private void setFlightLine(BaseFlightInfo bfi, ApsFlightInfo fi, String flightFlag) {
        //如果航线为空，则用历史数据填充
        if (Strings.isBlank(bfi.getAirline())) {
            List<ApsFlightInfo> flightLineList =
                    apsFlightInfoDao.listFlightInfoByFlightNoAndFlag(fi.getFlightNo(), flightFlag);
            for (ApsFlightInfo flightLineInfo : flightLineList) {
                if (Strings.isNotBlank(flightLineInfo.getFlightLine())) {
                    fi.setFlightLine(flightLineInfo.getFlightLine());
                    break;
                }
            }
        } else {
            fi.setFlightLine(bfi.getAirline());
        }
    }

    private void setFlightTime(BaseFlightInfo bfi, ApsFlightInfo fi) {
        LocalDateTime bFlightTime = null;
        // 解析起降时间
        // 如果为降落航班
        if ("A".equals(fi.getFlightFlag())) {
            if (bfi.getRealLandingDatetime() != null) {
                bFlightTime = bfi.getRealLandingDatetime();
            } else if (bfi.getPredictLandingDatetime() != null) {
                bFlightTime = bfi.getPredictLandingDatetime();
            } else if (bfi.getPlanLandingDatetime() != null) {
                bFlightTime = bfi.getPlanLandingDatetime();
            }
        } else {
            if (bfi.getRealTakeOffDatetime() != null) {
                bFlightTime = bfi.getRealTakeOffDatetime();
            } else if (bfi.getPredictTakeOffDatetime() != null) {
                bFlightTime = bfi.getPredictTakeOffDatetime();
            } else if (bfi.getPlanTakeOffDatetime() != null) {
                bFlightTime = bfi.getPlanTakeOffDatetime();
            }
        }
        if (bFlightTime != null) {
            // 将此日期时间与时区相结合以创建 ZonedDateTime
            ZonedDateTime zonedDateTime = bFlightTime.atZone(ZoneId.systemDefault());
            // 本地时间线LocalDateTime到即时时间线Instant时间戳
            Instant instant = zonedDateTime.toInstant();
            // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
            fi.setFlightTime(Date.from(instant));
        }
    }

    @Override
    public void cacheFlightCargoTravelerData(ApsFlightInfo fi, ApsFlightInfo origin, boolean traveller, Integer dataStatus) {
        ApsFlightInfoCache fiCache = apsFlightInfoDaoCache.getFlightInfoById(fi.getId());
        if (fiCache == null) {
            fiCache = new ApsFlightInfoCache();
            BeanUtils.copyProperties(fi, fiCache);
        }
        if (traveller) {
            fiCache.setPsgNumber(fi.getPsgNumber());
            fiCache.setFirstClassNumber(fi.getFirstClassNumber());
            fiCache.setClubClassNumber(fi.getClubClassNumber());
            fiCache.setEconomyClassNumber(fi.getEconomyClassNumber());
            fiCache.setAdultNumber(fi.getAdultNumber());
            fiCache.setChildNumber(fi.getChildNumber());
            fiCache.setInfantNumber(fi.getInfantNumber());
            fiCache.setTransitAdultNumber(fi.getTransitAdultNumber());
            fiCache.setTransitChildNumber(fi.getTransitChildNumber());
            fiCache.setTransitInfantNumber(fi.getTransitInfantNumber());
            fiCache.setDiplomaticPassportNumber(fi.getDiplomaticPassportNumber());
            fiCache.setCardHolderNumber(fi.getCardHolderNumber());
            fiCache.setAccompanyingCardHolderNumber(fi.getAccompanyingCardHolderNumber());
            fiCache.setImportantNumber(fi.getImportantNumber());
            fiCache.setAccompanyingImportantNumber(fi.getAccompanyingImportantNumber());
        } else {
            fiCache.setPlf(fi.getPlf());
            fiCache.setCargo(fi.getCargo());
            fiCache.setMail(fi.getMail());
            fiCache.setBag(fi.getBag());
            fiCache.setBagNumber(fi.getBagNumber());
            fiCache.setWeightUints(fi.getWeightUints());
            fiCache.setFlightFlag(fi.getFlightFlag());
            fiCache.setTaskFlag(fi.getTaskFlag());
            fiCache.setDaAirportCode(fi.getDaAirportCode());
            fiCache.setDaTime(fi.getDaTime());
            fiCache.setFlightFee(fi.getFlightFee());
            fiCache.setGroundFee(fi.getGroundFee());
            fiCache.setStayStartTime(fi.getStayStartTime());
            fiCache.setStayEndTime(fi.getStayEndTime());
            fiCache.setStayTime(fi.getStayTime());

        }

        apsFlightInfoDaoCache.saveAndFlush(fiCache);
        //只有数据状态是已确认的时候，才把状态修改为已修改
        if (dataStatus == 1) {
            setDataModified(fi, origin);
        }
    }

    @Override
    public void cacheSplitFlightCargoTravelerData(FlightInfoSegment fis, ApsFlightInfo afi) {
        FlightInfoSegmentCache fisc = new FlightInfoSegmentCache();
        BeanUtils.copyProperties(fis, fisc);
        flightInfoSegmentDaoCache.saveAndFlush(fisc);
        //setDataModified(afi, null);
    }

    @Override
    public void syncFlightData(List<FlightInfo> flightInfoList) {
        List<String> flightIdList = flightInfoList.stream().map(FlightInfo::getId).collect(Collectors.toList());
        List<ApsFlightInfoCache> fiCacheList = apsFlightInfoDaoCache.findAllById(flightIdList);
        if (CollectionUtils.isEmpty(fiCacheList)) {
            return;
        }
        List<FlightInfo> toProcessedList = new ArrayList<>();
        for (ApsFlightInfoCache cacheItem : fiCacheList) {
            for (FlightInfo item : flightInfoList) {
                if (item.getId().equals(cacheItem.getId())) {
                    BeanUtils.copyProperties(cacheItem, item);
                    toProcessedList.add(item);
                }
            }
        }
        if (CollectionUtils.isEmpty(toProcessedList)) {
            return;
        }
        for (FlightInfo fi : toProcessedList) {
            // 判断航班是否逻辑删除,或者取消,或者备降时航线到港当前机场
            if ("0".equals(fi.getInvalid())) {
                //如果删除，需要清空该航班与关联航班的停场时间
                if (fi.getRegNo() != null && fi.getStayStartTime() != null && fi.getStayEndTime() != null) {
                    ApsFlightInfo connectFlight = apsFlightInfoDao.getFlightInfoByStayTimeAndRegNo(fi.getRegNo(), fi.getAirportCode(), fi.getStayStartTime(), fi.getStayEndTime(), fi.getId());
                    if (connectFlight != null
                            && (connectFlight.getStayStartTime() != null || connectFlight.getStayEndTime() != null)) {
                        connectFlight.setStayStartTime(null);
                        connectFlight.setStayEndTime(null);
                        apsFlightInfoDao.save(connectFlight);
                    }
                }
                //如果航班删除了，需要删除该航班的所有账单数据
                List<String> feeCodeList = feeDao.getFeeCodeList(fi.getId(), "1,3");
                if (!CollectionUtils.isEmpty(feeCodeList)) {
                    // 删除费用信息
                    flightBillDao.updateUnpushedFlightBusinessBill(feeCodeList, fi.getId(), "备份数据同步",
                            new Date());
                }
            } else {
                // 计算离港航班停场时长

                // 如果航班旅客数已同步，则根据机号计算出客座率
                if ((fi.getPlf() == null || fi.getPlf() == 0.0) && fi.getPsgNumber() != null) {
                    getPlf(fi);
                }
            }
        }
        flightInfoDao.saveAllAndFlush(toProcessedList);
        List<String> toProcessedFlightIdList =
                toProcessedList.stream().map(FlightInfo::getId).collect(Collectors.toList());
        List<FlightInfoSegmentCache> fiscList =
                flightInfoSegmentDaoCache.getFlightInfoByIdList(toProcessedFlightIdList);
        if (CollUtil.isNotEmpty(fiscList)) {
            Map<String, FlightInfoSegmentCache> collect = fiscList.stream().collect(Collectors.toMap(FlightInfoSegmentCache::getId, item -> item));
            List<String> toUpdatedSegmentIdList = fiscList.stream()
                    .map(FlightInfoSegmentCache::getId).collect(Collectors.toList());
            List<FlightInfoSegment> fisList = flightInfoSegmentDao.getFlightInfoByIdList(toUpdatedSegmentIdList);
            for (FlightInfoSegment fis : fisList) {
                FlightInfoSegmentCache fisc = collect.get(fis.getId());
                BeanUtils.copyProperties(fisc, fis);
            }
            flightInfoSegmentDao.saveAllAndFlush(fisList);
        }
        // 批量推送到航司端
        importToAirlineService.sendToAirline(toProcessedList, TenantHolder.getTenant());
    }

    private void getPlf(FlightInfo flightInfo) {
        // 通过飞机注册号和航司二字码获取飞机信息
        AircraftInfo aircraftInfo = aircraftDao.getAircraftInfoByRegNoAndAirlineCode(flightInfo.getRegNo(), flightInfo.getFlightNo().substring(0, 2), new Date());
        if (aircraftInfo == null || aircraftInfo.getMaxSeat() == null) {
            return;
        }
        if (aircraftInfo.getMaxSeat() == 0) {
            flightInfo.setPlf(0d);
            return;
        }
        Integer adultNumber = flightInfo.getAdultNumber() == null ? 0 : flightInfo.getAdultNumber();
        Integer childNumber = flightInfo.getChildNumber() == null ? 0 : flightInfo.getChildNumber();
        Integer transitAdultNumber = flightInfo.getTransitAdultNumber() == null ? 0 : flightInfo.getTransitAdultNumber();
        Integer transitChildNumber = flightInfo.getTransitChildNumber() == null ? 0 : flightInfo.getTransitChildNumber();
        double plf = FormatUtils.formatData((adultNumber + childNumber + transitAdultNumber + transitChildNumber) / (double) aircraftInfo.getMaxSeat());
        flightInfo.setPlf(100 * plf);
    }

    private void setDataModified(ApsFlightInfo fi, ApsFlightInfo origin) {
        if (origin != null && !fi.equals(origin)) {
            apsFlightInfoDao.updateFlightInfoDataModified(fi.getId());
        }
    }

    private void getPlf(ApsFlightInfo apsFlightInfo) {
        // 通过飞机注册号和航司二字码获取飞机信息
        AircraftInfo aircraftInfo = aircraftDao.getAircraftInfoByRegNoAndAirlineCode(apsFlightInfo.getRegNo(), apsFlightInfo.getFlightNo().substring(0, 2), new Date());
        if (aircraftInfo == null || aircraftInfo.getMaxSeat() == null) {
            return;
        }
        if (aircraftInfo.getMaxSeat() == 0) {
            apsFlightInfo.setPlf(0d);
            return;
        }
        Integer adultNumber = apsFlightInfo.getAdultNumber() == null ? 0 : apsFlightInfo.getAdultNumber();
        Integer childNumber = apsFlightInfo.getChildNumber() == null ? 0 : apsFlightInfo.getChildNumber();
        Integer transitAdultNumber = apsFlightInfo.getTransitAdultNumber() == null ? 0 : apsFlightInfo.getTransitAdultNumber();
        Integer transitChildNumber = apsFlightInfo.getTransitChildNumber() == null ? 0 : apsFlightInfo.getTransitChildNumber();
        double plf = FormatUtils.formatData((adultNumber + childNumber + transitAdultNumber + transitChildNumber) / (double) aircraftInfo.getMaxSeat());
        apsFlightInfo.setPlf(100 * plf);
    }
}
