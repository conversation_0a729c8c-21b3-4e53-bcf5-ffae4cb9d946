package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.FlightBillForm;
import com.swcares.aiot.core.form.FlightBillRefuseForm;
import com.swcares.aiot.core.form.SubmitForm;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.vo.AirlineRelatedFeeNewVo;
import com.swcares.aiot.core.model.vo.SubmitVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ClassName：FlightBillService <br>
 * Description：(航班明细账单service接口)<br>
 * Copyright © 2020/6/17 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/17 18:54<br>
 * @version v1.0 <br>
 */
public interface FlightBillService {
    /**
     * Title: pageFlightBillInfoByCondition<br>
     * Author: 叶咏秋<br>
     * Description: (航班明细账单动态条件分页查询)<br>
     * Date:  16:56 <br>
     *
     * @param pageParam      :
     * @param flightBillForm :
     * @return : ResultBuilder
     */
    ResultBuilder<Map<String, Object>> pageFlightBillInfoByCondition(PageParam pageParam, FlightBillForm flightBillForm);

    /**
     * Title: countTotal<br>
     * Author: 叶咏秋<br>
     * Description: (航班明细账单总计)<br>
     * Date:  16:56 <br>
     *
     * @param flightBillForm return: ResultBuilder
     */
    ResultBuilder<BigDecimal> countTotal(FlightBillForm flightBillForm);

    /**
     * Title: exportFlightBillInfo<br>
     * Author: 叶咏秋<br>
     * Description: (导出航班明细账单)<br>
     * Date:  16:56 <br>
     *
     * @param flightBillForm :
     * @param response       :
     */
    void exportFlightBillInfo(FlightBillForm flightBillForm, HttpServletResponse response, LoginUserDetails user, String urlName);

    /**
     * Title: doReSettlement<br>
     * Author: 叶咏秋<br>
     * Description: (重新结算航班相关费用)<br>
     * Date:  9:45 <br>
     *
     * @param flightNos :
     * @param startDate :
     * @param endDate   :
     * @param user      :
     * @param dateType  :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> doReSettlement(String airportCode, String flightNos, String feeCode, Date startDate, Date endDate, LoginUserDetails user, Integer dateType);

    ResultBuilder<List<AirlineRelatedFeeNewVo>> listAirlineRelatedFeeInfo(String airportCode);

    /**
     * Title: submit<br>
     * Author: 刘志恒<br>
     * Description: 提交对账<br>
     * Date:  2024/1/16 10:08 <br>
     *
     * @param form :
     */
    ResultBuilder<SubmitVo> submit(SubmitForm form);

    Object upload(MultipartFile file);

    void saveOrUpdateBills(List<FlightBill> bills, String userName, Long tenant);

    Object refuseToHandle(FlightBillRefuseForm param);

    Object billRecord(String fltBillId, Integer limit);

    Object revocation(String id);

    Integer deleteFlightBillById(List<String> idList);

    Object uploadBatch(MultipartFile file);

    Object checkUploadFile(MultipartFile file);
}
