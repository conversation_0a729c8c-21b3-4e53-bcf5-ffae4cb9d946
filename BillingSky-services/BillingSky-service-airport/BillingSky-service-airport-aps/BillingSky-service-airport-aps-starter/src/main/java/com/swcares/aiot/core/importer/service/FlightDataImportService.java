package com.swcares.aiot.core.importer.service;

import com.swcares.aiot.core.importer.entity.*;

/**
 * ClassName：com.swcares.importer.service.FlightDataImportService
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/9/16 10:23
 * @version v1.0
 */
public interface FlightDataImportService {

    void importFlightData(BaseFlightInfo baseFlightInfo, boolean firstParse);

    void importFlightCargo(BaseFlightCargo baseFlightInfos, boolean firstParse);

    void importFlightTraveler(BaseFlightTraveler baseFlightInfos, boolean firstParse);

    void importSplitFlightCargo(BaseFlightCargoSegment baseFlightInfos);

    void importSplitFlightTraveler(BaseFlightTravelerSegment baseFlightInfos);

}
