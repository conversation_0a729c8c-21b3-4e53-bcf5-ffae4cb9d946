package com.swcares.aiot.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.swcares.aiot.core.common.enums.VariableStatusEnum;
import com.swcares.aiot.core.entity.*;
import com.swcares.aiot.core.service.*;
import com.swcares.aiot.dto.ServiceItemDto;
import com.swcares.aiot.service.NewSignDataService;
import com.swcares.aiot.vo.DcSignatureInfoVo;
import com.swcares.aiot.vo.FlightInfoVO;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.aiot.service.impl.NewSignDataServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/7/11 10:03
 * @version v1.0
 */
@Service
@Slf4j
public class NewSignDataServiceImpl implements NewSignDataService {

    public static final String AIRPORT_SIGN_DATA_SYNC = "机场保障数据同步";
    public static final String LIMIT_SQL = " limit 1 ";
    @Resource
    private ITFlightInfoService flightInfoService;
    @Resource
    private ITServiceRecordService serviceRecordService;
    @Resource
    private ITServiceRecordConfirmService serviceRecordConfirmService;
    @Resource
    private ITVariableRecordService variableRecordService;
    @Resource
    private ITAirportSignItemService itAirportSignItemService;
    @Resource
    private ITVariableGuaranteeService variableGuaranteeService;


    @Override
    public void saveSignData(DcSignatureInfoVo dcSignatureInfoVo) {
        log.info("接收到机场签单数据，参数为：{}", dcSignatureInfoVo);
        String tenantId = dcSignatureInfoVo.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            log.info("同步机场签单数据，租户id为空，参数为：{}", tenantId);
            return;
        }
        //设置租户id
        TenantHolder.setTenant(Long.valueOf(tenantId));
        Long flightId = dcSignatureInfoVo.getBaseFlightId();
        if (flightId==null) {
            log.info("同步机场签单数据，航班id为空，参数为：{}", dcSignatureInfoVo);
            return;
        }
        FlightInfoMb flightInfo = flightInfoService.lambdaQuery().eq(FlightInfoMb::getId, String.valueOf(flightId)).eq(FlightInfoMb::getInvalid, "1").one();
        if (flightInfo == null) {
            log.info("同步机场签单数据，航班id查询aps航班数据为空，参数为：{}", flightId);
            //新建航班数据
            flightInfo = saveTempFlightInfo(dcSignatureInfoVo);
        }
        //解析成业务保障数据
        TServiceRecord serviceRecord = analysisSignData(dcSignatureInfoVo, flightInfo);
        if (serviceRecord == null) {
            return;
        }
        //判断航班业务保障数据是否确认
        if (VariableStatusEnum.CONFIRMED.getValue().equals(flightInfo.getVariableStatus())
                ||VariableStatusEnum.MODIFIED.getValue().equals(flightInfo.getVariableStatus())) {
            //保障业务保障备份数据
            saveServiceRecordConfirm(flightId, serviceRecord);
        } else {
            //保存业务保障数据
            saveServiceRecord(flightId, serviceRecord);
        }
        //清除租户id
        TenantHolder.clear();
    }

    private FlightInfoMb saveTempFlightInfo(DcSignatureInfoVo dcSignatureInfoVo) {
        FlightInfoMb flightInfo;
        FlightInfoVO flightInfoVO = dcSignatureInfoVo.getFlightInfoVO();
        flightInfo=new FlightInfoMb();
        flightInfo.setId(String.valueOf(flightInfoVO.getId()));
        flightInfo.setFlightNo(flightInfoVO.getFlightNo());
        flightInfo.setFlightDate(flightInfoVO.getFlightDate());
        flightInfo.setAirportCode(flightInfoVO.getAirportCode());
        flightInfo.setFlightFlag("1".equals(flightInfoVO.getIsArrv())?"A":"D");
        flightInfo.setDataStatus(0);
        flightInfo.setInvalid("1");
        flightInfo.setCreateTime(LocalDateTime.now());
        flightInfo.setCreateBy("同步保障数据临时创建");
        flightInfo.setModifiedTime(LocalDateTime.now());
        flightInfo.setModifiedBy("同步保障数据临时创建");
        flightInfoService.saveOrUpdate(flightInfo);
        return flightInfo;
    }

    private void saveServiceRecordConfirm(Long flightId, TServiceRecord serviceRecord) {
        //根据flightId、itemId、serviceCode查询是否已存在备份业务保障数据
        TServiceRecordConfirm tServiceRecordConfirm = serviceRecordConfirmService.lambdaQuery().eq(TServiceRecordConfirm::getFlightId, String.valueOf(flightId))
                .eq(StringUtils.isNotBlank(serviceRecord.getBillBusDataItemId()),
                        TServiceRecordConfirm::getBillBusDataItemId, serviceRecord.getBillBusDataItemId())
                .eq(TServiceRecordConfirm::getServiceCode, serviceRecord.getServiceCode())
                .eq(TServiceRecordConfirm::getInvalid,"1")
                .orderByAsc(TServiceRecordConfirm::getModifiedTime)
                .last(LIMIT_SQL)
                .one();
        if (tServiceRecordConfirm!=null) {
            //已存在数据则更新
            BeanUtil.copyProperties(serviceRecord, tServiceRecordConfirm, "id");
            serviceRecordConfirmService.updateById(tServiceRecordConfirm);
            checkIsMoreBridge(flightId, tServiceRecordConfirm,true);
        } else {
            //不存在则新建
            tServiceRecordConfirm = new TServiceRecordConfirm();
            BeanUtil.copyProperties(serviceRecord, tServiceRecordConfirm);
            tServiceRecordConfirm.setCreateBy(AIRPORT_SIGN_DATA_SYNC);
            tServiceRecordConfirm.setCreateTime(LocalDateTime.now());
            serviceRecordConfirmService.save(tServiceRecordConfirm);
            checkIsMoreBridge(flightId, tServiceRecordConfirm,false);
        }
    }

    private void checkIsMoreBridge(Long flightId, TServiceRecordConfirm tServiceRecordConfirm,boolean isUpdate) {
        if("0".equals(tServiceRecordConfirm.getInvalid())){
            return;
        }
        if("CBT".equals(tServiceRecordConfirm.getServiceCode())){
            TServiceRecordConfirm stSr = serviceRecordConfirmService.lambdaQuery().eq(TServiceRecordConfirm::getFlightId, String.valueOf(flightId))
                    .eq(TServiceRecordConfirm::getServiceCode, "CT")
                    .eq(TServiceRecordConfirm::getInvalid,"1")
                    .one();

            doubleOrThreeBridge(tServiceRecordConfirm, stSr,isUpdate);
        } else if ("CT".equals(tServiceRecordConfirm.getServiceCode())) {
            TServiceRecordConfirm cbtSr = serviceRecordConfirmService.lambdaQuery().eq(TServiceRecordConfirm::getFlightId, String.valueOf(flightId))
                    .eq(TServiceRecordConfirm::getServiceCode, "CBT")
                    .eq(TServiceRecordConfirm::getInvalid,"1")
                    .orderByAsc(TServiceRecordConfirm::getModifiedTime)
                    .last(LIMIT_SQL)
                    .one();
            doubleOrThreeBridge(cbtSr, tServiceRecordConfirm,isUpdate);
        }
    }

    private void doubleOrThreeBridge(TServiceRecordConfirm cbtSr, TServiceRecordConfirm stSr,boolean isUpdate) {
        if(stSr !=null&& stSr.getUsedNumber()!=null&&cbtSr!=null&&cbtSr.getUsedNumber()!=null){
            if(isUpdate){
                serviceRecordConfirmService.lambdaUpdate().set(TServiceRecordConfirm::getInvalid,"0")
                        .eq(TServiceRecordConfirm::getServiceCode, "CBT")
                        .eq(TServiceRecordConfirm::getFlightId,cbtSr.getFlightId())
                        .like(TServiceRecordConfirm::getBillBusDataItemId, getBillBusDataItemId(stSr.getBillBusDataItemId(), "_"))
                        .update();
            }

            if(BigDecimal.valueOf(2).equals(stSr.getUsedNumber())){
                TServiceRecordConfirm serviceRecord1=new TServiceRecordConfirm();
                BeanUtil.copyProperties(cbtSr, serviceRecord1);
                serviceRecord1.setBillBusDataItemId(getBillBusDataItemId(stSr.getBillBusDataItemId(), "_1"));

                serviceRecord1.setId(null);
                serviceRecordConfirmService.saveOrUpdate(serviceRecord1);
            } else if ( BigDecimal.valueOf(3).equals(stSr.getUsedNumber() )) {
                TServiceRecordConfirm serviceRecord1=new TServiceRecordConfirm();
                BeanUtil.copyProperties(cbtSr, serviceRecord1);
                serviceRecord1.setBillBusDataItemId(getBillBusDataItemId(stSr.getBillBusDataItemId(), "_1"));


                TServiceRecordConfirm serviceRecord2=new TServiceRecordConfirm();
                BeanUtil.copyProperties(cbtSr, serviceRecord2);
                serviceRecord2.setBillBusDataItemId(getBillBusDataItemId(stSr.getBillBusDataItemId(), "_2"));

                serviceRecord1.setId(null);
                serviceRecord2.setId(null);

                serviceRecordConfirmService.saveOrUpdate(serviceRecord1);
                serviceRecordConfirmService.saveOrUpdate(serviceRecord2);
            }
        }
    }

    private static String getBillBusDataItemId(String stSr, String num) {
        return (stSr == null ? "" : stSr) + num;
    }


    private void saveServiceRecord(Long flightId, TServiceRecord serviceRecord) {
        //根据flightId、itemId、serviceCode查询是否已存在业务保障数据
        TServiceRecord tServiceRecord = serviceRecordService.lambdaQuery().eq(TServiceRecord::getFlightId, String.valueOf(flightId))
                .eq(StringUtils.isNotBlank(serviceRecord.getBillBusDataItemId()),
                        TServiceRecord::getBillBusDataItemId, serviceRecord.getBillBusDataItemId())
                .eq(TServiceRecord::getServiceCode, serviceRecord.getServiceCode())
                .eq(TServiceRecord::getInvalid,"1")
                .orderByAsc(TServiceRecord::getModifiedTime)
                .last(LIMIT_SQL)
                .one();
        if (tServiceRecord!=null) {
            BeanUtil.copyProperties(serviceRecord, tServiceRecord, "id");
            serviceRecordService.updateById(tServiceRecord);
            checkIsMoreBridgeSr(flightId, serviceRecord,true);
        } else {
            serviceRecord.setCreateBy(AIRPORT_SIGN_DATA_SYNC);
            serviceRecord.setCreateTime(LocalDateTime.now());
            serviceRecordService.save(serviceRecord);
            checkIsMoreBridgeSr(flightId, serviceRecord,false);
        }
    }

    private void checkIsMoreBridgeSr(Long flightId, TServiceRecord serviceRecord,boolean isUpdate) {
        if("0".equals(serviceRecord.getInvalid())){
            return;
        }
        if("CBT".equals(serviceRecord.getServiceCode())){
            TServiceRecord stSr = serviceRecordService.lambdaQuery().eq(TServiceRecord::getFlightId, String.valueOf(flightId))
                    .eq(TServiceRecord::getServiceCode, "CT")
                    .eq(TServiceRecord::getInvalid,"1")
                    .one();
            doubleOrThreeBridg(serviceRecord, stSr, isUpdate);
        } else if ("CT".equals(serviceRecord.getServiceCode())) {
            TServiceRecord cbtSr = serviceRecordService.lambdaQuery().eq(TServiceRecord::getFlightId, String.valueOf(flightId))
                    .eq(TServiceRecord::getServiceCode, "CBT")
                    .eq(TServiceRecord::getInvalid,"1")
                    .orderByAsc(TServiceRecord::getModifiedTime)
                    .last(LIMIT_SQL)
                    .one();
            doubleOrThreeBridg(cbtSr, serviceRecord,isUpdate);
        }
    }

    private void doubleOrThreeBridg(TServiceRecord cbtSr, TServiceRecord stSr,boolean isUpdate) {
        if(stSr !=null&& stSr.getUsedNumber()!=null&&cbtSr!=null&&cbtSr.getUsedNumber()!=null){
            if(isUpdate){
                serviceRecordService.lambdaUpdate().set(TServiceRecord::getInvalid,"0")
                        .eq(TServiceRecord::getServiceCode, "CBT")
                        .eq(TServiceRecord::getFlightId,cbtSr.getFlightId())
                        .like(TServiceRecord::getBillBusDataItemId, getBillBusDataItemId(stSr.getBillBusDataItemId(), "_"))
                        .update();
            }
            if(BigDecimal.valueOf(2).equals(stSr.getUsedNumber())){
                TServiceRecord serviceRecord1=new TServiceRecord();
                BeanUtil.copyProperties(cbtSr, serviceRecord1);
                serviceRecord1.setId(null);
                serviceRecord1.setBillBusDataItemId(getBillBusDataItemId(stSr.getBillBusDataItemId(),"_1"));
                serviceRecordService.saveOrUpdate(serviceRecord1);
            } else if (BigDecimal.valueOf(3).equals(stSr.getUsedNumber())) {
                TServiceRecord serviceRecord1=new TServiceRecord();
                BeanUtil.copyProperties(cbtSr, serviceRecord1);
                serviceRecord1.setBillBusDataItemId(getBillBusDataItemId(stSr.getBillBusDataItemId(),"_1"));

                TServiceRecord serviceRecord2=new TServiceRecord();
                BeanUtil.copyProperties(cbtSr, serviceRecord2);
                serviceRecord2.setBillBusDataItemId(getBillBusDataItemId(stSr.getBillBusDataItemId(),"_2"));

                serviceRecord1.setId(null);
                serviceRecord2.setId(null);

                serviceRecordService.saveOrUpdate(serviceRecord1);
                serviceRecordService.saveOrUpdate(serviceRecord2);
            }
        }
    }


    private TServiceRecord analysisSignData(DcSignatureInfoVo dcSignatureInfoVo, FlightInfoMb flightInfo) {
        String itemCode = dcSignatureInfoVo.getItemCode();
        //获取itemcode对应机场字典数据
        TAirportSignItem signItem = itAirportSignItemService.lambdaQuery().eq(TAirportSignItem::getItemCode, itemCode)
                .eq(TAirportSignItem::getDeleted, Boolean.FALSE).one();
        if (signItem == null) {
            log.error("同步机场签单数据，未获取到机场签单字典表数据，itemCode:{}", itemCode);
            return null;
        }
        //获取映射关系，及匹配规则
        TVariableGuarantee variableGuarantee = variableGuaranteeService.lambdaQuery().eq(TVariableGuarantee::getItemId, signItem.getId())
                .eq(TVariableGuarantee::getInvalid, "1").one();
        if (variableGuarantee == null) {
            log.error("同步机场签单数据，未建立匹配关系，itemCode:{}", itemCode);
            return null;
        }
        // 校验匹配规则
        if (checkRule(flightInfo, variableGuarantee, itemCode)) {
            return null;
        }

        //获取itemcode对结算业务字典数据
        TVariableRecord variableRecord = variableRecordService.lambdaQuery()
                .eq(TVariableRecord::getId, variableGuarantee.getVariableId()).one();
        if (variableRecord == null) {
            log.error("同步机场签单数据，业务保障数据字典异常，匹配关系数据为：{}", variableGuarantee);
            return null;
        }
        TServiceRecord serviceRecord = new TServiceRecord();
        serviceRecord.setServiceCode(variableRecord.getVariable());
        serviceRecord.setServiceName(variableRecord.getVariableName());
        serviceRecord.setBillBusDataItemId(dcSignatureInfoVo.getItemId());
        serviceRecord.setAirportCode(dcSignatureInfoVo.getAirportCode());
        serviceRecord.setFlightId(flightInfo.getId());
        serviceRecord.setModifiedBy(AIRPORT_SIGN_DATA_SYNC);
        serviceRecord.setModifiedTime(LocalDateTime.now());
        if(Integer.valueOf(1).equals(dcSignatureInfoVo.getBusinessDelete())|| Boolean.TRUE.equals(dcSignatureInfoVo.getDeleted())){
            log.info("接收到机场签单数据，删除或业务删除为true，dcSignatureInfoVo：{}", dcSignatureInfoVo);
            serviceRecord.setInvalid("0");
            return serviceRecord;
        }
        if ("number_format".equals(dcSignatureInfoVo.getItemType()) ) {
            serviceRecord.setUsedNumber(BigDecimal.valueOf(Long.parseLong(dcSignatureInfoVo.getTimes())));
        } else if ("time_format".equals(dcSignatureInfoVo.getItemType())) {
            if("H".equalsIgnoreCase(variableRecord.getVariableUnit())){
                //使用数据中心时长作为使用值
                analysisTimeFormat(dcSignatureInfoVo, serviceRecord, variableGuarantee);
            }else {
                //使用数据中心次数作为使用值
                serviceRecord.setUsedNumber(BigDecimal.valueOf(Long.parseLong(dcSignatureInfoVo.getTimes())));
            }
        } else if("selector_format".equals(dcSignatureInfoVo.getItemType())&&StringUtils.isNotBlank(dcSignatureInfoVo.getOther())){
            serviceRecord.setUsedNumber(BigDecimal.valueOf(Long.parseLong(dcSignatureInfoVo.getOther())));
        }else {
            log.error("同步机场签单数据，无正确ItemType数据格式：{}", dcSignatureInfoVo);
            return null;
        }
        return serviceRecord;
    }

    private static boolean checkRule(FlightInfoMb flightInfo, TVariableGuarantee variableGuarantee, String itemCode) {
        String landFlag = variableGuarantee.getLandFlag();
        if (!"B".equals(landFlag) && !flightInfo.getFlightFlag().equals(landFlag)) {
            log.error("同步机场签单数据，匹配关系起降规则不匹配，过站签单归集:{}，航班期间标识：{}", itemCode, flightInfo.getFlightFlag());
            return true;
        }
        if ("0".equals(variableGuarantee.getDataUpdate())
                && (flightInfo.getVariableStatus() == 1 || flightInfo.getVariableStatus() == 2)) {
            log.error("同步机场签单数据，该匹配关系在航班保障数据确认后不可更新，航班保障数据状态：{}", flightInfo.getVariableStatus());
            return true;
        }
        return false;
    }

    private static void analysisTimeFormat(DcSignatureInfoVo dcSignatureInfoVo, TServiceRecord serviceRecord, TVariableGuarantee variableGuarantee) {
        //判断开始时间是否为空
        if (dcSignatureInfoVo.getStartTime() != null) {
            serviceRecord.setStartTime(dcSignatureInfoVo.getStartTime());
        }
        //判断结束时间是否为空
        if (dcSignatureInfoVo.getEndTime() != null) {
            serviceRecord.setEndTime(dcSignatureInfoVo.getEndTime());
        }
        //判断时间时长是否为空
        if (dcSignatureInfoVo.getUseDuration() != null) {
            double useDuration = Double.parseDouble(dcSignatureInfoVo.getUseDuration());
            String conversionRules = variableGuarantee.getConversionRules();
            if ("2".equals(conversionRules)) {
                useDuration = Math.ceil(useDuration * 2) / 2;
            } else if ("3".equals(conversionRules) && useDuration != 0.0) {
                useDuration = 1;
            }
            serviceRecord.setUsedNumber(BigDecimal.valueOf(useDuration));
        }
    }

    @Override
    public void saveDict(ServiceItemDto serviceItem) {
        String tenantId = serviceItem.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            log.info("同步机场签单数据，租户id为空，参数为：{}", tenantId);
            return;
        }
        //设置租户id
        TenantHolder.setTenant(Long.valueOf(tenantId));
        TAirportSignItem tairportSignItem = new TAirportSignItem();
        tairportSignItem.setId(serviceItem.getId());
        tairportSignItem.setItemName(serviceItem.getDescription());
        tairportSignItem.setItemCode(serviceItem.getCode());
        tairportSignItem.setUnit(serviceItem.getUnit());
        tairportSignItem.setAirportCode(serviceItem.getAirportCode());
        itAirportSignItemService.saveOrUpdate(tairportSignItem);
    }
}
