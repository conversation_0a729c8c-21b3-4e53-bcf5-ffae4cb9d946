package com.swcares.aiot.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.BillFileAttachment;
import com.swcares.aiot.core.entity.EstimateBill;
import com.swcares.aiot.core.enums.FileBusinessTypeEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.service.IBillFileAttachmentService;
import com.swcares.aiot.core.service.IEstimateBillService;
import com.swcares.aiot.file.client.AttachmentClient;
import com.swcares.aiot.file.vo.AttachmentVO;
import com.swcares.aiot.service.IFileAttachmentBizService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.worm.hutool.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Title ：FileAttachmentBizServiceImpl <br>
 * Package ：com.swcares.aiot.service.impl <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : 附件处理service实现 <br>
 *
 * <AUTHOR> <br>
 * date 2024年 09月03日 20:04 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FileAttachmentBizServiceImpl implements IFileAttachmentBizService {

    public static final String EXCEL_SUFFIX = ".xls";

    @Resource
    private IBillFileAttachmentService iBillFileAttachmentService;
    @Resource
    private AttachmentClient attachmentClient;
    @Resource
    private IEstimateBillService iEstimateBillService;

    @Override
    public Object uploadFile(MultipartFile file, Integer businessType) {
        //上传
        AttachmentVO uploadResult = upload(file, businessType);
        if (businessType.equals(FileBusinessTypeEnum.ATTACHMENT_PROVE_FILE.getFileType())) {
            return uploadResult;
        }
        BillFileAttachment fileAttachment = new BillFileAttachment().setFileName(uploadResult.getFileName())
                .setOriginalName(uploadResult.getOriginalName())
                .setFileKey(uploadResult.getId().toString())
                .setSuffix(uploadResult.getFileExtension())
                .setFileType(uploadResult.getFileType())
                .setBusinessType(businessType);
        iBillFileAttachmentService.save(fileAttachment);
        return fileAttachment.getFileKey();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void associatedBusinessInfo(List<BillFileAttachment> currentFileList, FileBusinessTypeEnum businessType, Long tableId) {
        List<BillFileAttachment> dbFileList = iBillFileAttachmentService.lambdaQuery()
                .eq(BillFileAttachment::getBusinessType, businessType.getFileType())
                .eq(BillFileAttachment::getDeleted, Boolean.FALSE)
                .eq(BillFileAttachment::getTableId, tableId).list();
        if (CollectionUtils.isEmpty(currentFileList)) {
            dbFileList.forEach(item -> item.setDeleted(Boolean.TRUE));
            iBillFileAttachmentService.updateBatchById(dbFileList);
            return;
        }
        List<String> currentFileKeyList =
                currentFileList.stream().map(BillFileAttachment::getFileKey).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> dbFileKeyList = dbFileList.stream().map(BillFileAttachment::getFileKey).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        addBusinessInfo(currentFileKeyList, businessType, tableId);
        removeUnusedFile(currentFileKeyList, dbFileKeyList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assFileByFileKeyList(List<String> currentFileKeyList, FileBusinessTypeEnum businessType, Long tableId) {
        List<BillFileAttachment> dbFileList = iBillFileAttachmentService.lambdaQuery()
                .eq(BillFileAttachment::getBusinessType, businessType.getFileType())
                .eq(BillFileAttachment::getDeleted, Boolean.FALSE)
                .eq(BillFileAttachment::getTableId, tableId).list();
        if (CollectionUtils.isEmpty(currentFileKeyList)) {
            dbFileList.forEach(item -> item.setDeleted(Boolean.TRUE));
            iBillFileAttachmentService.updateBatchById(dbFileList);
            return;
        }
        List<String> dbFileKeyList = dbFileList.stream().map(BillFileAttachment::getFileKey).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        addBusinessInfo(currentFileKeyList, businessType, tableId);
        removeUnusedFile(currentFileKeyList, dbFileKeyList);

    }

    @Override
    public List<BillFileAttachment> queryFileList(FileBusinessTypeEnum businessType, Long tableId) {
        return iBillFileAttachmentService.lambdaQuery().eq(BillFileAttachment::getDeleted, Boolean.FALSE)
                .eq(BillFileAttachment::getTableId, tableId)
                .eq(BillFileAttachment::getBusinessType, businessType.getFileType()).list();
    }

    @Override
    public List<BillFileAttachment> queryFileList(FileBusinessTypeEnum businessType, List<Long> tableIdList) {
        return iBillFileAttachmentService.lambdaQuery().eq(BillFileAttachment::getDeleted, Boolean.FALSE)
                .in(BillFileAttachment::getTableId, tableIdList)
                .eq(BillFileAttachment::getBusinessType, businessType.getFileType()).list();
    }

    @Override
    public List<BillFileAttachment> queryFileList(List<String> fileKeyList) {
        return iBillFileAttachmentService.lambdaQuery().eq(BillFileAttachment::getDeleted, Boolean.FALSE)
                .in(BillFileAttachment::getFileKey, fileKeyList).list();
    }

    @Override
    public void downloadFile(String fileKey, Long tableId, HttpServletRequest request, HttpServletResponse response) {
        //检查key是否存在
        BillFileAttachment file = iBillFileAttachmentService.lambdaQuery().eq(BillFileAttachment::getDeleted, Boolean.FALSE)
                .eq(BillFileAttachment::getFileKey, fileKey)
                .eq(Objects.nonNull(tableId) && tableId != 0, BillFileAttachment::getTableId, tableId).one();
        if (Objects.isNull(file)) {
            throw new BusinessException(ExceptionCodes.FILE_MISSING);
        }
        try (InputStream inputStream = attachmentClient.download(Long.valueOf(file.getFileKey()), 0L).body().asInputStream()) {
            String fileName = URLEncoder.encode(file.getOriginalName(), "utf-8");
            response.setHeader("Access-Control-Expose-Headers", HttpUtils.HEADER_CONTENT_DISPOSITION);
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            response.setContentType("application/force-download");
            response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
            FileUtils.writeFile(response, inputStream);
        } catch (IOException e) {
            log.error("文件下载失败!message:{}", e.getMessage(), e);
            throw new BusinessException(ExceptionCodes.FILE_DOWNLOAD_FAILED);
        }
    }

    @Override
    public void downloadFile(String fileKey, String originalName, HttpServletRequest request, HttpServletResponse response) {
        try (InputStream inputStream = attachmentClient.download(Long.valueOf(fileKey), 0L).body().asInputStream()) {
            String fileName;
            if(StringUtils.isBlank(originalName)){
                AttachmentVO data = attachmentClient.getFileInfo(Long.valueOf(fileKey), 0L).getData();
                if(Objects.isNull(data)){
                    throw new BusinessException(ExceptionCodes.FILE_DOWNLOAD_FAILED);
                }
                fileName = URLEncoder.encode(data.getOriginalName(), "utf-8");
            }else {
                fileName = URLEncoder.encode(originalName, "utf-8");
            }
            response.setHeader("Access-Control-Expose-Headers", HttpUtils.HEADER_CONTENT_DISPOSITION);
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            response.setContentType("application/force-download");
            response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
            FileUtils.writeFile(response, inputStream);
        } catch (IOException e) {
            log.error("文件下载失败!message:{}", e.getMessage(), e);
            throw new BusinessException(ExceptionCodes.FILE_DOWNLOAD_FAILED);
        }
    }

    private void removeUnusedFile(List<String> currentFileList, List<String> dbFileList) {
        if (CollectionUtils.isEmpty(dbFileList)) {
            return;
        }
        iBillFileAttachmentService.lambdaUpdate().in(BillFileAttachment::getFileKey, dbFileList)
                .notIn(BillFileAttachment::getFileKey, currentFileList)
                .set(BillFileAttachment::getDeleted, Boolean.TRUE).update();
    }

    private void addBusinessInfo(List<String> currentFileKeyList, FileBusinessTypeEnum businessType, Long tableId) {
        if (FileBusinessTypeEnum.INVOICE.equals(businessType)) {
            EstimateBill estimateBill = iEstimateBillService.getById(tableId);
            List<BillFileAttachment> files = iBillFileAttachmentService.lambdaQuery().in(BillFileAttachment::getFileKey, currentFileKeyList).eq(BillFileAttachment::getBusinessType, businessType).list();
            Map<String, String> fileMap = files.stream().collect(Collectors.toMap(BillFileAttachment::getFileKey, BillFileAttachment::getSuffix));
            // 发票名称保存为：客户名称+账期+“发票”
            String name = "{}-{}-发票{}.{}";
            for (int i = 0; i < currentFileKeyList.size(); i++) {
                iBillFileAttachmentService.lambdaUpdate().eq(BillFileAttachment::getFileKey, currentFileKeyList.get(i))
                        .eq(BillFileAttachment::getBusinessType, businessType)
                        .set(BillFileAttachment::getTableId, tableId)
                        .set(BillFileAttachment::getOriginalName, CharSequenceUtil.format(name, estimateBill.getCustomerName(), estimateBill.getPaymentPeriod(), i + 1, fileMap.get(currentFileKeyList.get(i))))
                        .update();
            }
        } else {
            iBillFileAttachmentService.lambdaUpdate().in(BillFileAttachment::getFileKey, currentFileKeyList)
                    .eq(BillFileAttachment::getBusinessType, businessType)
                    .set(BillFileAttachment::getTableId, tableId).update();
        }
    }


    private AttachmentVO upload(MultipartFile file, Integer businessType) {
        //按业务类型建文件夹
        BaseResult<AttachmentVO> uploadResult = attachmentClient.upload(file, 0L, null,
                FileBusinessTypeEnum.getFileTypeDesc(businessType),
                UserContext.getCurrentUserName());
        if (Objects.isNull(uploadResult) || Objects.isNull(uploadResult.getData()) ||
                //上传失败
                2 == uploadResult.getData().getFileStatus()) {
            throw new BusinessException(ExceptionCodes.FILE_UPLOAD_FAILED);
        }
        return uploadResult.getData();
    }

    @Override
    public AttachmentVO uploadAttachment(MultipartFile file, Integer businessType) {
        return upload(file, businessType);
    }


    /**
     * Title: 上传Excel<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/12 10:56 <br>
     *
     * @param wb         excel数据
     * @param fileName   excel文件名
     * @param fileBusinessTypeEnum 文件业务类型
     */
    public Long uploadExcel(FileBusinessTypeEnum fileBusinessTypeEnum, String fileName, Workbook wb) {
        Assert.notNull(fileBusinessTypeEnum, fileName, wb);
        MultipartFile file = workbookToCommonsMultipartFile(wb, fileName);
        AttachmentVO attachmentVO = uploadAttachment(file, fileBusinessTypeEnum.getFileType());
        return attachmentVO.getId();
    }

    @Override
    public Long uploadExcelOutputStream(FileBusinessTypeEnum fileBusinessTypeEnum, String errorFileName, ByteArrayOutputStream outputStream) {
        // 判断上传文件是否为空
        if (null == outputStream ) {
            throw new GenericException(BusinessMessageEnum.MINIO_UPLOAD_FILE_NULL.getCode(),
                    BusinessMessageEnum.MINIO_UPLOAD_FILE_NULL.getMsg());
        }
        // 新的文件名 = 存储桶名称_时间戳.后缀名
        errorFileName = errorFileName.substring(0, errorFileName.lastIndexOf(".")) + "_" + DateUtils.formatToLong(new Date()) + errorFileName.substring(errorFileName.lastIndexOf("."));
        MultipartFile file = getFileFromOutputStream(errorFileName, outputStream);
        // 开始上传
        AttachmentVO attachmentVO = uploadAttachment(file, fileBusinessTypeEnum.getFileType());
        return attachmentVO.getId();
    }

    private MultipartFile getFileFromOutputStream(String fileName, ByteArrayOutputStream outputStream) {
        // 文件名必须带上扩展名
        String originalFileName = fileName.endsWith(".xlsx") ? fileName : fileName + ".xlsx";
        return new MockMultipartFile(
                "file",                         // 表单字段名
                originalFileName,               // 原始文件名
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // MIME 类型
                outputStream.toByteArray()      // 文件内容
        );
    }


    /**
     * Title: workbook转换成MultipartFile<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/10/12 10:54 <br>
     *
     * @param  workbook  workBook
     * @param  fileName  文件名
     */
    private MultipartFile workbookToCommonsMultipartFile(Workbook workbook, String fileName) {
        //Workbook转FileItem
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem fileItem = factory.createItem("textField", "text/plain", true, fileName);
        try {
            OutputStream os = fileItem.getOutputStream();
            workbook.write(os);
            os.close();
            //FileItem转MultipartFile
            return new CommonsMultipartFile(fileItem);
        } catch (Exception e) {
            log.error("文件转换失败！fileName = {}", fileName, e);
        }
        return null;
    }

    @Override
    public <T> Long uploadExcel(FileBusinessTypeEnum fileBusinessTypeEnum,List<T> data, String fileName, Class<T> clazz) {
        Assert.notNull(data, fileName);
        MultipartFile file = generateExcelFile(data,fileName,clazz);
        AttachmentVO attachmentVO = uploadAttachment(file, fileBusinessTypeEnum.getFileType());
        return attachmentVO.getId();
    }

    public static <T> MultipartFile generateExcelFile(List<T> data, String fileName, Class<T> clazz) {
        // 1. 创建内存输出流
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 2. 使用 EasyExcel 写入数据
            ExcelWriter excelWriter = EasyExcelFactory.write(outputStream, clazz).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet("Sheet1").build();
            excelWriter.write(data, writeSheet);
            // 3. 必须显式关闭 writer 才能完成写入
            excelWriter.finish();
            // 4. 创建 MultipartFile
            return new MockMultipartFile(
                    fileName + EXCEL_SUFFIX,             // 文件名
                    fileName + EXCEL_SUFFIX,             // 原始文件名
                    "application/vnd.ms-excel",     // Content-Type
                    outputStream.toByteArray()       // 文件内容
            );
        } catch (IOException e) {
            log.error("生成 Excel文件失败", e);
            throw new BusinessException(ExceptionCodes.GET_EXCEL_ERROR);
        }
    }
}
