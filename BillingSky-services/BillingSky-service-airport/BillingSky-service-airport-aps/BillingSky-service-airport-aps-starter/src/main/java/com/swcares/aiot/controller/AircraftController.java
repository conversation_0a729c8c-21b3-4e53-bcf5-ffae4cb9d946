package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.AircraftForm;
import com.swcares.aiot.core.form.AircraftImportForm;
import com.swcares.aiot.core.model.entity.AircraftInfo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.AircraftService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;


/**
 * ClassName：AircraftController <br>
 * Description：飞机信息接口<br>
 * Copyright © 2020-5-14 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-5-14 15:48<br>
 * @version v1.0 <br>
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/aircraft")
@Api(value = "AircraftController", tags = {"飞机操作接口"})
@Slf4j
public class AircraftController  {
    @Resource
    private AircraftService aircraftService;

    @ApiOperation(value = "新增飞机信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/saveAircraft")
    public Object saveAircraft(@RequestBody @ApiParam(name = "aircraftForm", value = "飞机信息") @Valid AircraftForm aircraftForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return aircraftService.saveAircraft(aircraftForm, user);
    }

    @ApiOperation(value = "分页查询飞机信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airlineShortName", value = "航司简称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "regNo", value = "飞机注册号", dataType = "String", paramType = "query"),
    })
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AircraftInfo.class)})
    @ApiParam(name = "pageParam", value = "分页信息")
    @GetMapping(value = "/pageAircraftByCondition")
    public Object pageAircraftByCondition(@Validated PageParam pageParam,
                                          String airlineCode,
                                          String airlineShortName,
                                          String regNo) {
        return aircraftService.pageAircraftByCondition(airlineCode, airlineShortName, regNo, pageParam);
    }

    @ApiOperation(value = "批量删除飞机信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "airlineCode", value = "航司二字码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airlineShortName", value = "航司简称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "regNo", value = "飞机注册号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ids", value = "排除的飞机id", dataType = "String", paramType = "query"),
    })
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/deleteAircraftByCondition")
    public Object deleteAircraftByCondition(@RequestBody Map<String, String> data) {
        String airlineCode = data.get("airlineCode");
        String airlineShortName = data.get("airlineShortName");
        String regNo = data.get("regNo");
        String ids = data.get("ids");
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return aircraftService.deleteAircraftByCondition(airlineCode, airlineShortName, regNo, ids, user);
    }

    @ApiOperation(value = "修改飞机信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/updateAircraft")
    public Object updateAircraft(@RequestBody @ApiParam(name = "airrcaftForm", value = "飞机信息") @Validated({Update.class}) AircraftForm aircraftForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return aircraftService.updateAircraft(aircraftForm, user);
    }

    @ApiOperation(value = "导入飞机信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/importAircraft")
    public Object importAircraft(
            @RequestParam("file") @ApiParam(name = "file", value = "上传的飞机信息") MultipartFile file,
            @Validated AircraftImportForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return aircraftService.importAircraft(file, form.getAirportCode(), form.getSettlementStartDate(), form.getSettlementEndDate(), user);
    }

    @ApiOperation(value = "根据飞机id删除飞机信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @DeleteMapping(value = "/delAircraftInfo")
    public Object delAircraftInfo(@RequestParam @ApiParam(name = "aircraftIds", value = "飞机ID") String aircraftIds) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return aircraftService.delAircraftInfo(aircraftIds, user);
    }

    @ApiOperation(value = "根据飞机id查询飞机信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AircraftInfo.class)})
    @GetMapping(value = "/getAircraftInfoById")
    public Object getAircraftInfoById(@RequestParam @ApiParam(name = "aircraftId", value = "飞机id") String aircraftId) {
        return aircraftService.getAircraftInfoById(aircraftId);
    }

    @ApiOperation(value = "根据航班日期重新结算")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/doReSettlementByFlightDate")
    public Object doReSettlementByFlightDate(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                                             @RequestParam @ApiParam(name = "startDate", value = "开始日期") @DateTimeFormat(pattern
                                                     = "yyyy-MM-dd") Date startDate,
                                             @RequestParam @ApiParam(name = "endDate", value = "结束日期") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return aircraftService.doReSettlementByFlightDate(airportCode, startDate, endDate, user);
    }

    @ApiOperation(value = "模糊匹配飞机注册号")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AircraftInfo.class)})
    @GetMapping(value = "/findByRegnoLike")
    public Object findByRegnoLike(@RequestParam @ApiParam(name = "regNo", value = "飞机注册号") String regNo) {
        return aircraftService.findByRegnoLike(regNo);
    }

    @ApiOperation(value = "查询未存储在机号表的航班机号")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/findInvalidRegon")
    public Object findInvalidRegon() {
        return aircraftService.findInvalidRegon();
    }

    @ApiOperation(value = "模糊查询获取机型列表")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping(value = "/findAirplaneModelList")
    public Object findAirplaneModelList(String airplaneModel) {
        return aircraftService.findAirplaneModelList(airplaneModel);
    }

    @ApiOperation(value = "模糊查询去重机号")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AircraftInfo.class)})
    @GetMapping(value = "/findDistinctRegno")
    public Object findDistinctRegno(@RequestParam @ApiParam(name = "regNo", value = "飞机注册号") String regNo) {
        return aircraftService.findDistinctRegno(regNo);
    }

}
