package com.swcares.aiot.service;

import com.swcares.aiot.core.model.entity.AirportThreeCharCode;
import com.swcares.aiot.core.model.entity.SubsidyParam;
import com.swcares.aiot.core.model.vo.SubsidyFormulaVo;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.form.ParamForm;
import com.swcares.aiot.core.form.SubsidyFormulaSaveForm;
import com.swcares.aiot.core.form.SubsidyFormulaUpdateForm;
import com.swcares.aiot.core.form.SubsidyParamSaveForm;

import java.util.List;

/**
 * ClassName：com.swcares.service.SubsidyFormulaService
 * Description：补贴公式service接口
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/2 10:08
 * @version v1.0
 */
public interface SubsidyFormulaService {

    List<SubsidyFormulaVo> getFormulas(String flightLineId, String airportCode);

    void saveFormula(SubsidyFormulaSaveForm form , LoginUserDetails user);

    void deleteFormula(String id, LoginUserDetails user);

    void updateFormula(SubsidyFormulaUpdateForm form, LoginUserDetails user);

    List<SubsidyParam> getParamList(String airportCode);

    List<AirportThreeCharCode> getAirportCodeList(String selectAirportCode);

    void completeParam(List<ParamForm> paramList, LoginUserDetails user);

    void insertParam(SubsidyParamSaveForm form, LoginUserDetails user);

}
