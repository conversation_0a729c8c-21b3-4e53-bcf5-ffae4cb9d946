package com.swcares.aiot.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.excel.EasyExcelDataListener;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.CollectorsUtils;
import com.swcares.aiot.core.common.util.CsvUtils;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.ExcelUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.core.common.util.SsssUtil;
import com.swcares.aiot.core.common.util.ThreadUtil;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.TFlightBill;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.FlightBillForm;
import com.swcares.aiot.core.form.FlightBillRefuseForm;
import com.swcares.aiot.core.form.ReCalcForm;
import com.swcares.aiot.core.form.SubmitForm;
import com.swcares.aiot.core.mapstruct.MsFlightBillImport;
import com.swcares.aiot.core.model.dto.FlightBillExportDto;
import com.swcares.aiot.core.model.dto.FlightBillImportDto;
import com.swcares.aiot.core.model.dto.FlightInfoDto;
import com.swcares.aiot.core.model.entity.FlightBill;
import com.swcares.aiot.core.model.entity.FlightBillHistory;
import com.swcares.aiot.core.model.entity.ServiceRecord;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.model.vo.AirlineRelatedFeeNewVo;
import com.swcares.aiot.core.model.vo.AirlineRelatedFeeVo;
import com.swcares.aiot.core.model.vo.FlightBillCsvVo;
import com.swcares.aiot.core.model.vo.FlightBillHistoryVo;
import com.swcares.aiot.core.model.vo.FlightBillPageVo;
import com.swcares.aiot.core.model.vo.FlightBillSubmitVo;
import com.swcares.aiot.core.model.vo.ServiceRecordPDFVo;
import com.swcares.aiot.core.model.vo.SubmitVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.core.service.ITFlightBillService;
import com.swcares.aiot.dao.FlightBillDao;
import com.swcares.aiot.dao.FlightBillHistoryDao;
import com.swcares.aiot.dao.FlightInfoDao;
import com.swcares.aiot.dao.ServiceRecordDao;
import com.swcares.aiot.service.AirlineBillNewService;
import com.swcares.aiot.service.FlightBillHistoryService;
import com.swcares.aiot.service.FlightBillService;
import com.swcares.aiot.service.LogService;
import com.swcares.aiot.service.ReCalcService;
import com.swcares.aiot.service.UploadStatusService;
import com.swcares.aiot.statemachine.StatemachineTemplate;
import com.swcares.aiot.statemachine.biz.events.EnumRevocationStatusChangeEvent;
import com.swcares.aiot.statemachine.biz.status.EnumBillStatus;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.worm.hutool.HttpUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.swcares.aiot.statemachine.biz.status.EnumBillStatus.AWAIT_EXAMINE;
import static com.swcares.aiot.statemachine.biz.status.EnumBillStatus.CONFIRMED;
import static com.swcares.baseframe.common.cons.CommonErrors.DELETE_ERROR;
import static com.swcares.baseframe.common.cons.CommonErrors.PARAM_VALUE_INVALID_ERROR;
import static com.swcares.baseframe.common.cons.CommonErrors.QUERY_ERROR;

/**
 * ClassName：FlightBillServiceImpl <br>
 * Description：(航班明细账单业务逻辑处理)<br>
 * Copyright © 2020/6/17 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/17 18:56<br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class FlightBillServiceImpl implements FlightBillService {
    @Resource
    private FlightBillDao flightBillDao;
    @Resource
    private LogService logService;
    @Resource
    private ServiceRecordDao serviceRecordDao;
    @Resource
    private ReCalcService reCalcService;
    @Resource
    private AirlineBillNewService airlineBillNewService;
    @Resource
    private SsssUtil ssssUtil;
    @Resource
    private UploadStatusService uploadStatusService;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private FlightBillHistoryDao flightBillHistoryDao;
    @Resource
    private StatemachineTemplate<TFlightBill> statemachineTemplate;
    @Resource
    private ITFlightBillService itFlightBillService;
    @Resource
    private FlightBillHistoryService flightBillHistoryService;
    @Resource
    private MsFlightBillImport msFlightBillImport;

    private static final String BILLS = "bills";

    private static final String POWERCAR = "POWERCAR";

    private static final String INFLATION = "INFLATION";

    @Override
    public ResultBuilder<Map<String, Object>> pageFlightBillInfoByCondition(PageParam pageParam, FlightBillForm flightBillForm) {
        Map<String, Object> rm = this.pagelightBillInfo(flightBillForm, pageParam);
        List<FlightBill> bills = castList(rm.get(BILLS),FlightBill.class);
        if (CollUtil.isEmpty(bills)) {
            return new ResultBuilder.Builder<Map<String, Object>>().builder();
        }

        //查 ServiceRecord 获取签单 和 用量
        List<String> fltIds = bills.stream().map(FlightBill::getFlightId).distinct().collect(Collectors.toList());
        List<ServiceRecord> srList = serviceRecordDao.listServiceRecordByFlightIdList(fltIds);
        Map<String, List<ServiceRecord>> srMap = srList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId));

        Set<String> srSet = new HashSet<>();
        List<FlightBillPageVo> voList = new ArrayList<>();
        //修改客桥费计价量展示
        for (FlightBill bill : bills) {
            List<ServiceRecord> serviceRecords = srMap.get(bill.getFlightId());
            FlightBillPageVo vo = new FlightBillPageVo();
            BeanUtils.copyProperties(bill, vo);
            handleServiceRecords(bill, serviceRecords, vo, srSet);
            //设置国航计价量为3位，其他为2位
            setPricingAmount(vo,FlightBillPageVo::getFlightNo,FlightBillPageVo::getFeeCode,
                    FlightBillPageVo::setPricingAmount,FlightBillPageVo::getPricingAmount);

            //设置金额与单价为两位小数
            vo.setChargePrice(vo.getChargePrice().setScale(2, RoundingMode.HALF_UP));
            vo.setUnitPrice(vo.getUnitPrice().setScale(2, RoundingMode.HALF_UP));

            voList.add(vo);
        }
        rm.put(BILLS, voList);
        return new ResultBuilder.Builder<Map<String, Object>>().data(rm).builder();
    }

    private void handleServiceRecords(FlightBill bill, List<ServiceRecord> serviceRecords, FlightBillPageVo vo, Set<String> srSet) {
        //设置单据
        if (CollUtil.isNotEmpty(serviceRecords)) {
            Optional<ServiceRecord> any = serviceRecords.stream().filter(v -> StringUtils.isNotBlank(v.getSignPdfUrl()) && v.getServiceCode().equals(bill.getIndicatorCode())).findAny();
            any.ifPresent(serviceRecord -> vo.setSignPdfUrl(serviceRecord.getSignPdfUrl()));

            handlePowercar(vo, serviceRecords);
            handleInflation(vo, serviceRecords);
            handlePassengerrBidgeFeeOrCbt(bill, serviceRecords, vo, srSet);
        }
    }

    private void handlePassengerrBidgeFeeOrCbt(FlightBill bill, List<ServiceRecord> serviceRecords, FlightBillPageVo vo, Set<String> srSet) {
        if ("CBT".equals(bill.getServiceRecord()) || "客桥费".equals(vo.getFeeName())) {
            for (ServiceRecord sr : serviceRecords) {
                Date starDate = sr.getStartTime();
                Date endDate = sr.getEndTime();
                //计算使用时长，与费用中的时长进行匹配
                Double stayTime = getStayTime(sr, starDate, endDate, srSet);
                BigDecimal pa = vo.getChargePrice().divide(BigDecimal.valueOf(200), RoundingMode.UP);
                if(stayTime != null && BigDecimal.valueOf(stayTime).compareTo(pa) == 0) {
                    //设置客桥费时，单价与计价量问题
                    vo.setPricingAmount(pa);
                    vo.setUnitPrice(BigDecimal.valueOf(200));
                    srSet.add(sr.getId());
                    break;
                }
            }
        }
    }

    private Double getStayTime(ServiceRecord sr, Date starDate, Date endDate, Set<String> srSet) {
        double stayTime = starDate == null ? 0 : DateUtils.calculateLengthOfTime(sr.getServiceCode(), starDate, endDate);
        if (srSet.contains(sr.getId()) || stayTime == 0) {
            return null;
        } else if (stayTime < 1) {
            stayTime = 1;
        } else {
            stayTime = Math.ceil(stayTime * 2) / 2;
        }
        return stayTime;
    }

    private void handleInflation(FlightBillPageVo vo, List<ServiceRecord> serviceRecords) {
        if (INFLATION.equals(vo.getFeeCode())) {
            Optional<ServiceRecord> first = serviceRecords.stream().filter(v -> "CSUF".equals(v.getServiceCode())).findFirst();
            if (first.isPresent()) {
                vo.setPricingAmount(BigDecimal.valueOf(first.get().getUsedNumber()));
                vo.setUnitPrice(vo.getChargePrice().divide(vo.getPricingAmount(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    private void handlePowercar(FlightBillPageVo vo, List<ServiceRecord> serviceRecords) {
        if (POWERCAR.equals(vo.getFeeCode())) {
            Optional<ServiceRecord> first = serviceRecords.stream().filter(v -> "CPUF".equals(v.getServiceCode())).findFirst();
            if (first.isPresent()) {
                vo.setPricingAmount(BigDecimal.valueOf(first.get().getUsedNumber()));
                vo.setUnitPrice(vo.getChargePrice().divide(vo.getPricingAmount(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    private Map<String, String> getPdfUrlReflection(List<FlightBill> bills) {
        Map<String, String> pdfMap = new HashMap<>();
        List<String> serviceCode = bills.stream()
                .map(FlightBill::getIndicatorCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(serviceCode)) {
            List<String> flightIdList = bills.stream()
                    .map(FlightBill::getFlightId)
                    .distinct()
                    .collect(Collectors.toList());
            List<ServiceRecordPDFVo> serviceRecordPdfVos = serviceRecordDao.listByFlightIdAndServiceCode(flightIdList, serviceCode);
            pdfMap = serviceRecordPdfVos.stream().collect(Collectors.toMap(k -> k.getFlightId() + k.getServiceCode(), v -> StringUtils.isNotBlank(v.getSignPdfUrl()) ? v.getSignPdfUrl() : ""));
        }
        return pdfMap;
    }

    private Map<String, Object> pagelightBillInfo(FlightBillForm flightBillForm, PageParam pageParam) {
        String airportCode = flightBillForm.getAirportCode();
        Date startDate = flightBillForm.getStartDate();
        Date endDate = flightBillForm.getEndDate();
        String fromAirportCode = flightBillForm.getFromAirportCode();
        String toAirportCode = flightBillForm.getToAirportCode();
        String flightNo = flightBillForm.getFlightNo();
        String choosedFeeInfo = flightBillForm.getChoosedFeeInfo();
        List<String> choseFeeInfos = new ArrayList<>();
        if (StringUtils.isNotBlank(choosedFeeInfo)) {
            choseFeeInfos.addAll(Arrays.asList(choosedFeeInfo.split(",")));
        }
        String settleCode = flightBillForm.getSettleCode();
        List<String> settleCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(settleCode)) {
            settleCodeList.addAll(Arrays.asList(settleCode.split(",")));
        }

        this.setFlightTimeDefaultRange(flightBillForm);

        //查询count
        int count = flightBillDao.getBillCountByCondition(airportCode, startDate, endDate,
                flightBillForm.getFlightTimeStartDate(), flightBillForm.getFlightTimeEndDate(),
                fromAirportCode, toAirportCode, flightNo, choseFeeInfos, settleCodeList, flightBillForm.getRegNo(),
                flightBillForm.getFlightFlag(), flightBillForm.getSubmit(), flightBillForm.getRevocation());

        Map<String, Object> rm = new HashMap<>();
        rm.put("count", count);

        //查询list
        List<FlightBill> billList = flightBillDao.pageFlightBillInfoByCondition(airportCode,
                startDate, endDate, flightBillForm.getFlightTimeStartDate(), flightBillForm.getFlightTimeEndDate(),
                fromAirportCode, toAirportCode, flightNo, choseFeeInfos, settleCodeList,
                flightBillForm.getRegNo(), flightBillForm.getFlightFlag(), flightBillForm.getSubmit(), flightBillForm.getRevocation(),
                (pageParam.getPage() - 1) * pageParam.getLimit(), pageParam.getLimit());
        rm.put(BILLS, billList);
        return rm;
    }

    private void setFlightTimeDefaultRange(FlightBillForm flightBillForm) {
        //如果航班日期和起降日期都是空的则默认起降日期当前前一个月的
        if ((flightBillForm.getStartDate() == null || flightBillForm.getEndDate() == null)
                && (flightBillForm.getFlightTimeStartDate() == null || flightBillForm.getFlightTimeEndDate() == null)) {
            flightBillForm.setFlightTimeEndDate(new Date());
            Calendar calendar = CalendarUtil.calendar(DateUtil.beginOfDay(new Date()));
            calendar.add(Calendar.MONTH, -1);
            flightBillForm.setFlightTimeStartDate(calendar.getTime());
        }
    }

    /**
     * 航班明细账单动态条件查询
     *
     * @param flightBillForm :
     * @return :
     */
    private List<FlightBill> getFlightBillInfoList(FlightBillForm flightBillForm) {
        String airportCode = flightBillForm.getAirportCode();
        String choosedFeeInfo = flightBillForm.getChoosedFeeInfo();
        Date startDate = flightBillForm.getStartDate();
        Date endDate = flightBillForm.getEndDate();
        String fromAirportCode = flightBillForm.getFromAirportCode();
        String toAirportCode = flightBillForm.getToAirportCode();
        String flightNo = flightBillForm.getFlightNo();
        String settleCode = flightBillForm.getSettleCode();
        List<String> settleCodeList = new ArrayList<>();
        if (settleCode == null) {
            settleCodeList.add("");
        } else {
            settleCodeList.addAll(Arrays.asList(settleCode.split(",")));
        }
        List<String> choseFeeInfos = new ArrayList<>();
        if (CharSequenceUtil.isBlank(choosedFeeInfo)) {
            choseFeeInfos.add("");
        } else {
            choseFeeInfos.addAll(Arrays.asList(choosedFeeInfo.split(",")));
        }
        this.setFlightTimeDefaultRange(flightBillForm);
        /*
         * 查询list
         */
        return flightBillDao.listFlightBillInfoByCondition(airportCode,
                startDate, endDate, flightBillForm.getFlightTimeStartDate(), flightBillForm.getFlightTimeEndDate(),
                fromAirportCode, toAirportCode, flightNo, choseFeeInfos, settleCodeList,
                flightBillForm.getRegNo(), flightBillForm.getFlightFlag(),
                flightBillForm.getSubmit());
    }


    @Override
    public ResultBuilder<BigDecimal> countTotal(FlightBillForm flightBillForm) {
        List<FlightBill> resultList = this.getFlightBillInfoList(flightBillForm);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ResultBuilder.Builder<BigDecimal>().data(BigDecimal.ZERO).builder();
        }
        //修改客桥费计价量展示
        for (FlightBill fb : resultList) {
            //设置客桥费的使用时间
            if ((fb.getServiceRecord() != null && "CBT".equals(fb.getServiceRecord())) || (fb.getFeeName() != null && "客桥费".equals(fb.getFeeName()))) {
                //设置客桥费时，单价与计价量问题
                fb.setPricingAmount(fb.getChargePrice().divide(BigDecimal.valueOf(200), RoundingMode.HALF_UP));
                fb.setUnitPrice(BigDecimal.valueOf(200));
            }

            //设置国航计价量为3位，其他为2位
            setPricingAmount(fb,FlightBill::getFlightNo,FlightBill::getFeeCode
                    ,FlightBill::setPricingAmount,FlightBill::getPricingAmount);

            //设置金额与单价为两位小数
            fb.setChargePrice(fb.getChargePrice().setScale(2, RoundingMode.HALF_UP));
            fb.setUnitPrice(fb.getUnitPrice().setScale(2, RoundingMode.HALF_UP));
        }
        BigDecimal chargePrice = resultList.stream().collect(CollectorsUtils.summingBigDecimal(FlightBill::getChargePrice));
        return new ResultBuilder.Builder<BigDecimal>().data(chargePrice).builder();
    }

    @Override
    public void exportFlightBillInfo(FlightBillForm flightBillForm, HttpServletResponse response, LoginUserDetails user, String urlName) {
        log.info("开始导出航班明细账单，导出查询参数为：{}", flightBillForm);
        this.setFlightTimeDefaultRange(flightBillForm);
        //将开始日期的月份作为文件名的结算月份
        String settleMonth = FormatUtils.formatDateToMonth(flightBillForm.getStartDate() == null ? flightBillForm.getFlightTimeStartDate() : flightBillForm.getStartDate());
        //设置文件名
        String fileName = FormatUtils.formatDateToDay(new Date()) + "_APT_A_" + flightBillForm.getAirportCode() + "_TLJ_" + settleMonth;
        //设置 header
        FileUtils.setCsvResponseHeader(fileName, response);
        //获取数据
        List<FlightBill> flightBillList = this.getFlightBillInfoList(flightBillForm);
        //数据转换
        List<FlightBillCsvVo> flightBillCsvVoList = new ArrayList<>();
        //匹配过的特车设备id集合，防止重复
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");


        List<String> powercarFlightIdList = flightBillList.stream()
                .filter(fb -> (POWERCAR.equals(fb.getServiceRecord())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        Map<String, List<ServiceRecord>> powercarSrMap = getListMap(flightBillForm, powercarFlightIdList);

        List<String> inflationFlightIdList = flightBillList.stream()
                .filter(fb -> (INFLATION.equals(fb.getServiceRecord())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        Map<String, List<ServiceRecord>> inflationSrMap = getMap(flightBillForm, inflationFlightIdList);

        for (FlightBill flightBill : flightBillList) {
            FlightBillCsvVo data = new FlightBillCsvVo();

            Date flightTime = flightBill.getFlightTime();
            String flightFlag = flightBill.getFlightFlag();

            data.setOriginalId(null);
            data.setAccountAirportCode(flightBill.getAirportCode());
            data.setBusinessAirportCode(flightBill.getAirportCode());
            data.setPayAirlineCode(flightBill.getSettleCode());
            data.setBusinessAirlineCode(flightBill.getSettleCode());
            data.setFlightDate(FormatUtils.formatDateToDay(flightBillForm.getDateType() == 1 ? flightBill.getFlightDate() : flightBill.getFlightTime()));
            data.setFlightNo(flightBill.getFlightNo());
            data.setRegNo(flightBill.getRegNo());
            data.setFlightModel(flightBill.getFlightModel());
            data.setFlightLine(flightBill.getFlightLine());
            handleDom(flightBill, data);
            data.setDaAirportCode(flightBill.getAirportCode());
            String serviceRecord = flightBill.getServiceRecord();
            data.setStartTime(null);
            data.setEndTime(null);
            data.setPricingAmount(flightBill.getPricingAmount());
            data.setUnitPrice(flightBill.getUnitPrice());
            if(flightBill.getServiceStartTime()!=null) {
                data.setStartTime(sdf.format(flightBill.getServiceStartTime()));
            }
            if(flightBill.getServiceEndTime()!=null) {
                data.setEndTime(sdf.format(flightBill.getServiceEndTime()));
            }
            //处理客梯车单价和位数，并判断客梯车使用时间
            handleCbt(flightBill, serviceRecord, data);
            //设置气源车和电源车的计价量为业务保障数据的使用量
            handlePowInf(flightBill, powercarSrMap, data, inflationSrMap);

            //设置国航计价量为3位，其他为2位
            setPricingAmount(flightBill,FlightBill::getFlightNo,FlightBill::getFeeCode
                    ,FlightBill::setPricingAmount,FlightBill::getPricingAmount);

            //设置金额为两位小数
            data.setUnitPrice(data.getUnitPrice().setScale(2, RoundingMode.HALF_UP));
            data.setChargePrice(flightBill.getChargePrice().setScale(2, RoundingMode.HALF_UP));

            data.setFlightTime(FormatUtils.formatDateTimeToMinute(flightTime));
            data.setFlightFlag(flightFlag);
            String tf = "W/Z";
            data.setTaskFlag(tf);
            data.setFlightStatus(CommonConstants.FLIGHT_STATUS_MAP.get(tf));
            data.setFeeCode(flightBill.getFeeCode());
            data.setFeeName(flightBill.getFeeName());
            data.setOriginalBillNo(null);
            data.setRefuseAmount(null);
            data.setAdjustAmount(null);
            flightBillCsvVoList.add(data);
        }
        String[] head = getHeaders();

        //字段名
        List<String> fieldNameList = new ArrayList<>();
        Field[] fields = FlightBillCsvVo.class.getDeclaredFields();
        for (Field field : fields) {
            fieldNameList.add(field.getName());
        }
        String[] fieldArray = fieldNameList.toArray(new String[0]);
        try {
            ServletOutputStream csvResult = response.getOutputStream();
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + URLUtil.encode(fileName, StringUtil.UTF8) + ".csv");
            List<Object[]> objects = CsvUtils.getData(flightBillCsvVoList, fieldArray, FlightBillCsvVo.class);
            for (int i = 0; i < objects.size(); i++) {
                objects.get(i)[0] = i + 2;
            }
            CsvUtils.simpleExport(true, "\n", head, objects, fileName, csvResult);

        } catch (IOException e) {
            throw new GenericException(BusinessMessageEnum.IO_ERROR.getCode(), BusinessMessageEnum.IO_ERROR.getMsg());
        }
        String content = Constants.LogEnum.LOG_MEG_1.getValue()
                + urlName + Constants.LogEnum.LOG_MEG_EXPORT.getValue();
        logService.addLogForExport(user, flightBillForm, content, flightBillForm.getAirportCode(), "航班明细账单");
    }

    private void handleDom(FlightBill flightBill, FlightBillCsvVo data) {
        if (flightBill.getFlightLineType() != null && "D".equals(flightBill.getFlightLineType())) {
            data.setFlightLineType("DOM");
        } else {
            data.setFlightLineType(flightBill.getFlightLineType());
        }
        data.setFlightSegment(flightBill.getFlightSegment());
        if (flightBill.getFlightSegmentType() != null && "D".equals(flightBill.getFlightSegmentType())) {
            data.setFlightSegmentType("DOM");
        } else {
            data.setFlightSegmentType(flightBill.getFlightSegmentType());
        }
    }

    private void handleCbt(FlightBill flightBill, String serviceRecord, FlightBillCsvVo data) {
        //设置客桥费的使用时间
        if (("CBT".equals(serviceRecord)) || ("客桥费".equals(flightBill.getFeeName()))) {

            BigDecimal pa = flightBill.getChargePrice().divide(BigDecimal.valueOf(200), RoundingMode.UP);
            //设置客桥费时，单价与计价量问题
            data.setPricingAmount(pa);
            data.setUnitPrice(BigDecimal.valueOf(200));
        }
    }

    private String[] getHeaders() {
        // 表头
        return new String[]{
                //机场原始数据ID （空）
                Constants.FlightBillEnum.ORIGINAL_ID.getValue(),
                //开账机场代码
                Constants.FlightBillEnum.ACCOUNT_AIRPORT_CODE.getValue(),
                //实际运营机场代码
                Constants.FlightBillEnum.BUSINESS_AIRPORT_CODE.getValue(),
                //付款航空公司代码
                Constants.FlightBillEnum.PAY_AIRLINE_CODE.getValue(),
                //实际运营航空公司代码
                Constants.FlightBillEnum.BUSINESS_AIRLINE_CODE.getValue(),
                //航班日期
                Constants.FlightBillEnum.FLIGHT_DATE.getValue(),
                //航班号
                Constants.FlightBillEnum.FLIGHT_NO.getValue(),
                //飞机号
                Constants.FlightBillEnum.REG_NO.getValue(),
                //机型
                Constants.FlightBillEnum.FLIGHT_MODEL.getValue(),
                //航线
                Constants.FlightBillEnum.FLIGHT_LINE.getValue(),
                //航线性质
                Constants.FlightBillEnum.FLIGHT_LINE_TYPE.getValue(),
                //航段
                Constants.FlightBillEnum.FLIGHT_SEGMENT.getValue(),
                //航段性质
                Constants.FlightBillEnum.FLIGHT_SEGMENT_TYPE.getValue(),
                //发生机场
                Constants.FlightBillEnum.DA_AIRPORT_CODE.getValue(),
                //开始时间  (空)
                Constants.FlightBillEnum.START_TIME.getValue(),
                //结束时间 (空)
                Constants.FlightBillEnum.END_TIME.getValue(),
                //起降时间
                Constants.FlightBillEnum.FLIGHT_TIME.getValue(),
                //起降标志
                Constants.FlightBillEnum.FLIGHT_FLAG.getValue(),
                //任务性质
                Constants.FlightBillEnum.TASK_FLAG.getValue(),
                //航班状态
                Constants.FlightBillEnum.FLIGHT_STATUS.getValue(),
                //费用名称
                Constants.FlightBillEnum.FEE_CODE.getValue(),
                //计价量
                Constants.FlightBillEnum.PRICING_AMOUNT.getValue(),
                //单价
                Constants.FlightBillEnum.UNIT_PRICE.getValue(),
                //金额
                Constants.FlightBillEnum.CHARGE_PRICE.getValue(),
                //备注
                Constants.FlightBillEnum.FEE_NAME.getValue(),
                //原始账单号 （空）
                Constants.FlightBillEnum.ORIGINAL_BILL_NO.getValue(),
                //拒付金额 （空）
                Constants.FlightBillEnum.REFUSE_AMOUNT.getValue(),
                //调整金额 （空）
                Constants.FlightBillEnum.ADJUST_AMOUNT.getValue()
        };
    }

    private void handlePowInf(FlightBill flightBill, Map<String, List<ServiceRecord>> powercarSrMap, FlightBillCsvVo data, Map<String, List<ServiceRecord>> inflationSrMap) {
        if (POWERCAR.equals(flightBill.getFeeCode())) {
            List<ServiceRecord> srList = powercarSrMap.get(flightBill.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                data.setPricingAmount(BigDecimal.valueOf(srList.get(0).getUsedNumber()));
                data.setUnitPrice(flightBill.getChargePrice().divide(data.getPricingAmount(), 2, RoundingMode.HALF_UP));

            }
        }
        if (INFLATION.equals(flightBill.getFeeCode())) {
            List<ServiceRecord> srList = inflationSrMap.get(flightBill.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                data.setPricingAmount(BigDecimal.valueOf(srList.get(0).getUsedNumber()));
                data.setUnitPrice(flightBill.getChargePrice().divide(data.getPricingAmount(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    private Map<String, List<ServiceRecord>> getMap(FlightBillForm flightBillForm, List<String> inflationFlightIdList) {
        List<ServiceRecord> inflationSrList =
                serviceRecordDao.listServiceRecordByFlightIdAndServiceCode(inflationFlightIdList,
                        flightBillForm.getAirportCode(), "CSUF");
        return inflationSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                Collectors.mapping(t -> t, Collectors.toList())));
    }

    private Map<String, List<ServiceRecord>> getListMap(FlightBillForm flightBillForm, List<String> powercarFlightIdList) {
        List<ServiceRecord> cetSrList =
                serviceRecordDao.listServiceRecordByFlightIdAndServiceCode(powercarFlightIdList,
                        flightBillForm.getAirportCode(), "CPUF");
        return cetSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                Collectors.mapping(t -> t, Collectors.toList())));
    }


    @Override
    public ResultBuilder<Object> doReSettlement(String airportCode, String flightNos, String feeCode, Date startDate, Date endDate,
                                                LoginUserDetails user, Integer dateType) {
        List<String> flightNoList = new ArrayList<>();
        //按,分割航班号
        if (!CharSequenceUtil.isEmpty(flightNos)) {
            String[] flightNo = flightNos.split(",");
            Collections.addAll(flightNoList, flightNo);
        }
        if (flightNoList.size() > 20) {
            throw new GenericException(BusinessMessageEnum.FLIGHTNO_COUNT_TOO_MANY.getCode(), BusinessMessageEnum.FLIGHTNO_COUNT_TOO_MANY.getMsg());
        }
        if (!CharSequenceUtil.isEmpty(airportCode)) {
            ReCalcForm form = new ReCalcForm();
            form.setAirportCode(airportCode);
            form.setFlightNo(flightNos);
            form.setFeeCodes(feeCode);
            form.setStartDate(startDate);
            form.setEndDate(endDate);
            form.setDateType(dateType);
            //调用重新结算
            reCalcService.execute(form, user, TenantHolder.getTenant());
        }
        return new ResultBuilder.Builder<>().builder();
    }

    /**
     * 获取某个机场下的所有航司和费用列表
     */
    @Override
    public ResultBuilder<List<AirlineRelatedFeeNewVo>> listAirlineRelatedFeeInfo(String airportCode) {
        List<AirlineRelatedFeeVo> airlineRelatedFeeVoList = flightBillDao.listAirlineRelatedFeeInfo(airportCode);
        Map<String, List<AirlineRelatedFeeVo>> map =
                airlineRelatedFeeVoList.stream().collect(Collectors.groupingBy(AirlineRelatedFeeVo::getAirlineShortName));
        return new ResultBuilder.Builder<List<AirlineRelatedFeeNewVo>>().data(getAirlineRelatedFeeVoList(map)).builder();
    }

    private List<AirlineRelatedFeeNewVo> getAirlineRelatedFeeVoList(Map<String, List<AirlineRelatedFeeVo>> map) {
        List<AirlineRelatedFeeNewVo> airlineRelatedFeeNewVoList = new ArrayList<>();
        for (Map.Entry<String, List<AirlineRelatedFeeVo>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<AirlineRelatedFeeVo> value = entry.getValue();
            AirlineRelatedFeeNewVo airlineRelatedFeeNewVo = new AirlineRelatedFeeNewVo();
            airlineRelatedFeeNewVo.setAirlineShortName(key);
            airlineRelatedFeeNewVo.setAirlineRelatedFeeVoList(value);
            airlineRelatedFeeNewVoList.add(airlineRelatedFeeNewVo);
        }
        return airlineRelatedFeeNewVoList;
    }

    @Transactional
    @Override
    public ResultBuilder<SubmitVo> submit(SubmitForm form) {
        Date endDate = form.getEndDate();
        endDate = (new DateTime(endDate)).plusHours(23).plusMinutes(59).plusMillis(59).toDate();
        form.setEndDate(endDate);
        List<FlightBill> flightBillList = flightBillDao.getFlightBillsByCondition(form.getAirportCode(), form.getAirlineCode(),
                form.getStartDate(), form.getEndDate(), form.getFeeCode(), form.getDateType());
        return submit(flightBillList, form, false);
    }

    private ResultBuilder<SubmitVo> submit(List<FlightBill> flightBillList, SubmitForm form, boolean refuse) {
        SubmitVo resVo = new SubmitVo();
        if (flightBillList.isEmpty()) {
            return new ResultBuilder.Builder<SubmitVo>().data(resVo).builder();
        }
        Map<String, List<FlightBillSubmitVo>> airlineBillMap = new HashMap<>();
        Set<String> srSet = new HashSet<>();
        Map<String, List<ServiceRecord>> cbtSrMap = new HashMap<>();
        Map<String, List<ServiceRecord>> cetSrMap = new HashMap<>();
        Map<String, List<ServiceRecord>> powercarSrMap = new HashMap<>();
        Map<String, List<ServiceRecord>> inflationSrMap = new HashMap<>();
        List<String> cbtBillFlightIdList = flightBillList.stream()
                .filter(fb -> ("CBT".equals(fb.getServiceRecord())) || ("客桥费".equals(fb.getFeeName())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        cbtSrMap = getStringListMap(form, cbtBillFlightIdList, cbtSrMap);

        List<String> cetBillFlightIdList = flightBillList.stream()
                .filter(fb -> ("CET".equals(fb.getServiceRecord())) || ("客梯车".equals(fb.getFeeName())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        cetSrMap = getStringListMap(form, cbtBillFlightIdList, cetBillFlightIdList, cetSrMap);

        List<String> powercarFlightIdList = flightBillList.stream()
                .filter(fb -> (POWERCAR.equals(fb.getServiceRecord())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        powercarSrMap = getListMap(form, cbtBillFlightIdList, powercarFlightIdList, powercarSrMap);

        List<String> inflationFlightIdList = flightBillList.stream()
                .filter(fb -> (INFLATION.equals(fb.getServiceRecord())))
                .map(FlightBill::getFlightId).collect(Collectors.toList());
        inflationSrMap = getMap(form, cbtBillFlightIdList, inflationFlightIdList, inflationSrMap);
        Map<String, String> pdfMap = this.getPdfUrlReflection(flightBillList);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        for (FlightBill fb : flightBillList) {
            FlightBillSubmitVo submitVo = new FlightBillSubmitVo();
            BeanUtils.copyProperties(fb, submitVo);
            String serviceRecord = fb.getServiceRecord();

            handleDom(submitVo);
            handleCbt(fb, serviceRecord, cbtSrMap, srSet, submitVo, sdf);
            handleCet(sdf, cetSrMap, fb, serviceRecord, submitVo);
            handlePowercar(powercarSrMap, fb);
            handleInflation(inflationSrMap, fb);
            //设置国航计价量为3位，其他为2位
            setPricingAmount(submitVo,FlightBillSubmitVo::getFlightNo,FlightBillSubmitVo::getFeeCode
                    ,FlightBillSubmitVo::setPricingAmount,FlightBillSubmitVo::getPricingAmount);
            //设置金额为两位小数
            submitVo.setUnitPrice(submitVo.getUnitPrice().setScale(2, RoundingMode.HALF_UP));
            submitVo.setChargePrice(submitVo.getChargePrice().setScale(2, RoundingMode.HALF_UP));
            if (CharSequenceUtil.isEmpty(submitVo.getTaskFlag())) {
                submitVo.setTaskFlag("W/Z");
            }

            handleFlightBillSubmit(submitVo, pdfMap);

            String airline = fb.getSettleCode();
            List<FlightBillSubmitVo> arlineList = airlineBillMap.getOrDefault(airline, new ArrayList<>());
            arlineList.add(submitVo);
            airlineBillMap.put(airline, arlineList);
        }

        submiteRconciliation(form, refuse, airlineBillMap, resVo);
        return new ResultBuilder.Builder<SubmitVo>().data(resVo).builder();
    }

    private static void handleFlightBillSubmit(FlightBillSubmitVo submitVo, Map<String, String> pdfMap) {
        // 如果存在指标项 根据航班id和指标项取pdf
        if (StringUtils.isNotBlank(submitVo.getIndicatorCode())) {
            List<String> codeList = Arrays.stream(submitVo.getIndicatorCode().split("/")).collect(Collectors.toList());
            codeList.forEach(code -> {
                if (pdfMap.containsKey(submitVo.getFlightId() + code)) {
                    submitVo.setPdfUrl(pdfMap.get(submitVo.getFlightId() + code));
                }
            });
        }
    }

    private void submiteRconciliation(SubmitForm form, boolean refuse, Map<String, List<FlightBillSubmitVo>> airlineBillMap, SubmitVo resVo) {
        int fail = 0;
        int success = 0;
        for (Map.Entry<String, List<FlightBillSubmitVo>> en : airlineBillMap.entrySet()) {
            List<FlightBillSubmitVo> airlineList = en.getValue();
            if (airlineList.isEmpty()) {
                continue;
            }
            try {
                //拒绝处理时账单不更新状态 也不写历史记录（拒绝处理接口单独写了）
                if (!refuse) {
                    //状态修改成待审核
                    airlineList.forEach(vo -> vo.setSubmit(AWAIT_EXAMINE.getStatus().toString()));
                    List<String> ids = airlineList.stream().map(FlightBillSubmitVo::getId).collect(Collectors.toList());
                    flightBillDao.updateFlightBillStatusByIds(ids, AWAIT_EXAMINE.getStatus());
                    flightBillHistoryDao.copyBillHistory(ids, UserContext.getCurrentUserName(), BillOperation.SUBMIT.getCode());
                }
                ssssUtil.send("FLIGHT_BILL", form.getAirportCode(), Collections.singletonList(en.getKey()), airlineList);
                success += airlineList.size();
            } catch (Exception e) {
                log.error("提交对账出现业务异常", e);
                fail += airlineList.size();
            }
        }

        resVo.setSuccess(success);
        resVo.setFail(fail);
    }

    private void handleDom(FlightBillSubmitVo submitVo) {
        if (submitVo.getFlightLineType() != null && "D".equals(submitVo.getFlightLineType())) {
            submitVo.setFlightLineType("DOM");
        } else {
            submitVo.setFlightLineType(submitVo.getFlightLineType());
        }
        submitVo.setFlightSegment(submitVo.getFlightSegment());
        if (submitVo.getFlightSegmentType() != null && "D".equals(submitVo.getFlightSegmentType())) {
            submitVo.setFlightSegmentType("DOM");
        } else {
            submitVo.setFlightSegmentType(submitVo.getFlightSegmentType());
        }
    }

    private void handleInflation(Map<String, List<ServiceRecord>> inflationSrMap, FlightBill fb) {
        if (INFLATION.equals(fb.getFeeCode())) {
            List<ServiceRecord> srList = inflationSrMap.get(fb.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                fb.setPricingAmount(BigDecimal.valueOf(srList.get(0).getUsedNumber()));
                fb.setUnitPrice(fb.getChargePrice().divide(fb.getPricingAmount(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    private void handlePowercar(Map<String, List<ServiceRecord>> powercarSrMap, FlightBill fb) {
        if (POWERCAR.equals(fb.getFeeCode())) {
            List<ServiceRecord> srList = powercarSrMap.get(fb.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                fb.setPricingAmount(BigDecimal.valueOf(srList.get(0).getUsedNumber()));
                fb.setUnitPrice(fb.getChargePrice().divide(fb.getPricingAmount(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    private void handleCet(SimpleDateFormat sdf, Map<String, List<ServiceRecord>> cetSrMap, FlightBill fb, String serviceRecord, FlightBillSubmitVo submitVo) {
        //设置客梯车费用的使用时间
        if (("CET".equals(serviceRecord)) || (fb.getFeeName() != null && "客梯车".equals(fb.getFeeName()))) {
            List<ServiceRecord> srList = cetSrMap.get(fb.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                Date starDate = srList.get(0).getStartTime();
                Date endDate = srList.get(0).getEndTime();
                submitVo.setStartTime(sdf.format(starDate));
                submitVo.setEndTime(sdf.format(endDate));
            }
        }
    }

    private void handleCbt(FlightBill fb, String serviceRecord, Map<String, List<ServiceRecord>> cbtSrMap, Set<String> srSet, FlightBillSubmitVo submitVo, SimpleDateFormat sdf) {
        //设置客桥费的使用时间
        if (("CBT".equals(serviceRecord)) || (fb.getFeeName() != null && "客桥费".equals(fb.getFeeName()))) {
            List<ServiceRecord> srList = cbtSrMap.get(fb.getFlightId());
            if (CollectionUtils.isNotEmpty(srList)) {
                extracted(fb, srSet, submitVo, sdf, srList);
            }

        }
    }

    private void extracted(FlightBill fb, Set<String> srSet, FlightBillSubmitVo submitVo, SimpleDateFormat sdf, List<ServiceRecord> srList) {
        for (ServiceRecord sr : srList) {
            Date starDate = sr.getStartTime();
            Date endDate = sr.getEndTime();
            //计算使用时长，与费用中的时长进行匹配
            Double stayTime = getStayTime(sr, starDate, endDate, srSet);
            BigDecimal pa = fb.getChargePrice().divide(BigDecimal.valueOf(200), RoundingMode.UP);
            if (stayTime != null&&BigDecimal.valueOf(stayTime).compareTo(pa) == 0) {
                submitVo.setStartTime(sdf.format(starDate));
                submitVo.setEndTime(sdf.format(endDate));
                //设置客桥费时，单价与计价量问题
                submitVo.setPricingAmount(pa);
                submitVo.setUnitPrice(BigDecimal.valueOf(200));
                srSet.add(sr.getId());
                break;
            }
        }
    }

    private Map<String, List<ServiceRecord>> getMap(SubmitForm form, List<String> cbtBillFlightIdList, List<String> inflationFlightIdList, Map<String, List<ServiceRecord>> inflationSrMap) {
        if (CollectionUtils.isNotEmpty(cbtBillFlightIdList)) {
            List<ServiceRecord> inflationSrList =
                    serviceRecordDao.listServiceRecordByFlightIdAndServiceCode(inflationFlightIdList,
                            form.getAirportCode(), "CSUF");
            inflationSrMap = inflationSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                    Collectors.mapping(t -> t, Collectors.toList())));
        }
        return inflationSrMap;
    }

    private Map<String, List<ServiceRecord>> getListMap(SubmitForm form, List<String> cbtBillFlightIdList, List<String> powercarFlightIdList, Map<String, List<ServiceRecord>> powercarSrMap) {
        if (CollectionUtils.isNotEmpty(cbtBillFlightIdList)) {
            List<ServiceRecord> cetSrList =
                    serviceRecordDao.listServiceRecordByFlightIdAndServiceCode(powercarFlightIdList,
                            form.getAirportCode(), "CPUF");
            powercarSrMap = cetSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                    Collectors.mapping(t -> t, Collectors.toList())));
        }
        return powercarSrMap;
    }

    private Map<String, List<ServiceRecord>> getStringListMap(SubmitForm form, List<String> cbtBillFlightIdList, List<String> cetBillFlightIdList, Map<String, List<ServiceRecord>> cetSrMap) {
        if (CollectionUtils.isNotEmpty(cbtBillFlightIdList)) {
            List<ServiceRecord> cetSrList =
                    serviceRecordDao.listServiceRecordByFlightIdAndServiceCode(cetBillFlightIdList,
                            form.getAirportCode(), "CET");
            cetSrMap = cetSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                    Collectors.mapping(t -> t, Collectors.toList())));
        }
        return cetSrMap;
    }

    private Map<String, List<ServiceRecord>> getStringListMap(SubmitForm form, List<String> cbtBillFlightIdList, Map<String, List<ServiceRecord>> cbtSrMap) {
        if (CollectionUtils.isNotEmpty(cbtBillFlightIdList)) {
            List<ServiceRecord> cbpSrList = serviceRecordDao.listServiceRecordByFlightIdAndServiceCode(cbtBillFlightIdList,
                    form.getAirportCode(), "CBT");
            cbtSrMap = cbpSrList.stream().collect(Collectors.groupingBy(ServiceRecord::getFlightId,
                    Collectors.mapping(t -> t, Collectors.toList())));
        }
        return cbtSrMap;
    }

    @SneakyThrows
    @Override
    public Object checkUploadFile(MultipartFile file){
        List<FlightBillImportDto> list=getFlightBillImportList(file);
        List<FlightBillExportDto> errorDatas = new CopyOnWriteArrayList<>();
        Map<String,List<FlightBillImportDto>> tempBills = new ConcurrentHashMap<>();
        StringBuilder sb=new StringBuilder();
        list.forEach(dto -> {
            String valid = checkFlightBillImportDto(dto);
            //错误数据集合
            if (CharSequenceUtil.isNotBlank(valid)) {
                FlightBillExportDto edto = msFlightBillImport.dto2FlightBillExportDto(dto);
                edto.setErrorMsg(valid);
                errorDatas.add(edto);
            } else {
                if(sb.length()==0){
                    sb.append(dto.getAirportCode());
                }
                List<FlightBillImportDto> flightBillImportDtoList=tempBills.getOrDefault(DateUtils.format(dto.getFlightDate()),new ArrayList<>());
                flightBillImportDtoList.add(dto);
                //正确集合
                tempBills.put(DateUtils.format(dto.getFlightDate()),flightBillImportDtoList);
            }
        });
        if (sb.length()==0) {
            throw new GenericException(BusinessMessageEnum.FILE_IMPORT_ERROR.getCode(), BusinessMessageEnum.FILE_IMPORT_ERROR.getMsg() + "请填写开账机场代码");
        }
        Long tenantId=TenantHolder.getTenant();
        tempBills.values().forEach(tempBillList->{
            TenantHolder.setTenant(tenantId);
            //校验上传已存在账单状态是否为未提交、有争议、已撤销状态
            List<String> keys = tempBillList.stream().map(v -> v.getAirportCode() + v.getFlightNo() + DateUtils.format(v.getFlightDate()) + v.getFlightSegment() + v.getFeeCode() + v.getFlightFlag()).collect(Collectors.toList());
            List<FlightBill> existBills = flightBillDao.getByUniqueKeys(keys);
            Map<String, List<FlightBill>> existBillMap=existBills.stream().collect(Collectors.groupingBy(v -> v.getAirportCode() + v.getFlightNo() + DateUtils.format(v.getFlightDate()) + v.getFlightSegment() + v.getFeeCode() + v.getFlightFlag()));
            //剔除重复数据
            getRepeatList(tempBillList,errorDatas,existBillMap);
            TenantHolder.clear();
        });

        if(CollUtil.isNotEmpty(errorDatas)){
            //错误数据上传,并返回错误文件地址
            return new ResultBuilder.Builder<>().msg(Boolean.FALSE.toString()).data(easyExcelExportErrorData(errorDatas, sb.toString())).builder();
        }
        return new ResultBuilder.Builder<>().builder();
    }

    private void getRepeatList(List<FlightBillImportDto> tempBills,List<FlightBillExportDto> errorDatas,Map<String, List<FlightBill>> existBillMap){
        Map<String, List<FlightBillImportDto>> billsMap = tempBills.stream().collect(Collectors.groupingBy(v -> v.getAirportCode() + v.getFlightNo() + DateUtils.format(v.getFlightDate()) + v.getFlightSegment() + v.getFeeCode() + v.getFlightFlag()));
        billsMap.forEach((k, v) -> {
            if (v.size() > 1) {
                v.forEach(ed -> {
                    FlightBillExportDto edto = new FlightBillExportDto();
                    BeanUtils.copyProperties(ed, edto);
                    edto.setErrorMsg("重复账单");
                    errorDatas.add(edto);
                });
            }else{
                v.forEach(dto->{
                    List<FlightBill> ebList=existBillMap.getOrDefault(k,new ArrayList<>());
                    if(!ebList.isEmpty()) {
                        FlightBill ebFlightBill=ebList.get(0);
                        if((!EnumBillStatus.NOT_SUBMITTED.getStatus().toString().equals(ebFlightBill.getSubmit())
                                &&!EnumBillStatus.CONTROVERSIAL.getStatus().toString().equals(ebFlightBill.getSubmit())
                                &&!EnumBillStatus.REVOKED.getStatus().toString().equals(ebFlightBill.getSubmit()))){
                            FlightBillExportDto edto = new FlightBillExportDto();
                            BeanUtils.copyProperties(dto, edto);
                            edto.setErrorMsg("账单状态不是未提交、有争议、已撤销状态");
                            errorDatas.add(edto);
                        }
                    }
                });
            }
        });
    }

    private List<FlightBillImportDto> getFlightBillImportList(MultipartFile file){
        //验证文件后缀合法性
        String ext = ExcelUtils.getFileName(file).substring(1);
        if (!Arrays.asList("xls,xlsx".split(",")).contains(ext)) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_ERROR.getCode(), BusinessMessageEnum.DATA_FILE_ERROR.getMsg() + ",请上传xls或xlsx格式文件");
        }

        //转换成对象集合
        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        List<FlightBillImportDto> list;
        try {
            list = EasyExcelFactory.read(file.getInputStream(),FlightBillImportDto.class, new EasyExcelDataListener()).sheet().doReadSync();
        } catch (Exception e) {
            log.error("机场端上传账单，出现业务异常", e);
            throw new BusinessException(CommonErrors.UPLOAD_ERROR);
        }
        return list;
    }

    @SneakyThrows
    @Override
    public Object upload(MultipartFile file) {
        List<FlightBillImportDto> list=getFlightBillImportList(file);
        log.info("导入航班明细账单，解析数据完成，解析到{}条账单数据",list.size());
        List<FlightBill> bills;

        //如果一个机场代码都没填直接返回错误 错误数据上传需要知道是哪个机场
        bills=list.parallelStream().filter(dto->StringUtils.isBlank(checkFlightBillImportDto(dto)))
                .map(dto-> msFlightBillImport.dto2Bean(dto)).collect(Collectors.toList());

        log.info("导入航班明细账单，过滤无效数据完成，过滤后获得{}条账单数据",bills.size());
        Long tenant = TenantHolder.getTenant();
        //正确数据入库
        String username = AuthenticationUtils.getCurrentUser().getUsername();
        ThreadPoolExecutor executor = ThreadUtil.getExecutor();
        int baseCount = 1000;
        int times = (bills.size() + baseCount - 1) / baseCount;
        AtomicInteger i = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        while (i.get() < times) {
            int j = i.getAndAdd(1);
            log.info("导入航班明细账单，第{}个分组提交存储县城",i);
            executor.submit(() -> {
                List<FlightBill> currentList = bills.stream().skip((long) j * baseCount).limit(baseCount).collect(Collectors.toList());
                saveOrUpdateBills(currentList, username, tenant);
                long endTime = System.currentTimeMillis();
                // 计算执行时间（毫秒）
                long executionTime = endTime - startTime;
                // 将执行时间转换为秒
                double executionTimeInSeconds = (double) executionTime / 1000;

                log.info("{}代码执行花费了{} 秒 " ,Thread.currentThread().getName(), executionTimeInSeconds);
            });
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public void saveOrUpdateBills(List<FlightBill> bills, String userName, Long tenant) {
        if (CollUtil.isEmpty(bills)) {
            return;
        }
        TenantHolder.setTenant(tenant);
        //查询重复数据 开账机场-航班号-航班日期-航段-费用代码-起降标识
        List<String> keys = bills.stream().map(v -> v.getAirportCode() + v.getFlightNo() + DateUtils.format(v.getFlightDate()) + v.getFlightSegment() + v.getFeeCode() + v.getFlightFlag()).collect(Collectors.toList());
        List<FlightBill> existBills = flightBillDao.getByUniqueKeys(keys);

        //查询航班id
        keys = bills.stream().map(v -> v.getAirportCode() + v.getFlightNo() + DateUtils.format(v.getFlightDate()) + v.getFlightSegment() + v.getFlightFlag()).collect(Collectors.toList());
        List<String[]> flightInfos = flightInfoDao.getByUniqueKeys(keys);
        List<FlightInfoDto> infos = flightInfos.stream().map(v -> new FlightInfoDto(v[0], v[1],
                DateUtil.parseDate(v[2]), v[3], v[4], v[5])).collect(Collectors.toList());
        Map<String, String> fltMap = infos.stream().collect(Collectors.toMap(k -> k.getAirportCode() + k.getFlightNo() + DateUtils.format(k.getFlightDate()) + k.getFlightSegment() + k.getFlightFlag(), FlightInfoDto::getId));

        List<FlightBill> removeBills = new ArrayList<>();
        Map<String, List<FlightBill>> map = existBills.stream().collect(Collectors.groupingBy(v -> v.getAirportCode() + v.getFlightNo() + DateUtils.format(v.getFlightDate()) + v.getFlightSegment() + v.getFeeCode() + v.getFlightFlag()));
        bills.forEach(bill -> {
            //结算日期按航班日期设置
            bill.setSettleMonth(bill.getFlightDate());
            bill.setFromAirportCode(bill.getFlightSegment().split("-")[0]);
            bill.setToAirportCode(bill.getFlightSegment().split("-")[1]);
            bill.setFlightLineType(CharSequenceUtil.isBlank(bill.getFlightLineType()) || bill.getFlightLineType().startsWith("D") ? "D" : "I");
            bill.setFlightSegmentType(CharSequenceUtil.isBlank(bill.getFlightSegmentType()) || bill.getFlightSegmentType().startsWith("D") ? "D" : "I");
            bill.setFlightId(fltMap.get(bill.getAirportCode() + bill.getFlightNo() + DateUtils.format(bill.getFlightDate()) + bill.getFlightSegment() + bill.getFlightFlag()));
            bill.setCreateBy(userName);
            bill.setCreateTime(new Date());
            bill.setModifiedBy(userName);
            bill.setModifiedTime(new Date());
            bill.setSubmit("0");
            String key = bill.getAirportCode() + bill.getFlightNo() + DateUtils.format(bill.getFlightDate()) + bill.getFlightSegment() + bill.getFeeCode() + bill.getFlightFlag();
            //重复数据更新
            if (map.containsKey(key)) {
                FlightBill oldBill = map.get(key).get(0);
                //账单状态为未提交、有争议、已撤销 且 有改变的账单才能导入被覆
                boolean b = compareBill(bill, oldBill);
                if((EnumBillStatus.NOT_SUBMITTED.getStatus().toString().equals(oldBill.getSubmit())
                        ||EnumBillStatus.CONTROVERSIAL.getStatus().toString().equals(oldBill.getSubmit())
                        ||EnumBillStatus.REVOKED.getStatus().toString().equals(oldBill.getSubmit()))&&!b){
                    bill.setAirlineShortName(oldBill.getAirlineShortName());
                    bill.setCreateBy(oldBill.getCreateBy());
                    bill.setCreateTime(oldBill.getCreateTime());
                    bill.setId(oldBill.getId());
                }else{
                    removeBills.add(bill);
                }
            }
        });

        bills.removeAll(removeBills);
        log.info("已存在确认或相同数据 {} 条，新增或更新数据 {} 条", removeBills.size(), bills.size());
        saveBillHistory(bills,userName);
        log.info("{}导入完成", Thread.currentThread().getName());
    }

    private void saveBillHistory(List<FlightBill> bills,String userName){
        if (CollUtil.isNotEmpty(bills)) {
            flightBillDao.saveAll(bills);
            //记录变更历史
            List<FlightBillHistory> histories = bills.stream().map(v -> {
                FlightBillHistory billHistory = new FlightBillHistory();
                BeanUtils.copyProperties(v, billHistory);
                billHistory.setId(null);
                billHistory.setFlightBillId(v.getId());
                billHistory.setCreateTime(new Date());
                billHistory.setCreateBy(userName);
                billHistory.setOperation(BillOperation.GENERATE.getCode());
                return billHistory;
            }).collect(Collectors.toList());
            flightBillHistoryDao.saveAll(histories);
        }
    }

    @Transactional
    @Override
    public Object refuseToHandle(FlightBillRefuseForm param) {
        if (null != param.getProveFile()) {
            try {
                JSON.parseArray(param.getProveFile());
            } catch (Exception e) {
                log.error("机场端-证明文件不是json格式:{}", param.getProveFile());
                throw new BusinessException(ExceptionCodes.PROVE_FILE_NOT_JSON_ERROR);
            }
        }
        int i = flightBillDao.updateFlightBillById(param.getId(), EnumBillStatus.REFUSE_PROCESS.getStatus(), param.getReason(), param.getProveFile());
        if (i <= 0) {
            throw new BusinessException(PARAM_VALUE_INVALID_ERROR, "id");
        }
        flightBillHistoryDao.copyBillHistory(Collections.singletonList(param.getId()), UserContext.getCurrentUserName(), BillOperation.REJECT_HANDLE.getCode());
        //推送航司对账
        FlightBill bill = flightBillDao.getById(param.getId());
        SubmitForm form = new SubmitForm();
        form.setAirlineCode(bill.getSettleCode());
        form.setAirportCode(bill.getAirportCode());
        form.setStartDate(bill.getSettleMonth());
        form.setEndDate(bill.getSettleMonth());
        submit(Collections.singletonList(bill), form, true);
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public Object billRecord(String fltBillId, Integer limit) {
        long count = flightBillHistoryDao.count(fltBillId);
        List<FlightBillHistory> histories = flightBillHistoryDao.getHistoriesByFltBillId(fltBillId, limit);
        List<FlightBillHistoryVo> historiesVo = histories.stream().map(historie -> {
            FlightBillHistoryVo flightBillHistoryVo = new FlightBillHistoryVo();
            BeanUtils.copyProperties(historie, flightBillHistoryVo);
            flightBillHistoryVo.setProveFile(null);
            // 对json字符串防止转义处理
            if (StringUtils.isNotEmpty(historie.getProveFile())) {
                JSONArray array = JSON.parseArray(historie.getProveFile());
                flightBillHistoryVo.setProveFiles(array);
            }
            return flightBillHistoryVo;
        }).collect(Collectors.toList());
        Map<String, Object> rm = new HashMap<>();
        rm.put("count", count);
        rm.put("histories", historiesVo);
        return new ResultBuilder.Builder<>().data(rm).builder();
    }

    @Override
    @Transactional
    public Object revocation(String id) {
        TFlightBill bill = itFlightBillService.getById(id);
        int submit = Integer.parseInt(bill.getSubmit());
        //已确认和待审核账单可以撤销
        if (CONFIRMED.getStatus() == submit || AWAIT_EXAMINE.getStatus() == submit) {
            statemachineTemplate.updateRevocationStatusEvent(itFlightBillService, TFlightBill::getRevocation, TFlightBill::getId, id, EnumRevocationStatusChangeEvent.AIRPORT_APPLY_REVOCATION);
            //历史记录
            flightBillHistoryService.copyBillHistory(Collections.singletonList(bill.getId()), UserContext.getCurrentUserName(), BillOperation.APPLY_CANCEL.getCode());
            //提交航司
            ssssUtil.send("REVOKE_BILL", bill.getAirportCode(), Collections.singletonList(bill.getSettleCode()), bill.getId());
        } else {
            throw new BusinessException(QUERY_ERROR, "账单当前状态不能撤销");
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    @Transactional
    public Integer deleteFlightBillById(List<String> idList) {
        if (idList.isEmpty()) {
            throw new BusinessException(DELETE_ERROR, "删除航班不能为空");
        }
        List<FlightBill> flightBillList = flightBillDao.getFlightBillListByIdList(idList);
        List<String> deleteId = new ArrayList<>();
        String airportCode=null;
        for (FlightBill flightBill : flightBillList) {
            if ("1".equals(flightBill.getInvalid()) && flightBill.getManuallyDelete().equals(0) && "0".equals(flightBill.getSubmit())) {
                deleteId.add(flightBill.getId());
                if(StringUtils.isBlank(airportCode)&&StringUtils.isNoneBlank(flightBill.getAirportCode())) {
                    airportCode = flightBill.getAirportCode();
                }
            }
        }
        Integer deleteNum = flightBillDao.deleteByIdList(deleteId);
        if (deleteNum > 0) {
            flightBillHistoryDao.deleteByBillIdList(deleteId);
        }
        airlineBillNewService.checkAndNewAirlineBill(airportCode);
        return deleteNum;
    }

    private boolean compareBill(FlightBill bill, FlightBill oldBill) {
        if (bill == oldBill) {
            return true;
        }
        if (oldBill == null) {
            return false;
        }
        return Objects.equals(bill.getFlightId(), oldBill.getFlightId())
                && Objects.equals(bill.getAirportCode(), oldBill.getAirportCode())
                && Objects.equals(bill.getAirlineShortName(), oldBill.getAirlineShortName())
                && Objects.equals(bill.getFlightDate() == null ? null : DateUtils.format(bill.getFlightDate()), oldBill.getFlightDate() == null ? null : DateUtils.format(oldBill.getFlightDate()))
                && Objects.equals(bill.getFlightTime() == null ? null : DateUtils.format(bill.getFlightTime()), oldBill.getFlightTime() == null ? null : DateUtils.format(oldBill.getFlightTime()))
                && Objects.equals(bill.getFlightNo(), oldBill.getFlightNo())
                && Objects.equals(bill.getFlightLine(), oldBill.getFlightLine())
                && Objects.equals(bill.getFlightLineType(), oldBill.getFlightLineType())
                && Objects.equals(bill.getFlightSegment(), oldBill.getFlightSegment())
                && Objects.equals(bill.getFlightSegmentType(), oldBill.getFlightSegmentType())
                && Objects.equals(bill.getDaAirportCode(), oldBill.getDaAirportCode())
                && Objects.equals(bill.getFromAirportCode(), oldBill.getFromAirportCode())
                && Objects.equals(bill.getToAirportCode(), oldBill.getToAirportCode())
                && Objects.equals(bill.getRegNo(), oldBill.getRegNo())
                && Objects.equals(bill.getFlightModel(), oldBill.getFlightModel())
                && Objects.equals(bill.getFlightFlag(), oldBill.getFlightFlag())
                && Objects.equals(bill.getTaskFlag(), oldBill.getTaskFlag())
                && Objects.equals(bill.getSettleMonth() == null ? null : DateUtils.format(bill.getSettleMonth()), oldBill.getSettleMonth() == null ? null : DateUtils.format(oldBill.getSettleMonth()))
                && Objects.equals(bill.getSettleCode(), oldBill.getSettleCode())
                && Objects.equals(bill.getFeeCode(), oldBill.getFeeCode())
                && Objects.equals(bill.getFeeName(), oldBill.getFeeName())
                && bill.getPricingAmount().compareTo(oldBill.getPricingAmount()) == 0
                && bill.getUnitPrice().compareTo(oldBill.getUnitPrice()) == 0
                && bill.getChargePrice().compareTo(oldBill.getChargePrice()) == 0
                && Objects.equals(bill.getSubmit(), oldBill.getSubmit());
    }

    private String easyExcelExportErrorData(List<FlightBillExportDto> errorDatas, String airportCode){
        String id = uploadStatusService.newUpload("航班明细账单上传错误数据", airportCode, AuthenticationUtils.getCurrentUser());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcelFactory.write(outputStream, FlightBillExportDto.class).sheet("模板").doWrite(errorDatas);
        uploadStatusService.uploadSuccessOutputStream(id, "航班明细账单上传错误数据.xls", outputStream);
        return uploadStatusService.uploadGetUrlByFileName(id);
    }
    @Override
    public Object uploadBatch(MultipartFile file) {
        //转换成对象集合
        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        List<FlightBillImportDto> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), FlightBillImportDto.class, params);
        } catch (Exception e) {
            log.error("机场端上传账单，出现业务异常", e);
            throw new BusinessException(CommonErrors.UPLOAD_ERROR);
        }

        List<FlightBill> bills = new ArrayList<>();
        list.forEach(dto -> {
            //正确集合
            FlightBill flightBill = new FlightBill();
            BeanUtils.copyProperties(dto, flightBill);
            bills.add(flightBill);
        });


        Long tenant = TenantHolder.getTenant();

        //正确数据入库
        String username = AuthenticationUtils.getCurrentUser().getUsername();
        ThreadPoolExecutor executor = ThreadUtil.getExecutor();
        int baseCount = 1000;
        int times = (bills.size() + baseCount - 1) / baseCount;
        AtomicInteger i = new AtomicInteger(0);
        while (i.get() < times) {
            int j = i.getAndAdd(1);
            executor.submit(() -> {
                List<FlightBill> currentList = bills.stream().skip((long) j * baseCount).limit(baseCount).collect(Collectors.toList());
                saveOrUpdateBills(currentList, username, tenant);
            });
        }

        return null;
    }

    private String checkFlightBillImportDto(FlightBillImportDto dto) {
        if(StringUtils.isBlank(dto.getAirportCode())){
            return "开账机场代码不能为空";
        }
        if(StringUtils.isBlank(dto.getSettleCode())){
            return "付款航空公司代码不能为空";
        }
        if(dto.getFlightDate() == null){
            return "航班日期不能为空";
        }
        if(StringUtils.isBlank(dto.getFlightNo())){
            return "航班号不能为空";
        }
        if(StringUtils.isBlank(dto.getFlightSegment())){
            return "航段不能为空";
        }
        if(StringUtils.isBlank(dto.getFlightFlag())){
            return "起降标志不能为空";
        }
        if(StringUtils.isBlank(dto.getFeeCode())){
            return "费用代码不能为空";
        }
        return null;
    }

    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return new ArrayList<>();
    }


    public <T>  void setPricingAmount(T object, Function<T, String> flightNoFunction
            , Function<T, String> feeCodeFunction, BiConsumer<T, BigDecimal> setter,
                                 Function<T, BigDecimal> getPricingAmountFunction){
        //设置国航计价量为3位，其他为2位
        if (flightNoFunction.apply(object).startsWith("CA") || "SC-D".equals(feeCodeFunction.apply(object))) {
            setter.accept(object,getPricingAmountFunction.apply(object).setScale(3, RoundingMode.HALF_UP));
        } else {
            setter.accept(object,getPricingAmountFunction.apply(object).setScale(2, RoundingMode.HALF_UP));
        }
    }


}
