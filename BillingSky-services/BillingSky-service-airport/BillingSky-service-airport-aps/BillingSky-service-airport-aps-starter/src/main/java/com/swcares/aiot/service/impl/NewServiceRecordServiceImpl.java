package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.aiot.client.remote.IIndicatorBizRemoteClient;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.InvalidEnum;
import com.swcares.aiot.core.common.enums.VariableStatusEnum;
import com.swcares.aiot.core.common.util.EasyExcelUtil;
import com.swcares.aiot.core.common.util.ExcelFlightUtils;
import com.swcares.aiot.core.common.util.ExcelUtils;
import com.swcares.aiot.core.common.util.FormatUtils;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TServiceRecord;
import com.swcares.aiot.core.entity.TServiceRecordConfirm;
import com.swcares.aiot.core.enums.EnumIndicatorDataFormat;
import com.swcares.aiot.core.form.FlightInfoSearchForm;
import com.swcares.aiot.core.mapstruct.MsServiceRecord;
import com.swcares.aiot.core.model.dto.DictItemDto;
import com.swcares.aiot.core.model.dto.ServiceRecordDataDto;
import com.swcares.aiot.core.model.dto.ServiceRecordSaveBatchDto;
import com.swcares.aiot.core.model.dto.ServiceRecordSaveByFlightDto;
import com.swcares.aiot.core.model.vo.FlightBusinessDataExcelParserVo;
import com.swcares.aiot.core.model.vo.FlightBusinessDataExcelVO;
import com.swcares.aiot.core.model.vo.NewServiceRecordVo;
import com.swcares.aiot.core.service.ITFlightInfoService;
import com.swcares.aiot.core.service.ITServiceRecordConfirmService;
import com.swcares.aiot.core.service.ITServiceRecordService;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.aiot.service.NewServiceRecordService;
import com.swcares.aiot.service.UploadStatusService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.NewServiceRecordServiceImpl
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/7 15:52
 * @version v1.0
 */
@Slf4j
@Service
public class NewServiceRecordServiceImpl implements NewServiceRecordService {

    @Resource
    private ITServiceRecordService serviceRecordService;
    @Resource
    private ITServiceRecordConfirmService serviceRecordConfirmService;
    @Resource
    private IIndicatorBizRemoteClient iIndicatorBizRemoteClient;
    @Resource
    private ITFlightInfoService flightInfoService;
    @Resource
    private MsServiceRecord msServiceRecord;
    @Resource
    private UploadStatusService uploadStatusService;

    @Override
    public List<IndAllIndicatorRetrVo> getServiceDict() {
        BaseResult<List<IndAllIndicatorRetrVo>> indicatorDicRes = iIndicatorBizRemoteClient.list();
        if (indicatorDicRes == null || indicatorDicRes.getCode() != 200 || CollUtil.isEmpty(indicatorDicRes.getData())) {
            throw new BusinessException(ExceptionCodes.SERVICE_DICT_ERROR);
        }
        List<IndAllIndicatorRetrVo> indicatorDicResList = indicatorDicRes.getData();
        indicatorDicResList = indicatorDicResList.stream()
                .filter(indAllIndicatorRetrVo -> Integer.valueOf(1).equals(indAllIndicatorRetrVo.getSort()))
                .collect(Collectors.toList());
        return indicatorDicResList;
    }

    @Override
    public boolean saveDeviceUsedListData(List<ServiceRecordSaveBatchDto> serviceRecordSaveBatchDtoList, LoginUserDetails user) {
        //获取所有指标项
        Map<String, IndAllIndicatorRetrVo> serviceDict = getServiceDict().stream()
                .collect(Collectors.toMap(IndAllIndicatorRetrVo::getCode, indAllIndicatorRetrVo -> indAllIndicatorRetrVo));
        Set<String> deleteSet = new HashSet<>();
        serviceRecordSaveBatchDtoList.forEach(serviceRecordSaveBatchDto -> {
                    //如果校验数据，数据不完整或不正确则跳过
                    if (checkDataFormat(serviceRecordSaveBatchDto, serviceDict, serviceRecordSaveBatchDto.getFlightId())) {
                        //判断（航班+serviceCode）是否删除过，删除过则不再删除
                        if (!deleteSet.contains(serviceRecordSaveBatchDto.getFlightId() + "_" + serviceRecordSaveBatchDto.getServiceCode())) {
                            serviceRecordService.lambdaUpdate().eq(TServiceRecord::getFlightId, serviceRecordSaveBatchDto.getFlightId())
                                    .eq(TServiceRecord::getServiceCode, serviceRecordSaveBatchDto.getServiceCode())
                                    .eq(TServiceRecord::getInvalid, InvalidEnum.NORMAL.getValue())
                                    .set(TServiceRecord::getInvalid, InvalidEnum.DELETED.getValue()).update();
                            deleteSet.add(serviceRecordSaveBatchDto.getFlightId() + "_" + serviceRecordSaveBatchDto.getServiceCode());
                        }
                        //生成业务保障数据并保存
                        generateAndSave(serviceRecordSaveBatchDto.getFlightId(), serviceRecordSaveBatchDto.getAirportCode(), user, serviceRecordSaveBatchDto, serviceDict);
                    }
                }
        );
        return true;
    }

    @Override
    public boolean saveDeviceUsedListByFlight(ServiceRecordSaveByFlightDto saveByFlightDto, LoginUserDetails user) {
        //获取所有指标项
        Map<String, IndAllIndicatorRetrVo> serviceDict = getServiceDict().stream()
                .collect(Collectors.toMap(IndAllIndicatorRetrVo::getCode, indAllIndicatorRetrVo -> indAllIndicatorRetrVo));
        if (saveByFlightDto == null || StringUtils.isBlank(saveByFlightDto.getFlightId())) {
            log.error("导入业务保障数据，flightId为空，参数：{}", saveByFlightDto);
            return false;
        }
        //删除航班下所有业务保障数据
        serviceRecordService.lambdaUpdate().eq(TServiceRecord::getFlightId, saveByFlightDto.getFlightId())
                .eq(TServiceRecord::getInvalid, InvalidEnum.NORMAL.getValue())
                .set(TServiceRecord::getInvalid, InvalidEnum.DELETED.getValue()).update();
        saveByFlightDto.getServiceRecordDataDtoList().forEach(serviceRecordDataDto -> {
            if (checkDataFormat(serviceRecordDataDto, serviceDict, saveByFlightDto.getFlightId())) {
                //生成业务保障数据并保存
                generateAndSave(saveByFlightDto.getFlightId(), saveByFlightDto.getAirportCode(), user, serviceRecordDataDto, serviceDict);
            }
        });
        return true;
    }

    private void saveServiceOrConfirm(FlightInfoMb flightInfo, TServiceRecord serviceRecord) {
        //判断航班业务保障数据确认状态是否确认
        if (VariableStatusEnum.CONFIRMED.getValue().equals(flightInfo.getVariableStatus())
                ||VariableStatusEnum.MODIFIED.getValue().equals(flightInfo.getVariableStatus())) {
            log.error("航班数据已确认，无法录入，flightInfo:{}", flightInfo);
        } else {
            serviceRecordService.save(serviceRecord);
        }
    }

    private static boolean checkDataFormat(ServiceRecordDataDto serviceRecordSaveBatchDto, Map<String, IndAllIndicatorRetrVo> serviceDict, String flightId) {
        if (!serviceDict.containsKey(serviceRecordSaveBatchDto.getServiceCode()) || StringUtils.isBlank(flightId)) {
            log.error("导入业务保障数据，code或flightId为空，参数：{}", serviceRecordSaveBatchDto);
            return false;
        }
        IndAllIndicatorRetrVo indAllIndicatorRetrVo = serviceDict.get(serviceRecordSaveBatchDto.getServiceCode());
        if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_RANGE.getCode().equals(indAllIndicatorRetrVo.getDataFmt())) {
            if (serviceRecordSaveBatchDto.getStartTime() == null || serviceRecordSaveBatchDto.getEndTime() == null) {
                log.error("导入业务保障数据，时间类型，开始/结束时间为空，参数：{}", serviceRecordSaveBatchDto);
                return false;
            }
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_NUMBER.getCode().equals(indAllIndicatorRetrVo.getDataFmt())) {
            if (serviceRecordSaveBatchDto.getUsedNumber() == null) {
                log.error("导入业务保障数据，数值类型，次数为空，参数：{}", serviceRecordSaveBatchDto);
                return false;
            }

        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_SELECTOR.getCode().equals(indAllIndicatorRetrVo.getDataFmt())) {
            if (serviceRecordSaveBatchDto.getSelectorOption() == null) {
                log.error("导入业务保障数据，选择器类型，选中值为空，参数：{}", serviceRecordSaveBatchDto);
                return false;
            }
        } else {
            log.error("导入业务保障数据，类型为空，参数：{}", serviceRecordSaveBatchDto);
            return false;
        }
        return true;
    }


    private void generateAndSave(String flightId, String airportCode, LoginUserDetails user,
                                 ServiceRecordDataDto serviceRecordDataDto, Map<String, IndAllIndicatorRetrVo> serviceDict) {
        FlightInfoMb flightInfo = flightInfoService.lambdaQuery().eq(FlightInfoMb::getId, flightId)
                .eq(FlightInfoMb::getAirportCode, airportCode)
                .eq(FlightInfoMb::getInvalid, InvalidEnum.NORMAL.getValue())
                .one();
        if (flightInfo == null) {
            log.error("导入业务保障数据，航班id无对应航班数据，参数flightId：{}", flightId);
            return;
        }
        TServiceRecord serviceRecord = msServiceRecord.saveBatchDtoToEntity(serviceRecordDataDto);
        IndAllIndicatorRetrVo indAllIndicatorRetrVo = serviceDict.get(serviceRecordDataDto.getServiceCode());
        serviceRecord.setFlightId(flightId);
        serviceRecord.setAirportCode(airportCode);
        serviceRecord.setServiceName(indAllIndicatorRetrVo.getName());
        //计算设置时间类型的时长或选择器字典
        setUsedNumerOrDict(serviceRecordDataDto, indAllIndicatorRetrVo, serviceRecord);
        serviceRecord.setCreateBy(user.getUsername());
        serviceRecord.setCreateTime(LocalDateTime.now());
        serviceRecord.setModifiedBy(user.getUsername());
        serviceRecord.setModifiedTime(LocalDateTime.now());
        //保存业务保障数据
        saveServiceOrConfirm(flightInfo, serviceRecord);
    }

    private static void setUsedNumerOrDict(ServiceRecordDataDto serviceRecordDataDto, IndAllIndicatorRetrVo indAllIndicatorRetrVo, TServiceRecord serviceRecord) {
        //如果时间范围，则计算时长
        if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_RANGE.getCode().equals(indAllIndicatorRetrVo.getDataFmt())
                && serviceRecordDataDto.getStartTime() != null && serviceRecordDataDto.getEndTime() != null) {
            BigDecimal usedNumber = BigDecimal.valueOf(calculateHourDifference(serviceRecordDataDto.getStartTime(), serviceRecordDataDto.getEndTime()));
            serviceRecord.setUsedNumber(usedNumber);
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_SELECTOR.getCode().equals(indAllIndicatorRetrVo.getDataFmt())
                && indAllIndicatorRetrVo.getDictItems() != null && !indAllIndicatorRetrVo.getDictItems().isEmpty()) {
            serviceRecord.setSelectorDict(indAllIndicatorRetrVo.getDictItems());
        }
    }

    @Override
    public Map<String, List<NewServiceRecordVo>> listDeviceUsedDataByFlightId(String flightId) {
        List<NewServiceRecordVo> resList = new ArrayList<>();
        FlightInfoMb flightInfoMb = flightInfoService.lambdaQuery().eq(FlightInfoMb::getId, flightId)
                .eq(FlightInfoMb::getInvalid, InvalidEnum.NORMAL.getValue())
                .one();
        if (flightInfoMb == null) {
            throw new BusinessException(ExceptionCodes.CALC_FLIGHT_NUM_NULL);
        }

        boolean isConfirm = !(VariableStatusEnum.UNCONFIRMED.getValue().equals(flightInfoMb.getVariableStatus())||
                VariableStatusEnum.TO_BE_CONFIRMED.getValue().equals(flightInfoMb.getVariableStatus()));
        List<TServiceRecord> serviceRecordList = serviceRecordService.lambdaQuery().eq(TServiceRecord::getFlightId, flightId)
                .eq(TServiceRecord::getInvalid, InvalidEnum.NORMAL.getValue())
                .list();
        //获取备份数据map
        Map<String, List<TServiceRecordConfirm>> confirmDataMap = getConfirmDataMap(flightId, isConfirm);
        for (TServiceRecord serviceRecord : serviceRecordList) {
            NewServiceRecordVo serviceRecordVo = msServiceRecord.entityToVo(serviceRecord);
            if (isConfirm && !confirmDataMap.isEmpty()) {
                List<TServiceRecordConfirm> serviceRecordConfirms = confirmDataMap
                        .get(serviceRecord.getServiceCode() + "_" + serviceRecord.getBillBusDataItemId());
                if (CollUtil.isNotEmpty(serviceRecordConfirms)) {
                    TServiceRecordConfirm tServiceRecordConfirm = serviceRecordConfirms.get(0);
                    serviceRecordVo.setChangeStartTime(tServiceRecordConfirm.getStartTime());
                    serviceRecordVo.setChangeEndTime(tServiceRecordConfirm.getEndTime());
                    serviceRecordVo.setChangeUsedNumber(tServiceRecordConfirm.getUsedNumber());
                    serviceRecordVo.setChangeSelectorOption(tServiceRecordConfirm.getSelectorOption());
                    serviceRecordConfirms.remove(0);
                    if (serviceRecordConfirms.isEmpty()) {
                        confirmDataMap.remove(serviceRecord.getServiceCode() + "_" + serviceRecord.getBillBusDataItemId());
                    }
                }
            }
            resList.add(serviceRecordVo);
        }
        //将确认后新增的业务保障数据添加到查询结果中
        if (isConfirm && !confirmDataMap.isEmpty()) {
            confirmDataMap.forEach((k, v) -> v.forEach(serviceRecordConfirm -> {
                NewServiceRecordVo serviceRecordVo = new NewServiceRecordVo();
                serviceRecordVo.setChangeStartTime(serviceRecordVo.getStartTime());
                serviceRecordVo.setChangeEndTime(serviceRecordVo.getEndTime());
                serviceRecordVo.setChangeUsedNumber(serviceRecordVo.getUsedNumber());
                resList.add(serviceRecordVo);
            }));
        }
        return resList.stream().collect(Collectors.groupingBy(NewServiceRecordVo::getServiceCode));
    }

    private Map<String, List<TServiceRecordConfirm>> getConfirmDataMap(String flightId, boolean isConfirm) {
        Map<String, List<TServiceRecordConfirm>> confirmDataMap = new HashMap<>();
        if (isConfirm) {
            List<TServiceRecordConfirm> serviceRecordConfirmList = serviceRecordConfirmService.lambdaQuery().eq(TServiceRecordConfirm::getFlightId, flightId)
                    .eq(TServiceRecordConfirm::getInvalid, InvalidEnum.NORMAL.getValue())
                    .list();
            for (TServiceRecordConfirm serviceRecordConfirm : serviceRecordConfirmList) {
                List<TServiceRecordConfirm> tempConfirmList = confirmDataMap
                        .getOrDefault(serviceRecordConfirm.getServiceCode() + "_" + serviceRecordConfirm.getBillBusDataItemId(), new ArrayList<>());
                tempConfirmList.add(serviceRecordConfirm);
                confirmDataMap.put(serviceRecordConfirm.getServiceCode(), tempConfirmList);
            }
        }
        return confirmDataMap;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importFlightBusinessData(MultipartFile file, String airportCode, LoginUserDetails user) {
        String uploadId = uploadStatusService.newUpload("业务保障数据上传", airportCode, user);
        List<FlightBusinessDataExcelParserVo> error = new ArrayList<>();
        try {
            // todo加锁

            //获取指标项字典
            Map<String, IndAllIndicatorRetrVo> indictorDict = getIndictorDict();
            List<FlightBusinessDataExcelParserVo> successData = this.importFlightBusinessDataHandle(file, airportCode, indictorDict, error);
            uploadStatusService.startUpload(uploadId);
            if (!CollectionUtils.isEmpty(successData)) {

                // 保存服务记录信息
                List<TServiceRecord> serviceRecordList = new ArrayList<>();
                for (FlightBusinessDataExcelParserVo successDatum : successData) {
                    TServiceRecord serviceRecord = new TServiceRecord();
                    String flightId = successDatum.getId();
                    serviceRecord.setFlightId(flightId);
                    serviceRecord.setAirportCode(successDatum.getAirportCode());

                    LocalDate flightDate = getLocalDate(successDatum);
                    serviceRecord.setFlightDate(flightDate);
                    serviceRecord.setFlightNo(successDatum.getFlightNo());
                    serviceRecord.setFlightFlag(successDatum.getFlightFlag());
                    IndAllIndicatorRetrVo indAllIndicatorRetrVo = indictorDict.get(successDatum.getServiceName());
                    String serviceCode = indAllIndicatorRetrVo.getCode();
                    serviceRecord.setServiceCode(serviceCode);
                    serviceRecord.setServiceName(successDatum.getServiceName());

                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    BigDecimal usedNumber;
                    if (CharSequenceUtil.isNotBlank(successDatum.getNumber())) {
                        usedNumber = new BigDecimal(successDatum.getNumber());
                        serviceRecord.setUsedNumber(usedNumber);
                    } else if (CharSequenceUtil.isNotBlank(successDatum.getStartTime()) && CharSequenceUtil.isNotBlank(successDatum.getEndTime())) {
                        LocalDateTime startTime = LocalDateTime.parse(successDatum.getStartTime(), formatter);
                        LocalDateTime endTime = LocalDateTime.parse(successDatum.getEndTime(), formatter);
                        serviceRecord.setStartTime(startTime);
                        serviceRecord.setEndTime(endTime);
                        usedNumber = BigDecimal.valueOf(calculateHourDifference(startTime, endTime));
                        serviceRecord.setUsedNumber(usedNumber);
                    } else if (CharSequenceUtil.isNotBlank(successDatum.getOther())) {
                        String seletorItem = successDatum.getOther();
                        JsonNode dictItems = indAllIndicatorRetrVo.getDictItems();
                        ObjectMapper objectMapper = new ObjectMapper();
                        List<DictItemDto> dictItemDtoList =
                                objectMapper.readValue(dictItems.traverse(),
                                        new TypeReference<List<DictItemDto>>() {
                                        });
                        for (DictItemDto dictItemDto : dictItemDtoList) {
                            if (StringUtils.equals(dictItemDto.getBizName(), seletorItem)) {
                                serviceRecord.setSelectorOption(dictItemDto.getBizValue());
                                serviceRecord.setSelectorDict(dictItems);
                                break;
                            }
                        }
                    }

                    serviceRecord.setCreateBy(user.getUsername());
                    serviceRecord.setCreateTime(LocalDateTime.now());
                    serviceRecord.setModifiedBy(user.getUsername());
                    serviceRecord.setModifiedTime(LocalDateTime.now());
                    serviceRecordList.add(serviceRecord);

                    // 删除该航班该保障项的老数据
                    serviceRecordService.lambdaUpdate().eq(TServiceRecord::getFlightId, flightId)
                            .eq(TServiceRecord::getInvalid, InvalidEnum.NORMAL.getValue())
                            .eq(TServiceRecord::getServiceCode, serviceCode)
                            .set(TServiceRecord::getInvalid, InvalidEnum.DELETED.getValue())
                            .update();
                }
                //保存新数据
                serviceRecordService.saveBatch(serviceRecordList);
            }

            if (CollectionUtils.isEmpty(error)) {
                uploadStatusService.uploadSuccess(uploadId, null, null);
            } else {
                handleExcelParserVo(file, error, uploadId);
            }

        } catch (Exception e) {
            log.error("上传业务保障数据异常：{}", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            uploadStatusService.uploadFail(uploadId, "上传失败");
        } finally {
            //todo解锁

        }
        return CollectionUtils.isEmpty(error) ? "上传成功！" : "上传失败！";
    }

    private static LocalDate getLocalDate(FlightBusinessDataExcelParserVo successDatum) {
        LocalDate flightDate;
        try {
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            flightDate = LocalDate.parse(successDatum.getFlightDate(), dateFormatter);
        } catch (Exception e) {
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            flightDate = LocalDate.parse(successDatum.getFlightDate(), dateFormatter);
        }
        return flightDate;
    }

    public List<FlightBusinessDataExcelParserVo> importFlightBusinessDataHandle(MultipartFile file, String airportCode
            , Map<String, IndAllIndicatorRetrVo> indictorDict, List<FlightBusinessDataExcelParserVo> error) {
        // 文件格式校验
        ExcelUtils.checkSuffix(file);
        // 读取上传的业务保障数据
        List<FlightBusinessDataExcelParserVo> excelDataParserVOList = ExcelFlightUtils.readFlightBusinessExcel(file, airportCode);
        List<FlightBusinessDataExcelParserVo> successList = new ArrayList<>();
        for (FlightBusinessDataExcelParserVo excelDataParserVO : excelDataParserVOList) {
            String flightNo = excelDataParserVO.getFlightNo();
            String flightDate = excelDataParserVO.getFlightDate();
            String flightFlag = excelDataParserVO.getFlightFlag();
            if (StringUtils.isBlank(flightNo) || StringUtils.isBlank(flightDate) || StringUtils.isBlank(flightFlag)) {
                continue;
            }
            FlightInfoMb flightInfoMb = flightInfoService.lambdaQuery().eq(FlightInfoMb::getFlightNo, flightNo)
                    .eq(FlightInfoMb::getFlightDate, flightDate)
                    .eq(FlightInfoMb::getFlightFlag, flightFlag)
                    .eq(FlightInfoMb::getFlightFlag, flightFlag)
                    .eq(FlightInfoMb::getInvalid, InvalidEnum.NORMAL.getValue())
                    .last("limit 1").one();
            if (flightInfoMb == null) {
                excelDataParserVO.setErrorReason("航班不存在");
                error.add(excelDataParserVO);
                continue;
            }
            if (VariableStatusEnum.CONFIRMED.getValue().equals(flightInfoMb.getVariableStatus())
                    || VariableStatusEnum.MODIFIED.getValue().equals(flightInfoMb.getVariableStatus())) {
                excelDataParserVO.setErrorReason("航班已确认");
                error.add(excelDataParserVO);
                continue;
            }
            excelDataParserVO.setId(flightInfoMb.getId());
            IndAllIndicatorRetrVo indAllIndicatorRetrVo = indictorDict.get(excelDataParserVO.getServiceName());
            if (checkError(error, excelDataParserVO, indAllIndicatorRetrVo)) continue;
            successList.add(excelDataParserVO);
        }
        return successList;
    }

    private static boolean checkError(List<FlightBusinessDataExcelParserVo> error, FlightBusinessDataExcelParserVo excelDataParserVO, IndAllIndicatorRetrVo indAllIndicatorRetrVo) {
        if (indAllIndicatorRetrVo == null) {
            excelDataParserVO.setErrorReason("指标项不存在");
            error.add(excelDataParserVO);
            return true;
        }
        if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_NUMBER.getCode().equals(indAllIndicatorRetrVo.getDataFmt())
                && StringUtils.isBlank(excelDataParserVO.getNumber())) {
            excelDataParserVO.setErrorReason("数值类型指标项数值为空");
            error.add(excelDataParserVO);
            return true;
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_RANGE.getCode().equals(indAllIndicatorRetrVo.getDataFmt())
                && (StringUtils.isBlank(excelDataParserVO.getStartTime()) && StringUtils.isBlank(excelDataParserVO.getEndTime()))) {
            excelDataParserVO.setErrorReason("时间类型指标项时间为空");
            error.add(excelDataParserVO);
            return true;
        } else if (EnumIndicatorDataFormat.INDICATOR_DATA_FORMAT_SELECTOR.getCode().equals(indAllIndicatorRetrVo.getDataFmt())
                && StringUtils.isBlank(excelDataParserVO.getOther())) {
            excelDataParserVO.setErrorReason("选择器类型指标项选项为空");
            error.add(excelDataParserVO);
            return true;
        }
        return false;
    }


    private Map<String, IndAllIndicatorRetrVo> getIndictorDict() {
        Map<String, IndAllIndicatorRetrVo> indicatorDictMap = new HashMap<>();
        BaseResult<List<IndAllIndicatorRetrVo>> indicatorDicRes = iIndicatorBizRemoteClient.list();
        if (indicatorDicRes != null && indicatorDicRes.getCode() == 200 && CollUtil.isNotEmpty(indicatorDicRes.getData())) {
            List<IndAllIndicatorRetrVo> indicatorDicList = indicatorDicRes.getData();
            for (IndAllIndicatorRetrVo indAllIndicatorRetrVo : indicatorDicList) {
                if (Integer.valueOf(1).equals(indAllIndicatorRetrVo.getSort())) {
                    indicatorDictMap.put(indAllIndicatorRetrVo.getName(), indAllIndicatorRetrVo);
                }
            }
        }
        return indicatorDictMap;
    }

    public static double calculateHourDifference(LocalDateTime start, LocalDateTime end) {
        // 计算时间差
        Duration duration = Duration.between(start, end);
        // 向上取整到两位小数
        BigDecimal bd = BigDecimal.valueOf(duration.getSeconds()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }

    private void handleExcelParserVo(MultipartFile file, List<FlightBusinessDataExcelParserVo> error, String uploadId) {
        List<Object[]> errorData = new ArrayList<>();
        for (FlightBusinessDataExcelParserVo excelParserVo : error) {
            String[] obj = new String[9];
            obj[0] = "".equals(excelParserVo.getAirportCode()) ? " " : excelParserVo.getAirportCode();
            obj[1] = "".equals(excelParserVo.getFlightDate()) ? " " : excelParserVo.getFlightDate();
            obj[2] = "".equals(excelParserVo.getFlightNo()) ? " " : excelParserVo.getFlightNo();
            obj[3] = "".equals(excelParserVo.getFlightFlag()) ? " " : excelParserVo.getFlightFlag();
            obj[4] = "".equals(excelParserVo.getServiceName()) ? " " : excelParserVo.getServiceName();
            obj[5] = "".equals(excelParserVo.getStartTime()) ? " " : excelParserVo.getStartTime();
            obj[6] = "".equals(excelParserVo.getEndTime()) ? " " : excelParserVo.getEndTime();
            obj7(excelParserVo, obj);
            obj8(excelParserVo, obj);
            errorData.add(obj);
        }
        XSSFWorkbook errorSheets = ExcelFlightUtils.exportFlightBusinessError(errorData);
        uploadStatusService.uploadSuccess(uploadId, file.getOriginalFilename(), errorSheets);
    }

    private void obj8(FlightBusinessDataExcelParserVo excelParserVo, String[] obj) {
        obj[8] = "".equals(excelParserVo.getErrorReason()) ? " " : excelParserVo.getErrorReason();
    }

    private void obj7(FlightBusinessDataExcelParserVo excelParserVo, String[] obj) {
        obj[7] = "".equals(excelParserVo.getNumber()) ? " " : excelParserVo.getNumber();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportFlightBusinessData(FlightInfoSearchForm flightInfoSearchForm, HttpServletResponse response, LoginUserDetails user) {
        this.setFlightTimeDefaultRange(flightInfoSearchForm);
        // 用于保存需要导出数据
        List<FlightBusinessDataExcelVO> resultList = new ArrayList<>();
        // 根据页面条件从数据库中获取数据
        List<FlightInfoMb> flightList = flightInfoService.lambdaQuery().eq(FlightInfoMb::getAirportCode, flightInfoSearchForm.getAirportCode())
                .between(flightInfoSearchForm.getStartDate() != null && flightInfoSearchForm.getEndDate() != null
                        , FlightInfoMb::getFlightDate, flightInfoSearchForm.getStartDate(), flightInfoSearchForm.getEndDate())
                .between(flightInfoSearchForm.getFlightTimeStartDate() != null && flightInfoSearchForm.getFlightTimeEndDate() != null
                        , FlightInfoMb::getFlightTime, flightInfoSearchForm.getFlightTimeStartDate(), flightInfoSearchForm.getFlightTimeEndDate())
                .eq(StringUtils.isNotBlank(flightInfoSearchForm.getFlightNo()), FlightInfoMb::getFlightNo, flightInfoSearchForm.getFlightNo())
                .eq(StringUtils.isNotBlank(flightInfoSearchForm.getFlightFlag()), FlightInfoMb::getFlightFlag, flightInfoSearchForm.getFlightFlag())
                .eq(StringUtils.isNotBlank(flightInfoSearchForm.getAirlineCode()), FlightInfoMb::getAirlineCode, flightInfoSearchForm.getAirlineCode())
                .list();

        if (CollUtil.isNotEmpty(flightList)) {
            Map<String, List<TServiceRecord>> flightIdServiceRecordListMap = getStringListMap(flightList);
            for (FlightInfoMb flightInfo : flightList) {
                List<TServiceRecord> serviceRecordList = flightIdServiceRecordListMap.get(flightInfo.getId());
                if (!CollectionUtils.isEmpty(serviceRecordList)) {
                    serviceRecordList.forEach(serviceRecord -> {
                        FlightBusinessDataExcelVO flightBusinessDataExcelVO = ObjectUtils.copyBean(flightInfo, FlightBusinessDataExcelVO.class);
                        flightBusinessDataExcelVO.setFlightDate(getDateByLocalDate(flightInfo.getFlightDate()));
                        flightBusinessDataExcelVO.setServiceName(serviceRecord.getServiceName());
                        flightBusinessDataExcelVO.setStartTime(getDateByLocalDateTime(serviceRecord.getStartTime()));
                        flightBusinessDataExcelVO.setEndTime(getDateByLocalDateTime(serviceRecord.getEndTime()));
                        if (serviceRecord.getUsedNumber() == null && StringUtils.isBlank(serviceRecord.getSelectorOption())) {
                            return;
                        }
                        setValue(flightInfoSearchForm, serviceRecord, flightBusinessDataExcelVO, resultList);
                    });
                }
            }
        }

        // 导出数据的时间段
        String startTimeStr = FormatUtils.formatDateToString(flightInfoSearchForm.getStartDate() == null ? flightInfoSearchForm.getFlightTimeStartDate() : flightInfoSearchForm.getStartDate()).replace("-", "");
        String endTimeStr = FormatUtils.formatDateToString(flightInfoSearchForm.getEndDate() == null ? flightInfoSearchForm.getFlightTimeEndDate() : flightInfoSearchForm.getEndDate()).replace("-", "");
        // 表名
        String fileName = Constants.FlightBusinessInfoEnum.FLIGHT_BUSINESS_EXCEL_NAME_PREFIX
                .getValue() + startTimeStr + "_" + endTimeStr
                + Constants.FlightBusinessInfoEnum.FLIGHT_BUSINESS_EXCEL_NAME_SUFFIX.getValue();
        EasyExcelUtil.responseExcel(response, fileName, "sheet1", FlightBusinessDataExcelVO.class, resultList,
                CollUtil.toList(new LongestMatchColumnWidthStyleStrategy()));

    }

    private static void setValue(FlightInfoSearchForm flightInfoSearchForm, TServiceRecord serviceRecord, FlightBusinessDataExcelVO flightBusinessDataExcelVO, List<FlightBusinessDataExcelVO> resultList) {
        if (StringUtils.isNotBlank(serviceRecord.getSelectorOption())) {
            JsonNode selectorDict = serviceRecord.getSelectorDict();
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                List<DictItemDto> dictItemDtoList = objectMapper.readValue(selectorDict.traverse(),
                        new TypeReference<List<DictItemDto>>() {
                        });
                for (DictItemDto dictItemDto : dictItemDtoList) {
                    if (dictItemDto.getBizValue().equals(serviceRecord.getSelectorOption())) {
                        flightBusinessDataExcelVO.setOther(dictItemDto.getBizName());
                    }
                }
            } catch (IOException e) {
                log.error("获取选择器字典异常：{}", e.getMessage());
                return;
            }

        } else {
            BigDecimal usedNumber = serviceRecord.getUsedNumber();
            DecimalFormat df = new DecimalFormat("0.00");
            flightBusinessDataExcelVO.setUsedNumber(df.format(usedNumber.stripTrailingZeros()));
            flightBusinessDataExcelVO.setUsedNumber2(df.format(usedNumber));
        }
        flightBusinessDataExcelVO.setAirportCode(flightInfoSearchForm.getAirportCode());
        resultList.add(flightBusinessDataExcelVO);
    }

    private Map<String, List<TServiceRecord>> getStringListMap(List<FlightInfoMb> flightList) {
        List<String> flightIdList = flightList.stream().map(FlightInfoMb::getId).distinct().collect(Collectors.toList());
        List<TServiceRecord> allServiceRecordList = serviceRecordService.lambdaQuery().in(TServiceRecord::getFlightId, flightIdList)
                .eq(TServiceRecord::getInvalid, InvalidEnum.NORMAL.getValue()).list();
        return allServiceRecordList.stream().collect(Collectors.groupingBy(TServiceRecord::getFlightId));
    }

    private void setFlightTimeDefaultRange(FlightInfoSearchForm form) {
        //如果航班日期和起降日期都是空的则默认起降日期当前前一个月的
        if ((form.getStartDate() == null || form.getEndDate() == null)
                && (form.getFlightTimeStartDate() == null || form.getFlightTimeEndDate() == null)) {
            form.setFlightTimeEndDate(new Date());
            Calendar calendar = CalendarUtil.calendar(DateUtil.beginOfDay(new Date()));
            calendar.add(Calendar.MONTH, -1);
            form.setFlightTimeStartDate(calendar.getTime());
        }
    }

    private Date getDateByLocalDateTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.of("UTC")).toInstant());
    }

    private Date getDateByLocalDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.of("UTC")).toInstant());
    }


    @Override
    public void exportFlightBusinessDataTemplate(HttpServletResponse response) {
        List<IndAllIndicatorRetrVo> serviceDict = getServiceDict();
        List<String> variableName =
                serviceDict.stream().map(IndAllIndicatorRetrVo::getName).collect(Collectors.toList());
        ExcelFlightUtils.exportFlightBusinessDataTemplate(response, variableName);
    }

}
