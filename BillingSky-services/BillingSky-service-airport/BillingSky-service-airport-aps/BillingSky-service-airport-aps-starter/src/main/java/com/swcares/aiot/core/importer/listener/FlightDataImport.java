package com.swcares.aiot.core.importer.listener;

import com.alibaba.fastjson2.JSON;
import com.rabbitmq.client.Channel;
import com.swcares.aiot.core.common.util.RedisLockUtils;
import com.swcares.aiot.core.importer.entity.*;
import com.swcares.aiot.core.importer.service.FlightDataImportService;
import com.swcares.aiot.core.importer.vo.*;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * ClassName：com.swcares.importer.listener.FlightDataImport
 * Description：数据中台数据导入
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/9/13 17:14
 * @version v1.0
 */
@Service
@Slf4j
public class FlightDataImport {

    public static final String APS_BASE_KEY = "aps:base_flight:";
    @Resource
    private FlightDataImportService flightDataImportService;
    @Resource
    private RedisLockUtils redisLockUtils;
    @Resource
    private RedissonClient redissonClient;

    /*@RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "aps.flight", durable = "true"),
                    key = {"flightData.base"},
                    exchange = @Exchange(value = "bls.airport.flight.service.exchange", ignoreDeclarationExceptions = "true")
            )
    )*/
    public void flightInfo(Message msg, Channel channel) throws IOException {
        String messageStr = null;
        try {
            messageStr = new String(msg.getBody(), StandardCharsets.UTF_8);
            ImportFlightInfoVo vo = JSON.parseObject(messageStr, ImportFlightInfoVo.class);
            TenantHolder.setTenant(vo.getTenantId());
            List<BaseFlightInfo> baseFlightInfos = vo.getData();
            for (BaseFlightInfo bf : baseFlightInfos) {
                flightInfoBiz0(bf, vo);
            }
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("FlightDataImport.flightInfo error, messageStr = {}", messageStr, e);
            channel.basicNack(msg.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantHolder.clear();
        }
    }

    private void flightInfoBiz0(BaseFlightInfo bf, ImportFlightInfoVo vo) {
        String lockKey = APS_BASE_KEY + vo.getTenantId() + ":" + bf.getId();
        try {
            RLock lock = redissonClient.getLock(lockKey);
            lock.lock(30L, TimeUnit.SECONDS);
            flightDataImportService.importFlightData(bf, true);
        } catch (Exception e) {
            log.error("数据中心导入航班失败 bf = {}", bf, e);
        } finally {
            redisLockUtils.unlock(lockKey);
        }
    }

    /*@RabbitListener(
            bindings = @QueueBinding(value = @Queue(value = "aps.cargo", durable = "true"),
                    key = {"flightData.cargo"},
                    exchange = @Exchange(value = "bls.airport.flight.service.exchange", ignoreDeclarationExceptions = "true")
            )
    )*/
    public void cargoInfo(Message msg, Channel channel) throws IOException {
        String messageStr = new String(msg.getBody(), StandardCharsets.UTF_8);
        ImportFlightCargoVo vo = JSON.parseObject(messageStr, ImportFlightCargoVo.class);
        try {
            TenantHolder.setTenant(vo.getTenantId());
            List<BaseFlightCargo> baseFlightCargos = vo.getData();
            for (BaseFlightCargo bfc : baseFlightCargos) {
                cargoInfoBiz0(bfc, vo);
            }
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("deal queue=aps.cargo  message error! messageStr = {}", messageStr, e);
            channel.basicNack(msg.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantHolder.clear();
        }
    }

    private void cargoInfoBiz0(BaseFlightCargo bfc, ImportFlightCargoVo vo) {
        String lockKey = APS_BASE_KEY + vo.getTenantId() + ":" + bfc.getBaseFlightId();
        try {
            RLock lock = redissonClient.getLock(lockKey);
            lock.lock(30L, TimeUnit.SECONDS);
            flightDataImportService.importFlightCargo(bfc, true);
        } catch (Exception e) {
            log.error("数据中心导入货邮失败! bfc = {}", bfc, e);
        } finally {
            redisLockUtils.unlock(lockKey);
        }
    }

    /*@RabbitListener(
            bindings = @QueueBinding(value = @Queue(value = "aps.traveler", durable = "true"),
                    key = {"flightData.traveler"},
                    exchange = @Exchange(value = "bls.airport.flight.service.exchange", ignoreDeclarationExceptions = "true")
            )
    )*/
    public void traveler(Message msg, Channel channel) throws IOException {
        String messageStr = new String(msg.getBody(), StandardCharsets.UTF_8);
        ImportFlightTravelerVo vo = JSON.parseObject(messageStr, ImportFlightTravelerVo.class);
        try {
            TenantHolder.setTenant(vo.getTenantId());
            List<BaseFlightTraveler> baseFlightTravelers = vo.getData();
            for (BaseFlightTraveler bft : baseFlightTravelers) {
                travelerBiz0(bft, vo);
            }
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("数据中心导入航班旅客失败! msg = {}", msg, e);
            channel.basicNack(msg.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantHolder.clear();
        }
    }

    private void travelerBiz0(BaseFlightTraveler bft, ImportFlightTravelerVo vo) {
        String lockKey = APS_BASE_KEY + vo.getTenantId() + ":" + bft.getBaseFlightId();
        try {
            RLock lock = redissonClient.getLock(lockKey);
            lock.lock(30L, TimeUnit.SECONDS);
            flightDataImportService.importFlightTraveler(bft, true);
        } catch (Exception e) {
            log.error("数据中心导入航班旅客失败! bft = {}", bft, e);
        } finally {
            redisLockUtils.unlock(lockKey);
        }
    }


    /*@RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "aps.split.cargo", durable = "true"),
                    key = {"flightData.split.cargo"},
                    exchange = @Exchange(value = "bls.airport.flight.service.exchange", ignoreDeclarationExceptions = "true")
            )
    )*/
    public void splitCargo(Message msg, Channel channel) throws IOException {
        String messageStr = new String(msg.getBody(), StandardCharsets.UTF_8);
        ImportFlightCargoSegmentVo vo = JSON.parseObject(messageStr, ImportFlightCargoSegmentVo.class);
        try {
            TenantHolder.setTenant(vo.getTenantId());
            List<BaseFlightCargoSegment> baseFlightCargos = vo.getData();
            for (BaseFlightCargoSegment bft : baseFlightCargos) {
                splitCargoBiz0(bft);
            }
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("数据中心导入拆分货邮失败！messageStr= {}", messageStr, e);
            channel.basicNack(msg.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantHolder.clear();
        }
    }

    private void splitCargoBiz0(BaseFlightCargoSegment bft) {
        try {
            flightDataImportService.importSplitFlightCargo(bft);
        } catch (Exception e) {
            log.error("数据中心导入拆分货邮失败 bft={}", bft, e);
        }
    }

    /*@RabbitListener(
            bindings = @QueueBinding(value = @Queue(value = "aps.split.traveler", durable = "true"),
                    key = "flightData.split.traveler",
                    exchange = @Exchange(value = "bls.airport.flight.service.exchange", ignoreDeclarationExceptions = "true")
            )
    )*/
    public void splitTraveler(Message msg, Channel channel) throws IOException {
        String messageStr = new String(msg.getBody(), StandardCharsets.UTF_8);
        ImportFlightTravelerSegmentVo vo =
                JSON.parseObject(messageStr, ImportFlightTravelerSegmentVo.class);
        try {
            TenantHolder.setTenant(vo.getTenantId());
            List<BaseFlightTravelerSegment> baseFlightTravelers = vo.getData();
            for (BaseFlightTravelerSegment bft : baseFlightTravelers) {
                splitTravelerBiz0(bft);
            }
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("数据中心导入旅客失败！messageStr= {}", messageStr, e);
            channel.basicNack(msg.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantHolder.clear();
        }
    }

    private void splitTravelerBiz0(BaseFlightTravelerSegment bft) {
        try {
            flightDataImportService.importSplitFlightTraveler(bft);
        } catch (Exception e) {
            log.error("数据中心导入拆分旅客失败! bft={}", bft, e);
        }
    }


}
