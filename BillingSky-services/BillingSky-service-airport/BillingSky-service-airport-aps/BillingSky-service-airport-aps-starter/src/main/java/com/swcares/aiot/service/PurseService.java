package com.swcares.aiot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.model.dto.ErPurseDto;
import com.swcares.aiot.core.model.dto.ErPursePageDto;
import com.swcares.aiot.core.model.vo.ErPursePageVo;
import com.swcares.aiot.core.model.vo.ErPursePeVo;
import com.swcares.aiot.core.model.vo.ErPurseVo;
import com.swcares.baseframe.common.base.BaseResult;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.PurseService
 * Description：离港返还钱包管理Service
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/28 11:28
 * @version v1.0
 */
public interface PurseService {

    /**
     * Title：page
     * Description：钱包分页接口
     * author：liuzhiheng
     * date：2025/2/27 09:47
     * @param erPursePageDto: page参数
     * return: BaseResult<Page<ErPursePageVo>>
     */
    BaseResult<Page<ErPursePageVo>> page(ErPursePageDto erPursePageDto);

    /**
     * Title：addErPurse
     * Description：新增钱包
     * author：liuzhiheng
     * date：2025/2/27 10:16
     * @param erPurseDto: 入参
     * return: BaseResult<Boolean>
     */
    BaseResult<Boolean> addErPurse(ErPurseDto erPurseDto);

    /**
     * Title：updateErPurse
     * Description：编辑钱包
     * author：liuzhiheng
     * date：2025/2/27 10:18
     * @param erPurseDto:  入参
     * return: BaseResult<Boolean>
     */
    BaseResult<Boolean> updateErPurse( ErPurseDto erPurseDto);
    /**
     * Title：getById
     * Description：根据Id查询钱包
     * author：liuzhiheng
     * date：2025/2/28 13:34
     * @param id:
     * return: BaseResult<ErPurseVo>
     */
    BaseResult<ErPurseVo> getById(Long id);

    /**
     * Title：getErPursePeList
     * Description：查询收收款方钱包信息
     * author：liuzhiheng
     * date：2025/2/28 17:05
     * return: BaseResult<List<ErPursePeVo>>
     */
    BaseResult<List<ErPursePeVo>> getErPursePeList();
}
