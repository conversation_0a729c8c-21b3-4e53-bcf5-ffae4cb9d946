package com.swcares.aiot.controller;

import com.swcares.aiot.TenantConvertUtil;
import com.swcares.aiot.client.IExcessLuggageAdjustmentOrderApplyExamineBizClient;
import com.swcares.aiot.client.IExcessLuggageOrderHistoryBizClient;
import com.swcares.aiot.client.ILuggageAdjustmentOrderHandleBizClient;
import com.swcares.aiot.client.ILuggageOrderHandleBizClient;
import com.swcares.aiot.core.cons.CommonCons;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.model.dto.ApplyAdjustmentDTO;
import com.swcares.aiot.model.dto.ExamineAdjustmentDTO;
import com.swcares.aiot.model.dto.ExcessLuggageAdjustmentOrderPageDto;
import com.swcares.aiot.model.dto.ExcessLuggageOrderDto;
import com.swcares.aiot.model.dto.ExcessLuggageOrderHistoryPageDto;
import com.swcares.aiot.model.dto.ExcessLuggageOrderPageDto;
import com.swcares.aiot.model.vo.ExcessLuggageAdjustmentOrderPageClientVo;
import com.swcares.aiot.model.vo.ExcessLuggageAdjustmentOrderVO;
import com.swcares.aiot.model.vo.ExcessLuggageOrderAnotherVo;
import com.swcares.aiot.model.vo.ExcessLuggageOrderHistoryPageVo;
import com.swcares.aiot.model.vo.ExcessLuggageOrderHistoryVo;
import com.swcares.aiot.model.vo.ExcessLuggageOrderPageClientVo;
import com.swcares.aiot.model.vo.ExcessLuggageOrderTotalVo;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.LuggageOrderHandleApsBizController <br>
 * Description：机场端逾重行李查询 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024/12/2 <br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@ApiVersion("机场端-行李-api")
@RequestMapping("/luggageOrderHandleApsBiz")
@Api(value = "LuggageOrderHandleApsBizController", tags = {"机场端逾重行李接口"})
public class LuggageOrderHandleApsBizController {

    @Resource
    private ILuggageOrderHandleBizClient luggageOrderHandleBizClient;

    @Resource
    private ILuggageAdjustmentOrderHandleBizClient iLuggageAdjustmentOrderHandleBizClient;

    @Resource
    private IExcessLuggageOrderHistoryBizClient iExcessLuggageOrderHistoryBizClient;

    @Resource
    private IExcessLuggageAdjustmentOrderApplyExamineBizClient iExcessLuggageAdjustmentOrderApplyExamineBizClient;

    @ApiOperation(value = "机场端-逾重行李分页查询接口")
    @PostMapping("/page")
    PagedResult<List<ExcessLuggageOrderPageClientVo>> page(@RequestBody @Validated ExcessLuggageOrderPageDto pageDto) {
        excessLuggageOrderPageDto(pageDto);
        return luggageOrderHandleBizClient.clientPage(pageDto);
    }

    @ApiOperation(value = "查询-合计总额")
    @PostMapping("/totalAmount")
    public BaseResult<ExcessLuggageOrderTotalVo> totalAmount(@RequestBody ExcessLuggageOrderPageDto pageDto) {
        excessLuggageOrderPageDto(pageDto);
        return luggageOrderHandleBizClient.totalAmount(pageDto);
    }

    @ApiOperation(value = "导出日账单")
    @PostMapping("/export")
    public void totalAmount(@RequestBody ExcessLuggageOrderPageDto pageDto, HttpServletResponse response) {
        excessLuggageOrderPageDto(pageDto);
        try {
            byte[] excelBytes = luggageOrderHandleBizClient.export(pageDto);
            // 设置响应头
            String fileName = "对账通逾重托运行李日账单明细_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 写入响应流
            response.getOutputStream().write(excelBytes);
            response.flushBuffer();
        } catch (Exception e) {
            log.error("导出文件出错", e);
        }
    }

    private void excessLuggageOrderPageDto(ExcessLuggageOrderPageDto pageDto) {
        pageDto.setStatus(CommonCons.UNALLOCATED);
    }

    @ApiOperation(value = "根据ID查询申请逾重行李账单")
    @GetMapping("/adjustmentApply/{luggageOrderId}/{customerCode}")
    public BaseResult<ExcessLuggageAdjustmentOrderVO> adjustmentApply(@PathVariable("luggageOrderId") Long luggageOrderId, @PathVariable("customerCode") String customerCode) {
        return iLuggageAdjustmentOrderHandleBizClient.getExcessLuggageOrderById(luggageOrderId, customerCode, "apply");
    }

    @ApiOperation(value = "根据ID查询审核逾重行李账单")
    @GetMapping("/adjustmentExamine/{luggageOrderId}/{customerCode}")
    public BaseResult<ExcessLuggageAdjustmentOrderVO> adjustmentExamine(@PathVariable("luggageOrderId") Long luggageOrderId, @PathVariable("customerCode") String customerCode) {
        return iLuggageAdjustmentOrderHandleBizClient.getExcessLuggageOrderById(luggageOrderId, customerCode, null);
    }

    @ApiOperation(value = "申请调账")
    @PostMapping("/applyAdjustment")
    public BaseResult<Object> applyAdjustment(@Validated @RequestBody ApplyAdjustmentDTO dto) {
        return iLuggageAdjustmentOrderHandleBizClient.applyAdjustment(dto);
    }

    @ApiOperation(value = "调账账单分页")
    @PostMapping("/adjustmentPage")
    public PagedResult<List<ExcessLuggageAdjustmentOrderPageClientVo>> adjustmentPage(@RequestBody ExcessLuggageAdjustmentOrderPageDto dto) {
        return iLuggageAdjustmentOrderHandleBizClient.clientPage(dto);
    }

    @ApiOperation(value = "逾重行李-账单记录分页查询")
    @PostMapping("/adjustmentOrderPage")
    public PagedResult<List<ExcessLuggageOrderHistoryPageVo>> adjustmentOrderPage(@RequestBody @Validated ExcessLuggageOrderHistoryPageDto pageDto) {
        String tenantCode = TenantConvertUtil.getTenantCode(TenantHolder.getTenant());
        if (ObjectUtils.isEmpty(tenantCode)) {
            log.error("机场端-逾重行李账单记录分页查询未获取到当前租户的编码！tenantId={}", TenantHolder.getTenant());
            throw new BusinessException(ExceptionCodes.TENANT_CODE_NOT_FOUND_ERROR);
        }
        pageDto.setBillCustomerCodes(Collections.singletonList(tenantCode));
        return iExcessLuggageOrderHistoryBizClient.page(pageDto);
    }

    @GetMapping("/getExcessLuggageOrderHistoryById")
    @ApiOperation(value = "通过参数查询账单记录详情")
    public BaseResult<ExcessLuggageOrderHistoryVo> getExcessLuggageOrderHistoryById(@ApiParam(value = "主键id", required = true) @RequestParam("id") Long id,
                                                                                    @ApiParam(value = "机场/航司（账单客户code）") @RequestParam(value = "billCustomerCode") String billCustomerCode) {
        return iExcessLuggageOrderHistoryBizClient.getExcessLuggageOrderHistoryById(id, billCustomerCode);
    }

    @ApiOperation(value = "审核调账")
    @PostMapping("/examineAdjustment")
    public BaseResult<Object> examineAdjustment(@Validated @RequestBody ExamineAdjustmentDTO dto) {
        return iExcessLuggageAdjustmentOrderApplyExamineBizClient.examineAdjustment(dto);
    }

    @ApiOperation("获取账单的分摊主体数")
    @GetMapping("/getSharingSubjectNumber")
    BaseResult<Object> getSharingSubjectNumber(@RequestParam("id") Long id, @RequestParam("excessLuggageOrderSharingId") Long excessLuggageOrderSharingId) {
        ExcessLuggageOrderDto dto = new ExcessLuggageOrderDto(id, null, excessLuggageOrderSharingId);
        return luggageOrderHandleBizClient.getSharingSubjectNumber(dto);
    }

    @ApiOperation(value = "根据ID查询是否有相同逾重行李账单数据")
    @GetMapping("/getById/{luggageOrderId}")
    public BaseResult<List<ExcessLuggageOrderAnotherVo>> getExcessLuggageOrderVoById(@PathVariable("luggageOrderId") Long luggageOrderId) {
        return iLuggageAdjustmentOrderHandleBizClient.getExcessLuggageOrderVoById(luggageOrderId);
    }
}
