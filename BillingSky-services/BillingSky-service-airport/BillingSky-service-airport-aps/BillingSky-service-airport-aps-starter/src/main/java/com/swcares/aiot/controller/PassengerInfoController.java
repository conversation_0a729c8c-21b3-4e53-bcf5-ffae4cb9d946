package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.valid.group.Insert;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.PassengerInfoForm;
import com.swcares.aiot.core.form.PassengerInfoSearchForm;
import com.swcares.aiot.core.model.entity.PassengerInfo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.PassengerInfoService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * ClassName：PassengerInfoController <br>
 * Description：旅客信息操作接口<br>
 * Copyright © 2020/5/14 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/5/14 16:00<br>
 * @version v1.0 <br>
 */

@Slf4j
@RestController
@RequestMapping("/api/passengerInfo")
@Api(value = "PassengerInfoController", tags = {"旅客信息操作接口"})
@Validated
public class PassengerInfoController {

    @Resource
    private PassengerInfoService passengerInfoService;

    @GetMapping("/pagePassengerInfoByFlightId")
    @ApiOperation(value = "通过航班id分页查询该航班所有旅客信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功！", response = PassengerInfo.class)
    })
    public Object pagePassengerInfoByFlightId(@Validated @ApiParam(name = "pageParam", value = "分页/条件") PageParam pageParam,
                                              @ApiParam(name = "flightId", value = "航班id", required = true)
                                              @RequestParam @NotBlank(message = "flightId不能为空")
                                              @Size(min = 1, max = 128, message = "flightId必须为1到128个字符长度") String flightId) {
        return passengerInfoService.pagePassengerInfoByFlightId(pageParam, flightId);
    }

    @GetMapping("/pagePassengerInfoByCondition")
    @ApiOperation(value = "动态条件分页查询指定航班的旅客信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功！", response = PassengerInfo.class)
    })
    public Object pagePassengerInfoByCondition(@Validated @ApiParam(name = "pageParam", value = "分页/条件") PageParam pageParam,
                                               @Validated @ApiParam(name = "passengerInfoSearchForm", value = "旅客信息查询条件")
                                               PassengerInfoSearchForm passengerInfoSearchForm) {
        return passengerInfoService.pagePassengerInfoByCondition(pageParam, passengerInfoSearchForm);
    }

    @PostMapping("/savePassengerInfo")
    @ApiOperation(value = "新增旅客信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功！", response = Object.class)
    })
    public Object savePassengerInfo(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                    @RequestBody @Validated({Insert.class})
                                    @ApiParam(name = "passengerInfoForm", value = "旅客信息表单")
                                    PassengerInfoForm passengerInfoForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return passengerInfoService.savePassengerInfo(passengerInfoForm, user, urlName);
    }

    /**
     * Title: getPassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (通过旅客id查询旅客信息)<br>
     * Date:  9:33 <br>
     *
     * @param pid return: java.lang.Object
     */
    @GetMapping("/getPassengerInfoById")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功！", response = PassengerInfo.class)
    })
    @ApiOperation(value = "根据旅客id查询旅客信息")
    public Object getPassengerInfoById(@RequestParam @ApiParam(name = "pid", value = "旅客id")
                                       @NotBlank(message = "pid不能为空") String pid) {
        return passengerInfoService.getPassengerInfoById(pid);
    }


    /**
     * Title: updatePassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (更新旅客信息)<br>
     * Date:  18:34 <br>
     *
     * @param passengerInfoForm return: java.lang.Object
     */
    @PutMapping("/updatePassengerInfoById")
    @ApiOperation(value = "更新旅客信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功！", response = Object.class)
    })
    public Object updatePassengerInfoById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                          @RequestBody @Validated({Update.class}) @ApiParam(name = "passengerInfoForm", value = "旅客信息表单")
                                          PassengerInfoForm passengerInfoForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return passengerInfoService.updatePassengerInfoById(passengerInfoForm, user, urlName);
    }

    /**
     * Title: delPassengerInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (批量删除旅客信息,即数据有效状态设置为0)<br>
     * Date:  18:53 <br>
     *
     * @param ids return: java.lang.Object
     */
    @DeleteMapping("/delPassengerInfoById")
    @ApiOperation(value = "批量删除旅客信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功！", response = Object.class)
    })
    public Object delPassengerInfoById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                       @RequestParam @ApiParam(name = "ids", value = "批量删除的id,以逗号分隔", required = true)
                                       @NotBlank(message = "ids不能为空") String ids) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return passengerInfoService.delPassengerInfoById(ids, user, urlName);
    }

    @ApiOperation(value = "批量导出旅客信息")
    @GetMapping("/exportPassengerInfoBatch")
    public void exportPassengerInfoBatch(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                         @Validated @ApiParam(name = "passengerInfoSearchForm", value = "旅客信息查询条件")
                                         PassengerInfoSearchForm passengerInfoSearchForm, HttpServletResponse response) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        passengerInfoService.exportPassengerInfoBatch(passengerInfoSearchForm, response, urlName, user);
    }
}
