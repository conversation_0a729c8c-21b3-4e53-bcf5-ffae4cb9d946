package com.swcares.aiot.controller.feign;

import com.swcares.aiot.client.IAircraftInfoClient;
import com.swcares.aiot.core.entity.AircraftAirlineInfoVo;
import com.swcares.aiot.core.entity.AircraftInfoVo;
import com.swcares.aiot.service.AircraftService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.controller.feign.AircraftFeignController
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/13 11:27
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/feign/api/aircraft")
@Api(value = "AircraftFeignController", tags = {"feign飞机操作接口"})
@Slf4j
public class AircraftFeignController implements IAircraftInfoClient {

    @Resource
    private AircraftService aircraftService;

    @Override
    @ApiOperation(value = "通过飞机号查询飞机信息表中的航班信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AircraftAirlineInfoVo.class)})
    @GetMapping(value = "/listAirlineInfoByRegno")
    public List<AircraftAirlineInfoVo> listAirlineInfoByRegno(@RequestParam @ApiParam(name = "regNo", value = "飞机注册号") String regNo,
                                                              @RequestParam @ApiParam(name = "effectiveDate", value = "生效时间")
                                                              @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate effectiveDate) {
        return aircraftService.listAirlineInfoByRegno(regNo, effectiveDate);
    }

    @Override
    @ApiOperation(value = "通过航司简称模糊查询飞机信息表中的航班信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AircraftAirlineInfoVo.class)})
    @GetMapping(value = "/listAirlineInfoByAirlineShortName")
    public List<AircraftAirlineInfoVo> listAirlineInfoByAirlineShortName(@RequestParam @ApiParam(name = "airlineShortName", value = "航司简称")
                                                                         String airlineShortName) {
        return aircraftService.listAirlineInfoByAirlineShortName(airlineShortName);
    }

    @Override
    @ApiOperation(value = "获取传入时间点之后更新或新增的飞机信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = AircraftInfoVo.class)})
    @GetMapping(value = "/getAirraftInfoByUpdateTime")
    public List<AircraftInfoVo> getAirraftInfoByUpdateTime(@RequestParam @ApiParam(name = "updateTime", value = "更新时间")@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime updateTime){
        return aircraftService.getAirraftInfoByUpdateTime( updateTime);
    }
}
