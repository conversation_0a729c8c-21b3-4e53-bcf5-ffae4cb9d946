package com.swcares.aiot.service;

import com.swcares.aiot.core.model.dto.FlightInfoConfirmDto;
import com.swcares.baseframe.common.security.LoginUserDetails;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.NewFlightInfoService
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/7 15:47
 * @version v1.0
 */
public interface NewFlightInfoService {

    List<String> confirmFlightInfo(FlightInfoConfirmDto dto);

    void cancelConfirmFlightInfo(String flightIds, LoginUserDetails user);

    List<String> confirmFlightBusinessInfo(FlightInfoConfirmDto dto);

    void cancelConfirmFlightBusinessInfo(String flightIds, LoginUserDetails user);


}
