package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.TenantUtil;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.core.common.util.FormulaUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.FeeForm;
import com.swcares.aiot.core.form.FormulaForm;
import com.swcares.aiot.core.form.ServiceFeeForm;
import com.swcares.aiot.core.model.entity.FeeInfo;
import com.swcares.aiot.core.model.entity.FormulaInfo;
import com.swcares.aiot.core.model.vo.FeeCodeAndNameVo;
import com.swcares.aiot.core.model.vo.FeeVo;
import com.swcares.aiot.core.model.vo.FormulaVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.FeeDao;
import com.swcares.aiot.dao.FormulaDao;
import com.swcares.aiot.service.FeeService;
import com.swcares.aiot.service.FormulaService;
import com.swcares.aiot.service.LogService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.swcares.aiot.core.common.util.FormulaUtils.checkFormulaData;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a>
 * Title：FeeServiceImpl.java
 * Package：com.swcares.modules.settlement.service.impl
 * Description：
 * Copyright © 2020-6-4 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * date 2020-6-4 17:10
 */
@Service
@Slf4j
public class FeeServiceImpl implements FeeService {
    private static final String ERROR_MSG = "出现业务异常";

    @Value("${swcares.tenant.subsystem-code}")
    private String systemCode;
    @Resource
    private FeeDao feeDao;
    @Resource
    private FormulaDao formulaDao;
    @Resource
    private FormulaService formulaService;
    @Resource
    private LogService logService;

    @Override
    public ResultBuilder<Page<FeeInfo>> pageFeeRulesByCondition(String condition, String airportCode, String feeType, PageParam pageParam) {
        if (CharSequenceUtil.isBlank(condition)) {
            condition = null;
        }
        if (CharSequenceUtil.isBlank(airportCode)) {
            airportCode = null;
        }
        Pageable pageable = PageRequest.of(pageParam.getPage() - 1, pageParam.getLimit());
        return new ResultBuilder.Builder<Page<FeeInfo>>().data(feeDao.pageFeeRulesByCondition(condition, airportCode, feeType, pageable)).builder();
    }

    @Override
    public ResultBuilder<FeeInfo> getFeeRulesById(String feeId) {
        return new ResultBuilder.Builder<FeeInfo>().data(feeDao.findByFeeId(feeId)).builder();
    }

    @Override
    public ResultBuilder<List<FeeInfo>> listFeeByFeeTypeAndAirportCode(String feeType, String airportCode, String airlineId, String name) {
        return new ResultBuilder.Builder<List<FeeInfo>>().data(feeDao.listFeeByFeeTypeAndAirportCode(feeType, airportCode, airlineId, name)).builder();
    }

    @Override
    @Transactional
    public ResultBuilder<List<FeeInfo>> copyFeeByFeeId(List<String> feeIds, String airlineId, LoginUserDetails user) {
        String airportCode = "";
        for (String feeId : feeIds) {
            //查询模板费用信息
            FeeInfo feeInfo = feeDao.findByFeeId(feeId);
            if (CharSequenceUtil.isBlank(airportCode)) {
                airportCode = feeInfo.getAirportCode();
            }
            inspectFeeRule(feeDao.findAirlineFeeByFeeCode(feeInfo.getFeeCode(), airlineId) != null, BusinessMessageEnum.CREATE_FEE_ERROR);
            //查询模板费用下的所有公式
            List<FormulaInfo> formulaInfos = formulaDao.listFormulaByFeeId(feeId);
            //创建新的费用
            FeeInfo newFeeInfo = new FeeInfo();
            //将新费用和航司关联起来
            newFeeInfo.setAirlineId(airlineId);
            newFeeInfo.setCreateBy(user.getUsername());
            newFeeInfo.setCreateTime(new Date());
            FeeForm feeForm = new FeeForm();
            //复制模板费用
            try {
                BeanUtils.copyProperties(feeForm, feeInfo);
                BeanUtils.copyProperties(newFeeInfo, feeForm);
            } catch (IllegalAccessException | InvocationTargetException e) {
                throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
            }
            newFeeInfo.setIsExpired(feeInfo.getIsExpired());
            newFeeInfo.setFeePriority(feeInfo.getFeePriority());
            FeeInfo newFee = feeDao.save(newFeeInfo);
            //复制所有公式
            for (FormulaInfo formulaInfo : formulaInfos) {
                copyFeeByFeeIdBiz0(feeIds, airlineId, user, formulaInfo, airportCode, newFee);
            }
        }
        return new ResultBuilder.Builder<List<FeeInfo>>().data(feeDao.listFeeRuleByAirlineId(airlineId, airportCode)).builder();
    }

    private void copyFeeByFeeIdBiz0(List<String> feeIds, String airlineId, LoginUserDetails user, FormulaInfo formulaInfo, String airportCode, FeeInfo newFee) {
        FormulaForm formulaForm = new FormulaForm();
        FormulaInfo newformula = new FormulaInfo();
        try {
            BeanUtils.copyProperties(formulaForm, formulaInfo);
            BeanUtils.copyProperties(newformula, formulaForm);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
        }
        //判断公式是否引用费用
        String formula = newformula.getFormula();
        if (formula.contains("[") && formula.contains("]")) {
            //获取引用模版费用id
            String modelId = formula.substring(formula.indexOf("[") + 1, formula.indexOf("]"));
            //被引用的费用是否在本次coyp的feeIds中
            if (!feeIds.contains(modelId)) {
                //判断该航司是否已引用被引用费用
                FeeInfo modelFeeInfo = feeDao.findByFeeId(modelId);
                List<FeeInfo> feeList = feeDao.getFeeInfoByAirlineAndModelId(airlineId, airportCode, modelFeeInfo.getFeeCode());
                if (feeList.isEmpty()) {
                    throw new GenericException(BusinessMessageEnum.QUOTE_FEE_MISSING_1.getCode(),
                            BusinessMessageEnum.QUOTE_FEE_MISSING_1.getMsg() + formulaInfo.getFormulaName()
                                    + BusinessMessageEnum.QUOTE_FEE_MISSING_2.getMsg() + modelFeeInfo.getFeeName()
                                    + BusinessMessageEnum.QUOTE_FEE_MISSING_3.getMsg());
                }
            }
        }
        //将复制出的新公式和费用关联起来
        newformula.setFeeRulesId(newFee.getId());
        newformula.setCreateBy(user.getUsername());
        newformula.setCreateTime(new Date());
        formulaDao.save(newformula);
    }

    @Override
    @Transactional
    public ResultBuilder<Object> delFeeRulesById(String feeId, LoginUserDetails user, String urlName) {
        FeeInfo feeInfo = feeDao.findByFeeId(feeId);
        List<FormulaInfo> formulaInfos = formulaDao.listFormulaByFeeId(feeId);

        //判断有无公式引用该费用
        if (!feeInfo.getFeePriority().equals(0)) {
            //判断引用该费用的公式是否有效
            throw new GenericException(BusinessMessageEnum.FORMULA_DEL_ERROR_1.getCode(), BusinessMessageEnum.FORMULA_DEL_ERROR_1.getMsg());
        }
        //如果存在有效公式引用了此费用，也不允许删除
        if (!formulaDao.getFormulaByQuoteFeeId(feeId).isEmpty()) {
            throw new GenericException(BusinessMessageEnum.FORMULA_DEL_ERROR_2.getCode(), BusinessMessageEnum.FORMULA_DEL_ERROR_2.getMsg());
        }

        formulaDao.deleteByFeeId(feeId, user.getUsername(), new Date());
        feeDao.deleteByFeeId(feeId, user.getUsername(), new Date());

        for (FormulaInfo formula : formulaInfos
        ) {
            //判断该公式是否引用了费用
            if (formula.getFormula().contains("[") && formula.getFormula().contains("]")) {
                //修改引用费用的费用等级
                FeeInfo feeInfo1 = feeDao.findByFeeId(formula.getFormula().substring(formula.getFormula().indexOf("[") + 1, formula.getFormula().indexOf("]")));
                feeInfo1.setFeePriority(feeInfo1.getFeePriority() - 1);
                feeDao.save(feeInfo1);
            }
        }

        if ("C".equals(feeInfo.getFeeType())) {
            String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_DELETE.getValue();
            FeeForm feeForm = new FeeForm();
            try {
                BeanUtils.copyProperties(feeForm, feeInfo);
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(ERROR_MSG, e);
            }
            feeForm.setFeeId(null);
            logService.addLogForDelete(feeForm, user, content, feeForm.getAirportCode());
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<FeeInfo> getFeeByFeeCode(String feeCode, String feeType) {
        return new ResultBuilder.Builder<FeeInfo>().data(feeDao.findByFeeCode(feeCode, feeType)).builder();
    }

    @Override
    @Transactional
    public ResultBuilder<String> saveFeeRule(FeeForm feeForm, LoginUserDetails user, String urlName) {
        //创建费用
        FeeInfo feeInfo = new FeeInfo();
        //判断费用代码的重复
        inspectFeeRule(feeDao.findAirlineFeeByFeeCode(feeForm.getAirportCode(), feeForm.getAirlineId()) != null, BusinessMessageEnum.CREATE_FEE_ERROR);
        //校验公式格式
        checkFormulaData(feeForm);
        feeInfo.setAirportCode(feeForm.getAirportCode());
        feeInfo.setFeeCode(feeForm.getFeeCode());
        feeInfo.setFeeName(feeForm.getFeeName());
        feeInfo.setFeeType(feeForm.getFeeType());
        feeInfo.setCreateBy(user.getUsername());
        //指定航司id 是否机务费用 3.3优化
        feeInfo.setAirlineId(feeForm.getAirlineId());
        feeInfo.setIsServiceFee(feeForm.getIsServiceFee());
        feeInfo.setCreateTime(new Date());
        FeeInfo fee = feeDao.save(feeInfo);
        //费用下创建公式
        FormulaInfo formulaInfo = new FormulaInfo();

        formulaInfo.setFeeRulesId(fee.getId());
        formulaInfo.setFormulaName(feeForm.getFormulaName());
        formulaInfo.setDescription(feeForm.getDescription());
        formulaInfo.setAirportLevel(feeForm.getAirportLevel());
        formulaInfo.setAirportType(feeForm.getAirportType());
        formulaInfo.setFlightType(String.join(",",feeForm.getFlightType()));
        formulaInfo.setBelongWay(feeForm.getBelongWay());
        formulaInfo.setPricingWay(feeForm.getPricingWay());
        formulaInfo.setFeeUnit(feeForm.getFeeUnit());
        formulaInfo.setAirlineId(feeForm.getAirlineId());
        //新增公式生效时间
        formulaInfo.setStartDate(feeForm.getStartTime());
        formulaInfo.setEndDate(feeForm.getEndTime());
        //设置是否过站
        formulaInfo.setIsTransit(feeForm.getIsTransit());
        formulaInfo.setAltSpecial(feeForm.getAltSpecial());
        formulaInfo.setSpecialVariable(feeForm.getSpecialVariable());
        formulaInfo.setSafeguardType(feeForm.getSafeguardType());

        if (feeForm.getPricingWay().equals(Constants.FormulaEnum.PRICING_WAY_CONDITION.getValue())) {
            List<FormulaVo> list = feeForm.getFormulaVoList();
            feeForm.setFormula(FormulaUtils.formatFormula(list));
        } else {
            if (CharSequenceUtil.isBlank(feeForm.getFormula())) {
                throw new GenericException(BusinessMessageEnum.INPUT_ERROR.getCode(), BusinessMessageEnum.INPUT_ERROR.getMsg());
            }
        }
        inspectFeeRule(FileUtils.hasChinese(feeForm.getFormula()), BusinessMessageEnum.FORMULA_FORMAT_ERROR);
        String formula = feeForm.getFormula();
        //根据公式中的引用变量，判断公式参数类型
        //参数为航班确认数据
        boolean flightVar = false;
        //参数为保障确定数据
        boolean serviceVar = false;
        if (formula.contains("{F") || formula.contains("{P")) {
            flightVar = true;
        }
        if (formula.contains("{C")) {
            serviceVar = true;
        }
        if (flightVar && serviceVar) {
            formulaInfo.setFormulaType("4");
        } else if (serviceVar) {
            formulaInfo.setFormulaType("3");
        } else if (flightVar) {
            formulaInfo.setFormulaType("2");
        } else {
            formulaInfo.setFormulaType("1");
        }
        formulaInfo.setFormula(formula);
        formulaInfo.setCreateTime(new Date());
        formulaInfo.setCreateBy(user.getUsername());
        formulaDao.save(formulaInfo);
        formulaService.changeIsExpired(feeInfo);
        //判断该公式是否引用了费用
        if (formulaInfo.getFormula().contains("[") && formulaInfo.getFormula().contains("]")) {
            //修改引用费用的费用等级
            FeeInfo feeInfo1 = feeDao.findByFeeId(formulaInfo.getFormula().substring(formulaInfo.getFormula().indexOf("[") + 1, formulaInfo.getFormula().indexOf("]")));
            feeInfo1.setFeePriority(feeInfo1.getFeePriority() + 1);
            feeDao.save(feeInfo1);
        }
        if ("C".equals(feeForm.getFeeType())) {
            String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_SAVE.getValue();
            feeForm.setFeeId(null);
            logService.addLogForSave(feeForm, user, content, feeForm.getAirportCode());
        }
        return new ResultBuilder.Builder<String>().data(fee.getId()).builder();
    }

    private void inspectFeeRule(boolean feeDao, BusinessMessageEnum createFeeError) {
        if (feeDao) {
            log.error(createFeeError.getMsg());
            throw new GenericException(createFeeError.getCode(), createFeeError.getMsg());
        }
    }

    @Override
    public ResultBuilder<List<FeeInfo>> listFeeRulesByFeeName(String feeName, String airportCode) {
        return new ResultBuilder.Builder<List<FeeInfo>>().data(feeDao.listFeeRulesByFeeName(feeName, airportCode)).builder();
    }

    @Override
    public ResultBuilder<List<FeeVo>> listFeeRuleByAirlineId(String airlineId, String flag, String airportCode) {
        List<FeeInfo> feeInfos = feeDao.listFeeRuleByAirlineId(airlineId, airportCode);
        List<FeeVo> feeVos = new ArrayList<>();
        for (FeeInfo fee : feeInfos
        ) {
            boolean flags = false;
            FeeVo feeVo = new FeeVo();
            try {
                BeanUtils.copyProperties(feeVo, fee);
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(ERROR_MSG, e);
                throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
            }
            List<FormulaInfo> formulaInfos;
            if (flag.equals(Constants.FormulaEnum.FORMULA_USED_FLAG.getValue())) {
                formulaInfos = formulaDao.listFormulaByFeeIdAndUsed(fee.getId());
                if (formulaInfos.isEmpty()) {
                    flags = true;
                }
            } else {
                formulaInfos = formulaDao.listFormulaByFeeId(fee.getId());
            }
            if (flags) {
                continue;
            }
            feeVo.setFormulaInfos(formulaInfos);
            feeVos.add(feeVo);
        }
        return new ResultBuilder.Builder<List<FeeVo>>().data(feeVos).builder();
    }

    @Override
    @Transactional
    public ResultBuilder<Object> updateFeeRulesByFeeId(FeeForm feeForm, LoginUserDetails user, String urlName) {

        FeeInfo feeInfo = feeDao.findByFeeId(feeForm.getFeeId());
        FeeForm feeForm1 = new FeeForm();
        try {
            BeanUtils.copyProperties(feeForm1, feeInfo);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new GenericException(BusinessMessageEnum.DATA_NOT_EXIST.getCode(), BusinessMessageEnum.DATA_NOT_EXIST.getMsg());
        }
        feeForm.setFeeId(null);
        if (CharSequenceUtil.isNotBlank(feeForm.getFeeCode())) {
            feeInfo.setFeeCode(feeForm.getFeeCode());
        }
        if (CharSequenceUtil.isNotBlank(feeForm.getFeeName())) {
            feeInfo.setFeeName(feeForm.getFeeName());
        }
        feeInfo.setIsServiceFee(feeForm.getIsServiceFee());
        feeInfo.setModifiedBy(user.getUsername());
        feeInfo.setModifiedTime(new Date());

        feeDao.save(feeInfo);
        if ("C".equals(feeForm.getFeeType())) {
            String content = Constants.LogEnum.LOG_MEG_1.getValue() + urlName + Constants.LogEnum.LOG_MEG_UPDATE.getValue();
            logService.addLogForUpdate(feeForm1, feeForm, user, content, feeInfo.getAirportCode());
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<List<FeeVo>> listFeeByFeeCodeAndFeeType(String feeType, String airportCode, String feeCode, String airlineId) {
        List<FeeInfo> feeInfos = feeDao.listFeeByFeeCodeAndFeeTypeAndAirlineId(feeType, airportCode, feeCode, airlineId);
        List<FeeVo> feeVos = new ArrayList<>();
        for (FeeInfo fee : feeInfos
        ) {
            FeeVo feeVo = new FeeVo();
            try {
                BeanUtils.copyProperties(feeVo, fee);
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(ERROR_MSG, e);
                throw new GenericException(BusinessMessageEnum.COPY_ATTR_ERROR.getCode(), BusinessMessageEnum.COPY_ATTR_ERROR.getMsg());
            }
            List<FormulaInfo> formulaInfos = formulaDao.listFormulaByFeeId(fee.getId());
            feeVo.setFormulaInfos(formulaInfos);
            feeVos.add(feeVo);
        }
        return new ResultBuilder.Builder<List<FeeVo>>().data(feeVos).builder();
    }

    @Override
    @Transactional
    public ResultBuilder<Object> updateServiceFeeByFeeId(List<ServiceFeeForm> listServiceFee, String airportCode) {
        for (ServiceFeeForm serviceFeeForm : listServiceFee
        ) {
            feeDao.updateServiceFeeByFeeId(serviceFeeForm.getFeeId(), serviceFeeForm.getIsServiceFee(), airportCode);
        }
        return new ResultBuilder.Builder<>().builder();
    }

    @Override
    public ResultBuilder<List<String>> getDistinctAllFee(String airportCode) {
        return new ResultBuilder.Builder<List<String>>().data(feeDao.getDistinctAllFee(airportCode)).builder();
    }

    @Override
    public ResultBuilder<List<FeeInfo>> getFeeByFeeCodeAndFeeNameAndAirlineId(String airportCode, String airlineId, String feeCode, String feeName) {
        return new ResultBuilder.Builder<List<FeeInfo>>().data(feeDao.getFeeByFeeCodeAndFeeNameAndAirlineId(airportCode, airlineId, feeCode, feeName)).builder();
    }

    @Override
    public ResultBuilder<List<FeeCodeAndNameVo>> getFeeCodeAndNameByAirportCode(String airportCode) {
        List<Object[]> feeCodeAndeName = feeDao.getFeeNameAndCode(airportCode);
        List<FeeCodeAndNameVo> resList = new ArrayList<>();
        for (Object[] obj : feeCodeAndeName) {
            FeeCodeAndNameVo fcn = new FeeCodeAndNameVo();
            fcn.setFeeName((String) obj[0]);
            fcn.setFeeCode((String) obj[1]);
            resList.add(fcn);
        }
        return new ResultBuilder.Builder<List<FeeCodeAndNameVo>>().data(resList).builder();
    }

    @Override
    public void allFormulaExpiredJudgment() {
        log.info("开始处理判断全部费用是否过期。");
        List<Long> tenantIdList = TenantUtil.getAirlineTenantIdList(systemCode);
        for (Long tenantId : tenantIdList) {
            TenantHolder.setTenant(tenantId);
            List<FeeInfo> feeInfoList = feeDao.getAllInvalidFee();
            for (FeeInfo feeInfo : feeInfoList) {
                formulaService.changeIsExpired(feeInfo);
            }
            TenantHolder.clear();
        }
        log.info("结束处理判断全部费用是否过期。");
    }
}
