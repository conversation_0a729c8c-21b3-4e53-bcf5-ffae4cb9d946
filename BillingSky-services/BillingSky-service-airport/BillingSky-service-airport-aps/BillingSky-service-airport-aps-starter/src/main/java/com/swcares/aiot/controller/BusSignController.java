package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.form.BugSignSearchForm;
import com.swcares.aiot.core.form.SubmitForm;
import com.swcares.aiot.core.model.vo.BillItemBusVo;
import com.swcares.aiot.core.model.vo.BusSignHistoryResultVO;
import com.swcares.aiot.service.BusSignService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.controller.BusSignController
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/11/14 14:09
 * @version v1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/BusSign")
@Api(value = "BusSignController", tags = {"车辆签单接口"})
public class BusSignController {

    @Resource
    private BusSignService busSignService;

    @PostMapping("/pageBusSign")
    @ApiOperation(value = "分页查询车辆签单数据")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ResultBuilder.class)})
    public ResultBuilder<Object[]> pageBusSign(@Validated @RequestBody BugSignSearchForm bugSignSearchForm) {
        return busSignService.pageBusSign(bugSignSearchForm);
    }

    @PostMapping("/exportBusSign")
    @ApiOperation(value = "导出车辆签单数据")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ResultBuilder.class)})
    public void exportBusSign(@Validated @RequestBody BugSignSearchForm bugSignSearchForm,
                              HttpServletResponse res) {
        busSignService.exportBusSign(bugSignSearchForm, res);
    }

    @GetMapping("/getQueryCondition")
    @ApiOperation(value = "获取车辆签单查询条件字典表")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！", response = ResultBuilder.class)})
    @ApiParam(name = "airportCode", value = "机场三字码")
    public ResultBuilder<List<BillItemBusVo>> getQueryCondition(String airportCode) {
        return busSignService.getQueryCondition(airportCode);
    }

    @ApiOperation(value = "提交对账表")
    @PostMapping(value = "/submit")
    public Object submit(@RequestBody @Validated @ApiParam(name = "SubmitForm", value = "提交对账表单", required = true) SubmitForm form) {
        return busSignService.submit(form);
    }

    @ApiOperation(value = "账单记录")
    @GetMapping(value = "/history")
    public Object history(Long signId, int page) {
        BusSignHistoryResultVO result = new BusSignHistoryResultVO();
        result.setData(busSignService.busSignhistoryList(signId, page));
        result.setTotal(busSignService.busSignhistoryTotal(signId));
        return result;
    }

    @ApiOperation(value = "修改状态")
    @PostMapping(value = "/status")
    public Object status(String id, String operation, String reason) {
        busSignService.changeBusSignStatus(id, operation, reason);
        return true;
    }

}
