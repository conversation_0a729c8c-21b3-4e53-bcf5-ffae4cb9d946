package com.swcares.aiot.service.impl;

import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.SubsidyUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.SubsidyFlightForm;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.dao.*;
import com.swcares.aiot.service.SubsidyBillService;
import com.swcares.aiot.service.SubsidySettlementService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * ClassName：com.swcares.service.impl.SubsidySettlementServiceImpl
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/18 9:38
 * @version v1.0
 */
@Service
public class SubsidySettlementServiceImpl implements SubsidySettlementService {

    @Resource
    private SubsidyFormulaDao subsidyFormulaDao;
    @Resource
    private SubsidyParamDao subsidyParamDao;
    @Resource
    private SubsidyFlightParamJoinDao subsidyFlightParamJoinDao;
    @Resource
    private SubsidyFormulaParamJoinDao subsidyFormulaParamJoinDao;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private SubsidyBillDao subsidyBillDao;
    @Resource
    private SubsidyFormulaBillDao subsidyFormulaBillDao;
    @Resource
    private FlightLineInfoDao flightLineInfoDao;
    @Resource
    private SubsidyBillService subsidyBillService;
    @Resource
    private SubsidyBillParamJoinDao subsidyBillParamJoinDao;

    @Override
    public boolean check(SubsidyFlightForm form) {
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        String airportCode = form.getAirportCode();
        String airlineCode = form.getAirline();
        String flightLineId = form.getFlightLine();

        FlightLineInfo fil = flightLineInfoDao.getFlightLineInfoById(flightLineId);
        // 当前航线为空
        if (fil == null) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_LINE_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_LINE_NULL_ERROR.getMsg());
        }

        // 根据机场、航司、航线获取公式信息
        List<SubsidyFormula> sfList =
                subsidyFormulaDao.getSubsidyFormulaByAirportCodeAirlineFlightLine(startDate,
                        endDate, airlineCode, flightLineId, airportCode);

        if (sfList.isEmpty()) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_FORMULA_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_FORMULA_NULL_ERROR.getMsg());
        }
        for (SubsidyFormula sf : sfList) {

            List<SubsidyFormulaBill> sfbList =
                    subsidyFormulaBillDao.getSubsidyFormulaBillByCondition(form.getStartDate(),
                            form.getEndDate(), sf.getId());
            if (!sfbList.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    @Transactional
    @Override
    public boolean settlement(SubsidyFlightForm form, LoginUserDetails user) {
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        String airportCode = form.getAirportCode();
        String airlineCode = form.getAirline();
        String flightLineId = form.getFlightLine();

        FlightLineInfo fil = flightLineInfoDao.getFlightLineInfoById(flightLineId);
        // 当前航线为空
        if (fil == null) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_LINE_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_LINE_NULL_ERROR.getMsg());
        }
        String flightLine = fil.getFlightLine();

        // 根据机场、航司、航线获取公式信息
        List<SubsidyFormula> sfList =
                subsidyFormulaDao.getSubsidyFormulaByAirportCodeAirlineFlightLine(startDate,
                        endDate, airlineCode, flightLineId, airportCode);

        if (sfList.isEmpty()) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_FORMULA_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_FORMULA_NULL_ERROR.getMsg());
        }
        // 航班数据为空标志位
        boolean flightNull = true;

        for (SubsidyFormula sf : sfList) {

            // 账单关联参数list
            List<SubsidyFormulaParamJoin> sfpjList = new ArrayList<>();

            List<SubsidyFormulaBill> sfbList =
                    subsidyFormulaBillDao.getSubsidyFormulaBillByCondition(form.getStartDate(),
                            form.getEndDate(), sf.getId());
            if (!sfbList.isEmpty()) {
                // throw new GenericException(
                // BusinessMessageEnum.SUBSIDY_SETTLEMENT_DATE_REPEAT.getCode(),
                // BusinessMessageEnum.SUBSIDY_SETTLEMENT_DATE_REPEAT.getMsg());
                for (SubsidyFormulaBill sfb : sfbList) {
                    subsidyBillService.deleteBill(sfb.getId(), user);
                    // 删除账单关联参数
                    subsidyBillParamJoinDao.deleteBelong3ByFlightIds(sfb.getId());
                }
                subsidyBillDao.flush();
            }
            // 根据公式获取相应参数信息
            String formula = sf.getFormula();

            Set<String> reSet = new HashSet<>();

            // 遍历指定时间内公式对应航班
            Date formulaStartDate = startDate;
            Date formulaEndDate = endDate;
            if (sf.getStartDate().after(startDate) || sf.getEndDate().before(endDate)) {
                formulaStartDate = DateUtils.laterTime(formulaStartDate, sf.getStartDate());
                formulaEndDate = DateUtils.earlyTime(formulaEndDate, sf.getEndDate());
            }

            // 获取相反航线
            String[] flArr = flightLine.split("-");
            String reFlighrLine = "";
            for (int i = 0; i < flArr.length; i++) {
                reFlighrLine = reFlighrLine + flArr[flArr.length - 1 - i];
                if (i != flArr.length - 1) {
                    reFlighrLine = reFlighrLine + "-";
                }
            }

            boolean addFlag = false;
            StringBuilder paramIdSb = new StringBuilder();
            // 遍历老公式，放入删除map
            for (char c : formula.toCharArray()) {
                if (addFlag) {
                    if (c == ']') {
                        addFlag = false;
                        String paramId = paramIdSb.toString();
                        reSet.add(paramId);
                        paramIdSb.setLength(0);
                    } else {
                        paramIdSb.append(c);
                    }
                } else {
                    if (c == '[') {
                        addFlag = true;
                    }
                }
            }
            // 航班参数
            Map<String, SubsidyParam> flightParamMap = new HashMap<>();

            // 将公式涉及参数全部查出，避免多次查询数据库
            // List<SubsidyParam> paramList = subsidyParamDao
            // .getSubsidyParamListById(reSet.stream().collect(Collectors.toList()));

            // 遍历将航线公式参数替换，并删除从map中删除
            // reSet.forEach(paramId -> {
            for (String paramId : reSet) {
                // 根据参数信息获取实际参数值
                List<SubsidyParam> spList = subsidyParamDao.getSubsidyParamById(paramId);
                if (spList.isEmpty()) {
                    continue;
                }
                SubsidyParam sp = spList.get(0);
                if (sp == null) {
                    // 抛出参数不存在异常
                    throw new GenericException(
                            BusinessMessageEnum.SUBSIDY_SETTLEMENT_PARAM_ERROR.getCode(),
                            BusinessMessageEnum.SUBSIDY_SETTLEMENT_PARAM_ERROR.getMsg());
                }
                // 如果参数为归属公式的参数,则查询关联表，替换实际值
                if ("1".equals(sp.getParamBelong())) {
                    // 公式格式的参数需要特殊处理
                    if ("2".equals(sp.getParamType())) {
                        String paramFormula = sp.getParamFormula();
                        // formula=formula.replaceAll("\\[" + paramId + "\\]",
                        // sp.getParamFormula());
                        boolean paramAddFlag = false;
                        Set<String> paramReSet = new HashSet<>();
                        StringBuilder formulaParamSb = new StringBuilder();
                        for (char c : paramFormula.toCharArray()) {
                            if (paramAddFlag) {
                                if (c == ']') {
                                    paramAddFlag = false;
                                    String formulaParamId = formulaParamSb.toString();
                                    paramReSet.add(formulaParamId);
                                    formulaParamSb.setLength(0);
                                } else {
                                    formulaParamSb.append(c);
                                }
                            } else {
                                if (c == '[') {
                                    paramAddFlag = true;
                                }
                            }
                        }

                        for (String formulaParamId : paramReSet) {
                            List<SubsidyParam> fSpList = subsidyParamDao.getSubsidyParamById(formulaParamId);
                            if (fSpList.isEmpty()) {
                                continue;
                            }
                            SubsidyParam formulaSp = fSpList.get(0);
                            if (formulaSp == null) {
                                // 抛出参数不存在异常
                                throw new GenericException(
                                        BusinessMessageEnum.SUBSIDY_SETTLEMENT_PARAM_ERROR
                                                .getCode(),
                                        BusinessMessageEnum.SUBSIDY_SETTLEMENT_PARAM_ERROR
                                                .getMsg());
                            }

                            // 如果参数为归属公式的参数,则查询关联表，替换实际值
                            if ("1".equals(formulaSp.getParamBelong())
                                    || "旅客均值".equals(formulaSp.getParamName())) {
                                String replaceValue = "0";
                                if ("旅客均值".equals(formulaSp.getParamName())) {
                                    List<Object[]> ids = flightInfoDao.getFlightInfoCountByTime(
                                            formulaStartDate, formulaEndDate, airlineCode,
                                            flightLine, reFlighrLine, airportCode);
                                    if (!ids.isEmpty()) {
                                        Object[] data = ids.get(0);
                                        BigDecimal flightCount = data[2] == null ? BigDecimal.ZERO
                                                : BigDecimal
                                                .valueOf(Double.parseDouble("" + data[2]));
                                        // 如果有相反航线数据，则相加
                                        if (ids.size() > 1) {
                                            flightCount = flightCount.add(ids.get(1)[2] == null
                                                    ? BigDecimal.ZERO
                                                    : BigDecimal.valueOf(Double
                                                    .parseDouble("" + ids.get(1)[2])));
                                        }
                                        if (!flightCount.equals(BigDecimal.ZERO)) {
                                            BigDecimal psg = data[3] == null ? BigDecimal.ZERO
                                                    : BigDecimal.valueOf(
                                                    Double.parseDouble("" + data[3]));
                                            if (ids.size() > 1) {
                                                psg = psg.add(ids.get(1)[3] == null
                                                        ? BigDecimal.ZERO
                                                        : BigDecimal.valueOf(Double
                                                        .parseDouble("" + ids.get(1)[3])));
                                            }
                                            replaceValue = "" + (psg.divide(flightCount, 0,
                                                    RoundingMode.DOWN));
                                        }
                                    }

                                } else {
                                    SubsidyFormulaParamJoin formulaSfj = subsidyFormulaParamJoinDao
                                            .getSubsidyFormulaParamJoinByFormulaAndParam(sf.getId(),
                                                    formulaParamId);
                                    replaceValue = Strings.isBlank(formulaSfj.getParamValue()) ? "0"
                                            : formulaSfj.getParamValue();
                                }
                                paramFormula = paramFormula
                                        .replaceAll("\\[" + formulaParamId + "\\]", replaceValue);
                            } else {
                                flightParamMap.put(formulaParamId, sp);
                            }
                        }
                        if (!paramFormula.contains("[")) {
                            // 将参数公式实际值算出来，并更新公式参数的paramValue值
                            String value = String.format("%.2f", Double
                                    .parseDouble("" + SubsidyUtils.calculateFormula(paramFormula)));
                            SubsidyFormulaParamJoin sfj = subsidyFormulaParamJoinDao
                                    .getSubsidyFormulaParamJoinByFormulaAndParam(sf.getId(),
                                            paramId);
                            sfj.setParamValue(value);
                            subsidyFormulaParamJoinDao.save(sfj);
                            sfpjList.add(sfj);
                            formula = formula.replaceAll("\\[" + paramId + "\\]", value);
                        }
                    } else {

                        SubsidyFormulaParamJoin sfj = subsidyFormulaParamJoinDao
                                .getSubsidyFormulaParamJoinByFormulaAndParam(sf.getId(), paramId);
                        String replaceValue = String.format("%.2f",
                                Double.parseDouble("" + (Strings.isBlank(sfj.getParamValue()) ? "0"
                                        : sfj.getParamValue())));
                        sfpjList.add(sfj);
                        formula = formula.replaceAll("\\[" + paramId + "\\]", replaceValue);
                    }
                } else if ("2".equals(sp.getParamBelong()) || "3".equals(sp.getParamBelong())) {
                    // 参数为航班参数，则先加入到航班参数map中
                    flightParamMap.put(paramId, sp);
                } else {
                    // 抛出参数异常
                    throw new GenericException(
                            BusinessMessageEnum.SUBSIDY_SETTLEMENT_PARAM_ERROR.getCode(),
                            BusinessMessageEnum.SUBSIDY_SETTLEMENT_PARAM_ERROR.getMsg());
                }

            }

            String resAmount = "0.0";
            boolean earlySettlment = false;
            // 如果遍历完后，公式中不包含未确定参数，则可直接算出补贴费用
            if (!formula.contains("[")) {
                earlySettlment = true;
                resAmount = SubsidyUtils.calculateFormula(formula);
            }


            List<FlightInfo> fiList = flightInfoDao.getFlightInfoByTime(formulaStartDate,
                    formulaEndDate, airlineCode, flightLine, reFlighrLine, airportCode);

            // 航班数据不为空，则将航班为空标志位置为false
            if (!fiList.isEmpty()) {
                flightNull = false;
            }
            // 公式账单结算金额
            BigDecimal formulaSettleResult = BigDecimal.ZERO;
            // 保底收入
            BigDecimal guaranteedIncome = BigDecimal.ZERO;
            // 新建公式账单
            SubsidyFormulaBill sfb = new SubsidyFormulaBill();
            sfb.setStartDate(formulaStartDate);
            sfb.setEndDate(formulaEndDate);
            sfb.setSubsidyFormulaId(sf.getId());
            sfb.setSettleResult(formulaSettleResult);

            sfb.setAirportCode(airportCode);
            sfb.setCreateBy(user.getUsername());
            sfb.setCreateTime(new Date());
            sfb.setModifiedBy(user.getUsername());
            sfb.setModifiedTime(new Date());

            subsidyFormulaBillDao.save(sfb);
            for (FlightInfo fi : fiList) {

                List<SubsidyBillParamJoin> sbpjList = new ArrayList<>();

                // 每个公式都需要代入航班参数的实际值进行计算
                if (!earlySettlment) {
                    String flightFormula = formula;
                    for (Map.Entry<String, SubsidyParam> en : flightParamMap.entrySet()) {
                        SubsidyParam paramInfo = en.getValue();
                        String paramName = paramInfo.getParamName();
                        String trueValue;
                        if ("班次".equals(paramName)) {
                            trueValue = "1";
                        } else if ("旅客数".equals(paramName)) {
                            trueValue = String.valueOf(fi.getPsgNumber()
                                    + (fi.getTransitAdultNumber() == null ? 0
                                    : fi.getTransitAdultNumber())
                                    + (fi.getTransitChildNumber() == null ? 0
                                    : fi.getTransitChildNumber())
                                    + (fi.getTransitInfantNumber() == null ? 0
                                    : fi.getTransitInfantNumber()));
                        } else {
                            SubsidyFlightParamJoin sfpj = subsidyFlightParamJoinDao
                                    .getSubsidyFlightParamJoinByFlightIdAndParamId(fi.getId(),
                                            en.getKey());


                            if (sfpj == null) {
                                trueValue = "0";
                            } else {
                                trueValue = Strings.isBlank(sfpj.getParamValue()) ? "0"
                                        : sfpj.getParamValue();
                            }
                        }

                        //  航班参数的为公式的情况？


                        flightFormula = flightFormula.replaceAll("\\[" + en.getKey() + "\\]", trueValue);

                        // 将计算值存入账单关联参数
                        SubsidyBillParamJoin sbpj = new SubsidyBillParamJoin();
                        sbpj.setParamId(paramInfo.getId());
                        sbpj.setParamName(paramName);
                        sbpj.setParamValue(trueValue);
                        sbpj.setParamBelong(paramInfo.getParamBelong());
                        sbpj.setParamType(paramInfo.getParamType());
                        sbpj.setNumType(paramInfo.getNumType());
                        sbpj.setUnit(paramInfo.getUnit());

                        sbpj.setCreateBy(user.getUsername());
                        sbpj.setCreateTime(new Date());
                        sbpj.setModifiedBy(user.getUsername());
                        sbpj.setModifiedTime(new Date());

                        sbpjList.add(sbpj);
                    }
                    resAmount = SubsidyUtils.calculateFormula(flightFormula);
                }

                SubsidyBill sb = new SubsidyBill();
                sb.setFlightId(fi.getId());
                sb.setFormulaBillId(sfb.getId());
                sb.setSettleResult(resAmount);
                formulaSettleResult =
                        formulaSettleResult.add(BigDecimal.valueOf(Double.parseDouble(resAmount)));
                // 计算保底收入
                SubsidyFlightParamJoin kpsr = subsidyFlightParamJoinDao
                        .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "客票收入");
                BigDecimal kpsrValue = BigDecimal.ZERO;
                if (kpsr != null && !Strings.isBlank(kpsr.getParamValue())) {
                    kpsrValue = BigDecimal.valueOf(Double.parseDouble(kpsr.getParamValue()));
                }
                SubsidyFlightParamJoin ryfjf = subsidyFlightParamJoinDao
                        .getSubsidyFlightParamJoinByFlightIdAndParamName(fi.getId(), "燃油附加费");
                BigDecimal ryfjfValue = BigDecimal.ZERO;
                if (ryfjf != null && !Strings.isBlank(ryfjf.getParamValue())) {
                    ryfjfValue = BigDecimal.valueOf(Double.parseDouble(ryfjf.getParamValue()));
                }
                BigDecimal flightGuaranteedIncome = ryfjfValue.add(kpsrValue)
                        .add(BigDecimal.valueOf(Double.parseDouble(resAmount)));
                sb.setGuaranteedIncome("" + flightGuaranteedIncome);
                guaranteedIncome = guaranteedIncome.add(flightGuaranteedIncome);

                sb.setAirportCode(airportCode);
                sb.setCreateBy(user.getUsername());
                sb.setCreateTime(new Date());
                sb.setModifiedBy(user.getUsername());
                sb.setModifiedTime(new Date());

                // 保存账单
                subsidyBillDao.save(sb);


                for (SubsidyBillParamJoin sbpj : sbpjList) {
                    sbpj.setBillId(sb.getId());
                    sbpj.setFormulaBillId(sfb.getId());
                }

                for (SubsidyFormulaParamJoin sfpj : sfpjList) {
                    SubsidyBillParamJoin sbpj = new SubsidyBillParamJoin();
                    sbpj.setBillId(sb.getId());
                    sbpj.setParamName(sfpj.getParamName());
                    sbpj.setParamValue(sfpj.getParamValue());
                    sbpj.setNumType(sfpj.getNumType());
                    sbpj.setParamId(sfpj.getParamId());
                    sbpj.setParamBelong(sfpj.getParamBelong());
                    sbpj.setParamType(sfpj.getParamType());
                    sbpj.setFormulaBillId(sfb.getId());
                    sbpj.setUnit(sfpj.getUnit());

                    sbpj.setCreateBy(user.getUsername());
                    sbpj.setCreateTime(new Date());
                    sbpj.setModifiedBy(user.getUsername());
                    sbpj.setModifiedTime(new Date());

                    sbpjList.add(sbpj);
                }
                subsidyBillParamJoinDao.saveAll(sbpjList);

            }

            sfb.setSettleResult(formulaSettleResult);
            sfb.setGuaranteedIncome(guaranteedIncome);
            sfb.setModifiedBy(user.getUsername());
            sfb.setModifiedTime(new Date());
            subsidyFormulaBillDao.save(sfb);

            // 更新航线预算
            List<FlightLineInfo> fliList = flightLineInfoDao
                    .getFlightLineInfoByAirlineAndLine(airlineCode, airportCode, flightLine);
            FlightLineInfo fli = fliList.get(0);
            fli.setUsed(formulaSettleResult.add(fli.getUsed()));
            fli.setModifiedBy(user.getUsername());
            fli.setModifiedTime(new Date());
            flightLineInfoDao.save(fli);

        }

        if (flightNull) {
            throw new GenericException(
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_FLIGHT_NULL_ERROR.getCode(),
                    BusinessMessageEnum.SUBSIDY_SETTLEMENT_FLIGHT_NULL_ERROR.getMsg());
        }

        return true;
    }
}
