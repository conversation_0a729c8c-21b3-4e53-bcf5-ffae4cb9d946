package com.swcares.aiot.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.TenantConvertUtil;
import com.swcares.aiot.client.IExcessLuggageTransOrderBizClient;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.model.dto.ExcessLuggageTransOrderBizPageDto;
import com.swcares.aiot.model.vo.ExcessLuggageTransOrderPageVo;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;


/**
 * ClassName：com.swcares.aiot.controller.ExcessLuggageTransOrderAssBizController<br>
 * Description：航司端逾重行李划拨账单接口 <br>
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.<br>
 * Company：Aviation Cares Of Southwest Chen Du LTD<br>
 *
 * <AUTHOR>
 * date 2025/4/17 9:48<br>
 * @version v1.0<br>
 */
@Slf4j
@RestController
@ApiVersion("机场端-行李-api")
@RequestMapping("/excessLuggageTransOrderAps")
@Api(tags = "机场端逾重行李划拨账单接口")
public class ExcessLuggageTransOrderApsBizController {

    @Resource
    private IExcessLuggageTransOrderBizClient iExcessLuggageTransOrderBizClient;

    @ApiOperation(value = "划拨账单分页查询")
    @PostMapping("/page")
    PagedResult<List<ExcessLuggageTransOrderPageVo>> page(@RequestBody ExcessLuggageTransOrderBizPageDto dto) {
        return iExcessLuggageTransOrderBizClient.page(dto);
    }

    @ApiOperation(value = "导出-划拨账单")
    @PostMapping("/export")
    private void export(@RequestBody ExcessLuggageTransOrderBizPageDto dto, HttpServletResponse response) {
        try {
            byte[] excelBytes = iExcessLuggageTransOrderBizClient.clientExport(dto);
            // 设置响应头
            String fileName = CharSequenceUtil.format("{}{}{}", "对账通逾重托运行李划拨账单明细", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN), ".xlsx");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 写入响应流
            response.getOutputStream().write(excelBytes);
            response.flushBuffer();
        } catch (Exception e) {
            log.error("导出文件出错", e);
        }
    }
}
