package com.swcares.aiot.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.OptimizedCalculator;
import com.swcares.aiot.core.common.util.ThreadPoolUtil;
import com.swcares.aiot.core.entity.TAirlineInfo;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.form.ReCalcForm;
import com.swcares.aiot.core.form.ReCalcMapAndListForm;
import com.swcares.aiot.core.mapstruct.MsReCalcProcessBiz;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.model.vo.MServiceRecordVo;
import com.swcares.aiot.core.model.vo.MSettleInfoVo;
import com.swcares.aiot.core.service.ITAirlineInfoService;
import com.swcares.aiot.dao.*;
import com.swcares.aiot.service.AirlineBillNewService;
import com.swcares.aiot.service.ReCalcProcessService;
import com.swcares.aiot.statemachine.biz.status.EnumBillStatus;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import swcares.psc.airport.CalcParam;
import swcares.psc.airport.Formula;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.service.impl.ReCalcProcessServiceImpl
 * Description：重新结算流程service实现类
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/11/25 9:55
 * @version v1.0
 */
@Slf4j
@Service
public class ReCalcProcessServiceImpl implements ReCalcProcessService {
    private static final String ERROR_MSG = "出现业务异常";
    @Resource
    private ReCalcProcessDao reCalcProcessDao;
    @Resource
    private MServiceRecordDao mServiceRecordDao;
    @Resource
    private FlightBillDao flightBillDao;
    @Resource
    private AirlineBillNewService airlineBillNewService;
    @Resource
    private AirlineDao airlineDao;
    @Resource
    private FormulaDao formulaDao;
    @Resource
    private FeeDao feeDao;
    @Resource
    private FlightBillHistoryDao flightBillHistoryDao;
    @Resource
    private VariableRecordDao variableRecordDao;
    @Resource
    private ServiceRecordDao serviceRecordDao;
    @Resource
    private ITAirlineInfoService airlineInfoService;
    @Resource
    private MsReCalcProcessBiz msReCalcProcessBiz;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reCalc(String airportCode, String airlineCode, List<String> flightNoList, List<String> flightIds,
                       List<String> formulaIdList, Date startDate, Date endDate, List<String> feeCodeList,
                       LoginUserDetails userDetails, Integer dateType) {
        LocalDateTime calcStartTime = LocalDateTime.now();
        if (log.isDebugEnabled()) {
            log.debug("=======================================开始结算,结算开始时间为：{}", LocalDateTimeUtil.formatNormal(calcStartTime));
        }

        List<RulesVariableRecord> indicatorList = variableRecordDao.findAll();
        // 缓存指标表全量
        HashMap<String, String> indicatorMap = getindicatorMap(indicatorList);

        endDate = (new DateTime(endDate)).plusHours(23).plusMinutes(59).plusSeconds(59).plusMillis(999).toDate();
        ReCalcForm form = new ReCalcForm(airportCode, airlineCode, null,
                null, null, null, startDate, endDate, false, null, dateType);
        List<String> flightIdList = getFlightIdListAndAddConnectFlightIds(airportCode, airlineCode,
                flightNoList, flightIds, startDate, endDate, dateType);
        List<List<String>> lists = Lists.partition(flightIdList, 5000);

        List<Object[]> objList;
        if (1 == dateType) {
            objList = reCalcProcessDao.getMSettleInfoList1(startDate, endDate, feeCodeList,
                    airportCode, airlineCode, formulaIdList, flightIds, flightNoList);
        } else {
            objList = reCalcProcessDao.getMSettleInfoList2(startDate, endDate, feeCodeList,
                    airportCode, airlineCode, formulaIdList, flightIds, flightNoList);
        }
        List<Object[]> reCalcLater = new ArrayList<>();
        Map<String, FlightBill> tempMsettleMap = new HashMap<>();
        List<FlightBill> saveBillList = new ArrayList<>();
        ReCalcMapAndListForm reCalcMapAndListForm = new ReCalcMapAndListForm(objList, lists, saveBillList, tempMsettleMap, reCalcLater, indicatorMap);
        //检查对象，并计算出账单
        checkMsvoAndCalc(airportCode, reCalcMapAndListForm, userDetails.getUsername());
        //计算引用其他费用的费用账单
        calcLaterBill(airportCode, feeCodeList, userDetails, reCalcLater, tempMsettleMap, indicatorMap, saveBillList);
        log.debug("结算计算完成，saveList结果：{}", saveBillList);
        //保存更新账单
        saveAndUpdateBill(form, flightNoList, flightIds, feeCodeList, userDetails, saveBillList);
        // 添加航司账单
        addAirlineBill(startDate, endDate, airportCode, TenantHolder.getTenant());
        LocalDateTime calcEndTime = LocalDateTime.now();
        Duration duration = Duration.between(calcStartTime, calcEndTime);
        log.info("===================================结算结束,结束时间为：{}, 此次结算用时{}秒",
                LocalDateTimeUtil.formatNormal(calcEndTime), duration.getSeconds());
    }

    private void calcLaterBill(String airportCode, List<String> feeCodeList, LoginUserDetails userDetails, List<Object[]> reCalcLater, Map<String, FlightBill> tempMsettleMap, HashMap<String, String> indicatorMap, List<FlightBill> saveBillList) {
        List<String> feeIdList = reCalcLater.stream().map(item -> {
            MSettleInfoVo laterMsVo = (MSettleInfoVo) item[0];
            String formula = laterMsVo.getFormula();
            return formula.substring(formula.indexOf("[") + 1, formula.indexOf("]"));
        }).distinct().collect(Collectors.toList());

        List<String> flightIdList2 = reCalcLater.stream().map(item -> {
            MSettleInfoVo laterMsVo = (MSettleInfoVo) item[0];
            return laterMsVo.getFlightId();
        }).distinct().collect(Collectors.toList());

        Map<String, FlightBill> flightBillMap = getAllValidFlightBills(flightIdList2);
        Map<String, FeeInfo> feeMap = findAllValidFees(feeIdList);

        //根据引用的费用项id 查出公式 再取公式里的指标项
        Map<String, List<FormulaInfo>> feeFormulaMap = this.findFormulaByFeeIds(feeIdList);

        for (Object[] laterObj : reCalcLater) {
            MSettleInfoVo laterMsVo = (MSettleInfoVo) laterObj[0];
            MServiceRecordVo msr = (MServiceRecordVo) laterObj[1];
            String formula = laterMsVo.getFormula();
            String feeId = formula.substring(formula.indexOf("[") + 1, formula.indexOf("]"));
            FlightBill quoteVo = tempMsettleMap
                    .get(laterMsVo.getFlightId() + "_" + feeId + "_" + laterMsVo.getAirportCode());
            String quoteSr;
            if (quoteVo == null) {
                FeeInfo fi = feeMap.get(feeId);
                String flightBillKey = airportCode + laterMsVo.getFlightId() + fi.getFeeCode();
                FlightBill fb = flightBillMap.get(flightBillKey);
                if ((CollectionUtils.isNotEmpty(feeCodeList) || !feeCodeList.contains(fi.getFeeCode())) && fb != null) {
                    quoteSr = fb.getChargePrice().toString();
                } else {
                    continue;
                }
            } else if (quoteVo.getChargePrice() == null) {
                quoteSr = "0";
            } else {
                quoteSr = quoteVo.getChargePrice().toString();
            }
            formula = formula.replace("[" + feeId + "]", "{rlsp}");
            laterMsVo.setFormula(formula);
            laterMsVo.setPricingWay("3");
            FlightBill bill = calc(laterMsVo, msr, quoteSr);
            if (bill.getChargePrice().compareTo(BigDecimal.ZERO) != 0) {
                //设置指标项信息
                setBillValue(userDetails, indicatorMap, feeFormulaMap, feeId, bill, msr, laterMsVo);
                saveBillList.add(bill);
            }
        }
    }

    private void setBillValue(LoginUserDetails userDetails, HashMap<String, String> indicatorMap, Map<String, List<FormulaInfo>> feeFormulaMap, String feeId, FlightBill bill, MServiceRecordVo msr, MSettleInfoVo laterMsVo) {
        this.setIndicatorInfo(getFormula(feeFormulaMap, feeId, laterMsVo), bill, msr, indicatorMap);
        bill.setFlightTime(DateUtils.parseDateTime(laterMsVo.getFlightTime()));
        bill.setSettleMonth(laterMsVo.getFlightDate());
        bill.setCreateBy(userDetails.getUsername());
        bill.setCreateTime(new Date());
        bill.setBillBusDataItemId(StringUtils.isBlank(msr.getCbtBillBusDataItemId()) ? null : msr.getCbtBillBusDataItemId());
        bill.setModifiedTime(new Date());
    }

    private String getFormula(Map<String, List<FormulaInfo>> feeFormulaMap, String feeId, MSettleInfoVo laterMsVo) {
        List<FormulaInfo> formulaInfoList = feeFormulaMap.get(feeId);
        if (formulaInfoList == null || formulaInfoList.isEmpty()) {
            return StringUtils.EMPTY;
        }
        Date flightDate = laterMsVo.getFlightDate();
        for (FormulaInfo formulaInfo : formulaInfoList) {
            if (flightDate.before(formulaInfo.getStartDate()) || flightDate.after(formulaInfo.getEndDate())) {
                continue;
            }
            return formulaInfo.getFormula();
        }
        return StringUtils.EMPTY;
    }

    private void saveAndUpdateBill(ReCalcForm form, List<String> flightNoList, List<String> flightIds, List<String> feeCodeList, LoginUserDetails userDetails, List<FlightBill> saveBillList) {
        /*
         * 1.查询老账单
         * 2.获取新账单
         * 3.对他俩求 交、差集（仅对feeCode 为CBT的单子用三个条件（flightId、billBusDataItemId、feeCode）来判断是否相等；其他的用两个条件(flightId、feeCode)）
         * 4.对交集部分 做更新，对差集部分 新账单中多出来的做插入，老账单中多出来的做删除
         * 5.记录账单的操作记录
         */
        String airportCode = form.getAirportCode();
        String airlineCode = form.getAirlineCode();
        Date startDate = form.getStartDate();
        Date endDate = form.getEndDate();
        Integer dateType = form.getDateType();
        try {
            // 记录账单的操作记录
            List<FlightBill> reviveBills = new ArrayList<>();
            // 老账单中多余的部分
            List<FlightBill> toDeleteList = new ArrayList<>();
            // 新账单中多余的部分
            List<FlightBill> toSaveList = new ArrayList<>();
            // 新、老账单交集部分
            List<FlightBill> toUpdateList = new ArrayList<>();
            // 老账单对应的map（key=flightId+feeCode+billDataItemId,value=账单）
            HashMap<String, List<FlightBill>> oldFlightBillsMap = new HashMap<>();
            // 查询老账单
            List<FlightBill> oldFlightBills = flightBillDao.getFlightBillOnCondition(airportCode, startDate, endDate, airlineCode, flightNoList, flightIds, feeCodeList, dateType);
            // 填充老账单到map
            addOldBillListAndMap(oldFlightBills, oldFlightBillsMap);
            // 新老账单求交集、差集
            extractNewAndOldBillList(saveBillList, oldFlightBillsMap, toUpdateList, reviveBills, toSaveList);
            // 填充老账单中多余的部分
            oldFlightBillsMap.forEach((key, value) -> {
                for (FlightBill oldBill : value) {
                    //未提交/有争议/已撤销 的账单可以重新结算 其他的都不行
                    if (EnumBillStatus.NOT_SUBMITTED.getStatus().toString().equals(oldBill.getSubmit())
                            || EnumBillStatus.CONTROVERSIAL.getStatus().toString().equals(oldBill.getSubmit())
                            || EnumBillStatus.REVOKED.getStatus().toString().equals(oldBill.getSubmit())) {
                        oldBill.setInvalid("0");
                        toDeleteList.add(oldBill);// 填充老账单中多余的部分
                    }
                }
            });
            ArrayList<FlightBill> flightBills = new ArrayList<>();
            flightBills.addAll(toDeleteList);
            flightBills.addAll(toSaveList);
            flightBills.addAll(toUpdateList);
            flightBills.addAll(reviveBills);
            Long tenant = TenantHolder.getTenant();
            List<List<FlightBill>> lists1 = Lists.partition(flightBills, 6000);
//            CountDownLatch countDownLatch = new CountDownLatch(lists1.size());
            for (List<FlightBill> bills : lists1) {
                ThreadPoolUtil.getPool().execute(() -> {
                    try {
                        TenantHolder.setTenant(tenant);
                        flightBillDao.saveAll(bills);
                        flightBillDao.flush();
                    } finally {
//                        countDownLatch.countDown();
                        TenantHolder.clear();
                    }
                });
            }
//            countDownLatch.await();
            // 把生成的账单记录到 历史账单表;初次结算的账单——操作为:账单生成,存toSaveList; 重新结算的账单——操作为:重新结算,存toUpdateList、toDeleteList列表
            log.info("=======================================结算，把生成的账单记录到 历史账单表，tenantId:{},tenantId{}，time{}", tenant, TenantHolder.getTenant(), LocalDateTime.now());
            log.info("toSaveList条数:{}", toSaveList.size());
            log.info("toUpdateList条数:{}", toUpdateList.size());
            log.info("toDeleteList条数:{}", toDeleteList.size());
            saveBillHistory(toSaveList, toUpdateList, toDeleteList, userDetails);
            log.info("=======================================结算，添加航司账单，{}", LocalDateTime.now());
        } catch (InterruptedException e) {
            log.warn("结算存储账单线程 Interrupted!", e);
            //Clean up whatever needs to be handled before interrupting
            Thread.currentThread().interrupt();
            throw new GenericException(BusinessMessageEnum.ReSETTLE_KJB_FAILURE.getCode(),
                    BusinessMessageEnum.ReSETTLE_KJB_FAILURE.getMsg());
        } catch (Exception e) {
            log.error("航班账单存储异常：{}", e.getMessage(), e);
            throw new GenericException(BusinessMessageEnum.ReSETTLE_KJB_FAILURE.getCode(),
                    BusinessMessageEnum.ReSETTLE_KJB_FAILURE.getMsg());
        }
    }

    private static void extractNewAndOldBillList(List<FlightBill> saveBillList, HashMap<String, List<FlightBill>> oldFlightBillsMap, List<FlightBill> toUpdateList, List<FlightBill> reviveBills, List<FlightBill> toSaveList) {
        for (FlightBill newFlightBill : saveBillList) {
            newFlightBill.setRevocation(0);
            String fidStr = newFlightBill.getFlightId() == null ? "" : newFlightBill.getFlightId();
            String feeCodeStr = newFlightBill.getFeeCode() == null ? "" : newFlightBill.getFeeCode();
            String uniqueKey = fidStr + "_" + feeCodeStr;

            if (oldFlightBillsMap.containsKey(uniqueKey)) {
                List<FlightBill> oldFlightBillList = oldFlightBillsMap.get(uniqueKey);
                FlightBill oldFlightBill = oldFlightBillList.get(0);
                //未提交/有争议/已撤销 的账单可以重新结算 其他的都不行
                checkOldBillStatus(toUpdateList, reviveBills, newFlightBill, oldFlightBill);
                oldFlightBillList.remove(0);
                if (oldFlightBillList.isEmpty()) {
                    // 将交集部分从oldMap移除,之后map中剩下的就是老账单中多余的部分
                    oldFlightBillsMap.remove(uniqueKey);
                }
            } else {
                // 填充新账单中多余的部分
                newFlightBill.setId(Long.toString(getMsgId()));
                toSaveList.add(newFlightBill);
            }
        }
    }

    private static long getMsgId() {
        return IdUtil.getSnowflake(RandomUtil.randomLong(1, 31), RandomUtil.randomLong(1, 31)).nextId();
    }

    private static void checkOldBillStatus(List<FlightBill> toUpdateList, List<FlightBill> reviveBills, FlightBill newFlightBill, FlightBill oldFlightBill) {
        if (EnumBillStatus.NOT_SUBMITTED.getStatus().toString().equals(oldFlightBill.getSubmit())
                || EnumBillStatus.CONTROVERSIAL.getStatus().toString().equals(oldFlightBill.getSubmit())
                || EnumBillStatus.REVOKED.getStatus().toString().equals(oldFlightBill.getSubmit())) {
            // 设置id
            newFlightBill.setId(oldFlightBill.getId());
            // 填充交集部分
            toUpdateList.add(newFlightBill);
        } else if ("0".equals(oldFlightBill.getInvalid())) {
            // 恢复成有效状态
            oldFlightBill.setInvalid("1");
            // 恢复取消确认(航班数据确认、业务保障数据却惹)后的账单
            reviveBills.add(oldFlightBill);
        }
    }

    private static void addOldBillListAndMap(List<FlightBill> oldFlightBills, HashMap<String, List<FlightBill>> oldFlightBillsMap) {
        for (FlightBill oldFlightBill : oldFlightBills) {
            String fidStr = oldFlightBill.getFlightId() == null ? "" : oldFlightBill.getFlightId();
            String feeCodeStr = oldFlightBill.getFeeCode() == null ? "" : oldFlightBill.getFeeCode();
            String uniqueKey = fidStr + "_" + feeCodeStr;
            List<FlightBill> oldFlightBillList = oldFlightBillsMap.getOrDefault(uniqueKey, new ArrayList<>());
            oldFlightBillList.add(oldFlightBill);
            oldFlightBillsMap.put(uniqueKey, oldFlightBillList);
        }
    }

    /**
     * Title：getmSettleInfoVo <br>
     * Description: 将objs转换为 MSettleInfoVo 对象 <br>
     *
     * @param objs 输入数组
     * @return com.swcares.aiot.core.model.vo.MSettleInfoVo
     * <AUTHOR> <br>
     * date 2024/10/21 <br>
     */
    private static @NotNull MSettleInfoVo getmSettleInfoVo(Object[] objs) {
        MSettleInfoVo msVo = new MSettleInfoVo();
        msVo.setFlightId(objs[0] == null ? "" : (String) objs[0]);
        msVo.setFlightNo(objs[1] == null ? "" : (String) objs[1]);
        msVo.setFlightDate((Date) objs[2]);
        msVo.setFlightSegment(objs[3] == null ? "" : (String) objs[3]);
        msVo.setFlightSegmentType(objs[4] == null ? "" : "" + objs[4]);
        msVo.setAirlineCode(objs[5] == null ? "" : (String) objs[5]);
        msVo.setAirportCode(objs[6] == null ? "" : "" + objs[6]);
        msVo.setFlightFlag(objs[7] == null ? "" : "" + objs[7]);
        msVo.setFlightLine(objs[10] == null ? "" : (String) objs[10]);
        msVo.setFlightLineType(objs[11] == null ? "" : "" + objs[11]);
        msVo.setFlightModel(objs[12] == null ? "" : (String) objs[12]);
        msVo.setFlightTime(objs[13] == null ? "" : "" + objs[13]);
        msVo.setFromAirportCode(objs[14] == null ? "" : (String) objs[14]);
        msVo.setToAirportCode(objs[15] == null ? "" : (String) objs[15]);
        setPsgInfo(objs, msVo);
        setPropertyTwo(objs, msVo);
        setPropertyThree(objs, msVo);
        msVo.setSafeguardType(objs[59] == null ? null : Integer.valueOf(String.valueOf(objs[59])));
        msVo.setFlightType(objs[60] == null ? "" : (String) objs[60]);
        return msVo;
    }

    private static void setPropertyThree(Object[] objs, MSettleInfoVo msVo) {
        msVo.setCalcWay(objs[44] == null ? null : "" + objs[44]);
        msVo.setPricingWay(objs[45] == null ? null : "" + objs[45]);
        msVo.setFeePriority(objs[46] == null ? null : (Integer) objs[46]);
        msVo.setFeeId(objs[47] == null ? null : (String) objs[47]);
        msVo.setFeeCode(objs[48] == null ? null : (String) objs[48]);
        msVo.setFeeName(objs[49] == null ? null : (String) objs[49]);
        msVo.setDataStatus(objs[50] == null ? null : "" + objs[50]);
        msVo.setVariableStatus(objs[51] == null ? null : "" + objs[51]);
        msVo.setFormulaType(objs[52] == null ? null : "" + objs[52]);
        msVo.setIsTransit(objs[53] == null ? null : "" + objs[53]);
        msVo.setAltSpecial(objs[54] == null ? null : "" + objs[54]);
        msVo.setSpecialVariable(objs[55] == null ? null : "" + objs[55]);
        msVo.setFlightStatus(objs[56] == null ? null : "" + objs[56]);
        msVo.setStayStartTime(objs[57] == null ? null : (Date) objs[57]);
        msVo.setStayEndTime(objs[58] == null ? null : (Date) objs[58]);
    }

    private static void setPropertyTwo(Object[] objs, MSettleInfoVo msVo) {
        msVo.setMaxTakeoffWeight(objs[31] == null ? BigDecimal.ZERO : new BigDecimal((Integer) objs[31]));
        msVo.setMaxSeat(objs[32] == null ? 0 : (Integer) objs[32]);
        msVo.setAirplaneType(objs[33] == null ? null : new BigDecimal("" + objs[33]));
        msVo.setAirplaneModel(objs[34] == null ? null : (String) objs[34]);
        msVo.setAirlineId(objs[35] == null ? null : (String) objs[35]);
        msVo.setSettleCode(objs[36] == null ? null : (String) objs[36]);
        msVo.setAirlineShortName(objs[37] == null ? null : (String) objs[37]);
        msVo.setFormulaId(objs[38] == null ? null : (String) objs[38]);
        msVo.setFormula(objs[39] == null ? null : (String) objs[39]);
        msVo.setAirportLevel(objs[40] == null ? null : (Integer) objs[40]);
        msVo.setFormulaName(objs[41] == null ? null : (String) objs[41]);
        msVo.setBelongWay(objs[42] == null ? null : "" + objs[42]);
        msVo.setCalcVariable(objs[43] == null ? null : (BigDecimal) objs[43]);
    }

    private static void setPsgInfo(Object[] objs, MSettleInfoVo msVo) {
        msVo.setPsgNumber(objs[16] == null ? BigDecimal.ZERO : new BigDecimal("" + objs[16]));
        msVo.setMail(objs[18] == null ? BigDecimal.ZERO : new BigDecimal("" + objs[18]));
        msVo.setCargo(objs[19] == null ? BigDecimal.ZERO : new BigDecimal("" + objs[19]));
        msVo.setCargoMailWeight(objs[20] == null ? BigDecimal.ZERO : new BigDecimal("" + objs[20]));
        msVo.setStayTime(objs[21] == null ? BigDecimal.ZERO : new BigDecimal("" + objs[21]));
        msVo.setAdultNumber(objs[22] == null ? BigDecimal.ZERO : new BigDecimal("" + objs[22]));
        msVo.setCardHolderNumber(objs[23] == null ? 0 : (Integer) objs[23]);
        msVo.setAccompanyingCardHolderNumber(objs[24] == null ? 0 : (Integer) objs[24]);
        msVo.setAccompanyingImportantNumber(objs[25] == null ? 0 : (Integer) objs[25]);
        msVo.setImportantNumber(objs[26] == null ? 0 : (Integer) objs[26]);
        msVo.setFirstClassNumber(objs[27] == null ? 0 : Integer.parseInt("" + objs[27]));
        msVo.setTaskFlag(objs[28] == null ? "" : (String) objs[28]);
        msVo.setRegNo(objs[29] == null ? "" : (String) objs[29]);
        msVo.setMaxPayload(objs[30] == null ? BigDecimal.ZERO : new BigDecimal((Integer) objs[30]));
    }

    private Map<String, List<FormulaInfo>> findFormulaByFeeIds(List<String> feeIdList) {
        if (CollectionUtils.isEmpty(feeIdList)) {
            return Collections.emptyMap();
        }
        return formulaDao.listFormulaByFeeIds(feeIdList).stream()
                .collect(Collectors.groupingBy(FormulaInfo::getFeeRulesId));
    }


    /**
     * 设置非航账单的指标项信息
     *
     * @param formula      :
     * @param bill         :
     * @param msr          :
     * @param indicatorMap :
     */
    private void setIndicatorInfo(String formula, FlightBill bill, MServiceRecordVo msr, HashMap<String, String> indicatorMap) {
        if (formula.contains("{") && formula.contains("}") && formula.indexOf("{") < formula.indexOf("}")) {
            String varCode = formula.substring(formula.indexOf("{") + 1, formula.indexOf("}"));
            if (varCode.startsWith("C")) {
                try {
                    String getCode = "get" + varCode.charAt(0) + varCode.substring(1).toLowerCase();
                    Object value = MServiceRecordVo.class.getMethod(getCode).invoke(msr);
                    bill.setIndicatorCode(getIndicatorString(bill.getIndicatorCode(), varCode));
                    String valueStr = value == null ? "0" : value.toString();
                    bill.setIndicatorValue(getIndicatorString(bill.getIndicatorValue(), valueStr));
                    bill.setIndicatorName(getIndicatorString(bill.getIndicatorName(), indicatorMap.get(varCode)));
                } catch (Exception e) {
                    log.error(ERROR_MSG, e);
                }
            }
        }
    }

    private String getIndicatorString(String checkValue, String val1) {
        return StringUtils.isBlank(checkValue) ? val1 : String.join("/", checkValue, val1);
    }

    private void saveBillHistory(List<FlightBill> toSaveList, List<FlightBill> toUpdateList, List<FlightBill> toDeleteList, LoginUserDetails userDetails) throws InterruptedException {
        ArrayList<FlightBillHistory> flightBillHistories = new ArrayList<>();
        // List<FlightBillHistory> histories4Save = ObjectUtils.copyBeans(toSaveList, FlightBillHistory.class);
        List<FlightBillHistory> histories4Save = msReCalcProcessBiz.saveToHistory(toSaveList, BillOperation.GENERATE.getCode(), userDetails);
//        for (FlightBillHistory h : histories4Save) {
//            // 账单生成
//            h.setOperation(BillOperation.GENERATE.getCode());
//        }

//        List<FlightBillHistory> histories4Update = ObjectUtils.copyBeans(toUpdateList, FlightBillHistory.class);
        List<FlightBillHistory> histories4Update = msReCalcProcessBiz.saveToHistory(toUpdateList, BillOperation.RESETTLEMENT.getCode(), userDetails);
//        for (FlightBillHistory h : histories4Update) {
//            // 重新结算
//            h.setOperation(BillOperation.RESETTLEMENT.getCode());
//        }

//        List<FlightBillHistory> histories4Delete = ObjectUtils.copyBeans(toDeleteList, FlightBillHistory.class);
        List<FlightBillHistory> histories4Delete = msReCalcProcessBiz.saveToHistory(toDeleteList, BillOperation.RESETTLEMENT.getCode(), userDetails);
//        for (FlightBillHistory h : histories4Delete) {
//            // 重新结算
//            h.setOperation(BillOperation.RESETTLEMENT.getCode());
//        }
        flightBillHistories.addAll(histories4Save);
        flightBillHistories.addAll(histories4Update);
        flightBillHistories.addAll(histories4Delete);

//        flightBillHistories.forEach(h -> {
//            h.setFlightBillId(h.getId());
//            h.setId(null);
//            h.setCreateBy(userDetails.getUsername());
//            h.setCreateTime(new Date());
//        });
        // 记录历史
        Long tenant = TenantHolder.getTenant();
        List<List<FlightBillHistory>> lists1 = Lists.partition(flightBillHistories, 6000);
//        CountDownLatch countDownLatch = new CountDownLatch(lists1.size());
        for (List<FlightBillHistory> bills : lists1) {
            ThreadPoolUtil.getPool().execute(() -> {
                try {
                    TenantHolder.setTenant(tenant);
                    flightBillHistoryDao.saveAll(bills);
                    flightBillHistoryDao.flush();
                } finally {
//                    countDownLatch.countDown();
                    TenantHolder.clear();
                }
            });
        }
//        countDownLatch.await();
    }


    private Map<String, FeeInfo> findAllValidFees(List<String> feeIdList) {
        if (CollectionUtils.isEmpty(feeIdList)) {
            return new HashMap<>();
        }
        List<FeeInfo> fees = feeDao.findAllValidFeeByFeeIdList(feeIdList);
        Map<String, FeeInfo> feeMap = new HashMap<>();
        if (fees != null) {
            feeMap = fees.stream().collect(Collectors.toMap(FeeInfo::getId, feeInfo -> feeInfo));
        }
        return feeMap;
    }


    private Map<String, FlightBill> getAllValidFlightBills(List<String> flightIdList) {
        if (CollectionUtils.isEmpty(flightIdList)) {
            return new HashMap<>();
        }
        List<FlightBill> flightBills = flightBillDao.getAllValidFlightBills(flightIdList);
        Map<String, FlightBill> flightBillMap = new HashMap<>();
        if (flightBills != null) {
            flightBillMap = flightBills.stream().collect(Collectors.toMap(flightBill ->
                            flightBill.getAirportCode() + flightBill.getFlightId() + flightBill.getFeeCode(),
                    flightBill -> flightBill, (oldValue, newValue) -> oldValue));
        }
        return flightBillMap;
    }

    /**
     * Title: getFormulaSwitch<br>
     * Author: 刘志恒<br>
     * Description: 处理公式中的if-else条件，转换成一条普通公式<br>
     * Date:  2022/11/28 14:36 <br>
     */
    private String getFormulaSwitch(String formula, MSettleInfoVo msVo, MServiceRecordVo msr) {
        String varCode = formula.substring(formula.indexOf("{") + 1, formula.indexOf("}"));
        String comparisonNumber = null;
        String comparisonSymbol;
        String cslt = "<=";
        String cseq = "==";
        String csls = "<";
        switch (varCode) {
            case "PM":
                comparisonNumber = msVo.getAirplaneModel();
                comparisonSymbol = cseq;
                break;
            case "PT":
                comparisonNumber = msVo.getAirplaneType().toString();
                comparisonSymbol = cseq;
                break;
            case "PMSN":
                comparisonNumber = msVo.getMaxSeat() == null ? "0" : msVo.getMaxSeat().toString();
                comparisonSymbol = cslt;
                break;
            case "FST":
                comparisonNumber = msVo.getStayTime() == null ? "0" : msVo.getStayTime().toString();
                comparisonSymbol = csls;
                break;
            case "PMW":
                comparisonNumber = msVo.getMaxTakeoffWeight() == null ? "0" : msVo.getMaxTakeoffWeight().toString();
                comparisonSymbol = cslt;
                break;
            case "PLW":
                comparisonNumber = msVo.getMaxPayload() == null ? "0" : msVo.getMaxPayload().toString();
                comparisonSymbol = cslt;
                break;
            case "CBAT":
                comparisonNumber = getBigDecimalValue(msr, MServiceRecordVo::getCbat);
                comparisonSymbol = cslt;
                break;
            case "CBT":
                comparisonNumber = getBigDecimalValue(msr, MServiceRecordVo::getCbt);
                comparisonSymbol = cslt;
                break;
            case "CBAN":
                comparisonNumber = getBigDecimalValue(msr, MServiceRecordVo::getCban);
                comparisonSymbol = cslt;
                break;
            case "CBEN":
                comparisonNumber = getBigDecimalValue(msr, MServiceRecordVo::getCben);
                comparisonSymbol = cslt;
                break;
            case "CBET":
                comparisonNumber = getBigDecimalValue(msr, MServiceRecordVo::getCbet);
                comparisonSymbol = cslt;
                break;
            case "COP":
                comparisonNumber = getBigDecimalValue(msr, MServiceRecordVo::getCop);
                comparisonSymbol = cslt;
                break;
            case "CADF":
                comparisonNumber = getBigDecimalValue(msr, MServiceRecordVo::getCadf);
                comparisonSymbol = cslt;
                break;
            default:
                String getCode = "get" + varCode.substring(0, 1).toUpperCase() + varCode.substring(1).toLowerCase();
                Object value = null;
                try {
                    value = MServiceRecordVo.class.getMethod(getCode).invoke(msr);
                } catch (Exception e) {
                    log.error(ERROR_MSG, e);
                }
                if (value != null) {
                    comparisonNumber = value.toString();
                }
                comparisonSymbol = cslt;
        }
        return Formula.analysis(formula, comparisonNumber, comparisonSymbol, varCode);
    }

    /**
     * Title: getBigDecimalValue<br>
     * Author: liuzhiheng<br>
     * Description: 获取BigDecimal类型的值，转为String（为空则设置为0）<br>
     * Date:  2025/4/7 10:33 <br>
     */
    private <T> String getBigDecimalValue(T obj, Function<T, BigDecimal> function) {
        BigDecimal value = function.apply(obj);
        return value == null ? "0" : value.toString();
    }

    /**
     * Title: calc<br>
     * Author: 刘志恒<br>
     * Description: 根据航班公式信息 及 保障信息进行结算，并生成账单<br>
     * Date:  2022/11/30 16:33 <br>
     */
    private FlightBill calc(MSettleInfoVo msVo, MServiceRecordVo msr, String rlspValue) {
        BigDecimal sr;
        String formula = msVo.getFormula();
        sr = calcFormula(formula, msVo, msr, rlspValue);
        String fb = msVo.getFlightFlag() + msVo.getBelongWay();
        BigDecimal fbv;
        switch (fb) {
            case "DD":
            case "DC":
            case "AA":
            case "AC":
                fbv = BigDecimal.ONE;
                break;
            case "DH":
            case "AH":
                fbv = BigDecimal.valueOf(0.5);
                break;
            default:
                fbv = BigDecimal.ZERO;
                break;
        }
        sr = sr.multiply(fbv);
        BigDecimal calcVariable = msVo.getCalcVariable();
        String calcWay = msVo.getCalcWay();
        if (calcWay != null) {
            if ("*".equals(calcWay)) {
                sr = sr.multiply(calcVariable);
            } else if ("+".equals(calcWay)) {
                sr = sr.add(calcVariable);
            }
        }
        if ("SC-D".equalsIgnoreCase(msVo.getFeeCode())) {
            sr = sr.setScale(3, RoundingMode.HALF_UP);
        }

        msVo.setSettleResult(sr);
        return getBill(msVo, fbv);
    }

    /**
     * Title: calcFormula<br>
     * Author: 刘志恒<br>
     * Description: 计算公式结果<br>
     * Date:  2022/11/28 16:06 <br>
     */
    private BigDecimal calcFormula(String formula, MSettleInfoVo msVo, MServiceRecordVo msr, String rlspValue) {
        List<CalcParam> paramList = new ArrayList<>();
        int leftIndex;
        int rightIndex;
        while ((leftIndex = formula.indexOf("{")) != -1 && (rightIndex = formula.indexOf("}")) != -1 && rightIndex > leftIndex) {
            String varCode = formula.substring(formula.indexOf("{") + 1, formula.indexOf("}"));
            BigDecimal varValue = BigDecimal.ZERO;
            if (varCode.startsWith("C")) {
                varValue = getVarValue(msVo, msr, varCode, varValue);
            } else {
                switch (varCode) {
                    case "FPSNC":
                        varValue = getVarValue(msVo.getCardHolderNumber(), varValue);
                        break;
                    case "FPSNV":
                        varValue = getVarValue(msVo.getImportantNumber(), varValue);
                        break;
                    case "FPSNNV":
                        varValue = getVarValue(msVo.getAccompanyingImportantNumber(), varValue);
                        break;
                    case "FPSN":
                        varValue = getVarValue(msVo.getPsgNumber(), varValue);
                        break;
                    case "FMW":
                        varValue = getVarValue(msVo.getCargoMailWeight(), varValue);
                        break;
                    case "FPSNA":
                        varValue = getVarValue(msVo.getAdultNumber(), varValue);
                        break;
                    case "FST":
                        varValue = getVarValue(msVo.getStayTime(), varValue);
                        break;
                    case "FPSNNC":
                        varValue = getVarValue(msVo.getAccompanyingCardHolderNumber(), varValue);
                        break;
                    case "FFCN":
                        varValue = getVarValue(msVo.getFirstClassNumber(), varValue);
                        break;
                    case "PMW":
                        varValue = getVarValue(msVo.getMaxTakeoffWeight(), varValue);
                        break;
                    case "PLW":
                        varValue = getVarValue(msVo.getMaxPayload(), varValue);
                        break;
                    case "rlsp":
                        varValue = new BigDecimal(rlspValue);
                        break;
                    default:
                }
            }
            formula = formula.replace("{" + varCode + "}", varCode);
            CalcParam cp = new CalcParam(varCode, varValue);
            paramList.add(cp);
        }
        return OptimizedCalculator.calc(formula, paramList);
    }

    private BigDecimal getVarValue(BigDecimal msVo, BigDecimal varValue) {
        if (msVo != null) {
            varValue = msVo;
        }
        return varValue;
    }

    private BigDecimal getVarValue(Integer msVo, BigDecimal varValue) {
        if (msVo != null) {
            varValue = new BigDecimal(msVo);
        }
        return varValue;
    }

    private BigDecimal getVarValue(MSettleInfoVo msVo, MServiceRecordVo msr, String varCode, BigDecimal varValue) {
        try {
            String getCode = "get" + varCode.charAt(0) + varCode.substring(1).toLowerCase();
            Object value = MServiceRecordVo.class.getMethod(getCode).invoke(msr);
            if (value != null) {
                varValue = new BigDecimal("" + value);
            }
            msVo.setServiceRecord(varCode);
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
        }
        return varValue;
    }

    /**
     * Title: getBill<br>
     * Author: 刘志恒<br>
     * Description: 生成账单信息<br>
     * Date:  2022/11/30 16:35 <br>
     */
    private FlightBill getBill(MSettleInfoVo msVo, BigDecimal fbv) {
        String formula = msVo.getFormula();
        String calcWay = msVo.getCalcWay() == null ? "0" : msVo.getCalcWay();
        BigDecimal calcVariable = msVo.getCalcVariable();
        BigDecimal sr = msVo.getSettleResult();
        BigDecimal rup;
        BigDecimal pricing;
        if ("1".equals(msVo.getPricingWay())) {
            String up = formula.substring(0, formula.indexOf("*"));
            rup = new BigDecimal(up);
            if ("*".equals(calcWay)) {
                rup = rup.multiply(calcVariable);
            } else if ("+".equals(calcWay)) {
                rup = rup.add(calcVariable);
            }
            pricing = sr.divide(rup, 4, RoundingMode.HALF_UP);
        } else {
            pricing = fbv;
            if (pricing.compareTo(BigDecimal.ZERO) == 0) {
                rup = sr;
            } else {
                rup = sr.divide(pricing, 4, RoundingMode.HALF_UP);
            }
        }

        FlightBill bill = new FlightBill();
        BeanUtils.copyProperties(msVo, bill);
        bill.setFlightModel(msVo.getAirplaneModel());
        bill.setChargePrice(sr);
        bill.setUnitPrice(rup);
        bill.setPricingAmount(pricing);
        bill.setServiceRecord(msVo.getServiceRecord());
        if ("PARK".equalsIgnoreCase(msVo.getFeeCode())) {
            bill.setServiceStartTime(msVo.getStayStartTime());
            bill.setServiceEndTime(msVo.getStayEndTime());
        }
        return bill;
    }

    /**
     * Title: checkByFlightData<br>
     * Author: 刘志恒<br>
     * Description: 根据航班基本信息检查是否符合部分公式的特殊需求<br>
     * Date:  2022/12/8 14:59 <br>
     *
     * @param msVo :
     */
    public boolean checkByFlightData(MSettleInfoVo msVo, Set<String> freeCargoAirline) {
        //判断当前航班是否需要免货邮
        if (checkCargoMail(msVo, freeCargoAirline)) {
            return false;
        }
        //旅客为0不收签牌费
        if ("CARD".equalsIgnoreCase(msVo.getFeeCode()) && (msVo.getPsgNumber() == null
                || msVo.getPsgNumber().compareTo(BigDecimal.ZERO) == 0)) {
            return false;
        }
        String dataStatus = msVo.getDataStatus() == null ? "0" : msVo.getDataStatus();
        String variableStatus = msVo.getVariableStatus() == null ? "0" : msVo.getVariableStatus();
        String formulaType = msVo.getFormulaType() == null ? "0" : msVo.getFormulaType();
        //判断公式是否需要航班确认
        if (("0".equals(dataStatus) || "3".equals(dataStatus)) && ("2".equals(formulaType) || "4".equals(formulaType))) {
            return false;
        }
        //判断公式是否需要业务数据确认
        return (!"0".equals(variableStatus) && !"3".equals(variableStatus)) || (!"3".equals(formulaType) && !"4".equals(formulaType));
    }

    /***
     * Title：checkCargoMail
     * Description：判断当前航班是否需要免货邮
     * author：liuzhiheng
     * date： 2025/4/7 10:05
     * @param msVo 计算临时对象
     * @param freeCargoAirline 免货邮航司
     */
    private boolean checkCargoMail(MSettleInfoVo msVo, Set<String> freeCargoAirline) {
        return "CARGOMAIL".equalsIgnoreCase(msVo.getFeeCode())
                && (msVo.getCargoMailWeight() == null || msVo.getCargoMailWeight().compareTo(BigDecimal.ZERO) == 0)
                && freeCargoAirline.contains(msVo.getAirlineShortName());
    }

    /**
     * Title: checkByServiceData<br>
     * Author: 刘志恒<br>
     * Description: 根据保障数据检查是否符合部分公式的特殊需求<br>
     * Date:  2022/12/8 14:59 <br>
     *
     * @param msVo : null
     */
    public boolean checkByServiceData(MSettleInfoVo msVo, MServiceRecordVo msr) {
        return !"BRIDGE".equalsIgnoreCase(msVo.getFeeCode()) || msr.getCbt().compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * Title: checkCBT<br>
     * Author: 刘志恒<br>
     * Description: 处理多廊桥<br>
     * Date:  2023/3/28 14:43 <br>
     *
     * @param msVo : null
     */
    public void checkCbt(MSettleInfoVo msVo, Map<String, MServiceRecordVo> serviceMap,
                         Map<String, MServiceRecordVo> addServiceMap,
                         Map<String, List<Map<String, Object>>> serviceCbtMap,
                         List<MServiceRecordVo> flightServiceList) {
        String formula = msVo.getFormula();
        List<Map<String, Object>> flightServiceCbtList;
        MServiceRecordVo flightServiceObj;
        if (addServiceMap.containsKey(msVo.getFlightId())) {
            flightServiceObj = addServiceMap.get(msVo.getFlightId());
        } else {
            flightServiceObj = serviceMap.getOrDefault(msVo.getFlightId(), new MServiceRecordVo());
        }
        if (formula.contains("{CBT}")
                && (flightServiceCbtList = serviceCbtMap.get(msVo.getFlightId())) != null
                && !flightServiceCbtList.isEmpty()) {
            for (Map<String, Object> sdrCbt : flightServiceCbtList) {
                MServiceRecordVo serviceObjTemp = new MServiceRecordVo();
                BeanUtils.copyProperties(flightServiceObj, serviceObjTemp);
                serviceObjTemp.setCbt(sdrCbt.get("CBT") == null ? BigDecimal.ZERO : (BigDecimal) sdrCbt.get("CBT"));
                if (sdrCbt.get("bill_bus_data_item_id") != null) {
                    serviceObjTemp.setCbtBillBusDataItemId((String) sdrCbt.get("bill_bus_data_item_id"));
                }
                flightServiceList.add(serviceObjTemp);
            }
        } else {
            flightServiceList.add(flightServiceObj);
        }
    }

    /***
     * Title：addAirlineBill
     * Description：添加航司账单
     * author：李军呈
     * date： 2024/8/16 9:39
     * @param airportCode   机场三字码
     */
    private void addAirlineBill(Date startDate, Date endDate, String airportCode, Long tenant) {
        airlineBillNewService.checkAndNewAirlineBill(startDate, endDate, airportCode, tenant);
    }


    private void assembleServiceTimeMap(Map<String, ServiceRecord> usedTimeServiceRecordMap
            , List<ServiceRecord> usedTimeServiceRecordList) {
        for (ServiceRecord serviceRecord : usedTimeServiceRecordList) {
            String serviceKey = serviceRecord.getFlightId() + "_" + serviceRecord.getServiceCode();
            if (usedTimeServiceRecordMap.containsKey(serviceKey)) {
                ServiceRecord tempServiceRecord = usedTimeServiceRecordMap.get(serviceKey);
                //多条业务保障数据时，开始时间用最早，结束时间用最晚
                if (serviceRecord.getStartTime().before(tempServiceRecord.getStartTime())) {
                    tempServiceRecord.setStartTime(serviceRecord.getStartTime());
                }
                if (serviceRecord.getEndTime().after(tempServiceRecord.getEndTime())) {
                    tempServiceRecord.setEndTime(serviceRecord.getEndTime());
                }
            } else {
                usedTimeServiceRecordMap.put(serviceKey, serviceRecord);
            }

        }
    }

    /***
     * Title：isLaterCalc
     * Description：判断当前公式是否需要稍后计算
     * author：liuzhiheng
     * date： 2025/4/7 9:55
     * @param msVo 计算临时对象
     * @param formula 公式
     * @param msr 计算临时业务数据对象
     * @param reCalcLater 稍后计算list
     */
    private boolean isLaterCalc(MSettleInfoVo msVo, String formula, MServiceRecordVo msr, List<Object[]> reCalcLater) {
        String srFormula = formula;
        if ("2".equals(msVo.getPricingWay())) {
            srFormula = getFormulaSwitch(srFormula, msVo, msr);
            msVo.setFormula(srFormula);
        }

        if (srFormula.contains("[")) {
            Object[] laterObj = new Object[2];
            laterObj[0] = msVo;
            laterObj[1] = msr;
            reCalcLater.add(laterObj);
            return true;
        }
        if ("1".equals(msVo.getAltSpecial()) && "alt".equalsIgnoreCase(msVo.getFlightStatus())
                && Strings.isNotBlank(msVo.getSpecialVariable())
                && ("CDC".equals(msVo.getSpecialVariable()) || "CDP".equals(msVo.getSpecialVariable()))) {
            if ("CDC".equals(msVo.getSpecialVariable())) {
                msVo.setFormula("(" + srFormula + ")*" + msr.getCdc());
            } else {
                msVo.setFormula("(" + srFormula + ")*" + msr.getCdp());
            }

        }
        return false;
    }

    /***
     * Title：getFlightIdListAndAddConnectFlightIds
     * Description：获取需要计算的航班id，并添加其关联航班id
     * author：liuzhiheng
     * date： 2025/4/7 11:25
     * @param airportCode 机场三字码
     * @param airlineCode 航司二字码
     * @param flightNoList 航班号list
     * @param flightIds 航班idlist
     * @param startDate 计算开始时间
     * @param endDate 计算结束时间
     * @param dateType 计算类型
     */
    private List<String> getFlightIdListAndAddConnectFlightIds(String airportCode, String airlineCode, List<String> flightNoList, List<String> flightIds,
                                                               Date startDate, Date endDate, Integer dateType) {
        List<String> flightIdList = reCalcProcessDao.getFlightIdList(startDate, endDate,
                airportCode, airlineCode, flightIds, flightNoList, dateType);

        if (CollectionUtils.isNotEmpty(flightIds)) {
            List<String> connectFlightIds = reCalcProcessDao.getConnectFlightIdById(flightIds);
            for (String connectFlightId : connectFlightIds) {
                boolean needAdd = true;
                for (String flightId : flightIdList) {
                    if (connectFlightId.equals(flightId)) {
                        needAdd = false;
                        break;
                    }
                }
                if (needAdd) {
                    flightIdList.add(connectFlightId);
                }
            }
        }
        return flightIdList;
    }


    private HashMap<String, String> getindicatorMap(List<RulesVariableRecord> indicatorList) {
        HashMap<String, String> indicatorMap = new HashMap<>();
        for (RulesVariableRecord r : indicatorList) {
            indicatorMap.put(r.getVariable(), r.getVariableName());
        }
        return indicatorMap;
    }

    private void addServiceListAndUsedTimeServiceRecordList(List<List<String>> lists, List<MServiceRecordVo> serviceList,
                                                            List<ServiceRecord> usedTimeServiceRecordList, Map<String, Integer> stMap) {
        for (List<String> subIdList : lists) {
            List<MServiceRecordVo> tempList = mServiceRecordDao.getServiceRecordByFlightId(subIdList);
            if (CollectionUtils.isNotEmpty(tempList)) {
                serviceList.addAll(tempList);
            }
            //查询业务保障数据的开始结束时间
            List<ServiceRecord> usedTimeServiceRecordTempList = serviceRecordDao.listServiceRecordByFlightIdList(subIdList);
            if (CollectionUtils.isNotEmpty(usedTimeServiceRecordTempList)) {
                usedTimeServiceRecordList.addAll(usedTimeServiceRecordTempList);
                for (ServiceRecord serviceRecord : usedTimeServiceRecordTempList) {
                    if ("ST".equals(serviceRecord.getServiceCode()) && serviceRecord.getUsedNumber() != null) {
                        stMap.put(serviceRecord.getFlightId(), (int) Math.round(serviceRecord.getUsedNumber()));
                    }
                }
            }
        }
    }


    private Map<String, List<Map<String, Object>>> getCBTMap(List<List<String>> lists) {
        List<Map<String, Object>> serviceCbtList = new ArrayList<>();
        for (List<String> subIdList : lists) {
            List<Map<String, Object>> tempList = mServiceRecordDao.getCBTServiceRecordByFlightId(subIdList);
            if (CollectionUtils.isNotEmpty(tempList)) {
                serviceCbtList.addAll(tempList);
            }
        }
        Map<String, List<Map<String, Object>>> serviceCbtMap = new HashMap<>();
        for (Map<String, Object> serviceObj : serviceCbtList) {
            String flightId = (String) serviceObj.get("flight_id");
            List<Map<String, Object>> cbtlist = serviceCbtMap.getOrDefault(flightId, new ArrayList<>());
            cbtlist.add(serviceObj);
            serviceCbtMap.put(flightId, cbtlist);
        }
        return serviceCbtMap;
    }


    private Map<String, MServiceRecordVo> getServiceMap(List<MServiceRecordVo> serviceList) {
        Map<String, MServiceRecordVo> serviceMap = new HashMap<>();
        for (MServiceRecordVo serviceObj : serviceList) {
            String flightId = serviceObj.getFlightId();
            serviceMap.put(flightId, serviceObj);
        }
        return serviceMap;
    }


    private void checkMsvoAndCalc(String airportCode, ReCalcMapAndListForm reCalcMapAndListForm,
                                  String userName) {
        List<Object[]> objList = reCalcMapAndListForm.getObjList();
        List<List<String>> lists = reCalcMapAndListForm.getLists();
        List<FlightBill> saveBillList = reCalcMapAndListForm.getSaveBillList();
        Map<String, FlightBill> tempMsettleMap = reCalcMapAndListForm.getTempMsettleMap();
        List<Object[]> reCalcLater = reCalcMapAndListForm.getReCalcLater();
        HashMap<String, String> indicatorMap = reCalcMapAndListForm.getIndicatorMap();
//        Set<String> freeCargoAirline = airlineDao.getAirlineCodeByAirportCode(airportCode);
        Set<String> freeCargoAirline = airlineInfoService.lambdaQuery().eq(TAirlineInfo::getAirportCode, airportCode)
                .eq(TAirlineInfo::getSpecialCmbFee, "1")
                .eq(TAirlineInfo::getInvalid, "1")
                .select(TAirlineInfo::getAirlineShortName)
                .list().stream().map(TAirlineInfo::getAirlineShortName).collect(Collectors.toSet());
        List<MServiceRecordVo> serviceList = new ArrayList<>();
        //保存需要计算的航班的业务保障数据开始结束时间的list
        List<ServiceRecord> usedTimeServiceRecordList = new ArrayList<>();
        //保障类型map
        Map<String, Integer> stMap = new HashMap<>();
        //添加serviceList和usedTimeServiceRecordList
        addServiceListAndUsedTimeServiceRecordList(lists, serviceList, usedTimeServiceRecordList, stMap);
        //组装业务保障数据开始结束时间Map
        Map<String, ServiceRecord> usedTimeServiceRecordMap = new HashMap<>();
        assembleServiceTimeMap(usedTimeServiceRecordMap, usedTimeServiceRecordList);

        Map<String, MServiceRecordVo> serviceMap = getServiceMap(serviceList);
        Map<String, MServiceRecordVo> addServiceMap = new HashMap<>();
        //获取客桥数据map
        Map<String, List<Map<String, Object>>> serviceCbtMap = getCBTMap(lists);
        for (Object[] objs : objList) {
            // 将objs转换为 MSettleInfoVo 对象
            MSettleInfoVo msVo = getmSettleInfoVo(objs);
            log.debug("结算处理当前对象：{}", msVo);
            //航班过站与公式是否过站不符，或formula为空，或不满足公式的特殊需求
            //以及判断过站航前航后是否与公式匹配
            if (checkSafeguard(msVo, stMap) || checkTransitAndFreeCargo(msVo, freeCargoAirline) || !checkFlightType(msVo)) {
                log.debug("航前航后不匹配跳过，或者免货物邮件费，或航段性质不匹配：{}", msVo);
                continue;
            }
            BigDecimal cargoMail = new BigDecimal(msVo.getCargoMailWeight() == null ? "0" : "" + msVo.getCargoMailWeight());
            cargoMail = cargoMail.setScale(3, RoundingMode.HALF_UP);
            msVo.setCargoMailWeight(cargoMail);
            String formula = msVo.getFormula();
            String serviceCode = getServiceCode(formula, msVo);
            setUseTime(serviceCode, usedTimeServiceRecordMap, msVo);

            if ("1".equals(msVo.getAltSpecial()) && "alt".equalsIgnoreCase(msVo.getFlightStatus())
                    && Strings.isNotBlank(msVo.getSpecialVariable())
                    && !"CDC".equals(msVo.getSpecialVariable())
                    && !"CDP".equals(msVo.getSpecialVariable())
                    && serviceCode != null) {
                formula = formula.replace(serviceCode, msVo.getSpecialVariable());
                msVo.setFormula(formula);
                serviceCode = msVo.getSpecialVariable();
            }
            List<MServiceRecordVo> flightServiceList = new ArrayList<>();
            if (serviceCode != null) {
                checkCbt(msVo, serviceMap, addServiceMap, serviceCbtMap, flightServiceList);
            } else {
                flightServiceList.add(new MServiceRecordVo());
            }
            msVo.setServiceRecord(serviceCode);
            calcBill(msVo, flightServiceList, saveBillList, tempMsettleMap, reCalcLater, indicatorMap, userName);
        }
    }

    private boolean checkFlightType(MSettleInfoVo msVo) {
        //判断公式的航班属性（国际国内）是否包含航班的航段属性
        if (StringUtils.isNotBlank(msVo.getFlightType()) && StringUtils.isNotBlank(msVo.getFlightSegmentType())) {
            return msVo.getFlightType().contains(msVo.getFlightSegmentType());
        }
        return false;
    }

    private static void setUseTime(String serviceCode, Map<String, ServiceRecord> usedTimeServiceRecordMap, MSettleInfoVo msVo) {
        //设置指标项开始结束时间
        if (StringUtils.isNotBlank(serviceCode)) {
            ServiceRecord serviceRecord = usedTimeServiceRecordMap.get(msVo.getFlightId() + "_" + serviceCode);
            if (serviceRecord != null) {
                msVo.setServiceStartTime(serviceRecord.getStartTime());
                msVo.setServiceEndTime(serviceRecord.getEndTime());
            }
        }
    }

    private boolean checkTransitAndFreeCargo(MSettleInfoVo msVo, Set<String> freeCargoAirline) {
        return StringUtils.isBlank(msVo.getFormula()) || !checkByFlightData(msVo, freeCargoAirline);
    }

    private boolean checkSafeguard(MSettleInfoVo msVo, Map<String, Integer> stMap) {
        boolean flag = false;
        //判断公式是否配置保障类型
        if (msVo.getSafeguardType() != null) {
            //获取航班航前，航后，过站
            Integer stNumber = stMap.get(msVo.getFlightId());
            //公式配置了保障类型情况下，航班有保障类型业务保障数据则判断是否一致，没有数据则不计算
            if (stNumber != null) {
                //判断电子签单传入的航前航后过站，是否与公式配置的
                flag = !stNumber.equals(msVo.getSafeguardType());
            } else {
                flag = true;
            }
        }
        return flag;
    }

    private static String getServiceCode(String formula, MSettleInfoVo msVo) {
        String serviceCode = null;
        if (Strings.isNotBlank(formula) && formula.contains("{") && formula.contains("}")) {
            if ("2".equals(msVo.getPricingWay()) && formula.contains(",")) {
                String tempFormula = formula.substring(formula.indexOf(","));
                if (tempFormula.contains("{") && tempFormula.contains("}")) {
                    serviceCode = tempFormula.substring(tempFormula.indexOf("{") + 1,
                            tempFormula.indexOf("}"));
                }
            } else {
                serviceCode = formula.substring(formula.indexOf("{") + 1, formula.indexOf("}"));

            }
        }
        return serviceCode;
    }

    private void calcBill(MSettleInfoVo msVo, List<MServiceRecordVo> flightServiceList, List<FlightBill> saveBillList,
                          Map<String, FlightBill> tempMsettleMap, List<Object[]> reCalcLater, HashMap<String, String> indicatorMap,
                          String userName) {
        String serviceCode = msVo.getServiceCode();
        FlightBill cbtBill = null;
        String formula = msVo.getFormula();
        for (MServiceRecordVo msr : flightServiceList) {
            if ((!checkByServiceData(msVo, msr)) || isLaterCalc(msVo, formula, msr, reCalcLater)) {
                return;
            }

            FlightBill bill = calc(msVo, msr, null);
            if (bill.getChargePrice().compareTo(BigDecimal.ZERO) != 0) {
                //设置指标项信息
                this.setIndicatorInfo(formula, bill, msr, indicatorMap);
                bill.setFlightTime(DateUtils.parseDateTime(msVo.getFlightTime()));
                bill.setSettleMonth(msVo.getFlightDate());
                bill.setCreateTime(new Date());
                bill.setCreateBy(userName);
                bill.setBillBusDataItemId(StringUtils.isBlank(msr.getCbtBillBusDataItemId()) ? null : msr.getCbtBillBusDataItemId());
                bill.setModifiedTime(new Date());
                cbtBill = getFlightBill(saveBillList, serviceCode, cbtBill, bill);
            }
            if (StringUtils.isBlank(serviceCode) || !"CBT".equalsIgnoreCase(serviceCode)) {
                tempMsettleMap.put(
                        msVo.getFlightId() + "_" + msVo.getFeeId() + "_" + msVo.getAirportCode(),
                        bill);
            }
        }
        if (cbtBill != null) {
            saveBillList.add(cbtBill);
            tempMsettleMap.put(
                    msVo.getFlightId() + "_" + msVo.getFeeId() + "_" + msVo.getAirportCode(),
                    cbtBill);
        }
    }

    private static FlightBill getFlightBill(List<FlightBill> saveBillList, String serviceCode, FlightBill cbtBill, FlightBill bill) {
        if (StringUtils.isNotBlank(serviceCode) && "CBT".equalsIgnoreCase(serviceCode)) {
            if (cbtBill == null) {
                cbtBill = bill;
            } else {
                cbtBill.setChargePrice(cbtBill.getChargePrice().add(bill.getChargePrice()));
                cbtBill.setUnitPrice(BigDecimal.valueOf(200L));
                cbtBill.setPricingAmount(cbtBill.getChargePrice().divide(cbtBill.getUnitPrice(), 2, RoundingMode.HALF_UP));
                cbtBill.setIndicatorValue(String.valueOf(new BigDecimal(cbtBill.getIndicatorValue()).add(new BigDecimal(bill.getIndicatorValue()))));
            }
        } else {
            saveBillList.add(bill);
        }
        return cbtBill;
    }
}
