package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.form.FeeForm;
import com.swcares.aiot.core.form.ServiceFeeForm;
import com.swcares.aiot.core.model.entity.FeeInfo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.FeeService;
import com.swcares.aiot.service.MqService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：FeeController <br>
 * Description：费用操作接口类<br>
 * Copyright © 2020-5-14 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-5-14 15:47<br>
 * @version v1.0 <br>
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/fee")
@Api(value = "FeeController", tags = {"费用操作接口"})
@Slf4j
public class FeeController {
    @Resource
    private FeeService feeService;
    @Resource
    private MqService<Object> mqService;

    @ApiImplicitParams({
            @ApiImplicitParam(name = "condition", value = "查询条件", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "airportCode", value = "机场三字码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "feeType", value = "费用类别", dataType = "String", paramType = "query", required = true)
    })
    @ApiOperation(value = "根据费用代码、名称查询机场下的费用")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/pageFeeRulesByCondition")
    public Object pageFeeRulesByCondition(@ApiParam(name = "pageParam", value = "分页信息") @Validated PageParam pageParam,
                                          String condition, String airportCode, String feeType) {
        return feeService.pageFeeRulesByCondition(condition, airportCode, feeType, pageParam);
    }

    @ApiOperation(value = "根据费用id查询费用详情")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/getFeeRulesById")
    public Object getFeeRulesById(@RequestParam @ApiParam(name = "feeId", value = "费用id") String feeId) {
        return feeService.getFeeRulesById(feeId);
    }

    @ApiOperation(value = "根据费用id删除费用")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @DeleteMapping(value = "/delFeeRulesById")
    public Object delFeeRulesById(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                  @RequestParam @ApiParam(name = "feeId", value = "费用id") String feeId) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        ResultBuilder<?> rb = feeService.delFeeRulesById(feeId, user, urlName);
        //将删除费用传入mq
        try {
            if (rb != null) {
                mqService.deletefee(feeId);
            }
        } catch (Exception e) {
            log.info("删除费用失败！feeId={}", feeId, e);
        }
        return rb;
    }

    @ApiOperation(value = "新增费用")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PostMapping(value = "/saveFeeRule")
    public Object saveFeeRule(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                              @RequestBody @ApiParam(name = "feeForm", value = "费用信息") @Valid FeeForm feeForm) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        ResultBuilder rb = feeService.saveFeeRule(feeForm, user, urlName);
        try {
            if (rb != null && rb.getData() != null) {
                mqService.addfee(String.valueOf(rb.getData()));
            }
        } catch (Exception e) {
            log.info("新增费用异常，param= {}, {}", urlName, feeForm , e);
        }
        return rb;
    }


    @ApiOperation(value = "根据费用名称查询机场下费用信息，若没有传名称则查全部")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "feeName", value = "费用名称", dataType = "String", paramType = "query")
    })
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/listFeeRulesByFeeName")
    public Object listFeeRulesByFeeName(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码", required = true) String airportCode, String feeName) {
        return feeService.listFeeRulesByFeeName(feeName, airportCode);

    }

    @ApiOperation(value = "根据航司id查询航司下的费用信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/listFeeRuleByAirlineId")
    public Object listFeeRuleByAirlineId(@RequestParam @ApiParam(name = "airlineId", value = "航司id") String airlineId,
                                         @RequestParam @ApiParam(name = "flag", value = "查询类型") String flag,
                                         @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return feeService.listFeeRuleByAirlineId(airlineId, flag, airportCode);
    }

    @ApiOperation(value = "费用信息修改")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @PutMapping(value = "/updateFeeRulesByFeeId")
    public Object updateFeeRulesByFeeId(@RequestParam @ApiParam(name = "urlName", value = "页面名称") String urlName,
                                        @RequestBody @ApiParam(name = "feeForm", value = "费用信息") @Validated({Update.class}) FeeForm feeForm) {
        String feeId = feeForm.getFeeId();
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        ResultBuilder rb = feeService.updateFeeRulesByFeeId(feeForm, user, urlName);
        try {
            mqService.updatefee(feeId);
        } catch (Exception e) {
            log.info("更新费用信息失败，请检查！feeId={}", feeId, e);
        }
        return rb;
    }

    @ApiOperation(value = "根据费用代码、机场三字码、费用类别、航司id查询费用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "airportCode", value = "机场三字码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "feeCode", value = "费用代码", dataType = "String", paramType = "query"),
    })
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/listFeeByFeeCodeAndFeeType")
    public Object listFeeByFeeCodeAndFeeType(@RequestParam @ApiParam(name = "feeType", value = "费用类别", required = true) String feeType,
                                             String airportCode, String feeCode, String airlineId) {
        return feeService.listFeeByFeeCodeAndFeeType(feeType, airportCode, feeCode, airlineId);
    }

    @ApiOperation(value = "根据费用代码查询费用信息")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/getFeeByFeeCode")
    public Object getFeeByFeeCode(@RequestParam @ApiParam(name = "feeCode", value = "费用代码", required = true) String feeCode,
                                  @RequestParam @ApiParam(name = "feeType", value = "费用类别", required = true) String feeType) {
        return feeService.getFeeByFeeCode(feeCode, feeType);
    }

    @ApiOperation(value = "复制机场下费用到航司")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = String.class)})
    @GetMapping("/addFeeInAirlineByFeeId")
    public Object copyFeeByFeeId(@RequestParam @ApiParam(name = "feeIds", value = "费用id", required = true) List<String> feeIds,
                                 @RequestParam @ApiParam(name = "airlineId", value = "航司id", required = true) String airlineId) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        return feeService.copyFeeByFeeId(feeIds, airlineId, user);
    }

    @ApiOperation(value = "航司费用列表页查询接口")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping("/listFeeByFeeTypeAndAirportCode")
    public Object listFeeByFeeTypeAndAirportCode(@RequestParam @ApiParam(name = "feeType", value = "费用类别", required = true) String feeType,
                                                 @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                                                 @RequestParam @ApiParam(name = "airlineId", value = "航司id", required = true) String airlineId,
                                                 @RequestParam(required = false) @ApiParam(name = "name", value = "输入框模糊匹配费用名称或code", required = false) String name) {
        return feeService.listFeeByFeeTypeAndAirportCode(feeType, airportCode, airlineId, name);
    }

    @ApiOperation(value = "根据费用id设置费用为机务类费用")
    @ApiResponses({@ApiResponse(code = 200, message = "success")})
    @PutMapping("/updateServiceFeeByFeeId")
    public Object updateServiceFeeByFeeId(@RequestBody @ApiParam(name = "listServiceFee", value = "机务类费用", required = true) List<ServiceFeeForm> listServiceFee,
                                          @RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode
    ) {
        return feeService.updateServiceFeeByFeeId(listServiceFee, airportCode);
    }


    @ApiOperation(value = "查询所有的费用去重展示(航司账单选择框用)")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/getDistinctAllFee")
    public Object getDistinctAllFee(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return feeService.getDistinctAllFee(airportCode);
    }

    @ApiOperation(value = "查询所有的费用去重展示(航司账单选择框用)")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/getFeeByFeeCodeAndFeeNameAndAirlineId")
    public Object getFeeByFeeCodeAndFeeNameAndAirlineId(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode,
                                                        @RequestParam @ApiParam(name = "airlineId", value = "航司id", required = true) String airlineId,
                                                        @RequestParam(required = false) @ApiParam(name = "feeName", value = "费用名称", required = false) String feeName,
                                                        @RequestParam(required = false) @ApiParam(name = "feeCode", value = "code", required = false) String feeCode) {
        return feeService.getFeeByFeeCodeAndFeeNameAndAirlineId(airportCode, airlineId, feeCode, feeName);
    }

    @ApiOperation(value = "查询去重后的feeCode和feeName")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = FeeInfo.class)})
    @GetMapping(value = "/getFeeCodeAndNameByAirportCode")
    public Object getFeeCodeAndNameByAirportCode(@RequestParam @ApiParam(name = "airportCode", value = "机场三字码") String airportCode) {
        return feeService.getFeeCodeAndNameByAirportCode(airportCode);
    }

    @ApiOperation(value = "定时任务判断费用是否过期")
    @GetMapping(value = "/allFormulaExpiredJudgment")
    public void allFormulaExpiredJudgment() {
        feeService.allFormulaExpiredJudgment();
    }

}
