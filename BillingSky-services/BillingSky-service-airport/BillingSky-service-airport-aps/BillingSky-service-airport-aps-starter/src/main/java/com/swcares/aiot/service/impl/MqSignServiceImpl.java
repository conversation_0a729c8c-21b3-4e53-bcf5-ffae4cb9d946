package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.aiot.ConvertCodeUtil;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.entity.BillBusDataItem;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.model.dto.ConfirmServiceRecordAddDto;
import com.swcares.aiot.core.model.dto.ServiceRecordSaveListDto;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.model.enums.BillOperation;
import com.swcares.aiot.core.model.vo.*;
import com.swcares.aiot.core.vo.SettlementItemVo;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;
import com.swcares.aiot.dao.*;
import com.swcares.aiot.service.MqSignService;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.service.impl.MqSignServiceImpl
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/6/23 16:51
 * @version v1.0
 */
@Service
@Slf4j
public class MqSignServiceImpl implements MqSignService {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String BUS_BILL_DATA_AUTO_SYNC = "车辆签单数据自动同步";
    private static final String SIGN_AUTO_IMPORT_SIGN = "电子签单自动导入-sign";
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private ServiceRecordDao serviceRecordDao;
    @Resource
    private ServiceRecordConfirmDao serviceRecordConfirmDao;
    @Resource
    private BillItemDao billItemDao;
    @Resource
    private VariableGuaranteeDao variableGuaranteeDao;
    @Resource
    private BusSignDao busSignDao;
    @Resource
    private BusSignHistoryDao busSignHistoryDao;
    @Resource
    private BusSignServiceRecordDao busSignServiceRecordDao;
    @Resource
    private BillItemBusDao billItemBusDao;
    @Resource
    private AirlineDao airlineDao;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final DateTimeFormatter busDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    @Transactional
    public void saveServiceRecord(List<SignBusDataSettlementVO> vo) {
        saveServiceRecordByCondition(vo, null, null);
    }

    private String getFlightDate(SignBusDataSettlementVO vo, DateTimeFormatter formatter) {
        String flightDate = vo.getFlightDate().format(formatter);
        if (vo.getPlanLandingDatetime() != null) {
            if (vo.getPlanLandingDatetime().isBefore(vo.getPlanLandingDatetime().toLocalDate().atStartOfDay().plusHours(4))) {
                flightDate = vo.getPlanLandingDatetime().toLocalDate().plusDays(-1L).format(formatter);
            }
        } else if (vo.getPlanTakeOffDatetime() != null && vo.getPlanTakeOffDatetime().isBefore(vo.getPlanTakeOffDatetime().toLocalDate().atStartOfDay().plusHours(4))) {
            flightDate = vo.getPlanTakeOffDatetime().toLocalDate().plusDays(-1L).format(formatter);
        }
        return flightDate;
    }

    private void addOldDeleteList(SignBusDataSettlementVO vo, Set<String> deleteList, Set<String> deleteConfirmList, Set<String> signDeleteList) {
        Long oldArriveFlightId = vo.getOldArriveFlightId();
        //删除历史到达航班id的历史业务保障数据
        if (oldArriveFlightId != null ) {
            //查询航班状态是否为已确认
            FlightInfo oldFi = flightInfoDao.getFlightInfoById(oldArriveFlightId.toString());
            if (oldFi != null) {
                addOldDeleteList(oldArriveFlightId, vo.getId(), oldFi, deleteList, deleteConfirmList, signDeleteList);
            }
        }
        Long oldTakeOffFlightId = vo.getOldTakeOffFlightId();
        //删除历史起飞航班id的历史业务保障数据
        if (oldTakeOffFlightId != null ) {
            //查询航班状态是否为已确认
            FlightInfo oldFi = flightInfoDao.getFlightInfoById(oldTakeOffFlightId.toString());
            if (oldFi != null) {
                addOldDeleteList(oldTakeOffFlightId, vo.getId(), oldFi, deleteList, deleteConfirmList, signDeleteList);
            }
        }
    }

    private void addOldDeleteList(Long oldId,Long signId,FlightInfo oldFi,Set<String> deleteList, Set<String> deleteConfirmList, Set<String> signDeleteList){
        if(oldId==null || signId == null){
            return;
        }
        if (oldFi.getVariableStatus() != 0 && oldFi.getVariableStatus() != 3) {
            //如果已确认，则删除备份service_revcord数据，并将service_record表的签单删除或更新字段设为1
            List<ServiceRecordConfirm> oldFlightSrcList = serviceRecordConfirmDao.findServiceRecordByFlightIdAndSignId(oldId.toString(), signId.toString());
            if (!oldFlightSrcList.isEmpty()) {
                deleteConfirmList.addAll(oldFlightSrcList.stream().map(ServiceRecordConfirm::getId).collect(Collectors.toList()));
            }
            List<ServiceRecord> oldFlightSrList = serviceRecordDao.findServiceRecordByFlightIdAndSignId(oldId.toString(), signId.toString());
            if (!oldFlightSrList.isEmpty()) {
                signDeleteList.addAll(oldFlightSrList.stream().map(ServiceRecord::getId).collect(Collectors.toList()));
            }
        } else {
            //如果未确认，直接将service_record表中对应的数据删除
            List<ServiceRecord> oldFlightSrList = serviceRecordDao.findServiceRecordByFlightIdAndSignId(oldId.toString(), signId.toString());
            if (!oldFlightSrList.isEmpty()) {
                deleteList.addAll(oldFlightSrList.stream().map(ServiceRecord::getId).collect(Collectors.toList()));
            }
        }
    }


    private void addFlightArr(String flightNos, SignBusDataSettlementVO vo, List<String[]> flightArr) {
        if (flightNos.startsWith("/")) {
            String[] strArr = new String[3];
            strArr[0] = flightNos.replace("/", "");
            strArr[1] = "D";
            strArr[2] = vo.getTakeOffFlightId() == null ? null : vo.getTakeOffFlightId().toString();
            flightArr.add(strArr);
        } else if (flightNos.endsWith("/")) {
            String[] strArr = new String[3];
            strArr[0] = flightNos.replace("/", "");
            strArr[1] = "A";
            strArr[2] = vo.getArriveFlightId() == null ? null : vo.getArriveFlightId().toString();
            flightArr.add(strArr);
        } else {//进出港
            String[] fns = flightNos.split("/");
            String[] strArrA = new String[3];
            strArrA[0] = fns[0];
            strArrA[1] = "A";
            strArrA[2] = vo.getArriveFlightId() == null ? null : vo.getArriveFlightId().toString();
            String[] strArrD = new String[3];
            strArrD[0] = fns[1];
            strArrD[1] = "D";
            strArrD[2] = vo.getTakeOffFlightId() == null ? null : vo.getTakeOffFlightId().toString();
            flightArr.add(strArrA);
            flightArr.add(strArrD);
        }
    }

    @Override
    public void saveServiceRecordByCondition(List<SignBusDataSettlementVO> voList, String syncflightNos, Long syncItemId) {
        ServiceRecordSaveListDto serviceRecordSaveListDto = new ServiceRecordSaveListDto();
        for (SignBusDataSettlementVO vo : voList) {
            log.info("接收电子签单消息，开始存储对象 : {}", vo);
            Map<String, FlightInfo> flightMap = new HashMap<>();
            List<String[]> flightArr = new ArrayList<>();
            String flightNos = vo.getFlightNo();
            String flightDate = getFlightDate(vo, dateTimeFormatter);
            addOldDeleteList(vo, serviceRecordSaveListDto.getDeleteList(), serviceRecordSaveListDto.getDeleteConfirmList(), serviceRecordSaveListDto.getSignDeleteList());
            addFlightArr(flightNos, vo, flightArr);

            for (BillBusDataItem bi : vo.getList()) {
                if (syncItemId != null && !syncItemId.equals(bi.getItemId())) {
                    log.error("手动同步签单签单消息，业务保障项id为{}，同步业务保障项id为空，或与同步对象签单项id不一致：{}",syncItemId,bi);
                    return;
                }
                saveBillBusDataItem(bi, vo, flightDate, flightArr, serviceRecordSaveListDto, flightMap, syncflightNos);
            }

        }
        deleteList(serviceRecordSaveListDto.getDeleteList(), serviceRecordSaveListDto.getSignDeleteList(), serviceRecordSaveListDto.getDeleteConfirmList());
        saveOrDeleteList(serviceRecordSaveListDto.getSaveList(), serviceRecordSaveListDto.getSaveConfirmList(),
                serviceRecordSaveListDto.getFlightServiceMap(), serviceRecordSaveListDto.getChangeFlightIdSet());
        if (serviceRecordSaveListDto.getDeleteList().isEmpty() && serviceRecordSaveListDto.getSaveList().isEmpty()
                && serviceRecordSaveListDto.getDeleteConfirmList().isEmpty() && serviceRecordSaveListDto.getSaveConfirmList().isEmpty()) {
            log.error(BusinessMessageEnum.SYNC_DATA_ERROR.getMsg());
        }
    }

    private void getCycleflightInfo(List<String[]> flightArr, SignBusDataSettlementVO vo, String landFlag, List<String[]> cycleflightInfo) {
        if (flightArr.size() > 1) {
            //如果是非过夜的进出港签单，根据归集判断同步航班
            if (Boolean.FALSE.equals(vo.getIsStayOvernight())) {
                if ("A".equals(landFlag)) {
                    cycleflightInfo.add(flightArr.get(0));
                } else if ("D".equals(landFlag)) {
                    cycleflightInfo.add(flightArr.get(1));
                } else {
                    cycleflightInfo.addAll(flightArr);
                }
            } else {//过夜进出港
                //航后落入降落
                if ("AFTER_FLIGHT".equals(vo.getSafeguardsType())) {
                    cycleflightInfo.add(flightArr.get(0));
                } else if ("BEFORE_FLIGHT".equals(vo.getSafeguardsType())) {
                    cycleflightInfo.add(flightArr.get(1));
                }
            }
        } else {
            cycleflightInfo.add(flightArr.get(0));
        }
    }

    private FlightInfo getFi(String[] flightInfo, Map<String, FlightInfo> flightMap, String flightRealDate,
                             String airportCode, boolean yesterdayFlag, SignBusDataSettlementVO vo) {
        FlightInfo fi;
        if (flightInfo[2] != null) {
            fi = flightMap.getOrDefault(flightInfo[2],
                    flightInfoDao.getFlightInfoById(flightInfo[2]));
            flightMap.put(flightInfo[2], fi);
        } else {
            fi = flightMap.getOrDefault(flightInfo[0] + flightInfo[1],
                    flightInfoDao.getFlightInfoByCondition(flightInfo[0], flightRealDate,
                            flightInfo[1], airportCode));
            if (yesterdayFlag && fi == null) {
                if ("A".equals(flightInfo[1]) && vo.getPlanLandingDatetime() != null) {
                    if (vo.getPlanLandingDatetime().isBefore(vo.getPlanLandingDatetime().toLocalDate().atStartOfDay().plusHours(4))) {
                        flightRealDate = vo.getPlanLandingDatetime().format(dateTimeFormatter);
                    }
                } else if ("D".equals(flightInfo[1]) && vo.getPlanTakeOffDatetime() != null && vo.getPlanTakeOffDatetime().isBefore(vo.getPlanTakeOffDatetime().toLocalDate().atStartOfDay().plusHours(4))) {
                    flightRealDate = vo.getPlanTakeOffDatetime().format(dateTimeFormatter);
                }

                fi = flightMap.getOrDefault(flightInfo[0] + flightInfo[1],
                        flightInfoDao.getFlightInfoByCondition(flightInfo[0], flightRealDate,
                                flightInfo[1], airportCode));
            }
            flightMap.put(flightInfo[0] + flightInfo[1], fi);
        }
        return fi;
    }

    private FlightInfo checkFlightInfo(String[] flightInfo, SignBusDataSettlementVO vo, Map<String, FlightInfo> flightMap, String flightDate, String airportCode) {
        String flightRealDate;
        boolean yesterdayFlag = false;
        if ("A".equals(flightInfo[1]) && vo.getPlanLandingDatetime() != null) {
            if (vo.getPlanLandingDatetime().isBefore(vo.getPlanLandingDatetime().toLocalDate().atStartOfDay().plusHours(4))) {
                flightRealDate = vo.getPlanLandingDatetime().toLocalDate().plusDays(-1L).format(dateTimeFormatter);
                yesterdayFlag = true;
            } else {
                flightRealDate = vo.getPlanLandingDatetime().format(dateTimeFormatter);
            }
        } else if ("D".equals(flightInfo[1]) && vo.getPlanTakeOffDatetime() != null) {
            if (vo.getPlanTakeOffDatetime().isBefore(vo.getPlanTakeOffDatetime().toLocalDate().atStartOfDay().plusHours(4))) {
                flightRealDate = vo.getPlanTakeOffDatetime().toLocalDate().plusDays(-1L).format(dateTimeFormatter);
                yesterdayFlag = true;
            } else {
                flightRealDate = vo.getPlanTakeOffDatetime().format(dateTimeFormatter);
            }
        } else {
            flightRealDate = flightDate;
        }
        return getFi(flightInfo, flightMap, flightRealDate, airportCode, yesterdayFlag, vo);
    }

    private void saveBillBusDataItem(BillBusDataItem bi, SignBusDataSettlementVO vo, String flightDate, List<String[]> flightArr,
                                     ServiceRecordSaveListDto serviceRecordSaveListDto, Map<String, FlightInfo> flightMap, String syncflightNos) {
        String airportCode = vo.getAirportCode();
        Long itemId = bi.getItemId();
        List<Object[]> obj = billItemDao.getBillItemAndServiceRecordById(itemId);
        if (ObjectUtils.isEmpty(obj)) {
            log.info("itemId = {} , 未找到 签单字段匹配记录", itemId);
            return;
        }
        for (Object[] objArr : obj) {
//            Date invalidDate = (Date) objArr[32];
//            if (invalidDate.after(DateUtils.parseDate(flightDate))) {
//                log.info("obj = {} , 未在匹配关系生效范围内", obj);
//                return;
//            }
            List<String[]> cycleflightInfo = new ArrayList<>();
            // 非过夜航班根据归集起飞降落判断航班号
            String landFlag = (String) objArr[29];
            //进出港航班
            getCycleflightInfo(flightArr, vo, landFlag, cycleflightInfo);
            // 遍历航班,如果非过夜航班且归集为起飞降落，则需要在两段航班中插入服务保障数据
            for (String[] flightInfo : cycleflightInfo) {
                if (CharSequenceUtil.isNotEmpty(syncflightNos) && !syncflightNos.contains(flightInfo[0])) {
                    continue;
                }
                FlightInfo fi = checkFlightInfo(flightInfo, vo, flightMap, flightDate, airportCode);
                saveServiceRecord(bi, fi, serviceRecordSaveListDto, objArr, vo, flightInfo, DateUtils.format(fi.getFlightDate()));
            }
        }

    }

    private void saveServiceRecord(BillBusDataItem bi, FlightInfo fi, ServiceRecordSaveListDto serviceRecordSaveListDto, Object[] objArr,
                                   SignBusDataSettlementVO vo, String[] flightInfo, String flightRealDate) {
        String airportCode = vo.getAirportCode();
        String serviceCode = objArr[19] == null ? null : (String) objArr[19];
        //批量删除集合
        Set<String> deleteList = serviceRecordSaveListDto.getDeleteList();
        Set<String> deleteConfirmList = serviceRecordSaveListDto.getDeleteConfirmList();
        Set<String> signDeleteList = serviceRecordSaveListDto.getSignDeleteList();
        List<ServiceRecord> saveList = serviceRecordSaveListDto.getSaveList();
        List<ServiceRecordConfirm> saveConfirmList = serviceRecordSaveListDto.getSaveConfirmList();
        //确认到航班时，先查出该航班所有的业务保障数据塞进去，然后处理新数据时，相同则删除，如果不同则将flightId塞进changeFlightIdSet
        Map<String, Map<String, List<ServiceRecord>>> flightServiceMap = serviceRecordSaveListDto.getFlightServiceMap();
        Set<String> changeFlightIdSet = serviceRecordSaveListDto.getChangeFlightIdSet();
        if (fi != null && fi.getVariableStatus() != 0 && fi.getVariableStatus() != 3) {
            log.info("FlightInfo = {} , 该航班已确认业务保障数据，同步到备份表", fi);
            Map<String, List<ServiceRecord>> serviceCodeMap = flightServiceMap.getOrDefault(fi.getId(), new HashMap<>());
            if (!serviceCodeMap.containsKey(serviceCode)) {
                List<ServiceRecord> repeatServiceList = serviceRecordDao.listServiceRecordByCondition(fi.getId(), airportCode, serviceCode);
                serviceCodeMap.put(serviceCode, repeatServiceList);
                flightServiceMap.put(fi.getId(), serviceCodeMap);
            }
            ConfirmServiceRecordAddDto confirmServiceRecordAddDto = new ConfirmServiceRecordAddDto()
                    .setFi(fi)
                    .setObjArr(objArr)
                    .setVo(vo)
                    .setDeleteConfirmList(deleteConfirmList)
                    .setBi(bi)
                    .setSaveConfirmList(saveConfirmList)
                    .setSignDeleteList(signDeleteList)
                    .setFlightServiceMap(flightServiceMap)
                    .setChangeFlightId(changeFlightIdSet);
            addConfirmServiceRecord(confirmServiceRecordAddDto);
        } else {
            log.info("FlightInfo = {} , 该航班未确认业务保障数据，同步到业务保障表", fi);
            addServiceRecord(fi, objArr, flightRealDate, flightInfo, vo, deleteList, bi, saveList);
        }
    }

    private void deleteList(Set<String> deleteList, Set<String> signDeleteList, Set<String> deleteConfirmList) {
        // 如果删除list不为空，则执行批量删除
        if (!deleteList.isEmpty()) {
            serviceRecordDao.deleteServiceRecordByIds(new ArrayList<>(deleteList));
        }

        //删除service_record_confirm表中的数据
        if (!deleteConfirmList.isEmpty()) {
            serviceRecordConfirmDao.deleteServiceRecordByIds(new ArrayList<>(deleteConfirmList));
        }

        //将service_record表中数据置为签单删除
        if (!signDeleteList.isEmpty()) {
            serviceRecordDao.signDeleteOrUpdate(new ArrayList<>(signDeleteList));
        }
    }

    private void saveOrDeleteList(List<ServiceRecord> saveList, List<ServiceRecordConfirm> saveConfirmList,
                                  Map<String, Map<String, List<ServiceRecord>>> flightServiceMap, Set<String> changeFlightIdSet) {

        // 如果savelist不为空，则执行批量保存·
        if (!saveList.isEmpty()) {
            serviceRecordDao.saveAll(saveList);
        }

        //新增service_record_confirm表中的数据
        if (!saveConfirmList.isEmpty()) {
            serviceRecordConfirmDao.saveAll(saveConfirmList);
        }
        //将航班业务保障数据状态置为已修改
        for (Map.Entry<String, Map<String, List<ServiceRecord>>> flightIdEntry : flightServiceMap.entrySet()) {
            for (Map.Entry<String, List<ServiceRecord>> entry : flightIdEntry.getValue().entrySet()) {
                if (!changeFlightIdSet.contains(entry.getKey())) {
                    List<ServiceRecord> excessiveService = entry.getValue();
                    if (excessiveService != null && !excessiveService.isEmpty()) {
                        changeFlightIdSet.add(entry.getKey());
                    }
                }
            }
        }
        if (!changeFlightIdSet.isEmpty()) {
            flightInfoDao.updateVariableByFlightIdS(new ArrayList<>(changeFlightIdSet));
        }

    }

    /**
     * Title: isInStayTime <br>
     * Description: 签单中的时间应该在飞机的停场时间内 <br>
     *
     * @param bi 签单数据
     * @param fi 航班数据
     * @return boolean
     * <AUTHOR>  <br>
     * date 2022/10/14 15:52<br>
     */
    private boolean isInStayTime(BillBusDataItem bi, FlightInfo fi) {
        if (fi == null || bi.getStartTime() == null || bi.getEndTime() == null) {
            return false;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime startzdt = bi.getStartTime().atZone(zoneId);
        Date startTime = Date.from(startzdt.toInstant());

        ZonedDateTime endzdt = bi.getEndTime().atZone(zoneId);
        Date endTime = Date.from(endzdt.toInstant());

        if (fi.getStayStartTime() != null && fi.getStayEndTime() != null) {
            return startTime.before(fi.getStayStartTime()) || endTime.after(fi.getStayEndTime());
        } else if ("A".equals(fi.getFlightFlag())) {
            //降落航班，签单开始时间大于停场开始时间
            return fi.getStayStartTime() != null && startTime.before(fi.getStayStartTime());
        } else if ("D".equals(fi.getFlightFlag())) {
            //起飞航班，签单结束时间小于停场结束时间
            return fi.getStayEndTime() != null && endTime.after(fi.getStayEndTime());
        }
        return false;
    }


    @Override
    @Transactional
    public void itemUpdate(SettlementItemVo ssiv) {
        BillItem oldBi = billItemDao.getBillItemById(ssiv.getId());
        if (oldBi == null) {
            log.error("id为：{}的数据未找到", ssiv.getId());
            throw new GenericException(BusinessMessageEnum.ITEM_GET_ERROE.getCode(),
                    BusinessMessageEnum.ITEM_GET_ERROE.getMsg());
        }
        Long itemId = ssiv.getId();
        // 如果格式变更，或者关联节点变更，则解除匹配关系
        if ((ssiv.getAssociatedNode() != null
                && !oldBi.getAssociatedNode().equals(ssiv.getAssociatedNode()))
                || (ssiv.getDataFormat() != null
                && !oldBi.getDataFormat().equals(ssiv.getDataFormat()))) {
            variableGuaranteeDao.deleteAllByItemId(itemId);
        }
        copyPropertiesIgnoreNull(ssiv, oldBi);

        // 设置id
        oldBi.setId(String.valueOf(itemId));
        // 设置删除字段
        if (ssiv.getDeleted() == null) {
            oldBi.setDeleted("0");
        } else {
            oldBi.setDeleted(String.valueOf(ssiv.getDeleted().getValue()));
        }
        // 如果删除，则删除匹配关系
        if ("1".equals(oldBi.getDeleted())) {
            variableGuaranteeDao.deleteAllByItemId(ssiv.getId());
        }
        oldBi.setFlightSelectorItem(OBJECT_MAPPER.valueToTree(ssiv.getFlightSelectorItemDTOList()).toString());
        oldBi.setId(String.valueOf(ssiv.getId()));
        oldBi.setUpdatedBy(SIGN_AUTO_IMPORT_SIGN);
        oldBi.setUpdatedTime(new Date());
        billItemDao.save(oldBi);
    }

    @Override
    public void itemInsert(SettlementItemVo ssi) {
        BillItem bi = new BillItem();
        copyPropertiesIgnoreNull(ssi, bi);
        bi.setId(String.valueOf(ssi.getId()));
        // 设置id
        bi.setId(String.valueOf(ssi.getId()));
        // 设置删除字段
        if (ssi.getDeleted() == null) {
            bi.setDeleted("0");
        } else {
            bi.setDeleted(String.valueOf(ssi.getDeleted().getValue()));
        }
        bi.setCreatedBy(SIGN_AUTO_IMPORT_SIGN);
        bi.setCreatedTime(new Date());
        bi.setUpdatedBy(SIGN_AUTO_IMPORT_SIGN);
        bi.setUpdatedTime(new Date());
        bi.setFlightSelectorItem(OBJECT_MAPPER.valueToTree(ssi.getFlightSelectorItemDTOList()).toString());
        billItemDao.save(bi);
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    @Override
    @Transactional
    public void itemDelete(SignDelete sd) {
        Long itemId = sd.getId();
        BillItem oldBi = billItemDao.getBillItemById(itemId);
        if (oldBi == null) {
            throw new GenericException(BusinessMessageEnum.ITEM_GET_ERROE.getCode(),
                    BusinessMessageEnum.ITEM_GET_ERROE.getMsg());
        }
        // 删除匹配关系
        variableGuaranteeDao.deleteAllByItemId(itemId);

        oldBi.setDeleted("1");
        oldBi.setUpdatedBy(SIGN_AUTO_IMPORT_SIGN);
        oldBi.setUpdatedTime(new Date());
        billItemDao.save(oldBi);
    }

    @Override
    @Transactional
    public void saveBusData(BusDataSendVo vo) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        BusSign newBs = new BusSign();
        BusSign oldBs = busSignDao.getBusSignBySignId(vo.getId());
        if (oldBs != null && oldBs.getId() != null) {
            // 账单状态为待审核、已确认、拒绝处理的账单，不会接收航班节点保障系统中车辆签单的更新
            if ("1".equals(oldBs.getSubmit()) || "3".equals(oldBs.getSubmit()) || "4".equals(oldBs.getSubmit())) {
                return;
            }
            copyPropertiesIgnoreNull(oldBs, newBs);
        }
        newBs.setFlightNo(vo.getFlightNo());
        newBs.setAirlineCode(vo.getAirlineCode());
        newBs.setAirlineShortName(vo.getAirlineShortName());
        newBs.setSettleCode(vo.getSettleCode());
        if (StringUtils.isBlank(newBs.getAirlineCode())) {
            String airlineCode = vo.getFlightNo().substring(0, 2);
            newBs.setAirlineCode(airlineCode);
            if (StringUtils.isBlank(newBs.getAirlineShortName())) {
                List<AirlineInfoAps> airlineInfoList = airlineDao.listAirlineByAirlineCode(airlineCode, vo.getAirportCode());
                if (!airlineInfoList.isEmpty()) {
                    newBs.setAirlineShortName(airlineInfoList.get(0).getAirlineShortName());
                }
            }
        }
        switch (vo.getDepartureArriveStatus()) {
            case "ARR":
                newBs.setFlightDate(DateUtils.parseDate(vo.getArriveFlightDate().format(formatter)));
                newBs.setFlightFlag("A");
                break;
            case "DEP":
                newBs.setFlightDate(DateUtils.parseDate(vo.getDepartureFlightDate().format(formatter)));
                newBs.setFlightFlag("D");
                break;
            case "ARR-DEP":
                newBs.setFlightDate(DateUtils.parseDate(vo.getArriveFlightDate().format(formatter)));
                newBs.setFlightFlag("A-D");
                break;
            default:
                return;
        }
        newBs.setFlightSegment(vo.getAirline());

        handleNewBs(vo, newBs, oldBs);
        busSignDao.save(newBs);

        List<BusSignServiceRecord> bssList = new ArrayList<>();
        handleBusSignServiceRecord(vo, bssList);
        busSignServiceRecordDao.deteleBySignIds(vo.getId());

        busSignServiceRecordDao.saveAll(bssList);

        BusSignHistory bsh = new BusSignHistory();
        copyPropertiesIgnoreNull(newBs, bsh);
        bsh.setId(null);
        handleServiceRecordsString(vo, bssList, bsh);
        bsh.setCreateTime(new Date());
        bsh.setCreateBy(BUS_BILL_DATA_AUTO_SYNC);
        if (oldBs == null) {
            bsh.setOperation(BillOperation.GENERATE.getCode());
        } else {
            bsh.setOperation(BillOperation.RESETTLEMENT.getCode());
        }
        busSignHistoryDao.save(bsh);
    }

    private void handleServiceRecordsString(BusDataSendVo vo, List<BusSignServiceRecord> bssList, BusSignHistory bsh) {
        if (!bssList.isEmpty()) {
            try {
                bsh.setServiceRecordsString(objectMapper.writeValueAsString(bssList));
            } catch (IOException e) {
                log.error("车辆签单历史保存服务项出错, param = {}", vo, e);
            }
        }
    }

    private void handleNewBs(BusDataSendVo vo, BusSign newBs, BusSign oldBs) {
        ZonedDateTime zonedDateTime = vo.getCreatedTime().atZone(ZoneId.systemDefault());
        Instant instant2 = zonedDateTime.toInstant();
        newBs.setSignCreatedTime(Date.from(instant2));
        newBs.setSignId(vo.getId());
        newBs.setAirportCode(vo.getAirportCode());
        newBs.setAirportName(ConvertCodeUtil.convertAirportCode(vo.getAirportCode()));
        newBs.setLicensePlateNumber(vo.getLicensePlateNumber());
        newBs.setUnitPrice(vo.getSumSettlementAmount());
        newBs.setSettlementAmount(vo.getSumSettlementAmount());
        newBs.setRemark(vo.getRemark());
        newBs.setSignPdfUrl(vo.getSignPdfUrl());
        if (oldBs == null) {
            newBs.setCreateBy(BUS_BILL_DATA_AUTO_SYNC);
            newBs.setCreateTime(new Date());
        }
        newBs.setModifiedBy(BUS_BILL_DATA_AUTO_SYNC);
        newBs.setModifiedTime(new Date());
        if (StringUtils.isNotEmpty(vo.getAirplaneNumber())) {
            newBs.setFlightReg(vo.getAirplaneNumber());
            if (StringUtils.isBlank(newBs.getSettleCode())) {
                newBs.setSettleCode(busSignDao.getSettleCode(vo.getAirplaneNumber(), new Date()));
            }
        } else {
            newBs.setIssue("没有机号");
        }
        newBs.setSubmit("0");
        if (oldBs != null) {
            newBs.setRevocation(0);
            newBs.setFeedback("");
        }
    }

    private void handleBusSignServiceRecord(BusDataSendVo vo, List<BusSignServiceRecord> bssList) {
        for (BusDataItemVehicleVO bdVo : vo.getBillBusDataItemVehicleVOList()) {
            BusSignServiceRecord bss = new BusSignServiceRecord();
            bss.setAirportCode(vo.getAirportCode());
            bss.setBillSignId(vo.getId());
            bss.setItemId(bdVo.getItemId());
            bss.setItemName(bdVo.getItemName());
            String dataFormat = bdVo.getDataFormat();
            if ("number_format".equals(dataFormat) && bdVo.getTimes() > 0) {
                bss.setItemValue(bdVo.getTimes().toString());
            } else if ("boolean_format".equals(dataFormat) && "1".equals(bdVo.getHaveOrNot())) {
                bss.setItemValue(bdVo.getHaveOrNot());
            } else if ("time_format".equals(dataFormat) && ObjectUtils.isNotEmpty(bdVo.getStartTime()) || ObjectUtils.isNotEmpty(bdVo.getEndTime())) {
                bss.setItemValue(bdVo.getStartTime().format(busDateTimeFormatter) + "-" + bdVo.getEndTime().format(busDateTimeFormatter));
            } else if ("selector_format".equals(dataFormat) && ObjectUtils.isNotEmpty(bdVo.getSelectorItemName()) && bdVo.getStatus().equals(true)) {
                bss.setItemValue(bdVo.getSelectorItemName());
            } else {
                continue;
            }
            bss.setDataFormat(dataFormat);
            bss.setCreateBy(BUS_BILL_DATA_AUTO_SYNC);
            bss.setCreateTime(new Date());
            bss.setModifiedBy(BUS_BILL_DATA_AUTO_SYNC);
            bss.setModifiedTime(new Date());
            bssList.add(bss);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBusItem(BusItemSendVo itemSendVo) {
        billItemBusDao.deleteAllItem();
        List<BusItemSaveVo> itemSaveVOList = itemSendVo.getItemSaveVOList();
        Set<Long> deleteData = new HashSet<>();
        List<BillItemBus> bibList = new ArrayList<>();
        for (BusItemSaveVo sivo : itemSaveVOList) {
            BillItemBus bib = getBillItemBus(itemSendVo, sivo, deleteData);
            bibList.add(bib);
        }
        if (!bibList.isEmpty()) {
            billItemBusDao.saveAll(bibList);
        }
    }

    private static @NotNull BillItemBus getBillItemBus(BusItemSendVo itemSendVo, BusItemSaveVo sivo, Set<Long> deleteData) {
        BillItemBus bib = new BillItemBus();
        bib.setItemId(sivo.getItemId());
        bib.setItemName(sivo.getItemName());
        bib.setItemType(sivo.getItemType());
        bib.setDataFormat(sivo.getDataFormat());
        bib.setInvalid("1");
        bib.setCreateBy("车辆保障项自动同步");
        bib.setCreateTime(new Date());
        bib.setModifiedBy("车辆保障项自动同步");
        bib.setModifiedTime(new Date());
        bib.setAirportCode(itemSendVo.getAirportCode());
        bib.setTemplateId(sivo.getTemplateId());
        if (sivo.getTemplateId() != null) {
            deleteData.add(sivo.getTemplateId());
        }
        return bib;
    }

    @Transactional
    @Override
    public void deleteBusItem(BusItemDeleteSendVo itemSendVo) {
        if (itemSendVo.getId() == null) {
            return;
        }
        if ("ByItemId".equals(itemSendVo.getDeleteType())) {
            BillItemBus billItemBus = billItemBusDao.getById(itemSendVo.getId().toString());
            billItemBus.setInvalid("0");
            billItemBusDao.save(billItemBus);
        } else {
            List<Long> idList = new ArrayList<>();
            idList.add(itemSendVo.getId());
            billItemBusDao.deleteByTemplateId(idList);
        }
    }

    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    private void addServiceRecord(FlightInfo fi, Object[] objArr, String flightRealDate, String[] flightInfo, SignBusDataSettlementVO vo, Set<String> deleteList, BillBusDataItem bi, List<ServiceRecord> saveList) {
        String serviceCode = (String) objArr[19];
        String dataUpdate = (String) objArr[30];
        String variableName = (String) objArr[20];
        String rule = (String) objArr[31];
        String apsType = (String) objArr[26];
        // 判断结算是否已有数据
        if (judgeReturnOrDeleteList(fi, flightRealDate, flightInfo, vo, deleteList, serviceCode, dataUpdate)) {
            log.info("FlightInfo = {} ，objArr={}, 该航班下的该保障数据已有数据，且不允许人工录入数据后同步更新", fi,objArr);
            return;
        }
        // setServiceRecord
        ServiceRecord sr = setServiceRecord(fi, flightRealDate, flightInfo, vo, bi, serviceCode, variableName);
        setRule(sr, bi, rule, apsType, serviceCode);
        if(sr.getUsedNumber()==null || sr.getUsedNumber()==0.0){
            log.info("FlightInfo = {} ，objArr={}, 同步数据为null或为0，不进行保存", fi,objArr);
            return;
        }
        // setBillBusDataItemAndServiceRecord
        setBillBusDataItemAndServiceRecord(vo, bi, sr);
        // saveList
        setSaveList(bi, saveList, sr);
    }

    private boolean judgeReturnOrDeleteList(FlightInfo fi, String flightRealDate, String[] flightInfo, SignBusDataSettlementVO vo, Set<String> deleteList, String serviceCode, String dataUpdate) {
        List<ServiceRecord> srList;
        List<String> deleteIds;
        if (fi != null) {
            srList = serviceRecordDao.listServiceRecordByCondition(fi.getId(), fi.getAirportCode(), serviceCode);
        } else {
            srList = serviceRecordDao.listServiceRecordByDateNoFlag(DateUtils.parseDate(flightRealDate), flightInfo[0], flightInfo[1], vo.getAirportCode(), serviceCode);
        }
        if (!srList.isEmpty()) {
            if ("0".equals(dataUpdate)) {

                return true;
            }
            //结算保障数据是非人工录入，则可以删除
            deleteIds = srList.stream()
                    .filter(s -> s.getModifiedBy() != null
                            && ((SIGN_AUTO_IMPORT_SIGN.equals(s.getModifiedBy()))
                            || "电子签单手动导入-sign".equals(s.getModifiedBy()))).map(ServiceRecord::getId).collect(Collectors.toList());

            deleteList.addAll(deleteIds);
        }
        return false;
    }

    private static void setBillBusDataItemAndServiceRecord(SignBusDataSettlementVO vo, BillBusDataItem bi, ServiceRecord sr) {
        sr.setCreateBy(SIGN_AUTO_IMPORT_SIGN);
        sr.setCreateTime(new Date());
        sr.setModifiedBy(SIGN_AUTO_IMPORT_SIGN);
        sr.setModifiedTime(new Date());
        sr.setSignPdfUrl(vo.getSignPdfUrl());
        sr.setBillBusDataId(vo.getId() == null ? null : vo.getId().toString());
        sr.setBillBusDataItemId(bi.getId().toString());
    }

    private @NotNull ServiceRecord setServiceRecord(FlightInfo fi, String flightRealDate, String[] flightInfo, SignBusDataSettlementVO vo, BillBusDataItem bi, String serviceCode, String variableName) {
        ServiceRecord sr = new ServiceRecord();
        if (fi != null) {
            sr.setFlightId(fi.getId());
        } else {
            sr.setFlightDate(DateUtils.parseDate(flightRealDate));
            sr.setFlightNo(flightInfo[0]);
            sr.setFlightFlag(flightInfo[1]);
            sr.setFlightId("");
        }
        sr.setServiceCode(serviceCode);
        sr.setServiceName(variableName);
        sr.setInvalid("1");
        sr.setAirportCode(vo.getAirportCode());
        if (isInStayTime(bi, fi)) {
            sr.setInScope("0");
        }
        return sr;
    }

    private static void setSaveList(BillBusDataItem bi, List<ServiceRecord> saveList, ServiceRecord sr) {
        saveList.add(sr);
        if (bi.getCorridorBridgeType() != null) {
            if ("double_bridge".equals(bi.getCorridorBridgeType())) {
                ServiceRecord newSr = new ServiceRecord();
                copyPropertiesIgnoreNull(sr, newSr);
                newSr.setBillBusDataItemId(newSr.getBillBusDataItemId() + "_1");
                saveList.add(newSr);
            } else if ("three_bridge".equals(bi.getCorridorBridgeType())) {
                ServiceRecord newSr = new ServiceRecord();
                copyPropertiesIgnoreNull(sr, newSr);
                newSr.setBillBusDataItemId(newSr.getBillBusDataItemId() + "_1");
                ServiceRecord newSr2 = new ServiceRecord();
                copyPropertiesIgnoreNull(sr, newSr2);
                newSr2.setBillBusDataItemId(newSr2.getBillBusDataItemId() + "_2");

                saveList.add(newSr);
                saveList.add(newSr2);
            }
        }
    }

    private void setRule(ServiceRecord sr, BillBusDataItem bi, String rule, String apsType, String serviceCode) {
        switch (rule) {
            case "0":
                rule0(sr, bi, apsType, serviceCode);
                break;
            case "1":
                Double userNumber = Double.valueOf(bi.getHaveOrNot());
                if (userNumber.equals(0.0)) {
                    return;
                }
                sr.setUsedNumber(userNumber);
                break;
            case "2":
                if (bi.getStartTime() == null || bi.getEndTime() == null) {
                    return;
                }
                if (bi.getStartTime().isAfter(bi.getEndTime())) {
                    return;
                }
                Duration duration = Duration.between(bi.getStartTime(), bi.getEndTime());
                long minutes = duration.toMinutes();
                long usdTime = (minutes / 30);
                if (minutes > usdTime * 30) {
                    usdTime++;
                }
                sr.setUsedNumber((double) usdTime / 2);
                break;
            default:
                sr.setUsedNumber(1.0);
        }
    }

    private void rule0(ServiceRecord sr, BillBusDataItem bi, String apsType, String serviceCode) {
        if ("H".equals(apsType)) {
            if (bi.getStartTime() == null || bi.getEndTime() == null) {
                return;
            }
            ZoneId zoneId = ZoneId.systemDefault();
            ZonedDateTime startzdt = bi.getStartTime().atZone(zoneId);
            Date startTime = Date.from(startzdt.toInstant());
            startTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(startTime));
            sr.setStartTime(startTime);

            ZonedDateTime endzdt = bi.getEndTime().atZone(zoneId);
            Date endTime = Date.from(endzdt.toInstant());
            endTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(endTime));
            sr.setEndTime(endTime);
            assert startTime != null;
            if (startTime.after(endTime)) {
                return;
            }
            sr.setUsedNumber(DateUtils.calculateLengthOfTime(serviceCode, startTime, endTime));
        } else {
            if (bi.getTimes() == null || bi.getTimes().equals(0.0)) {
                return;
            }
            sr.setUsedNumber(bi.getTimes());
        }
    }


    private void addConfirmServiceRecord(ConfirmServiceRecordAddDto confirmServiceRecordAddDto) {
        Object[] objArr = confirmServiceRecordAddDto.getObjArr();
        String serviceCode = (String) objArr[19];
        String dataUpdate = (String) objArr[30];
        String variableName = (String) objArr[20];
        String rule = (String) objArr[31];
        String apsType = (String) objArr[26];
        FlightInfo fi = confirmServiceRecordAddDto.getFi();
        if ("0".equals(dataUpdate)) {
            log.info("FlightInfo = {} ，objArr={}, 该航班下的该保障数据已有数据，且不允许人工录入数据后同步更新", fi,objArr);
            return;
        }
        Set<String> signDeleteList = confirmServiceRecordAddDto.getSignDeleteList();
        Set<String> deleteConfirmList = confirmServiceRecordAddDto.getDeleteConfirmList();
        SignBusDataSettlementVO vo = confirmServiceRecordAddDto.getVo();
        BillBusDataItem bi = confirmServiceRecordAddDto.getBi();
        replDataSignDeletes(fi, signDeleteList, serviceCode);
        replDataDeleteConfirms(fi, deleteConfirmList, serviceCode);
        ServiceRecordConfirm sr = getSr(fi, vo, bi, serviceCode, variableName, rule, apsType);
        if (sr == null || sr.getUsedNumber()==null ||sr.getUsedNumber()==0.0 ) {
            log.info("FlightInfo = {} ，objArr={}, 同步数据为null或为0，不进行保存", fi,objArr);
            return;
        }

        Map<String, Map<String, List<ServiceRecord>>> flightServiceMap = confirmServiceRecordAddDto.getFlightServiceMap();
        Set<String> changeFlightId = confirmServiceRecordAddDto.getChangeFlightId();
        replChangeFlightIds(fi, flightServiceMap, changeFlightId, serviceCode, sr);

        List<ServiceRecordConfirm> saveConfirmList = confirmServiceRecordAddDto.getSaveConfirmList();
        saveConfirmList.add(sr);
        if (bi.getCorridorBridgeType() != null) {
            if ("double_bridge".equals(bi.getCorridorBridgeType())) {
                ServiceRecordConfirm newSr = new ServiceRecordConfirm();
                copyPropertiesIgnoreNull(sr, newSr);

                newSr.setBillBusDataItemId(newSr.getBillBusDataItemId() + "_1");
                saveConfirmList.add(newSr);
            } else if ("three_bridge".equals(bi.getCorridorBridgeType())) {
                ServiceRecordConfirm newSr = new ServiceRecordConfirm();
                copyPropertiesIgnoreNull(sr, newSr);
                newSr.setBillBusDataItemId(newSr.getBillBusDataItemId() + "_1");
                ServiceRecordConfirm newSr2 = new ServiceRecordConfirm();
                copyPropertiesIgnoreNull(sr, newSr2);
                newSr2.setBillBusDataItemId(newSr2.getBillBusDataItemId() + "_2");

                saveConfirmList.add(newSr);
                saveConfirmList.add(newSr2);
            }
        }
    }

    private void replChangeFlightIds(FlightInfo fi, Map<String, Map<String, List<ServiceRecord>>> flightServiceMap, Set<String> changeFlightId, String serviceCode, ServiceRecordConfirm sr) {
        if (!changeFlightId.contains(fi.getId())) {
            boolean needChange = true;
            Map<String, List<ServiceRecord>> serviceMap = flightServiceMap.get(fi.getId());
            if (serviceMap != null) {
                List<ServiceRecord> repeatServiceList = serviceMap.get(serviceCode);
                if (repeatServiceList != null && !repeatServiceList.isEmpty()) {
                    Iterator<ServiceRecord> iterator = repeatServiceList.iterator();
                    needChange = isNeedChange(sr, iterator, needChange);
                    serviceMap.put(serviceCode, repeatServiceList);
                    flightServiceMap.put(fi.getId(), serviceMap);
                }
            }
            if (needChange) {
                changeFlightId.add(fi.getId());
            }
        }
    }

    private boolean isNeedChange(ServiceRecordConfirm sr, Iterator<ServiceRecord> iterator, boolean needChange) {
        while (iterator.hasNext()) {
            ServiceRecord oldSr = iterator.next();
            if (oldSr.getServiceCode().equals(sr.getServiceCode()) && ((equalsDate(oldSr.getStartTime(), sr.getStartTime())
                    && equalsDate(oldSr.getEndTime(), sr.getEndTime()))
                    || Objects.equals(oldSr.getUsedNumber(), sr.getUsedNumber()))) {
                needChange = false;
                iterator.remove();
                break;
            }

        }
        return needChange;
    }

    private ServiceRecordConfirm getSr(FlightInfo fi, SignBusDataSettlementVO vo, BillBusDataItem bi, String serviceCode, String variableName, String rule, String apsType) {
        ServiceRecordConfirm sr = new ServiceRecordConfirm();
        sr.setFlightId(fi.getId());
        sr.setServiceCode(serviceCode);
        sr.setServiceName(variableName);
        sr.setInvalid("1");
        sr.setAirportCode(vo.getAirportCode());
        if (isInStayTime(bi, fi)) {
            sr.setInScope("0");
        }
        // 无规则
        if ("0".equals(rule)) {
            if (replZero(bi, serviceCode, apsType, sr)) {
                return null;
            }
        } else if ("1".equals(rule)) {
            Double userNumber = Double.valueOf(bi.getHaveOrNot());
            if (userNumber.equals(0.0)) {
                return null;
            }
            sr.setUsedNumber(userNumber);
        } else if ("2".equals(rule)) {
            Long usdTime = getUsdTime(bi);
            if (usdTime == null) {
                return null;
            }
            sr.setUsedNumber((double) usdTime / 2);
        } else {// 3为统计计算为1次
            sr.setUsedNumber(1.0);
        }
        sr.setCreateBy(SIGN_AUTO_IMPORT_SIGN);
        sr.setCreateTime(new Date());
        sr.setModifiedBy(SIGN_AUTO_IMPORT_SIGN);
        sr.setModifiedTime(new Date());
        sr.setBillBusDataId(vo.getId() == null ? null : vo.getId().toString());
        sr.setSignPdfUrl(vo.getSignPdfUrl());
        sr.setBillBusDataItemId(bi.getId().toString());
        return sr;
    }

    private boolean replZero(BillBusDataItem bi, String serviceCode, String apsType, ServiceRecordConfirm sr) {
        if ("H".equals(apsType)) {
            return judgeApsType(bi, serviceCode, sr);
        } else {// 如果为数值
            if (bi.getTimes() == null) {
                return true;
            }
            sr.setUsedNumber(bi.getTimes());
        }
        return false;
    }

    private Long getUsdTime(BillBusDataItem bi) {
        if (bi.getStartTime() == null || bi.getEndTime() == null) {
            return null;
        }
        if (bi.getStartTime().isAfter(bi.getEndTime())) {
            return null;
        }
        Duration duration = Duration.between(bi.getStartTime(), bi.getEndTime());
        // 相差的分钟数
        long minutes = duration.toMinutes();
        long usdTime = (minutes / 30);
        if (minutes > usdTime * 30) {
            usdTime++;
        }
        return usdTime;
    }

    private boolean judgeApsType(BillBusDataItem bi, String serviceCode, ServiceRecordConfirm sr) {
        if (bi.getStartTime() == null || bi.getEndTime() == null) {
            return true;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime startzdt = bi.getStartTime().atZone(zoneId);
        Date startTime = Date.from(startzdt.toInstant());
        startTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(startTime));
        sr.setStartTime(startTime);
        ZonedDateTime endzdt = bi.getEndTime().atZone(zoneId);
        Date endTime = Date.from(endzdt.toInstant());
        endTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(endTime));
        sr.setEndTime(endTime);
        assert startTime != null;
        if (startTime.after(endTime)) {
            return true;
        }
        sr.setUsedNumber(DateUtils.calculateLengthOfTime(serviceCode, startTime, endTime));
        return false;
    }

    private void replDataDeleteConfirms(FlightInfo fi, Set<String> deleteConfirmList, String serviceCode) {

        List<ServiceRecordConfirm> srList = serviceRecordConfirmDao.listServiceRecordByCondition(fi.getId(), fi.getAirportCode(), serviceCode);
        if (!srList.isEmpty()) {
            //结算保障数据是非人工录入，则可以删除
            List<String> deleteIds = srList.stream().map(ServiceRecordConfirm::getId).collect(Collectors.toList());
            deleteConfirmList.addAll(deleteIds);
        }
    }

    private void replDataSignDeletes(FlightInfo fi, Set<String> signDeleteList, String serviceCode) {

        List<ServiceRecord> srDeleteList = serviceRecordDao.listServiceRecordByCondition(fi.getId(), fi.getAirportCode(), serviceCode);
        if (!srDeleteList.isEmpty()) {
            List<String> deleteIds = srDeleteList.stream().map(ServiceRecord::getId).collect(Collectors.toList());
            signDeleteList.addAll(deleteIds);
        }
    }

    private boolean equalsDate(Date d1, Date d2) {
        if (d1 == null || d2 == null) {
            return false;
        }
        return d1.compareTo(d2) == 0;
    }

}
