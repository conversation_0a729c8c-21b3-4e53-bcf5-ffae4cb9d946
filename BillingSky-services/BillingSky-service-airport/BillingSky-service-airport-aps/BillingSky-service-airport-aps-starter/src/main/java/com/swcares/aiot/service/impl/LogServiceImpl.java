package com.swcares.aiot.service.impl;

import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.entity.ExcelData;
import com.swcares.aiot.core.common.util.CompareObjectUtils;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.common.util.FileUtils;
import com.swcares.aiot.dao.LogDao;
import com.swcares.aiot.core.model.entity.Log;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.LogService;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/*
 *
 * ClassName：LogServiceImpl <br>
 * Description：<br>
 * Copyright © 2020-10-12 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020-12-16 11:13<br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class LogServiceImpl implements LogService {
    @Resource
    private LogDao logDao;

    @Override
    public void addLogForImport(LoginUserDetails user, String content, String airportCode) {
        Log log = new Log();
        log.setOperatorName(user.getUsername());
        log.setOperatorId(String.valueOf(user.getId()));
        log.setOperatorContent(content);
        log.setCreateTime(new Date());
        log.setAirportCode(airportCode);
        logDao.save(log);
    }

    @Override
    public void addLogForExport(LoginUserDetails user, Object object, String content, String airportCode,String formName) {
        Class clazz = object.getClass();
        Field[] field = clazz.getDeclaredFields();
        try {
            for (int i = 0;i < field.length;i ++){
                field[i].setAccessible(true);
                if (field[i].get(object)!=null){
                    if (field[i].isAnnotationPresent(ApiModelProperty.class)){
                        content += field[i].getAnnotation(ApiModelProperty.class).value()+":"+field[i].get(object)+";";
                    }else {
                        content += field[i].getName()+":"+field[i].get(object)+";";
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.error("deal ApiModelProperty fail ! clazz = {}", clazz, e);
        }
        Log log = new Log();
        log.setOperatorName(user.getUsername());
        log.setOperatorId(String.valueOf(user.getId()));
        content +=Constants.LogEnum.LOG_MEG_EXPORT_1.getValue()+formName+Constants.LogEnum.LOG_MEG_EXPORT_2.getValue();
        log.setOperatorContent(content);
        log.setCreateTime(new Date());
        log.setAirportCode(airportCode);
        logDao.save(log);
    }

    @Override
    public void exportLog(String airportCode, String operatorId, Date startTime, Date endTime, HttpServletResponse response) {
        //根据条件从数据库中获取数据
        List<Log> resultList = logDao.listLogByCondition(airportCode,operatorId,startTime,endTime);
        ExcelData excelData = new ExcelData();
        //表名
        if (startTime!=null && endTime!=null){
            excelData.setFileName(DateUtils.format(startTime)+
                    "-"+
                    DateUtils.format(endTime)+
                    Constants.LogEnum.FILE_NAME_LOG.getValue());
        }else {
            excelData.setFileName(Constants.LogEnum.FILE_NAME_LOG.getValue());
        }

        //表头
        String[] head = {
                Constants.LogEnum.OPERATOR_ID.getValue(),
                Constants.LogEnum.OPERATOR_NAME.getValue(),
                Constants.LogEnum.OPERATOR_TIME.getValue(),
                Constants.LogEnum.OPERATOR_CONTENT.getValue(),
        };
        excelData.setHead(head);
        //给excel赋值
        List<String[]> data = new ArrayList<>();
        for (Log log : resultList) {
            String[] logs = {
                    log.getOperatorId(),
                    log.getOperatorName(),
                    String.valueOf(log.getCreateTime()),
                    log.getOperatorContent()
            };
            data.add(logs);
        }
        excelData.setData(data);
        FileUtils.exportExcel(response,excelData);
    }

    @Override
    public ResultBuilder pageLogByCondition(String airportCode, String operatorId, Date startTime,Date endTime, PageParam pageParam) {
        Pageable pageable = PageRequest.of(pageParam.getPage()-1,pageParam.getLimit());
        endTime = DateUtils.addDateDays(endTime,1);
        return new ResultBuilder.Builder().data(logDao.pageLogByCondition(airportCode,operatorId, startTime,endTime,pageable)).builder();
    }

    @Override
    public void addLogForSave(Object object, LoginUserDetails user, String content, String airportCode) {
        //通过反射获取类信息
        Class <?> clazz = object.getClass();
        //获取列信息
        Field[] field = clazz.getDeclaredFields();
        content += "{";
        try {
            //遍历列属性
            for (int i = 0;i<field.length;i++){
                //设置允许读取类的信息
                field[i].setAccessible(true);
                if (field[i].get(object)!=null){
                    //判断类中字段上是否有ApiModelProperty注解,如果有则将列名设置为注解中的value值
                    if (field[i].isAnnotationPresent(ApiModelProperty.class)){
                        content += field[i].getAnnotation(ApiModelProperty.class).value()+":"+field[i].get(object)+";";
                    }else {
                        content += field[i].getName()+":"+field[i].get(object)+";";
                    }
                }

            }
        }catch (IllegalAccessException e){
            log.error("deal ApiModelProperty fail ! clazz = {}", clazz, e);
        }
        content += "}";
        Log log = new Log();
        log.setOperatorContent(content);
        log.setOperatorId(String.valueOf(user.getId()));
        log.setOperatorName(user.getUsername());
        log.setCreateTime(new Date());
        log.setAirportCode(airportCode);
        logDao.save(log);
    }
    @Override
    public void  addLogForUpdate(Object object1, Object object2, LoginUserDetails user,String content,String airportCode){
        content += "{";
        //对比两个类的值是否相等
        List<Map<String, Object>> list = CompareObjectUtils.compareTwoClass(object1,object2);
        Log log = new Log();
        log.setOperatorName(user.getUsername());
        log.setOperatorId(String.valueOf(user.getId()));
        log.setCreateTime(new Date());

        for(Map<String, Object> map : list){
            if (map.get("old") == null){
                map.put("old","无");
            }else {
                content += map.get("name") + ":"+map.get("new")+"(更新，由:" + map.get("old") + ",变更为:" + map.get("new") + "）;";
            }
        }
        content += "}";
        log.setOperatorContent(content) ;
        log.setAirportCode(airportCode);
        logDao.save(log);
    }

    @Override
    public void addLogForDelete(Object object, LoginUserDetails user, String content,String airportCode) {
        content +="{";
        Class<?> clazz = object.getClass();
        Field[] field = clazz.getDeclaredFields();
        try {
            for (int i = 0;i<field.length;i++){
                field[i].setAccessible(true);
                //过滤值为null
                if (field[i].get(object)!=null){
                    if (field[i].isAnnotationPresent(ApiModelProperty.class)){
                        content += field[i].getAnnotation(ApiModelProperty.class).value()+":"+field[i].get(object)+";";
                    }else {
                        content += field[i].getName()+":"+field[i].get(object)+";";
                    }
                }

            }
        }catch (IllegalAccessException e){
            log.error("处理注解ApiModelProperty失败，请检查！ clazz = {}", clazz, e);
        }
        content +="}";
        Log log = new Log();
        log.setCreateTime(new Date());
        log.setOperatorId(String.valueOf(user.getId()));
        log.setOperatorName(user.getUsername());
        log.setOperatorContent(content);
        log.setAirportCode(airportCode);
        logDao.save(log);
    }
}
