package com.swcares.aiot.controller;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.util.AuthenticationUtils;
import com.swcares.aiot.core.form.*;
import com.swcares.aiot.core.model.entity.SettlementStatus;
import com.swcares.aiot.core.model.vo.FlightLineInfoVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.service.FlightLineInfoService;
import com.swcares.aiot.service.SettlementStatusService;
import com.swcares.aiot.service.SubsidyFlightService;
import com.swcares.aiot.service.SubsidySettlementService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.controller.SubsidyController
 * Description：航线补贴Controller
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/2 10:02
 * @version v1.0
 */
@RestController
@ResponseBody
@RequestMapping(value = "/api/subsidy")
@Api(value = "SubsidyController", tags = {"航线补贴接口"})
@Slf4j
public class SubsidyController {
    @Resource
    private SubsidyFlightService subsidyFlightService;
    @Resource
    private FlightLineInfoService flightLineInfoService;
    @Resource
    private SubsidySettlementService subsidySettlementService;
    @Resource
    private SettlementStatusService settlementStatusService;

    @ApiOperation(value = "分页查询执飞航班汇总数据 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/pageFlightLineData")
    public ResultBuilder<Object[]> pageFlightLineData(SubsidyFlightForm form, @Validated PageParam pageParam) {

        Object[] resObj = subsidyFlightService.pageFlightLineData(form, pageParam);
        return new ResultBuilder.Builder<Object[]>().data(resObj).builder();
    }

    @ApiOperation(value = "获取执飞航班详情数据 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/listFlightDetailData")
    public ResultBuilder<Object[]> listFlightDetailData(SubsidyFlightDetailDataForm form) {

        Object[] resObj = subsidyFlightService.pageFlightLineDetailData(form);
        return new ResultBuilder.Builder<Object[]>().data(resObj).builder();
    }

    @ApiOperation(value = "分页查询客票收入和燃油附加费页面 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/pagePassengerTicket")
    public ResultBuilder<Object[]> pagePassengerTicket(SubsidyFlightForm form, @Validated PageParam pageParam) {
        Object[] resObj = subsidyFlightService.pagePassengerTicket(form, pageParam);
        return new ResultBuilder.Builder<Object[]>().data(resObj).builder();
    }

    @ApiOperation(value = "分页查询客票收入和燃油附加费页面总数统计 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/countPagePassengerTicket")
    public ResultBuilder<Object[]> countPagePassengerTicket(SubsidyFlightForm form) {
        Object[] resObj = subsidyFlightService.countPagePassengerTicket(form);
        return new ResultBuilder.Builder<Object[]>().data(resObj).builder();
    }

    @ApiOperation(value = "获取航线列表 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/getFlightLineInfoList")
    public ResultBuilder<List<FlightLineInfoVo>> getFlightLineInfoList(String airlineCode, String airportCode, String flightLine) {
        List<FlightLineInfoVo> list = flightLineInfoService.getFlightLineInfoList(airlineCode, airportCode, flightLine);
        return new ResultBuilder.Builder<List<FlightLineInfoVo>>().data(list).builder();
    }

    @ApiOperation(value = "保存航线 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/saveFlightLineInfo")
    public ResultBuilder<Object> saveFlightLineInfo(@RequestBody FlightLineInfoForm form) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightLineInfoService.saveFlightLineInfo(form, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "删除航线 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @DeleteMapping("/deleteFlightLine")
    public ResultBuilder<Object> deleteFlightLine(String id) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        flightLineInfoService.deleteFlightLine(id, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "更新轮挡时间 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/updateBlockTime")
    public ResultBuilder<Object> updateBlockTime(@RequestBody SubsidyUpdateBlockTimeForm form) {
        subsidyFlightService.updateFlightParam(form.getFlightId(), "轮挡时间", form.getValue(), form.getAirportCode());
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "新增客票收入 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/savePassengerTicket")
    public ResultBuilder<Object> savePassengerTicket(@RequestBody SubsidySavePassengerTicketForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFlightService.savePassengerTicket(form, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "修改客票收入 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/updatePassengerTicket")
    public ResultBuilder<Object> updatePassengerTicket(@RequestBody SubsidyUpdatePassengerTicketForm form) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFlightService.updatePassengerTicket(form, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "删除客票收入数据 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @DeleteMapping("/deletePassengerTicket")
    public ResultBuilder<Object> deletePassengerTicket(String id) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFlightService.deletePassengerTicket(id, user);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "结算航线补贴账单 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @PostMapping("/settlement")
    public ResultBuilder<Object> settlement(@RequestBody SubsidyFlightForm form) {

        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        SettlementStatus ss = settlementStatusService.subsidySettlement(form, user);
        try {
            subsidySettlementService.settlement(form, user);
        } catch (Exception e) {
            settlementStatusService.subsidySettlementFail(ss);
            throw e;
        }
        settlementStatusService.subsidySettlementSuccess(ss);
        return new ResultBuilder.Builder<>().builder();
    }

    @ApiOperation(value = "判断当前条件下账单是否为空 ")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ResultBuilder.class)})
    @GetMapping("/checkBill")
    public ResultBuilder<Boolean> checkBill(SubsidyFlightForm form) {
        Boolean res = subsidySettlementService.check(form);
        return new ResultBuilder.Builder<Boolean>().data(res).builder();
    }


    @GetMapping("/exportPassengerTicketStencil")
    @ApiOperation(value = "客票收入模版导出")
    public void exportPassengerTicketStencil(HttpServletResponse res) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFlightService.exportPassengerTicketStencil(res, user);
    }

    @PostMapping("/importPassengerTicket")
    @ApiOperation(value = "客票收入导入")
    public ResultBuilder<Object> importPassengerTicket(@RequestBody @ApiParam(name = "file", value = "导入航班实际起降时间的excel文件") MultipartFile file,
                                                       @RequestParam String airportCode, HttpServletRequest request) {
        LoginUserDetails user = AuthenticationUtils.getCurrentUser();
        subsidyFlightService.importPassengerTicket(file, airportCode, user, request);
        return new ResultBuilder.Builder<>().builder();
    }

    @GetMapping("/exportPassengerTicket")
    @ApiOperation(value = "客票燃油数据导出")
    public void exportPassengerTicket(HttpServletResponse res, SubsidyFlightForm form) {
        subsidyFlightService.exportPassengerTicket(res, form);
    }

    @GetMapping("/exportFlightLineDetailData")
    @ApiOperation(value = "执飞明细导出")
    public void exportFlightLineDetailData(HttpServletResponse res, SubsidyFlightForm form) {
        subsidyFlightService.exportFlightLineDetailData(form, res);
    }

}
