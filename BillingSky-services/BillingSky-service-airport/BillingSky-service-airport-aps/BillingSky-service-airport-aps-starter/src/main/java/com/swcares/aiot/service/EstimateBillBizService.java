package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.form.EstimateBillConfirmForm;
import com.swcares.aiot.core.form.EstimateBillPageForm;
import com.swcares.aiot.core.model.dto.HostAviationDownloadDto;
import com.swcares.aiot.core.model.dto.RaiseAnObjectionDto;
import com.swcares.aiot.vo.EstimateBillPageVo;
import com.swcares.aiot.vo.EstimateBillProveRecordVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ClassName：com.swcares.service.EstimateBillBizService
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 20234/7/16
 * @version v1.0
 */
public interface EstimateBillBizService {


    IPage<EstimateBillPageVo> page(EstimateBillPageForm form);

    void confirm(EstimateBillConfirmForm form);

    /**
     * Title：getEstimateBillProveList
     * Description：根据账单id查询账单存证列表
     * author：李军呈
     * date： 2024/8/13 19:05
     * @param estimateBillId :
     * @return java.util.List<com.swcares.aiot.core.entity.EstimateBillProve>
     */
    List<EstimateBillProveRecordVo> getEstimateBillProveList(String estimateBillId);

    /**
     * Title：hostAviationFileDownload
     * Description：HOST航账单文件下载
     * author：李军呈
     * date： 2024/8/30 21:04
     * @param dto :
     * @param response :
     */
    void hostAviationFileDownload(HostAviationDownloadDto dto, HttpServletResponse response) throws IOException;

    void raiseAnObjection(RaiseAnObjectionDto dto);

    /**
     * Title：hostAviationFileDownload
     * Description：HOST航账单文件预览
     * author：李军呈
     * date： 2024/8/30 21:04
     * @param dto :
     */
    Object hostAviationFilePreview(HostAviationDownloadDto dto);
}
