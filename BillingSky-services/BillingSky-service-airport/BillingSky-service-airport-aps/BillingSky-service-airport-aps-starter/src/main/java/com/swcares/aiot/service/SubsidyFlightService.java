package com.swcares.aiot.service;

import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.aiot.core.form.SubsidyFlightDetailDataForm;
import com.swcares.aiot.core.form.SubsidyFlightForm;
import com.swcares.aiot.core.form.SubsidySavePassengerTicketForm;
import com.swcares.aiot.core.form.SubsidyUpdatePassengerTicketForm;
import com.swcares.aiot.core.param.PageParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ClassName：com.swcares.service.SubsidyFlightService
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/15 9:49
 * @version v1.0
 */
public interface SubsidyFlightService {

    Object[] pageFlightLineData(SubsidyFlightForm form, PageParam pageParam);

    Object[] pageFlightLineDetailData(SubsidyFlightDetailDataForm form);
    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: 修改航班参数值<br>
     * Date:  2022/8/17 14:18 <br>
     *
     * @param flightId : null
     */
    boolean updateFlightParam(String flightId, String paramName, String value, String airportCode);

    Object[] pagePassengerTicket(SubsidyFlightForm form, PageParam pageParam);

    Object[] countPagePassengerTicket(SubsidyFlightForm form);

    boolean savePassengerTicket(SubsidySavePassengerTicketForm form, LoginUserDetails user);

    boolean updatePassengerTicket(SubsidyUpdatePassengerTicketForm form, LoginUserDetails user);

    boolean deletePassengerTicket(String flightIds, LoginUserDetails user);

    void exportPassengerTicketStencil(HttpServletResponse res, LoginUserDetails user);

    boolean importPassengerTicket(MultipartFile file, String airportCode, LoginUserDetails user,
            HttpServletRequest request);

    void exportPassengerTicket(HttpServletResponse res,SubsidyFlightForm form);

    void exportFlightLineDetailData(SubsidyFlightForm form,HttpServletResponse res);
}
