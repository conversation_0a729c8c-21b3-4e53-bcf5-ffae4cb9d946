package com.swcares.aiot.core.importer.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.rabbitmq.client.Channel;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.util.RedisLockUtils;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.model.dto.OldApsAirportDto;
import com.swcares.aiot.core.model.entity.SignDelete;
import com.swcares.aiot.core.model.vo.BusDataSendVo;
import com.swcares.aiot.core.model.vo.BusItemDeleteSendVo;
import com.swcares.aiot.core.model.vo.BusItemSendVo;
import com.swcares.aiot.core.vo.SettlementItemVo;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;
import com.swcares.aiot.service.MqSignService;
import com.swcares.aiot.service.NewMqSignService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.tenant.TenantContextHolder;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * ClassName：com.swcares.service.impl.MqTestService
 * Description：电子签单对接mq
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/6/13 13:56
 * @version v1.0
 */
@Slf4j
@Service
public class MqSignListener {
    public static final String DIRECT_EXCHANGE = "sign.settlement.exchange";

    /**
     * Description :  飞机保障业务路由及队列名
     */
    public static final String ROUTING_KEY_SYNC_FULL = "sync.full";
    public static final String QUEUE_NAME_SYNC_FULL = "aps.sync.full";


    public static final String ROUTING_KEY_SYNC_INCREMENTAL = "sync.incremental";
    public static final String QUEUE_NAME_SYNC_INCREMENTAL = "aps.sync.incremental";


    /**
     * Description :  签单模版项 路由 及 路由
     */
    public static final String ROUTING_KEY_ITEM_UPDATE = "item.update";
    public static final String QUEUE_NAME_ITEM_UPDATE = "aps.item.update";


    public static final String ROUTING_KEY_ITEM_INSERT = "item.insert";
    public static final String QUEUE_NAME_KEY_ITEM_INSERT = "aps.item.insert";

    public static final String ROUTING_KEY_ITEM_DELETE = "item.delete";
    public static final String QUEUE_NAME_KEY_ITEM_DELETE = "aps.item.delete";

    /**
     * Description : 车辆签单业务 mq 路由
     */
    public static final String ROUTING_KEY_SYNC_BUS_FULL = "sync.bus.full";
    public static final String QUEUE_NAME_SYNC_BUS_FULL = "aps.sync.bus.full";

    public static final String ROUTING_KEY_BUS_ITEM_FULL = "bus.item";
    public static final String QUEUE_NAME_KEY_BUS_ITEM_FULL = "aps.bus.item";

    public static final String ROUTING_KEY_BUS_ITEM_DELETE = "bus.item.delete";
    public static final String QUEUE_NAME_KEY_BUS_ITEM_DELETE = "aps.bus.item.delete";

    @Resource
    private MqSignService mqSignService;
    @Resource
    private RedisLockUtils redisLockUtils;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IConfConfigBizClient iConfConfigBizClient;
    @Resource
    private NewMqSignService newMqSignService;


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_SYNC_FULL, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_SYNC_FULL)
    )
    public void syncFull(Message message, Channel channel) throws IOException {
        String messageStr = "";
        try {
            messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接收到队列({})的信息 : {}", QUEUE_NAME_SYNC_FULL, messageStr);
            List<SignBusDataSettlementVO> signBusDataSettleVoList = JSON.parseArray(messageStr, SignBusDataSettlementVO.class);
            if (ObjectUtils.isEmpty(signBusDataSettleVoList)) {
                log.error("队列（{}）出现空消息对象，请检查, messageStr = {}", QUEUE_NAME_SYNC_FULL, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = signBusDataSettleVoList.get(0).getTenantId();
            // 获取机场三字码
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                log.error("队列({})的信息没有机场三字码 : {}", QUEUE_NAME_SYNC_FULL, messageStr);
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            // 将获取到的机场三字码放入对象中
            signBusDataSettleVoList.stream().forEach(item -> item.setAirportCode(airportCode));
            String lockKey = "aps_sign_service" + signBusDataSettleVoList.get(0).getId().toString();
            TenantHolder.setTenant(tenantId);
            try {
                RLock lock = redissonClient.getLock(lockKey);
                lock.lock(300L, TimeUnit.SECONDS);
                if (isOldAps(airportCode)) {
                    mqSignService.saveServiceRecord(signBusDataSettleVoList);
                }else {
                    newMqSignService.saveServiceRecord(signBusDataSettleVoList);
                }
            } finally {
                redisLockUtils.unlock(lockKey);
            }
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_SYNC_FULL, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_SYNC_FULL, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantHolder.clear();
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_SYNC_INCREMENTAL, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, type = ExchangeTypes.DIRECT, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_SYNC_INCREMENTAL)
    )
    public void syncIncremental(Message message, Channel channel) throws IOException {
        String messageStr = "";
        try {
            messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接收到队列({})的信息 : {}", QUEUE_NAME_SYNC_INCREMENTAL, messageStr);
            List<SignBusDataSettlementVO> vo =
                    JSON.parseArray(messageStr, SignBusDataSettlementVO.class);
            if (ObjectUtils.isEmpty(vo)) {
                log.error("队列（{}）出现空消息对象，请检查, messageStr = {}", QUEUE_NAME_SYNC_INCREMENTAL, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = vo.get(0).getTenantId();
            // 获取机场三字码
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            // 将获取到的机场三字码放入对象中
            vo.forEach(item -> item.setAirportCode(airportCode));

            TenantHolder.setTenant(tenantId);
            String lockKey = "aps_sign_service" + vo.get(0).getId().toString();
            RLock lock = redissonClient.getLock(lockKey);
            lock.lock(300L, TimeUnit.SECONDS);
            try {
                if (isOldAps(airportCode)) {
                    mqSignService.saveServiceRecord(vo);
                }else {
                    newMqSignService.saveServiceRecord(vo);
                }
            } finally {
                redisLockUtils.unlock(lockKey);
            }
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_SYNC_INCREMENTAL, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_SYNC_INCREMENTAL, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantHolder.clear();
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_ITEM_UPDATE, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, type = ExchangeTypes.DIRECT, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_ITEM_UPDATE)
    )
    public void itemUpdate(Message message, Channel channel) throws IOException {
        String messageStr = "";
        try {
            messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            SettlementItemVo ssiv = JSONObject.parseObject(messageStr, SettlementItemVo.class);
            log.info("接收到队列({})的信息 : {}", QUEUE_NAME_ITEM_UPDATE, messageStr);
            if (ObjectUtils.isEmpty(ssiv) || ObjectUtils.isEmpty(ssiv.getTenantId())) {
                log.error("处理队列（{}）数据出现异常，对象或租户id未空，messageStr = {}", QUEUE_NAME_ITEM_UPDATE, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = ssiv.getTenantId();
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            ssiv.setAirportCode(airportCode);
            // 指定租户库
            TenantContextHolder.setTenant(tenantId);
            mqSignService.itemUpdate(ssiv);
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_ITEM_UPDATE, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_ITEM_UPDATE, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantContextHolder.clear();
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_KEY_ITEM_INSERT, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, type = ExchangeTypes.DIRECT, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_ITEM_INSERT)
    )
    public void itemInsert(Message message, Channel channel) throws IOException {
        String messageStr = "";
        try {
            messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("消费者接收到{}队列信息 : {}", QUEUE_NAME_KEY_ITEM_INSERT, messageStr);
            SettlementItemVo ssi = JSONObject.parseObject(messageStr, SettlementItemVo.class);
            if (ObjectUtils.isEmpty(ssi) || ObjectUtils.isEmpty(ssi.getTenantId())) {
                log.error("处理队列（{}）数据出现异常，对象或租户id未空，messageStr = {}", QUEUE_NAME_KEY_ITEM_INSERT, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = ssi.getTenantId();
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            ssi.setAirportCode(airportCode);

            TenantContextHolder.setTenant(tenantId);
            mqSignService.itemInsert(ssi);
            log.info("消费者接收到队列{}并插入信息成功 : {} ", QUEUE_NAME_KEY_ITEM_INSERT, messageStr);
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_KEY_ITEM_INSERT, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_KEY_ITEM_INSERT, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantContextHolder.clear();
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_KEY_ITEM_DELETE, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, type = ExchangeTypes.DIRECT, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_ITEM_DELETE)
    )
    public void itemDelete(Message message, Channel channel) throws IOException {
        String messageStr = "";
        try {
            messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("消费者接收到{}队列信息 : {}", QUEUE_NAME_KEY_ITEM_DELETE, messageStr);
            SignDelete sd = JSONObject.parseObject(messageStr, SignDelete.class);
            if (ObjectUtils.isEmpty(sd) || ObjectUtils.isEmpty(sd.getTenantId())) {
                log.error("处理队列（{}）数据出现异常，对象或租户id未空，messageStr = {}", QUEUE_NAME_KEY_ITEM_DELETE, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = sd.getTenantId();
            // 获取机场三字码
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            sd.setAirportCode(airportCode);
            TenantContextHolder.setTenant(tenantId);
            mqSignService.itemDelete(sd);
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_KEY_ITEM_DELETE, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_KEY_ITEM_DELETE, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantContextHolder.clear();
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_SYNC_BUS_FULL, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, type = ExchangeTypes.DIRECT, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_SYNC_BUS_FULL)
    )
    public void syncBusFull(Message message, Channel channel) throws IOException {
        String messageStr = "";
        messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("消费者接收到{}队列信息 : {}", QUEUE_NAME_SYNC_BUS_FULL, messageStr);
        BusDataSendVo vo = JSON.parseObject(messageStr, BusDataSendVo.class);
        String lockKey = "aps_bus_" + vo.getId().toString();
        try {
            if (ObjectUtils.isEmpty(vo)) {
                log.error("队列（{}）出现空消息对象，请检查, messageStr = {}", QUEUE_NAME_SYNC_BUS_FULL, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = vo.getTenantId();
            // 获取机场三字码
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            vo.setAirportCode(airportCode);
            // 切换数据库 并 保存数据
            TenantHolder.setTenant(tenantId);
            RLock lock = redissonClient.getLock(lockKey);
            lock.lock(30L, TimeUnit.SECONDS);
            mqSignService.saveBusData(vo);
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_SYNC_BUS_FULL, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_SYNC_BUS_FULL, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            redisLockUtils.unlock(lockKey);
            TenantContextHolder.clear();
        }
    }

    /**
     * Title：busItem <br>
     * Description: 保存签单业务数据 <br>
     *
     * @param message 消息信息
     * @param channel 消息通道
     * date 2024/6/14 <br>
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_KEY_BUS_ITEM_FULL, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, type = ExchangeTypes.DIRECT, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_BUS_ITEM_FULL)
    )
    public void busItem(Message message, Channel channel) throws IOException {
        String messageStr = "";
        try {
            messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("消费者接收到{}队列信息 : {}", QUEUE_NAME_KEY_BUS_ITEM_FULL, messageStr);
            BusItemSendVo itemSendVo = JSONObject.parseObject(messageStr, BusItemSendVo.class);
            if (ObjectUtils.isEmpty(itemSendVo)) {
                log.error("队列（{}）出现空消息对象，请检查, messageStr = {}", QUEUE_NAME_KEY_BUS_ITEM_FULL, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = itemSendVo.getTenantId();
            // 获取机场三字码
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            itemSendVo.setAirportCode(airportCode);

            // 保存数据 使用租户Id
            TenantContextHolder.setTenant(itemSendVo.getTenantId());
            mqSignService.saveBusItem(itemSendVo);
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_KEY_BUS_ITEM_FULL, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_KEY_BUS_ITEM_FULL, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantContextHolder.clear();
        }
    }


    /**
     * Title：deleteBusItem <br>
     * Description: 删除签单业务数据 <br>
     *
     * @param message 消息对象
     * @param channel 消息通道
     * date 2024/6/14 <br>
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = QUEUE_NAME_KEY_BUS_ITEM_DELETE, durable = "true"),
            exchange = @Exchange(name = DIRECT_EXCHANGE, ignoreDeclarationExceptions = "true"),
            key = ROUTING_KEY_BUS_ITEM_DELETE)
    )
    public void deleteBusItem(Message message, Channel channel) throws IOException {
        String messageStr = "";
        try {
            messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("消费者接收到{}队列信息 : {}", QUEUE_NAME_KEY_BUS_ITEM_DELETE, messageStr);
            BusItemDeleteSendVo itemSendVo = JSONObject.parseObject(messageStr, BusItemDeleteSendVo.class);
            if (ObjectUtils.isEmpty(itemSendVo) || ObjectUtils.isEmpty(itemSendVo.getTenantId())) {
                log.error("处理队列（{}）数据出现异常，对象或租户id未空，messageStr = {}", QUEUE_NAME_KEY_ITEM_DELETE, messageStr);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 根据租户ID 转换出 机场三字码 并 设置到对象中
            Long tenantId = itemSendVo.getTenantId();
            String airportCode = getAirportCodeByTenantId(tenantId);
            if (ObjectUtils.isEmpty(airportCode)) {
                // 放回队列
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            itemSendVo.setAirportCode(airportCode);
            TenantContextHolder.setTenant(tenantId);
            mqSignService.deleteBusItem(itemSendVo);
            // 告诉服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (GenericException ge) {
            log.error(" queue={} 处理失败！messageStr = {}, code = {}, exceptionMsg= {} ", QUEUE_NAME_KEY_BUS_ITEM_DELETE, messageStr, ge.getCode(), ge.getParams(), ge);
            // 丢弃消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } catch (Exception e) {
            log.error(" queue={} 消息处理失败！messageStr = {}", QUEUE_NAME_KEY_BUS_ITEM_DELETE, messageStr, e);
            // 丢弃这条消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            TenantContextHolder.clear();
        }
    }


    /**
     * Title：getAirportCodeByTenantId <br>
     * Description: 根据租户Id获取机场三字码 <br>
     *
     * @param tenantId 租户id
     * @return java.lang.String
     * <AUTHOR> <br>
     * date 2024/6/14 <br>
     */
    @Nullable
    private String getAirportCodeByTenantId(Long tenantId) {
        if (ObjectUtils.isEmpty(tenantId)) {
            log.error("发送钉钉消息,队列：{}，数据：{}, 无租户Id", QUEUE_NAME_KEY_BUS_ITEM_DELETE, tenantId);
            return null;
        }
        String airportCode = ConfigUtil.getString(CommonConstants.KEY_TYPE_BASE_CONFIG, CommonConstants.VALUE_KEY_AIRPORT_CODE, tenantId);
        if (ObjectUtils.isEmpty(airportCode)) {
            log.error("发送钉钉消息, aps 租户id={}未配置对应的机场三字码，请检查配置表！", tenantId);
            return null;
        }
        return airportCode;
    }

    private boolean isOldAps(String airportCode) {
        BaseResult<JSONObject> jsonObjectBaseResult = iConfConfigBizClient.getConfig("OLD_APS_AIRPORT");
        if (ObjectUtil.isEmpty(jsonObjectBaseResult) || ObjectUtil.isEmpty(jsonObjectBaseResult.getData())) {
            log.error("{}机场未配置签单数据来源 ！ jsonObjectBaseResult = {}", airportCode, jsonObjectBaseResult);
        }
        OldApsAirportDto oldApsAirportDto = JSONUtil.toBean(jsonObjectBaseResult.getData().toJSONString(), OldApsAirportDto.class);
        if(CollUtil.isNotEmpty(oldApsAirportDto.getAirportCode())){
            for(String airport:oldApsAirportDto.getAirportCode()){
                if(StringUtils.equals(airportCode,airport)){
                    return true;
                }
            }
        }
        return false;

    }
}
