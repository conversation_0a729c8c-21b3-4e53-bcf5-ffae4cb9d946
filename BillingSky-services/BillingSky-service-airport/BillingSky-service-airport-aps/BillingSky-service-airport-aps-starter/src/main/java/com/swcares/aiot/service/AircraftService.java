package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.entity.AircraftAirlineInfoVo;
import com.swcares.aiot.core.entity.AircraftInfoVo;
import com.swcares.aiot.core.form.AircraftForm;
import com.swcares.aiot.core.model.entity.AircraftInfo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * ClassName：AircraftService <br>
 * Description：飞机操作service层<br>
 * Copyright © 2020-6-15 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR>
 * date 2020-6-15 14:07<br>
 * @version v1.0 <br>
 */
public interface AircraftService {
    /**
     * Title: saveAirrcraft<br>
     * Author: 李龙<br>
     * Description: 新增飞机信息<br>
     * Date:  14:23 <br>
     *
     * @param aircraftForm :
     * @param user         :
     *                     return: ResultBuilder
     */
    ResultBuilder<Object> saveAircraft(AircraftForm aircraftForm, LoginUserDetails user);

    /**
     * Title: pageAircraftByAirportCode<br>
     * Author: 李龙<br>
     * Description: 根据航司二字码、航司简称两个条件，查询机场下的飞机信息，如果条件为空则查全部<br>
     * Date:  14:35 <br>
     *
     * @param airlineCode      :
     * @param airlineShortName :
     * @param pageParam        :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> pageAircraftByCondition(String airlineCode, String airlineShortName, String regNo, PageParam pageParam);

    ResultBuilder<Object> deleteAircraftByCondition(String airlineCode, String airlineShortName, String regNo, String ids,LoginUserDetails user);

    /**
     * Title: updateAircraft<br>
     * Author: 李龙<br>
     * Description: 修改飞机信息<br>
     * Date:  14:37 <br>
     *
     * @param aircraftForm :
     * @param user         :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> updateAircraft(AircraftForm aircraftForm, LoginUserDetails user);

    /**
     * Title: importAircraft<br>
     * Author: 李龙<br>
     * Description: 导入飞机信息<br>
     * Date:  17:00 <br>
     *
     * @param file :
     * @param user :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> importAircraft(MultipartFile file, String airportCode, Date settlementStartDate, Date settlementEndDate, LoginUserDetails user);

    /**
     * Title: delAirrcaftInfo<br>
     * Author: 李龙<br>
     * Description: 根据id删除飞机信息<br>
     * Date:  14:41 <br>
     *
     * @param user :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> delAircraftInfo(String aircraftIds, LoginUserDetails user);

    /**
     * Title: getAircraftInfoById<br>
     * Author: 李龙<br>
     * Description:根据飞机id飞机信息<br>
     * Date:  15:06 <br>
     *
     * @param aircraftId return: ResultBuilder
     */
    ResultBuilder<AircraftInfo> getAircraftInfoById(String aircraftId);

    /**
     * Title: doReSettlementByFlightDate<br>
     * Author: 李龙<br>
     * Description: (根据航班日期重新结算)<br>
     * Date:  14:38 <br>
     *
     * @param airportCode :
     * @param startDate   :
     * @param endDate     return: ResultBuilder
     */
    ResultBuilder<Object> doReSettlementByFlightDate(String airportCode, Date startDate, Date endDate, LoginUserDetails user);

    /**
     * Title: findByRegnoLike<br>
     * Author: 李亚洲<br>
     * Description: (模糊匹配飞机注册号)<br>
     * Date:  14:38 <br>
     *
     * @param regNo return: ResultBuilder
     */
    ResultBuilder<Object> findByRegnoLike(String regNo);

    /**
     * Title: findDistinctRegno<br>
     * Author: 刘志恒<br>
     * Description: 模糊查询去重机号<br>
     * Date:  14:38 <br>
     *
     * @param regNo return: ResultBuilder
     */
    ResultBuilder<Object> findDistinctRegno(String regNo);

    /**
     * Title: 查询近一个月航班对应机号，未存在机号表中的机号<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/1/10 14:44 <br>
     * return: ResultBuilder
     */
    ResultBuilder<Object> findInvalidRegon();

    /**
     * Title: 获取机型列表<br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  2022/9/13 11:38 <br>
     */
    ResultBuilder<Object> findAirplaneModelList(String airplaneModel);

    /**
     * Title: listAirlineInfoByRegno
     * Description : 通过飞机号查询飞机信息表中的航班信息
     * @param regNo ：
     * @param effectiveDate ：
     * Returns : List<AircraftAirlineInfoVo>
     * Author : liuzhiheng
     * Date : 2024-09-02 11:13:59
     */
    List<AircraftAirlineInfoVo> listAirlineInfoByRegno(String regNo, LocalDate effectiveDate);

    /**
     * Title:listAirlineInfoByAirlineShortName
     * Description : 通过航司简称模糊查询飞机信息表中的航班信息
     * @param airlineShortName
     * Returns : List<AircraftAirlineInfoVo>
     * Author : liuzhiheng
     * Date : 2024-09-02 11:14:22
     */
    List<AircraftAirlineInfoVo> listAirlineInfoByAirlineShortName(String airlineShortName);


    List<AircraftInfoVo> getAirraftInfoByUpdateTime(LocalDateTime updateTime);
}
