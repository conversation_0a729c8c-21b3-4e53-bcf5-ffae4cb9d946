package com.swcares.aiot.core.importer.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.baseframe.common.enums.DeletedEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.base.flight.api.entity.BaseFlightInfo <br>
 * Description：航班信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-07-08 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BaseFlightInfo", description="航班信息表")
public class BaseFlightInfo extends BpBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "acdm_id")
    private Long acdmId;

    @ApiModelProperty(value = "航空公司两字码")
    private String airlineCode;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private LocalDate flightDate;

    @ApiModelProperty(value = "前序航班号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String preorderFlightNo;

    @ApiModelProperty(value = "航班日期")
    private LocalDateTime flightDateTime;

    @ApiModelProperty(value = "出发机场三字码")
    private String departureAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String destinationAirportCode;

    @ApiModelProperty(value = "航段")
    private String flightSegment;

    @ApiModelProperty(value = "航段属性（D:国内   I：国际  R: 地区）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String flightSegmentProperty;

    @ApiModelProperty(value = "航线;")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String airline;

    @ApiModelProperty(value = "航线属性（D:国内   I：国际  R: 地区）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String airlineProperty;

    @ApiModelProperty(value = "任务标识;")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String task;

    @ApiModelProperty(value = "航班状态")
    private String status;

    @ApiModelProperty(value = "延误时间长度")
    private Integer lengthOfDelay;

    @ApiModelProperty(value = "进出港标识;1：进港  0：出港")
    private String isArrv;

    @ApiModelProperty(value = "关联航班ID;")
    private Long connectFlightId;

    @ApiModelProperty(value = "机号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String aircraftNo;

    @ApiModelProperty(value = "机型")
    private String aircraftModel;

    @ApiModelProperty(value = "机位")
    private String aircraftParkingPosition;

    @ApiModelProperty(value = "登机口")
    private String gate;

    @ApiModelProperty(value = "值机柜台")
    private String checkInCounter;

    @ApiModelProperty(value = "行李转盘序号")
    private String baggageCarouselSerialNumber;

    @ApiModelProperty(value = "计划到达时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime planLandingDatetime;

    @ApiModelProperty(value = "预计到达时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime predictLandingDatetime;

    @ApiModelProperty(value = "实际到达时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime realLandingDatetime;

    @ApiModelProperty(value = "计划起飞时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime planTakeOffDatetime;

    @ApiModelProperty(value = "预计起飞时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime predictTakeOffDatetime;

    @ApiModelProperty(value = "实际起飞时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime realTakeOffDatetime;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "航班异常状态码  can 取消  alt 备降  rtn 返航 dly 延误")
    private String abnStatus;

    @ApiModelProperty(value = "航班异常原因")
    private String abnrsn;

    @ApiModelProperty(value = "航班计划状态 publish 已发布  uncfnfirm  未确认  confirm人工确认  cancel 接口取消 auto_cfrm 自动确认")
    private String flightStateCode;

    @ApiModelProperty(value = "航班状态 plan计划 dyn 动态  history 历史")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String stageCode;

    @ApiModelProperty(value = "共享航班号,多个以逗号分隔")
    private String shareFlight;

    @ApiModelProperty(value = "航班发布状态 LBD 催促登机 POK 登机截止 DEP 起飞 TBR 过站登机  BOR 本站登机  CKO 值机截止  CKI 值机开始 ARR 到达 ONR 前方起飞")
    private String publishStatus;

    @ApiModelProperty(value = "acdm删除标识(1：删除 0：正常)")
    private DeletedEnum acdmDeleted;

    @ApiModelProperty(value = "同步更新时间")
    private LocalDateTime syncUpdatedTime;

    @ApiModelProperty(value = "停场开始时间")
    private LocalDateTime stayStartTime;

    @ApiModelProperty(value = "停场结束时间")
    private LocalDateTime stayEndTime;

    @ApiModelProperty(value = "停场时间")
    private BigDecimal stayTime;

}
