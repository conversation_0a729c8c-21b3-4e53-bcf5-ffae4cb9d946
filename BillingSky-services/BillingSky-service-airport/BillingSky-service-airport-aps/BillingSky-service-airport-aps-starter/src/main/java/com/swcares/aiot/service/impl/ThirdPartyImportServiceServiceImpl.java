package com.swcares.aiot.service.impl;

import com.swcares.aiot.TenantConvertUtil;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.common.util.DateUtils;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.model.dto.*;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.model.vo.ThirdPartyServiceVo;
import com.swcares.aiot.dao.*;
import com.swcares.aiot.service.ThirdPartyImportServiceService;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.ThirdPartyImportServiceServiceImpl
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/23 09:50
 * @version v1.0
 */
@Slf4j
@Service
public class ThirdPartyImportServiceServiceImpl implements ThirdPartyImportServiceService {


    public static final String IMPORT_VARIANBLE_BY_THIRD_PARTY = "第三方同步导入";
    public static final String IMPORT_BY_THIRD_PARTY = "第三方导入业务保障数据";
    public static final String DOUBLE_BRIDGE = "double_bridge";
    public static final String THREE_BRIDGE = "three_bridge";
    public static final String CONVERSION_RULES_0 = "0";
    public static final String CONVERSION_RULES_1 = "1";
    public static final String CONVERSION_RULES_2 = "2";
    public static final String SIGN_IMPORT_BY_AUTO = "电子签单自动导入-sign";
    public static final String SIGN_IMPORT_BY_MANUAL = "电子签单手动导入-sign";
    public static final String NOT_IN_SCOPE = "0";
    public static final String IN_SCOPE = "1";

    @Resource
    private VariableRecordDao variableRecordDao;
    @Resource
    private VariableGuaranteeDao variableGuaranteeDao;
    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private ServiceRecordDao serviceRecordDao;
    @Resource
    private ServiceRecordConfirmDao serviceRecordConfirmDao;


    @Override
    public List<ThirdPartyServiceVo> getAllServiceInfo(String airportCode) {
        Long tenantId = TenantConvertUtil.getTenantId(airportCode);
        if (tenantId == null) {
            throw new BusinessException(ExceptionCodes.THIRD_PARTY_IMPORT_AIRPORT_ERROR);
        }
        List<RulesVariableRecord> serviceNameAndCodeList = variableRecordDao.listDeviceVariableRecord();
        return setserviceNameAndCodeList(serviceNameAndCodeList);
    }

    private List<ThirdPartyServiceVo> setserviceNameAndCodeList(List<RulesVariableRecord> serviceNameAndCodeList){
        List<ThirdPartyServiceVo> thirdPartyServiceVoList = new ArrayList<>();
        for (RulesVariableRecord variableRecord : serviceNameAndCodeList) {
            ThirdPartyServiceVo thirdPartyServiceVo = new ThirdPartyServiceVo();
            thirdPartyServiceVo.setServiceCode(variableRecord.getVariable());
            thirdPartyServiceVo.setServiceName(variableRecord.getVariableName());
            thirdPartyServiceVo.setUnit(variableRecord.getVariableUnit());
            thirdPartyServiceVoList.add(thirdPartyServiceVo);
        }
        return thirdPartyServiceVoList;
    }

    @Override
    public Boolean saveRela(ThirdPartyRelaDto thirdPartyRelaDto) {
        String airportCode = thirdPartyRelaDto.getAirportCode();
        Long tenantId = TenantConvertUtil.getTenantId(airportCode);
        if (tenantId == null) {
            throw new BusinessException(ExceptionCodes.THIRD_PARTY_IMPORT_AIRPORT_ERROR);
        }
        String apsServiceCode = thirdPartyRelaDto.getApsServiceCode();
        RulesVariableRecord variableRecord = variableRecordDao.getServiceCodeByVariable(apsServiceCode, airportCode);
        if (variableRecord == null) {
            throw new BusinessException(ExceptionCodes.THIRD_PARTY_IMPORT_SERVICE_CODE_ERROR);
        } else if (!apsServiceCode.startsWith("C")) {
            throw new BusinessException(ExceptionCodes.THIRD_PARTY_IMPORT_NOT_SERVICE_CODE_ERROR);
        }
        VariableGuarantee variableGuarantee = variableGuaranteeDao.getVariableGuaranteeByApsServiceCode(thirdPartyRelaDto.getApsServiceCode());
        if (variableGuarantee == null) {
            variableGuarantee = new VariableGuarantee();
            variableGuarantee.setType("2");
            variableGuarantee.setCreateBy(IMPORT_VARIANBLE_BY_THIRD_PARTY);
            variableGuarantee.setCreateTime(new Date());
        }
        BeanUtils.copyProperties(thirdPartyRelaDto, variableGuarantee);
        variableGuarantee.setVariableId(variableRecord.getId());
        variableGuarantee.setModifiedBy(IMPORT_VARIANBLE_BY_THIRD_PARTY);
        variableGuarantee.setModifiedTime(new Date());
        variableGuaranteeDao.save(variableGuarantee);
        return true;
    }

    @Override
    public Boolean deleteRela(ThirdPartyServiceDeleteDto thirdPartyServiceDeleteDto) {
        String airportCode = thirdPartyServiceDeleteDto.getAirportCode();
        Long tenantId = TenantConvertUtil.getTenantId(airportCode);
        if (tenantId == null) {
            throw new BusinessException(ExceptionCodes.THIRD_PARTY_IMPORT_AIRPORT_ERROR);
        }
        VariableGuarantee variableGuarantee = variableGuaranteeDao.getVariableGuaranteeByApsServiceCode(thirdPartyServiceDeleteDto.getApsServiceCode());
        if (variableGuarantee == null) {
            throw new BusinessException(ExceptionCodes.THIRD_PARTY_IMPORT_SERVICE_CODE_ERROR);
        }
        variableGuarantee.setInvalid("0");
        variableGuarantee.setModifiedBy(IMPORT_VARIANBLE_BY_THIRD_PARTY);
        variableGuarantee.setModifiedTime(new Date());
        variableGuaranteeDao.save(variableGuarantee);
        return true;
    }

    @Transactional
    @Override
    public Boolean importServiceRecord(ThirdPartyImportFlightDto thirdPartyImportFlightDto) {
        String airportCode = thirdPartyImportFlightDto.getAirportCode();
        Long tenantId = TenantConvertUtil.getTenantId(airportCode);
        if (tenantId == null) {
            throw new BusinessException(ExceptionCodes.THIRD_PARTY_IMPORT_AIRPORT_ERROR);
        }
        ImportServiceRecordListDto importServiceRecordListDto=new ImportServiceRecordListDto(airportCode);
        for (ThirdPartyImportServiceDto thirdPartyImportServiceDto : thirdPartyImportFlightDto.getServiceList()) {
            String itemCode = thirdPartyImportServiceDto.getItemCode();
            List<VariableGuarantee> variableGuaranteeList = variableGuaranteeDao.getVariableGuaranteeByItemCode(itemCode);
            if (variableGuaranteeList.isEmpty()) {
                log.info("itemCode = {} , 未找到 签单字段匹配记录", itemCode);
                continue;
            }
            for (VariableGuarantee variableGuarantee : variableGuaranteeList) {
                setServiceRecordList(thirdPartyImportFlightDto, variableGuarantee,  thirdPartyImportServiceDto, importServiceRecordListDto);
            }
        }
        deleteServiceRecord(importServiceRecordListDto);
        saveServiceRecord(importServiceRecordListDto);
        if (importServiceRecordListDto.getDeleteList().isEmpty() && importServiceRecordListDto.getSaveList().isEmpty()
                && importServiceRecordListDto.getDeleteConfirmList().isEmpty() && importServiceRecordListDto.getSaveConfirmList().isEmpty()) {
            log.error(BusinessMessageEnum.SYNC_DATA_ERROR.getMsg());
        }
        return true;
    }

    private void setServiceRecordList(ThirdPartyImportFlightDto thirdPartyImportFlightDto, VariableGuarantee variableGuarantee,
                                      ThirdPartyImportServiceDto thirdPartyImportServiceDto, ImportServiceRecordListDto importServiceRecordListDto) {
        Map<String, Map<String, List<ServiceRecord>>> flightServiceMap=importServiceRecordListDto.getFlightServiceMap();
        List<String[]> cycleflightInfo = new ArrayList<>();
        String landFlag = variableGuarantee.getLandFlag();
        String flightNos = thirdPartyImportFlightDto.getFlightNo();
        List<String[]> flightArr = splitFlightNo(flightNos);
        overNightFlight(flightArr, landFlag, cycleflightInfo);
        String airportCode = thirdPartyImportFlightDto.getAirportCode();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_PATTERN);
        String flightDate = thirdPartyImportFlightDto.getFlightDate().format(formatter);
        Date invalidDate = variableGuarantee.getInvalidDate();
        if (invalidDate.after(DateUtils.parseDate(flightDate))) {
            return;
        }
        RulesVariableRecord variableRecord = variableRecordDao.getServiceCodeByVariable(variableGuarantee.getApsServiceCode(), variableGuarantee.getAirportCode());
        for (String[] flightInfo : cycleflightInfo) {
            //进出港航班
            FlightInfo fi = flightInfoDao.getFlightInfoByCondition(flightInfo[0], flightDate, flightInfo[1], airportCode);
            String serviceCode = variableGuarantee.getApsServiceCode();
            if (fi != null && fi.getVariableStatus() != 0 && fi.getVariableStatus() != 3) {
                Map<String, List<ServiceRecord>> serviceCodeMap = flightServiceMap.getOrDefault(fi.getId(), new HashMap<>());
                String fiId=fi.getId();
                serviceCodeMap.computeIfAbsent(serviceCode,code->serviceRecordDao.listServiceRecordByCondition(fiId, airportCode, code));
                addConfirmServiceRecord(fi, variableGuarantee, variableRecord, thirdPartyImportServiceDto, importServiceRecordListDto);
            } else {
                if(fi==null){
                    fi=new FlightInfo();
                    fi.setFlightDate(DateUtils.parseDate(flightDate));
                    fi.setFlightNo(flightInfo[0]);
                    fi.setFlightFlag(flightInfo[1]);
                }
                addServiceRecord(fi, variableGuarantee, variableRecord, thirdPartyImportServiceDto,importServiceRecordListDto);
            }
        }
    }

    public void overNightFlight(List<String[]> flightArr, String landFlag, List<String[]> cycleflightInfo) {
        if (flightArr.size() > 1) {
            //如果是非过夜的进出港签单，根据归集判断同步航班
            if ("A".equals(landFlag)) {
                cycleflightInfo.add(flightArr.get(0));
            } else if ("D".equals(landFlag)) {
                cycleflightInfo.add(flightArr.get(1));
            } else {
                cycleflightInfo.addAll(flightArr);
            }
        } else {
            cycleflightInfo.add(flightArr.get(0));
        }
    }

    private void saveServiceRecord(ImportServiceRecordListDto importServiceRecordListDto) {
        List<ServiceRecord> saveList=importServiceRecordListDto.getSaveList();
        List<ServiceRecordConfirm> saveConfirmList=importServiceRecordListDto.getSaveConfirmList();
        Map<String, Map<String, List<ServiceRecord>>> flightServiceMap=importServiceRecordListDto.getFlightServiceMap();
        Set<String> changeFlightIdSet=importServiceRecordListDto.getChangeFlightIdSet();

        // 如果savelist不为空，则执行批量保存·
        if (!saveList.isEmpty()) {
            serviceRecordDao.saveAll(saveList);
        }
        //新增service_record_confirm表中的数据
        if (!saveConfirmList.isEmpty()) {
            serviceRecordConfirmDao.saveAll(saveConfirmList);
        }
        //将航班业务保障数据状态置为已修改
        for (Map.Entry<String, Map<String, List<ServiceRecord>>> flightIdEntry : flightServiceMap.entrySet()) {
            for (Map.Entry<String, List<ServiceRecord>> entry : flightIdEntry.getValue().entrySet()) {
                if (!changeFlightIdSet.contains(entry.getKey())) {
                    List<ServiceRecord> excessiveService = entry.getValue();
                    if (excessiveService != null && !excessiveService.isEmpty()) {
                        changeFlightIdSet.add(entry.getKey());
                    }
                }
            }
        }
        if (!changeFlightIdSet.isEmpty()) {
            flightInfoDao.updateVariableByFlightIdS(new ArrayList<>(changeFlightIdSet));
        }
    }

    private void deleteServiceRecord(ImportServiceRecordListDto importServiceRecordListDto) {
        Set<String> deleteList=importServiceRecordListDto.getDeleteList();
        Set<String> deleteConfirmList=importServiceRecordListDto.getDeleteConfirmList();
        Set<String> signDeleteList=importServiceRecordListDto.getSignDeleteList();
        // 如果删除list不为空，则执行批量删除
        if (!deleteList.isEmpty()) {
            serviceRecordDao.deleteServiceRecordByIds(new ArrayList<>(deleteList));
        }

        //删除service_record_confirm表中的数据
        if (!deleteConfirmList.isEmpty()) {
            serviceRecordConfirmDao.deleteServiceRecordByIds(new ArrayList<>(deleteConfirmList));
        }
        //将service_record表中数据置为签单删除
        if (!signDeleteList.isEmpty()) {
            serviceRecordDao.signDeleteOrUpdate(new ArrayList<>(signDeleteList));
        }
    }

    private List<String[]> splitFlightNo(String flightNos) {
        List<String[]> flightArr = new ArrayList<>();
        if (flightNos.startsWith("/")) {
            String[] strArr = new String[2];
            strArr[0] = flightNos.replace("/", "");
            strArr[1] = "D";
            flightArr.add(strArr);
        } else if (flightNos.endsWith("/")) {
            String[] strArr = new String[2];
            strArr[0] = flightNos.replace("/", "");
            strArr[1] = "A";
            flightArr.add(strArr);
        } else {//进出港
            String[] fns = flightNos.split("/");
            String[] strArrA = new String[2];
            strArrA[0] = fns[0];
            strArrA[1] = "A";
            String[] strArrD = new String[3];
            strArrD[0] = fns[1];
            strArrD[1] = "D";
            flightArr.add(strArrA);
            flightArr.add(strArrD);
        }
        return flightArr;
    }

    private void addServiceRecord(FlightInfo fi, VariableGuarantee variableGuarantee, RulesVariableRecord variableRecord,
                                  ThirdPartyImportServiceDto thirdPartyImportServiceDto,ImportServiceRecordListDto importServiceRecordListDto) {
        Date flightDate=fi.getFlightDate();
        String flightNo=fi.getFlightNo();
        String landFlag=fi.getFlightFlag();
        String airportCode=importServiceRecordListDto.getAirportCode();
        List<ServiceRecord> saveList=importServiceRecordListDto.getSaveList();
        Set<String> deleteList=importServiceRecordListDto.getDeleteList();
        List<ServiceRecord> srList;
        List<String> deleteIds;
        String serviceCode = variableRecord.getVariable();
        String dataUpdate = variableGuarantee.getDataUpdate();
        String variableName = variableRecord.getVariableName();
        String rule = variableGuarantee.getConversionRules();
        String apsType = variableRecord.getVariableUnit();
        // 判断结算是否已有数据
        if (fi.getId() != null) {
            srList = serviceRecordDao.listServiceRecordByCondition(
                    fi.getId(), fi.getAirportCode(), serviceCode);
        } else {
            srList = serviceRecordDao.listServiceRecordByDateNoFlag(flightDate,
                    flightNo, landFlag, airportCode, serviceCode);
        }
        if (!srList.isEmpty()) {
            if ("0".equals(dataUpdate)) {
                return;
            }
            //结算保障数据是非人工录入，则可以删除
            deleteIds = srList.stream()
                    .filter(s -> s.getModifiedBy() != null
                            && (SIGN_IMPORT_BY_AUTO.equals(s.getModifiedBy())
                            || SIGN_IMPORT_BY_MANUAL.equals(s.getModifiedBy())
                            || s.getModifiedBy().contains(IMPORT_BY_THIRD_PARTY))).map(ServiceRecord::getId).collect(Collectors.toList());

            deleteList.addAll(deleteIds);
        }
        ServiceRecord sr = assembleServiceRecord(fi, airportCode, thirdPartyImportServiceDto, serviceCode, variableName, rule, apsType);
        saveList.add(sr);
        if (thirdPartyImportServiceDto.getCorridorBridgeType() != null) {
            if (DOUBLE_BRIDGE.equals(thirdPartyImportServiceDto.getCorridorBridgeType())) {
                ServiceRecord newSr = new ServiceRecord();
                copyPropertiesIgnoreNull(sr, newSr);
                newSr.setCreateBy(IMPORT_BY_THIRD_PARTY + "-双廊桥-2");
                saveList.add(newSr);
            } else if (THREE_BRIDGE.equals(thirdPartyImportServiceDto.getCorridorBridgeType())) {
                ServiceRecord newSr = new ServiceRecord();
                copyPropertiesIgnoreNull(sr, newSr);
                newSr.setCreateBy(IMPORT_BY_THIRD_PARTY + "-三廊桥-2");
                ServiceRecord newSr2 = new ServiceRecord();
                copyPropertiesIgnoreNull(sr, newSr2);
                newSr2.setCreateBy(IMPORT_BY_THIRD_PARTY + "-三廊桥-3");

                saveList.add(newSr);
                saveList.add(newSr2);
            }
        }
    }

    public ServiceRecord assembleServiceRecord(FlightInfo fi , String airportCode
            , ThirdPartyImportServiceDto thirdPartyImportServiceDto, String serviceCode, String variableName, String rule, String apsType) {
        Date flightDate=fi.getFlightDate();
        String flightNo=fi.getFlightNo();
        String landFlag=fi.getFlightFlag();
        ServiceRecord sr = new ServiceRecord();
        if (fi.getId() != null) {
            sr.setFlightId(fi.getId());
        } else {
            sr.setFlightDate(flightDate);
            sr.setFlightNo(flightNo);
            sr.setFlightFlag(landFlag);
        }
        sr.setServiceCode(serviceCode);
        sr.setServiceName(variableName);
        sr.setAirportCode(airportCode);
        sr.setInScope(isInStayTime(thirdPartyImportServiceDto, fi));
        switch (rule) {
            // 无规则
            case CONVERSION_RULES_0:
                if (!setConversionRules0(apsType, thirdPartyImportServiceDto, sr, serviceCode)) {
                    return null;
                }
                break;
            case CONVERSION_RULES_1:
                if (thirdPartyImportServiceDto.getHaveOrNot() == null) {
                    return null;
                }
                Double userNumber = Double.valueOf(thirdPartyImportServiceDto.getHaveOrNot());
                if (userNumber.equals(0.0)) {
                    return null;
                }
                sr.setUsedNumber(userNumber);
                break;
            case CONVERSION_RULES_2:
                if (dateIsValid(thirdPartyImportServiceDto.getStartTime(), thirdPartyImportServiceDto.getEndTime())) {
                    return null;
                }
                Duration duration = Duration.between(thirdPartyImportServiceDto.getStartTime(), thirdPartyImportServiceDto.getEndTime());
                long minutes = duration.toMinutes();
                long usdTime = (minutes / 30);
                if (minutes > usdTime * 30) {
                    usdTime++;
                }
                sr.setUsedNumber((double) usdTime / 2);
                break;
            default:
                sr.setUsedNumber(1.0);
        }
        sr.setCreateBy(IMPORT_BY_THIRD_PARTY);
        sr.setCreateTime(new Date());
        sr.setModifiedBy(IMPORT_BY_THIRD_PARTY);
        sr.setModifiedTime(new Date());
        return sr;
    }

    private boolean setConversionRules0(String apsType, ThirdPartyImportServiceDto thirdPartyImportServiceDto, ServiceRecord sr, String serviceCode) {
        if ("H".equals(apsType)) {
            if (dateIsValid(thirdPartyImportServiceDto.getStartTime(), thirdPartyImportServiceDto.getEndTime())) {
                return false;
            }
            ZoneId zoneId = ZoneId.systemDefault();
            ZonedDateTime startzdt = thirdPartyImportServiceDto.getStartTime().atZone(zoneId);
            Date startTime = Date.from(startzdt.toInstant());
            startTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(startTime));
            sr.setStartTime(startTime);

            ZonedDateTime endzdt = thirdPartyImportServiceDto.getEndTime().atZone(zoneId);
            Date endTime = Date.from(endzdt.toInstant());
            endTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(endTime));
            sr.setEndTime(endTime);
            sr.setUsedNumber(DateUtils.calculateLengthOfTime(serviceCode, startTime, endTime));
        } else {
            if (thirdPartyImportServiceDto.getTimes() == null || thirdPartyImportServiceDto.getTimes().equals(0.0)) {
                return false;
            }
            sr.setUsedNumber(thirdPartyImportServiceDto.getTimes());
        }
        return true;
    }


    private void addConfirmServiceRecord(FlightInfo fi, VariableGuarantee variableGuarantee, RulesVariableRecord variableRecord, ThirdPartyImportServiceDto thirdPartyImportServiceDto
                                         ,ImportServiceRecordListDto importServiceRecordListDto) {
        Set<String> changeFlightId=importServiceRecordListDto.getChangeFlightIdSet();
        Map<String, Map<String, List<ServiceRecord>>> flightServiceMap=importServiceRecordListDto.getFlightServiceMap();
        Set<String> signDeleteList=importServiceRecordListDto.getSignDeleteList();
        List<ServiceRecordConfirm> saveConfirmList=importServiceRecordListDto.getSaveConfirmList();
        Set<String> deleteConfirmList=importServiceRecordListDto.getDeleteConfirmList();
        List<ServiceRecordConfirm> srList;
        List<String> deleteIds;
        String airportCode = fi.getAirportCode();
        String serviceCode = variableRecord.getVariable();
        String dataUpdate = variableGuarantee.getDataUpdate();
        String variableName = variableRecord.getVariableName();
        String rule = variableGuarantee.getConversionRules();
        String apsType = variableRecord.getVariableUnit();
        if ("0".equals(dataUpdate)) {
            return;
        }
        srList = serviceRecordConfirmDao.listServiceRecordByCondition(fi.getId(), fi.getAirportCode(), serviceCode);
        List<ServiceRecord> srDeleteList = serviceRecordDao.listServiceRecordByCondition(fi.getId(), fi.getAirportCode(), serviceCode);
        if (!srDeleteList.isEmpty()) {
            deleteIds = srDeleteList.stream().map(ServiceRecord::getId).collect(Collectors.toList());
            signDeleteList.addAll(deleteIds);
        }
        if (!srList.isEmpty()) {
            //结算保障数据是非人工录入，则可以删除
            deleteIds = srList.stream().map(ServiceRecordConfirm::getId).collect(Collectors.toList());
            deleteConfirmList.addAll(deleteIds);
        }
        ServiceRecordConfirm sr = assembleServiceRecordConfirm(fi,
                airportCode, thirdPartyImportServiceDto, serviceCode, variableName, rule, apsType);
        if (sr == null) {
            return;
        }
        if (!changeFlightId.contains(fi.getId())) {
            needChangeId(fi, flightServiceMap, changeFlightId, serviceCode, sr);
        }
        saveConfirmList.add(sr);
        if (thirdPartyImportServiceDto.getCorridorBridgeType() != null) {
            if (DOUBLE_BRIDGE.equals(thirdPartyImportServiceDto.getCorridorBridgeType())) {
                ServiceRecordConfirm newSr = new ServiceRecordConfirm();
                copyPropertiesIgnoreNull(sr, newSr);
                newSr.setCreateBy(IMPORT_BY_THIRD_PARTY + "-双廊桥-2");
                saveConfirmList.add(newSr);
            } else if (THREE_BRIDGE.equals(thirdPartyImportServiceDto.getCorridorBridgeType())) {
                ServiceRecordConfirm newSr = new ServiceRecordConfirm();
                copyPropertiesIgnoreNull(sr, newSr);
                newSr.setCreateBy(IMPORT_BY_THIRD_PARTY + "-三廊桥-2");
                ServiceRecordConfirm newSr2 = new ServiceRecordConfirm();
                copyPropertiesIgnoreNull(sr, newSr2);
                newSr2.setCreateBy(IMPORT_BY_THIRD_PARTY + "-三廊桥-3");

                saveConfirmList.add(newSr);
                saveConfirmList.add(newSr2);
            }
        }
    }

    /**
     * Title:assembleServiceRecordConfirm
     * <p>
     * Description: 组装业务保障数据备份表对象
     * data:
     * Author: liuzhiheng
     * Date: 2024-09-24 14:05:50
     */
    public ServiceRecordConfirm assembleServiceRecordConfirm(FlightInfo fi, String airportCode, ThirdPartyImportServiceDto thirdPartyImportServiceDto, String serviceCode, String variableName, String rule, String apsType) {
        ServiceRecordConfirm sr = new ServiceRecordConfirm();
        sr.setFlightId(fi.getId());
        sr.setServiceCode(serviceCode);
        sr.setServiceName(variableName);
        sr.setAirportCode(airportCode);
        sr.setInScope(isInStayTime(thirdPartyImportServiceDto, fi));
        switch (rule) {
            case CONVERSION_RULES_0:
                if ("H".equals(apsType)) {
                    if (dateIsValid(thirdPartyImportServiceDto.getStartTime(), thirdPartyImportServiceDto.getEndTime())) {
                        return null;
                    }
                    ZoneId zoneId = ZoneId.systemDefault();
                    ZonedDateTime startzdt = thirdPartyImportServiceDto.getStartTime().atZone(zoneId);
                    Date startTime = Date.from(startzdt.toInstant());
                    startTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(startTime));
                    sr.setStartTime(startTime);
                    ZonedDateTime endzdt = thirdPartyImportServiceDto.getEndTime().atZone(zoneId);
                    Date endTime = Date.from(endzdt.toInstant());
                    endTime = DateUtils.parseAnyDate(DateUtils.formatFlightTime(endTime));
                    sr.setEndTime(endTime);
                    sr.setUsedNumber(DateUtils.calculateLengthOfTime(serviceCode, startTime, endTime));
                } else {// 如果为数值
                    sr.setUsedNumber(thirdPartyImportServiceDto.getTimes());
                }
                break;
            case CONVERSION_RULES_1:
                if (thirdPartyImportServiceDto.getHaveOrNot() == null) {
                    return null;
                }
                Double userNumber = Double.valueOf(thirdPartyImportServiceDto.getHaveOrNot());
                sr.setUsedNumber(userNumber);
                break;
            case CONVERSION_RULES_2:
                if (dateIsValid(thirdPartyImportServiceDto.getStartTime(), thirdPartyImportServiceDto.getEndTime())) {
                    return null;
                }
                Duration duration = Duration.between(thirdPartyImportServiceDto.getStartTime(), thirdPartyImportServiceDto.getEndTime());
                // 相差的分钟数
                long minutes = duration.toMinutes();
                long usdTime = (minutes / 30);
                if (minutes > usdTime * 30) {
                    usdTime++;
                }
                sr.setUsedNumber((double) usdTime / 2);
                break;
            default:// 3为统计计算为1次
                sr.setUsedNumber(1.0);

        }
        if (sr.getUsedNumber() == null || sr.getUsedNumber().equals(0.0)) {
            return null;
        }
        sr.setCreateBy(IMPORT_BY_THIRD_PARTY);
        sr.setCreateTime(new Date());
        sr.setModifiedBy(IMPORT_BY_THIRD_PARTY);
        sr.setModifiedTime(new Date());

        return sr;
    }

    /**
     * Title:needChangeId
     * <p>
     * Description: 判断当前航班是否需要修改，并将航班id放进集合中
     * Author: liuzhiheng
     * Date: 2024-09-24 14:04:53
     */
    public void needChangeId(FlightInfo fi, Map<String, Map<String, List<ServiceRecord>>> flightServiceMap, Set<String> changeFlightId, String serviceCode, ServiceRecordConfirm sr) {
        boolean needChange = true;
        Map<String, List<ServiceRecord>> serviceMap = flightServiceMap.get(fi.getId());
        if (serviceMap != null) {
            List<ServiceRecord> repeatServiceList = serviceMap.get(serviceCode);
            if (repeatServiceList != null && !repeatServiceList.isEmpty()) {
                Iterator<ServiceRecord> iterator = repeatServiceList.iterator();
                while (iterator.hasNext()) {
                    ServiceRecord oldSr = iterator.next();
                    if (oldSr.getServiceCode().equals(sr.getServiceCode())
                            && ((equalsDate(oldSr.getStartTime(), sr.getStartTime()) && equalsDate(oldSr.getEndTime(), sr.getEndTime()))
                            || oldSr.getUsedNumber().equals(sr.getUsedNumber()))) {
                        needChange = false;
                        iterator.remove();
                        break;
                    }
                }
                serviceMap.put(serviceCode, repeatServiceList);
                flightServiceMap.put(fi.getId(), serviceMap);
            }
        }
        if (needChange) {
            changeFlightId.add(fi.getId());
        }
    }

    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }


    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    private boolean equalsDate(Date d1, Date d2) {
        if (d1 == null || d2 == null) {
            return false;
        }
        return d1.compareTo(d2) == 0;
    }


    private String isInStayTime(ThirdPartyImportServiceDto thirdPartyImportServiceDto, FlightInfo fi) {
        if (fi == null || thirdPartyImportServiceDto.getStartTime() == null || thirdPartyImportServiceDto.getEndTime() == null) {
            return IN_SCOPE;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime startzdt = thirdPartyImportServiceDto.getStartTime().atZone(zoneId);
        Date startTime = Date.from(startzdt.toInstant());

        ZonedDateTime endzdt = thirdPartyImportServiceDto.getEndTime().atZone(zoneId);
        Date endTime = Date.from(endzdt.toInstant());

        if (fi.getStayStartTime() != null && fi.getStayEndTime() != null) {
            if (startTime.before(fi.getStayStartTime())
                    || endTime.after(fi.getStayEndTime())) {
                return NOT_IN_SCOPE;
            }
        } else if ("A".equals(fi.getFlightFlag()) && fi.getStayStartTime() != null && startTime.before(fi.getStayStartTime())) {
            //降落航班，签单开始时间大于停场开始时间
            return NOT_IN_SCOPE;
        } else if ("D".equals(fi.getFlightFlag()) && fi.getStayEndTime() != null && endTime.after(fi.getStayEndTime())) {
            //起飞航班，签单结束时间小于停场结束时间
            return NOT_IN_SCOPE;
        }
        return IN_SCOPE;
    }

    public boolean dateIsValid(LocalDateTime startDate, LocalDateTime endDate) {
        if (startDate == null || endDate == null) {
            return true;
        }
        return startDate.isAfter(endDate);
    }

}
