package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.core.entity.FlightInfoMb;
import com.swcares.aiot.core.entity.TAircraftInfo;
import com.swcares.aiot.core.liteflow.ctx.CtxCalc;
import com.swcares.aiot.core.model.dto.ReCalcDto;
import com.swcares.aiot.core.model.dto.ReCalcErrorDto;
import com.swcares.aiot.core.service.ITAircraftInfoService;
import com.swcares.aiot.core.service.ITFlightInfoService;
import com.swcares.baseframe.common.exception.BusinessException;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.aiot.core.liteflow.cmp.ReCalcGetFlightInfoMap
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/8/12 10:07
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpCalcGetFlightInfoMap", name = "组件-对账通机场端-计算模块-获取航班信息")
public class CmpCalcGetFlightInfoMap extends NodeComponent {

    @Resource
    private ITFlightInfoService flightInfoService;
    @Resource
    private ITAircraftInfoService aircraftInfoService;

    @Override
    public void process() {
        CtxCalc ctxCalc = this.getContextBean(CtxCalc.class);
        ReCalcDto dto = ctxCalc.getDto();
        Map<String, ReCalcErrorDto> errorDtoMap = ctxCalc.getErrorDtoMap();
        Map<String, TAircraftInfo> flightAircraftInfoMap = new HashMap<>();
        Map<String, FlightInfoMb> flightInfoMap = new HashMap<>();
        String flightNo = dto.getFlightNo();
        List<String> flightNoList = new ArrayList<>();
        if (StringUtils.isNotBlank(flightNo)) {
            flightNoList.addAll(Arrays.asList(flightNo.split(",")));
        }
        //查询需计算条件范围内所有航班数据
        List<FlightInfoMb> flightInfoMbList = flightInfoService.lambdaQuery()
                .eq(FlightInfoMb::getAirportCode, dto.getAirportCode())
                .between(dto.getStartDate() != null && dto.getEndDate() != null, FlightInfoMb::getFlightDate, dto.getStartDate(), dto.getEndDate())
                .between(dto.getFlightTimeStartDate() != null && dto.getFlightTimeEndDate() != null, FlightInfoMb::getFlightTime, dto.getFlightTimeStartDate(), dto.getFlightTimeEndDate())
                .eq(StringUtils.isNotBlank(dto.getAirlineCode()), FlightInfoMb::getAirlineCode, dto.getAirlineCode())
                .in(CollUtil.isNotEmpty(dto.getFlightId()), FlightInfoMb::getId, dto.getFlightId())
                .in(!flightNoList.isEmpty(), FlightInfoMb::getFlightNo, flightNoList)
                .eq(FlightInfoMb::getInvalid, "1")
                .list();
        if (CollUtil.isEmpty(flightInfoMbList)) {
            log.error("机场端结算航班数量为0");
            throw new BusinessException(ExceptionCodes.CALC_FLIGHT_NUM_NULL);
        }
        //获取飞机信息map
        Map<String, List<TAircraftInfo>> aircraftInfoMap = getRegNoMap(dto);
        //可以计算的航班数据
        Map<String, List<FlightInfoMb>> calcFlightInfoMbMap = new HashMap<>();
        //校验航班信息
        checkFlightInfo(errorDtoMap, flightInfoMbList, aircraftInfoMap, calcFlightInfoMbMap, flightAircraftInfoMap, flightInfoMap);
        ctxCalc.setCalcFlightInfoMbMap(calcFlightInfoMbMap);
        ctxCalc.setFlightAircraftInfoMap(flightAircraftInfoMap);
        ctxCalc.setFlightInfoMap(flightInfoMap);
    }

    private void checkFlightInfo(Map<String, ReCalcErrorDto> errorDtoMap, List<FlightInfoMb> flightInfoMbList,
                                 Map<String, List<TAircraftInfo>> aircraftInfoMap, Map<String, List<FlightInfoMb>> calcFlightInfoMbMap,
                                 Map<String, TAircraftInfo> flightAircraftInfoMap, Map<String, FlightInfoMb> flightInfoMap) {
        for (FlightInfoMb flightInfoMb : flightInfoMbList) {
            if (checkRegNo(errorDtoMap, flightInfoMb)) {
                continue;
            }
            TAircraftInfo aircraft = getAircraft(flightInfoMb, aircraftInfoMap);
            //查不到飞机信息
            if (aircraft == null || StringUtils.isBlank(aircraft.getSettleCode())) {
                generateErrorMsg(flightInfoMb, "飞机信息缺失", "全部", errorDtoMap);
            } else {
                //按照结算代码分组可计算航班数据
                List<FlightInfoMb> tempList = calcFlightInfoMbMap.getOrDefault(aircraft.getSettleCode(), new ArrayList<>());
                tempList.add(flightInfoMb);
                calcFlightInfoMbMap.put(aircraft.getSettleCode(), tempList);
                flightAircraftInfoMap.put(flightInfoMb.getId(), aircraft);
                //判断航班状态确认
                if (!Integer.valueOf(1).equals(flightInfoMb.getDataStatus())) {
                    generateErrorMsg(flightInfoMb, "航班数据未确认", "航空性相关费用", errorDtoMap);
                }
                if (!Integer.valueOf(1).equals(flightInfoMb.getVariableStatus())) {
                    //判断业务状态确认
                    generateErrorMsg(flightInfoMb, "保障业务数据未确认", "保障业务相关费用", errorDtoMap);
                }
            }
            flightInfoMap.put(flightInfoMb.getId(), flightInfoMb);

        }
    }

    private static boolean checkRegNo(Map<String, ReCalcErrorDto> errorDtoMap, FlightInfoMb flightInfoMb) {
        boolean flag = false;
        if (StringUtils.isBlank(flightInfoMb.getRegNo())) {
            //航班数据没有机号
            generateErrorMsg(flightInfoMb, "航班数据没有机号", "全部", errorDtoMap);
            flag = true;
        } else if (StringUtils.isBlank(flightInfoMb.getFlightSegmentType())) {
            //航班数据没有航段性质
            generateErrorMsg(flightInfoMb, "航班数据没有航段性质", "全部", errorDtoMap);
            flag = true;
        }
        return flag;
    }

    private static void generateErrorMsg(FlightInfoMb flightInfoMb, String errorMsg, String errorFee, Map<String, ReCalcErrorDto> errorDtoMap) {
        ReCalcErrorDto reCalcErrorDto = errorDtoMap.get(flightInfoMb.getId());
        if (reCalcErrorDto == null) {
            reCalcErrorDto = new ReCalcErrorDto(flightInfoMb, errorMsg, errorFee);
        }else{
            reCalcErrorDto.setErrorMsg(reCalcErrorDto.getErrorMsg()+"、"+errorMsg);
            if(!"全部".equals(errorFee)){
                reCalcErrorDto.setErrorFee(reCalcErrorDto.getErrorFee()+"、"+errorFee);
            }else{
                reCalcErrorDto.setErrorFee(errorFee);
            }
        }
        errorDtoMap.put(flightInfoMb.getId(), reCalcErrorDto);
    }

    private TAircraftInfo getAircraft(FlightInfoMb flightInfoMb, Map<String, List<TAircraftInfo>> aircraftInfoMap) {
        String regNo = flightInfoMb.getRegNo();
        List<TAircraftInfo> tAircraftInfos = aircraftInfoMap.get(regNo);
        if (tAircraftInfos == null) {
            return null;
        }
        for (TAircraftInfo tAircraftInfo : tAircraftInfos) {
            if (tAircraftInfo.getStartDate().isBefore(flightInfoMb.getFlightDate())
                    && tAircraftInfo.getEndDate().isAfter(flightInfoMb.getFlightDate())) {
                return tAircraftInfo;
            }
        }
        return null;
    }

    private Map<String, List<TAircraftInfo>> getRegNoMap(ReCalcDto dto) {
        List<TAircraftInfo> aircraftInfoList = aircraftInfoService.lambdaQuery()
                //飞机信息的结束时间在结算范围的开始时间之后
                .ge(dto.getStartDate() != null, TAircraftInfo::getEndDate, dto.getStartDate())
                //飞机信息的开始时间在结算范围的结束时间之前
                .le(dto.getEndDate() != null, TAircraftInfo::getStartDate, dto.getEndDate())
                .ge(dto.getFlightTimeStartDate() != null, TAircraftInfo::getEndDate, dto.getFlightTimeStartDate())
                .le(dto.getFlightTimeEndDate() != null, TAircraftInfo::getStartDate, dto.getFlightTimeEndDate())
                .eq(TAircraftInfo::getInvalid, "1").list();
        Map<String, List<TAircraftInfo>> aircraftInfoMap = new HashMap<>();
        for (TAircraftInfo aircraftInfo : aircraftInfoList) {
            List<TAircraftInfo> aircraftInfoTempList = aircraftInfoMap.getOrDefault(aircraftInfo.getRegNo(), new ArrayList<>());
            aircraftInfoTempList.add(aircraftInfo);
            aircraftInfoMap.put(aircraftInfo.getRegNo(), aircraftInfoTempList);
        }
        return aircraftInfoMap;
    }
}
