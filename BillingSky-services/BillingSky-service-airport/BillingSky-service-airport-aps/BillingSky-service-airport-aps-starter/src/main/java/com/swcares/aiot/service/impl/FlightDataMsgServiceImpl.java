package com.swcares.aiot.service.impl;

import com.alibaba.fastjson2.JSON;
import com.swcares.aiot.core.importer.entity.BaseFlightCargo;
import com.swcares.aiot.core.importer.entity.BaseFlightInfo;
import com.swcares.aiot.core.importer.entity.BaseFlightTraveler;
import com.swcares.aiot.core.importer.service.FlightDataImportService;
import com.swcares.aiot.core.model.entity.FlightDataMsg;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;
import com.swcares.aiot.dao.FlightDataMsgDao;
import com.swcares.aiot.service.FlightDataMsgService;
import com.swcares.aiot.service.MqSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.service.impl.FlightDataMsgServiceImpl
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/2/27 9:42
 * @version v1.0
 */
@Service
@Slf4j
public class FlightDataMsgServiceImpl implements FlightDataMsgService {

    @Resource
    private FlightDataMsgDao flightDataMsgDao;
    @Resource
    private FlightDataImportService flightDataImportService;
    @Resource
    private MqSignService mqSignService;

    //航班数据
    private static final String FLIGHT_DATA = "1";
    //保障数据
    private static final String SERVICE_DATA = "2";

    //    @Async
    @Override
    public void saveFlightClassMsg(Object obj, String flightInfoFlag, String airportCode) {
        saveClassMsg(obj, flightInfoFlag, airportCode, FLIGHT_DATA);
    }

    //    @Async
    @Override
    public void saveServiceClassMsg(Object obj, String flightInfoFlag, String airportCode) {
        saveClassMsg(obj, flightInfoFlag, airportCode, SERVICE_DATA);
    }


    public void saveClassMsg(Object obj, String flightInfoFlag, String airportCode, String flightOrService) {
        try {
            FlightDataMsg fdm = new FlightDataMsg();
            fdm.setFlightInfoFlag(flightInfoFlag);
            fdm.setClassName(obj.getClass().getName());
            fdm.setMessage(JSON.toJSONString(obj));
            fdm.setAirportCode(airportCode);
            fdm.setCreateTime(new Date());
            fdm.setFlightOrService(flightOrService);
            flightDataMsgDao.save(fdm);
        } catch (Exception e) {
            log.error("出现业务异常", e);
        }
    }

    @Override
    @Transactional
    public void getFlightClassMsg(String flightInfoFlag, String airportCode) {
        getClassMsg(flightInfoFlag, airportCode, FLIGHT_DATA);
    }

    @Override
    @Transactional
    public void getServiceClassMsg(String flightInfoFlag, String airportCode) {
        getClassMsg(flightInfoFlag, airportCode, SERVICE_DATA);
    }


    public void getClassMsg(String flightInfoFlag, String airportCode, String flightOrService) {
        List<FlightDataMsg> fdmList = flightDataMsgDao.getFlightDataMsgByNameFlagAiport(
                flightInfoFlag, airportCode, flightOrService);
        List<FlightDataMsg> removeList = new ArrayList<>();
        for (FlightDataMsg fdm : fdmList) {
            String className = fdm.getClassName();
            Class<?> classType;
            try {
                classType = Class.forName(className);
            } catch (ClassNotFoundException e) {
                log.error("出现业务异常", e);
                continue;
            }
            String msg = fdm.getMessage();
            switch (classType.getSimpleName()) {
                case "BaseFlightInfo":
                    flightDataImportService.importFlightData((BaseFlightInfo) JSON.parseObject(msg, classType), false);
                    break;
                case "BaseFlightCargo":
                    flightDataImportService.importFlightCargo((BaseFlightCargo) JSON.parseObject(msg, classType), false);
                    break;
                case "BaseFlightTraveler":
                    flightDataImportService.importFlightTraveler((BaseFlightTraveler) JSON.parseObject(msg, classType), false);
                    break;
                case "SignBusDataSettlementVO":
                    SignBusDataSettlementVO sbVo = (SignBusDataSettlementVO) JSON.parseObject(msg, classType);
                    List<SignBusDataSettlementVO> sbList = new ArrayList<>();
                    sbList.add(sbVo);
                    mqSignService.saveServiceRecord(sbList);
                    break;
                default:
                    continue;
            }

            fdm.setInvalid("0");
            removeList.add(fdm);
        }
        flightDataMsgDao.saveAll(removeList);

    }
}
