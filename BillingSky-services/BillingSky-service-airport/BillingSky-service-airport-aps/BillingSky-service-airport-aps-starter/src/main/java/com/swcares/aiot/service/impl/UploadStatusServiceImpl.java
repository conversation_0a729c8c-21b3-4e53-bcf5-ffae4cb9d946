package com.swcares.aiot.service.impl;

import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.enums.FileBusinessTypeEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.model.entity.UploadStatus;
import com.swcares.aiot.core.model.entity.UserRead;
import com.swcares.aiot.core.model.vo.UploadStatusVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.dao.SettlementStatusDao;
import com.swcares.aiot.dao.UploadStatusDao;
import com.swcares.aiot.dao.UserReadDao;
import com.swcares.aiot.service.IFileAttachmentBizService;
import com.swcares.aiot.service.UploadStatusService;
import com.swcares.baseframe.common.security.LoginUserDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * ClassName：com.swcares.service.impl.ErrorFileNotificationServiceImpl
 * Description：上传文件通知实现类
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/10/17 10:05
 * @version v1.0
 */
@Slf4j
@Service
public class UploadStatusServiceImpl implements UploadStatusService {
    private static final String ERROR_MSG = "出现业务异常";
    public static final String UPLOAD_BUCKET_NAME = "aps-fail-upload";
    private static final String ERROR_FILE_NAME = "错误数据文件生成失败！";

    @Resource
    private IFileAttachmentBizService fileAttachmentBizService;
    @Resource
    private UploadStatusDao uploadStatusDao;
    @Resource
    private UserReadDao userReadDao;
    @Resource
    private SettlementStatusDao settlementStatusDao;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public String newUpload(String uploadName, String airportCode, LoginUserDetails user) {
        try {
            UploadStatus us = new UploadStatus();
            us.setUploadName(uploadName);
            us.setStatus("0");

            us.setCreateBy(user.getUsername());
            us.setCreateTime(new Date());
            us.setModifiedBy(user.getUsername());
            us.setModifiedTime(new Date());

            us.setAirportCode(airportCode);

            uploadStatusDao.saveAndFlush(us);
            return us.getId();
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
            return null;
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void startUpload(String uploadStatusId) {
        try {
            if (uploadStatusId == null) {
                return;
            }
            UploadStatus us = uploadStatusDao.getUploadStatusById(uploadStatusId);
            if (us == null) {
                throw new GenericException(BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getCode(),
                        BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getMsg());
            }
            us.setStatus("2");
            uploadStatusDao.saveAndFlush(us);
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void uploadFail(String uploadStatusId, String failReason) {
        try {
            if (uploadStatusId == null) {
                return;
            }
            UploadStatus us = uploadStatusDao.getUploadStatusById(uploadStatusId);
            if (us == null) {
                throw new GenericException(BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getCode(),
                        BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getMsg());
            }
            us.setFailReason(failReason);
            us.setStatus("-1");
            uploadStatusDao.save(us);
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void uploadSuccess(String uploadStatusId, String errorFileName, Workbook errorWb) {
        try {
            if (uploadStatusId == null) {
                return;
            }
            UploadStatus us = uploadStatusDao.getUploadStatusById(uploadStatusId);
            if (us == null) {
                throw new GenericException(BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getCode(),
                        BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getMsg());
            }
            // 将上传错误数据文件上传至minio
            if(errorWb!=null) {
                this.updateFile(errorWb != null && !Strings.isBlank(errorFileName),
                        fileAttachmentBizService.uploadExcel(FileBusinessTypeEnum.SETTLEMENT_FAIL_RECORD, errorFileName, errorWb), us);
            }
            us.setStatus("1");
            uploadStatusDao.save(us);
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
        }
    }

    private void updateFile(boolean errorWb, Long fileKey, UploadStatus us) {
        if (errorWb) {
            try {
                // 将minio存储的失败信息文件地址保存到数据库
                us.setFailFile(fileKey.toString());
            } catch (Exception e) {
                log.error(ERROR_MSG, e);
                us.setFailFile(ERROR_FILE_NAME);
            }
        }
    }

    @Override
    public int getUnreadUploadCount(String airportCode, LoginUserDetails user) {
        return userReadDao.getUnreadUploadCount(airportCode, "" + user.getId());
    }

    @Override
    public int getUnreadTotal(String airportCode, LoginUserDetails user) {
        int up = userReadDao.getUnreadUploadCount(airportCode, "" + user.getId());
        int set = settlementStatusDao.getUnreadSettlementStatus(airportCode, "" + user.getId());
        return up + set;
    }

    @Override
    public Pager<UploadStatusVo> pageUploadStatus(String airportCode, Date startDate, Date endDate, PageParam pageParam, LoginUserDetails user) {
        List<Object[]> objList = uploadStatusDao.getUploadStatusByDate(airportCode, "" + user.getId(), startDate, endDate);
        List<UploadStatusVo> usVoList = new ArrayList<>();
        // 将查询出的消息设置为已读
        List<UserRead> urList = new ArrayList<>();
        getUploadStatusVo(objList, usVoList, urList, user);
        userReadDao.saveAll(urList);
        return new Pager<>(pageParam.getPage(), pageParam.getLimit(), usVoList);
    }

    @Override
    public List<UploadStatusVo> listLatelyUploadStatus(String airportCode, LoginUserDetails user) {
        List<Object[]> usList = uploadStatusDao.getUploadStatusByDate(airportCode, "" + user.getId(), null, null);
        if (usList.size() > 10) {
            usList = usList.subList(0, 10);
        }
        List<UploadStatusVo> usVoList = new ArrayList<>();
        // 将查询出的消息设置为已读
        List<UserRead> urList = new ArrayList<>();
        getUploadStatusVo(usList, usVoList, urList, user);
        userReadDao.saveAll(urList);
        return usVoList;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void delete(String ids, LoginUserDetails user) {
        if (Strings.isBlank(ids)) {
            throw new GenericException(BusinessMessageEnum.UPLOAD_DELETE_NULL.getCode(),
                    BusinessMessageEnum.UPLOAD_DELETE_NULL.getMsg());
        }
        String[] arrId = ids.split(",");
        //获取需要删除上传记录对应的minio中的错误文件名
        List<String> fileNames = uploadStatusDao.getFileName(Arrays.asList(arrId));
        if (!fileNames.isEmpty()) {
            try {
                //todo 批量删除minio中的文件
                //minioUtils.deleteFile(UPLOAD_BUCKET_NAME, fileNames);
            } catch (Exception e) {
                log.error(ERROR_MSG, e);
            }
        }
        //将删除记录逻辑删除
        uploadStatusDao.deleteByIds(Arrays.asList(arrId), user.getUsername());
    }

    @Override
    public void uploadSuccessOutputStream(String uploadStatusId, String errorFileName, ByteArrayOutputStream outputStream) {
        try {
            if (uploadStatusId == null) {
                return;
            }
            UploadStatus us = uploadStatusDao.getUploadStatusById(uploadStatusId);
            if (us == null) {
                throw new GenericException(BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getCode(), BusinessMessageEnum.UPLOAD_ID_NOT_FOUND.getMsg());
            }
            // 将上传错误数据文件上传至minio
            updateFile(outputStream != null, fileAttachmentBizService.uploadExcelOutputStream(FileBusinessTypeEnum.UPLOAD_FAIL_RECORD,
                    errorFileName,
                    outputStream), us);
            us.setStatus("1");
            uploadStatusDao.save(us);
        } catch (Exception e) {
            log.error(ERROR_MSG, e);
        }
    }

    @Override
    public String uploadGetUrlByFileName(String id) {
        return getFileNameById(id);
    }

    public void getUploadStatusVo(List<Object[]> usList, List<UploadStatusVo> usVoList,
                                  List<UserRead> urList, LoginUserDetails user) {
        for (Object[] objs : usList) {
            UploadStatusVo us = new UploadStatusVo();
            us.setId("" + objs[0]);
            us.setUploadName("" + objs[1]);
            us.setStatus("" + objs[2]);
            if ("-1".equals(us.getStatus())) {
                us.setFailReason("" + objs[3]);
            } else if ("1".equals(us.getStatus()) && objs[4] != null && !ERROR_FILE_NAME.equals(objs[4])) {
                us.setFailFile(objs[4].toString());
            }

            us.setCreateTime((Date) objs[5]);
            us.setCreateBy("" + objs[6]);

            us.setRead("" + objs[7]);

            if ("0".equals(us.getRead())) {
                UserRead ur = new UserRead();
                ur.setSettlementUploadId(us.getId());
                ur.setType("2");
                ur.setUserId("" + user.getId());
                urList.add(ur);
            }
            usVoList.add(us);
        }
    }

    public String getFileNameById(String statusId) {
        UploadStatus us = uploadStatusDao.getUploadStatusById(statusId);
        return us.getFailFile();
    }
}
