package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.model.dto.FlightReportFormDTO;
import com.swcares.aiot.core.model.vo.FlightReportFormExcelVO;
import com.swcares.aiot.core.model.vo.FlightReportFormVO;
import com.swcares.aiot.core.model.vo.FlightTrendVO;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.aiot.core.common.util.EasyExcelUtil;
import com.swcares.aiot.dao.FlightReportFormDao;
import com.swcares.aiot.service.FlightReportFormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：FlightReportFormServiceImpl
 * Description：航班客座率报表 实现类
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2024/6/25 14:36
 * @version v1.0
 */
@Service
@Slf4j
public class FlightReportFormServiceImpl implements FlightReportFormService {

    @Resource
    private FlightReportFormDao flightReportFormDao;

    @Override
    public ResultBuilder<Page<FlightReportFormVO>> page(PageParam pageParam, FlightReportFormDTO dto) {
        Pageable pageable = PageRequest.of(pageParam.getPage() - 1, pageParam.getLimit());
        Page<FlightReportFormVO> page = flightReportFormDao.page(
                dto.getStartFlightDate(), dto.getEndFlightDate(),
                dto.getFlightNo(), dto.getFlightFlag(), pageable);
        for (FlightReportFormVO flight : page.getContent()) {
            // 旅客吞吐量 = 本站成人+本站儿童+过站成人+过站儿童 (出港加过站-进港不加过站)
            if (Constants.Indicators.FLIGHT_FLAG_D.getValue().equals(flight.getFlightFlag())) {
                flight.setTravelerThroughput(flight.getAdultNumber()
                        + flight.getChildNumber()
                        + flight.getTransitAdultNumber()
                        + flight.getTransitChildNumber());
            } else {
                flight.setTravelerThroughput(flight.getAdultNumber() + flight.getChildNumber());
            }
            if (CharSequenceUtil.isBlank(flight.getRegNo()) || CharSequenceUtil.isBlank(flight.getPlf()) || "null".equals(flight.getPlf())) {
                flight.setPlf("-");
            }
        }
        return new ResultBuilder.Builder<Page<FlightReportFormVO>>().data(page).builder();
    }

    @Override
    public ResultBuilder<List<FlightTrendVO>> getThroughputAndPlfTrend(FlightReportFormDTO dto) {
        List<FlightTrendVO> plfList = flightReportFormDao.getPlfAvg(dto.getStartFlightDate(), dto.getEndFlightDate(), dto.getFlightNo(), dto.getFlightFlag());
        return new ResultBuilder.Builder<List<FlightTrendVO>>().data(plfList).builder();
    }

    @Override
    public void exportFlightReportForm(FlightReportFormDTO dto, HttpServletResponse response) {
        List<FlightReportFormExcelVO> resultList = flightReportFormDao.exportFlightReportForm(
                dto.getStartFlightDate(), dto.getEndFlightDate(),
                dto.getFlightNo(), dto.getFlightFlag());
        for (FlightReportFormExcelVO flight : resultList) {
            // 旅客吞吐量 = 本站成人+本站儿童+过站成人+过站儿童 (出港加过站-进港不加过站)
            if (Constants.Indicators.FLIGHT_FLAG_D.getValue().equals(flight.getFlightFlag())) {
                flight.setTravelerThroughput(flight.getAdultNumber()
                        + flight.getChildNumber()
                        + flight.getTransitAdultNumber()
                        + flight.getTransitChildNumber());
            } else {
                flight.setTravelerThroughput(flight.getAdultNumber() + flight.getChildNumber());
            }
            if (CharSequenceUtil.isBlank(flight.getRegNo()) || CharSequenceUtil.isBlank(flight.getPlf()) || "null".equals(flight.getPlf())) {
                flight.setPlf("-");
            }
        }
        String fileName = Constants.FlightReportFormExcelEnum.FLIGHT_REPORT_FORM_NAME.getValue();
        EasyExcelUtil.responseExcel(response, fileName, "sheet1", FlightReportFormExcelVO.class, resultList,
                CollUtil.toList(new LongestMatchColumnWidthStyleStrategy()));
    }
}
