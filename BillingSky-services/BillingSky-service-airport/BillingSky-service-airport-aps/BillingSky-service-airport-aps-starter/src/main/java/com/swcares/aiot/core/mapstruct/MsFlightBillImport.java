package com.swcares.aiot.core.mapstruct;

import com.swcares.aiot.core.model.dto.FlightBillExportDto;
import com.swcares.aiot.core.model.dto.FlightBillImportDto;
import com.swcares.aiot.core.model.entity.FlightBill;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * ClassName：com.swcares.aiot.core.mapstruct.MsFlightBillImport
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/2/19 15:48
 * @version v1.0
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MsFlightBillImport {

    @Mapping(target = "transactionId", ignore = true)
    @Mapping(target = "toAirportCode", ignore = true)
    @Mapping(target = "submit", ignore = true)
    @Mapping(target = "settleMonth", ignore = true)
    @Mapping(target = "serviceStartTime", ignore = true)
    @Mapping(target = "serviceRecord", ignore = true)
    @Mapping(target = "serviceEndTime", ignore = true)
    @Mapping(target = "revocation", ignore = true)
    @Mapping(target = "proveFile", ignore = true)
    @Mapping(target = "modifiedTime", ignore = true)
    @Mapping(target = "modifiedBy", ignore = true)
    @Mapping(target = "manuallyDelete", ignore = true)
    @Mapping(target = "invalid", ignore = true)
    @Mapping(target = "indicatorValue", ignore = true)
    @Mapping(target = "indicatorName", ignore = true)
    @Mapping(target = "indicatorCode", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "fromAirportCode", ignore = true)
    @Mapping(target = "flightId", ignore = true)
    @Mapping(target = "feedback", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "chainId", ignore = true)
    @Mapping(target = "billBusDataItemId", ignore = true)
    @Mapping(target = "airlineShortName", ignore = true)
    FlightBill dto2Bean(FlightBillImportDto flightBillImportDto);

    @Mapping(target = "errorMsg", ignore = true)
    FlightBillExportDto dto2FlightBillExportDto(FlightBillImportDto flightBillImportDto);
}
