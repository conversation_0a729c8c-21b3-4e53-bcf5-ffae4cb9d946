package com.swcares.aiot.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.form.*;
import com.swcares.aiot.core.model.entity.*;
import com.swcares.aiot.core.model.vo.*;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：FlightInfoService <br>
 * Description：(航班数据采集service接口 )<br>
 * Copyright © 2020/6/18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/18 9:29<br>
 * @version v1.0 <br>
 */
public interface FlightInfoService {

    /**
     * Title: pageFlightInfoByCondition<br>
     * Author: 叶咏秋<br>
     * Description: (航班信息动态条件分页查询)<br>
     * Date:  9:32 <br>
     *
     * @param pageParam            :
     * @param flightInfoSearchForm return: ResultBuilder
     */
    ResultBuilder<IPage<FlightInfoVoNew>> pageFlightInfoByCondition(PageParam pageParam, FlightInfoSearchForm flightInfoSearchForm);

    /**
     * Title: getByServiceCode <br>
     * Description: 根据服务code查询航班信息 <br>
     *
     * @param pageParam            :
     * @param serviceCode          :
     * @param flightInfoSearchForm :
     * @return com.swcares.common.ResultBuilder
     * author 曾华川  <br>
     * date 2022/10/17 13:48<br>
     */
    ResultBuilder<Pager<FlightServiceVo>> getByServiceCode(PageParam pageParam, String serviceCode, FlightInfoSearchForm flightInfoSearchForm);

    /**
     * Title: getChangeNum<br>
     * Author: 刘志恒<br>
     * Description: 获取确认后修改航班条数<br>
     * Date:  2023/10/27 16:41 <br>
     */
    ResultBuilder<Integer> getChangeNum(FlightInfoSearchForm flightInfoSearchForm);

    /**
     * Title: countTotal<br>
     * Author: 叶咏秋<br>
     * Description: (航班信息数据总计)<br>
     * Date:  9:40 <br>
     *
     * @param flightInfoSearchForm return: ResultBuilder
     */
    ResultBuilder<List<FlightInfoCountVo>> countTotal(FlightInfoSearchForm flightInfoSearchForm);

    ResultBuilder<FlightInfoDataVO> countModifiedLostTotal(FlightInfoSearchForm flightInfoSearchForm);

    /**
     * Title: saveFlightInfo<br>
     * Author: 叶咏秋<br>
     * Description: (新建航班信息)<br>
     * Date:  10:50 <br>
     *
     * @param flightInfoForm :
     * @param user           return: ResultBuilder
     */
    ResultBuilder<FlightInfo> saveFlightInfo(FlightInfoForm flightInfoForm, LoginUserDetails user, String urlName);

    /**
     * Title: getFlightInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (通过id查看航班信息)<br>
     * Date:  12:14 <br>
     *
     * @param id return: ResultBuilder
     */
    ResultBuilder<FlightInfo> getFlightInfoById(String id);

    /**
     * Title: updateFlightInfoById<br>
     * Author: 叶咏秋<br>
     * Description: (通过航班id修改航班信息)<br>
     * Date:  17:05 <br>
     *
     * @param flightInfoForm :
     * @param user           return: ResultBuilder
     */
    ResultBuilder<FlightInfo> updateFlightInfoById(FlightInfoForm flightInfoForm, LoginUserDetails user, String urlName);

    /**
     * Title: delFlightInfoBatchById<br>
     * Author: 叶咏秋<br>
     * Description: (批量删除航班信息)<br>
     * Date:  9:53 <br>
     *
     * @param ids  :
     * @param user return: ResultBuilder
     */
    ResultBuilder<Object> delFlightInfoBatchById(String ids, LoginUserDetails user, String urlName);

    /**
     * Title: listServiceRecordByCondition<br>
     * Author: 叶咏秋<br>
     * Description: (查看某个航班的指定特车/设备的所有数据)<br>
     * Date:  9:36 <br>
     *
     * @param flightId      :
     * @param airportCode   :
     * @param indexDataFlag return: ResultBuilder
     */
    ResultBuilder<List<ServiceRecord>> listServiceRecordByCondition(String flightId, String airportCode, String indexDataFlag);

    /**
     * Title: saveDeviceUsedData<br>
     * Author: 叶咏秋<br>
     * Description: (上传某个航班的特车/设备数据)<br>
     * Date:  16:14 <br>
     *
     * @param serviceRecordForm :
     * @param user              return: ResultBuilder
     */
    ResultBuilder<List<String>> saveDeviceUsedData(ServiceRecordForm serviceRecordForm, LoginUserDetails user, String urlName);

    /**
     * Title: listDeviceUsedDataByFlightId<br>
     * Author: 叶咏秋<br>
     * Description: (查询某个航班的特车/设备数据)<br>
     * Date:  13:59 <br>
     *
     * @param flightId return: ResultBuilder
     */
    ResultBuilder<Map<String, List<ServiceRecordVo>>> listDeviceUsedDataByFlightId(String flightId);


    /**
     * Title : listDeviceUsedDataByFlightId <br>
     * author zhang_qiang  <br>
     * date 2024/5/6 10:33<br>
     *
     * @param flightIdList :
     * @return com.swcares.aiot.core.common.ResultBuilder
     */
    Map<String, List<ServiceRecord>> listDeviceUsedDataByFlightIdList(List<String> flightIdList);

    /**
     * Title: listDeviceUsedDataByFlightIds<br>
     * Author: 刘志恒<br>
     * Description: (查询多个航班的特车/设备数据)<br>
     * Date:  13:59 <br>
     *
     * @param flightId return: ResultBuilder
     */
    ResultBuilder<List<ListServiceRecordByFlightIdsVo>> listDeviceUsedDataByFlightIds(String flightId, String serviceCode);

    /**
     * Title: listDeviceVariableRecord<br>
     * Author: 叶咏秋<br>
     * Description: (查询航班使用的所有特车/设备的定义信息)<br>
     * Date:  15:33 <br>
     * return: ResultBuilder
     */
    ResultBuilder<List<RulesVariableRecord>> listDeviceVariableRecord();

    /**
     * Title: bridgeTemplate<br>
     * Author: wangxiong<br>
     * Description: 通过指定id获取航班数据 <br>
     * Date:  10:16 <br>
     *
     * @param ids :
     */
    void bridgeTemplate(String ids, HttpServletResponse response);

    /**
     * Title: importFlightDataBatch<br>
     * Author: wangxiong<br>
     * Description: 航班数据导入 <br>
     * Date:  16:05 <br>
     *
     * @param file 上传excel文件
     * @return : java.lang.Integer 重复数据
     */
    HashMap<String, Object> importFlightDataBatch(MultipartFile file, String airportCodeParam, String urlName, LoginUserDetails user);

    /**
     * Title: importBridgeDataBatch<br>
     * Author: wangxiong<br>
     * Description: 廊桥使用数据批量导入 <br>
     * Date:  16:13 <br>
     *
     * @param file :
     * @return : java.lang.Integer 已存在重复数据
     */
    HashMap<String, Object> importBridgeDataBatch(MultipartFile file, String airportCodeParam, String urlName, LoginUserDetails user);

    /**
     * Title: importBridgeRepeatDataBatch<br>
     * Author: wangxiong<br>
     * Description: 廊桥使用重复数据批量导入 <br>
     * Date:  11:36 <br>
     *
     * @param file :
     * @return : void
     */
    HashMap<String, Object> importBridgeRepeatDataBatch(MultipartFile file, String airportCodeParam, String urlName, LoginUserDetails user);

    /**
     * Title: importFlightRepeatDataBatch<br>
     * Author: wangxiong<br>
     * Description: 航班重复数据批量导入 <br>
     * Date:  16:48 <br>
     *
     * @param file 上传文件
     * @return : void
     */
    HashMap<String, Object> importFlightRepeatDataBatch(MultipartFile file, String airportParam, String urlName, LoginUserDetails user);

    /**
     * Title: exportBridgeDataBatch<br>
     * Author: wangxiong<br>
     * Description: 廊桥数据批量导出 <br>
     * Date:  10:14 <br>
     *
     * @param form :
     */
    void exportBridgeDataBatch(ExportForm form, HttpServletResponse res, String urlName, LoginUserDetails user);

    /**
     * Title: exportFlightDataBatch<br>
     * Author: wangxiong<br>
     * Description: 航班使用数据批量导出 <br>
     * Date:  10:16 <br>
     *
     * @param form :
     */
    void exportFlightDataBatch(ExportForm form, HttpServletResponse res, String urlName, LoginUserDetails user);

    /**
     * Title: importFlightLineDataBatch<br>
     * Author: wangxiong<br>
     * Description: 航线数据导入 <br>
     * Date:  9:28 <br>
     */
    void importFlightLineDataBatch(MultipartFile file, String param, String urlName, LoginUserDetails user);

    /**
     * Title: importAircraftDataBatch<br>
     * Author: wangxiong<br>
     * Description: 机号数据导入 <br>
     * Date:  17:30 <br>
     *
     * @param file :
     */
    void importAircraftDataBatch(MultipartFile file);

    /**
     * Title: <br>
     * Author: 李龙<br>
     * Description: (航班计划导入)<br>
     * Date:  16:39 <br>
     */
    ResultBuilder<List<FlightPlanInfo>> importFlightPlanInfo(MultipartFile file, LoginUserDetails user, String airportCode, String urlName);

    /**
     * Title: saveFlightPlanInfo<br>
     * Author: 李龙<br>
     * Description: (手动录入航班计划)<br>
     * Date:  14:23 <br>
     *
     * @param flightPlanForms :
     * @return : ResultBuilder
     */
    ResultBuilder<List<FlightPlanInfo>> saveFlightPlanInfo(List<FlightPlanForm> flightPlanForms, LoginUserDetails user, String airportCode, String urlName);

    /**
     * Title: pageFlightPlanInfoByCondition<br>
     * Author: 李龙<br>
     * Description: (根据航班日期查询航班计划)<br>
     * Date:  15:04 <br>
     *
     * @param flightDate :
     * @return : ResultBuilder
     */
    ResultBuilder<Page<FlightPlanInfo>> pageFlightPlanInfoByCondition(String flightDate, String flightNo, String airportCode, PageParam pageParam);

    /**
     * Title: delFlightPlanInfo<br>
     * Author: 李龙<br>
     * Description: (根据航班计划id删除航班计划)<br>
     * Date:  15:10 <br>
     *
     * @param id :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> delFlightPlanInfo(String id, LoginUserDetails user, String urlName);

    /**
     * Title: flightTemplate<br>
     * Author: wangxiong<br>
     * Description: 航班采集模板下载 <br>
     * Date:  14:40 <br>
     *
     * @param response :
     */
    void flightTemplate(HttpServletResponse response);

    /**
     * Title: exportFlightPlanTemplet<br>
     * Author: 李龙<br>
     * Description: (航班计划模板导出)<br>
     * Date:  13:38 <br>
     *
     * @param response :
     */
    void exportFlightPlanTemplate(HttpServletResponse response);

    /**
     * Title: coverFlightPlanInfo<br>
     * Author: 李龙<br>
     * Description: (覆盖原有航班计划)<br>
     * Date:  14:07 <br>
     *
     * @param flightPlanForms :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> coverFlightPlanInfo(List<FlightPlanForm> flightPlanForms, String airportCode);

    /**
     * Title: <br>
     * Author: 刘志恒<br>
     * Description: <br>
     * Date:  10:19 2020/12/23 <br>
     *
     * @param res  :
     * @param form return: void
     */
    void exportFlightAcca(HttpServletResponse res, FlightInfoSearchForm form, String urlName, LoginUserDetails user);

    /**
     * 航班数据导出
     *
     * @param urlName :
     * @param form    :
     * @param user    :
     * @param res     :
     */
    void exportFlightInfoByCondition(String urlName, FlightInfoExportForm form, LoginUserDetails user, HttpServletResponse res);

    /**
     * Title: confirmFlightInfo<br>
     * Author: lilong<br>
     * Description: (确认航班数据)<br>
     * Date:  16:55 <br>
     *
     * @param ids return: com.swcares.common.ResultBuilder
     */
    List<String> confirmFlightInfo(FlightInfoConfirmForm ids);

    /**
     * Title: confirmReSettlement<br>
     * Author: 刘志恒<br>
     * Description: 确认数据后重新结算<br>
     * Date:  2023/1/6 9:35 <br>
     *
     * @param ids         :
     * @param airportCode :
     * @param formulaType :
     * @param user        :
     */
    void confirmReSettlement(String ids, String airportCode, String formulaType,
                             LoginUserDetails user);

    /**
     * Title: cancelConfirmFlightInfo<br>
     * Author: lilong<br>
     * Description: (取消确认航班数据)<br>
     * Date:  16:56 <br>
     *
     * @param flightIds :
     * @param user      return: com.swcares.common.ResultBuilder
     */
    ResultBuilder<List<FlightInfo>> cancelConfirmFlightInfo(String flightIds, LoginUserDetails user);

    /**
     * Title: confirmFlightBusinessInfo <br>
     * Description: (确认业务保障数据) <br>
     *
     * @param ids :
     * @return com.swcares.common.ResultBuilder
     * author 曾华川  <br>
     * date 2022-10-18 15:47<br>
     */
    List<String> confirmFlightBusinessInfo(FlightBusinessInfoConfirmForm ids);

    /**
     * Title: cancelConfirmFlightBusinessInfo <br>
     * Description: (取消确认-业务保障数据) <br>
     *
     * @param flightIds :
     * @param user      :
     * @return com.swcares.common.ResultBuilder
     * author 曾华川  <br>
     * date 2022-10-18 15:51<br>
     */
    ResultBuilder<String> cancelConfirmFlightBusinessInfo(String flightIds, LoginUserDetails user);

    /**
     * Title: getOperateByFlightIdAndObject<br>
     * Author: lilong<br>
     * Description: (根据航班id和操作对象查询操作记录)<br>
     * Date:  10:00 <br>
     *
     * @param operateId :
     * @param obj       return: com.swcares.common.ResultBuilder
     */
    ResultBuilder<List<OperateRecord>> listOperateByFlightIdAndObject(String operateId, String obj);

    /**
     * Title: exportServiceRecordTemplate<br>
     * Author: lilong<br>
     * Description: (桥载数据下载)<br>
     * Date:  13:48 <br>
     *
     * @param response return: void
     */
    void exportBridgeServiceRecord(FlightInfoSearchForm flightInfoSearchForm, HttpServletResponse response, LoginUserDetails user, String urlName);

    /**
     * Title：importActualFlightTime
     * Description：导入航班实际起降时间
     * author：叶咏秋
     * date：2021/6/11
     *
     * @param file        :
     * @param airportCode :
     * @param user        :
     * @param urlName     :
     */
    void importActualFlightTime(MultipartFile file, String airportCode, LoginUserDetails user, String urlName);

    /**
     * Title: saveDeviceUsedListData <br>
     * Author: 刘志恒<br>
     * Description: 批量特车数据导入<br>
     * Date:  13:56 2021/06/15 <br>
     *
     * @param serviceRecordFormList :
     * @param user                  :
     * @param urlName               :
     * @return ResultBuilder
     */
    ResultBuilder<Object> saveDeviceUsedListData(List<ServiceSecordBatchForm> serviceRecordFormList,
                                                 LoginUserDetails user, String urlName);

    /**
     * Title: searchForFlights<br>
     * Author: 刘志恒<br>
     * Description: 模糊搜索航班号<br>
     * Date:  9:33 2021/06/18<br>
     * :
     *
     * @param flightNum :
     * @return ResultBuilder
     */
    ResultBuilder<List<String>> searchForFlights(String flightNum);

    /**
     * Title: updatePassengerDataBatch<br>
     * Author: 刘志恒<br>
     * Description: 批量录入头等舱/重要旅客/持卡旅客<br>
     * Date:  2021/9/24 10:16 <br>
     *
     * @param flightInfoForms :
     * @param user            :
     * @param urlName         :
     * @return ResultBuilder
     */
    ResultBuilder<Object> updatePassengerDataBatch(List<FlightUpdatePassengerForm> flightInfoForms, LoginUserDetails user, String urlName);

    /**
     * Title :  getFlightStayTime
     * Description : 计算飞机停场时长 获取飞机停机开始结束时间
     * Author :李寰宇
     * date: 2021/11/23 12:48
     *
     * @param airportCode 机场三字码
     * @param startTime   选择计算的时间区间 开始时间
     * @param endTime     选择计算的时间区间 结束时间
     * @return ResultBuilder 响应统一返回格式
     */
    ResultBuilder<Object> getFlightStayTime(String airportCode, String startTime, String endTime, LoginUserDetails user, String urlName);

    /**
     * Title: exportFlightBusinessData <br>
     * Description: 业务保障数据导出 <br>
     *
     * @param flightInfoSearchForm :
     * @param response             :
     * @param user                 :
     * @param urlName              :
     *                             author 曾华川  <br>
     *                             date 2022-10-18 17:42<br>
     */
    void exportFlightBusinessData(FlightInfoSearchForm flightInfoSearchForm, HttpServletResponse response,
                                  LoginUserDetails user, String urlName);

    /**
     * Title: importFlightBusinessData <br>
     * Description: 导入业务保障数据 <br>
     *
     * @param file             :
     * @param airportCodeParam :
     * @param urlName          :
     * @param user             :
     * @return com.swcares.common.ResultBuilder
     * author 曾华川  <br>
     * date 2022-10-24 10:13<br>
     */
    ResultBuilder<Object> importFlightBusinessData(MultipartFile file, String airportCodeParam, String urlName, LoginUserDetails user);

    /**
     * Title: exportFlightBusinessDataTemplate <br>
     * Description: 业务保障数据模板下载 <br>
     *
     * @param response :
     *                 author 曾华川  <br>
     *                 date 2022-11-03 15:55<br>
     */
    void exportFlightBusinessDataTemplate(HttpServletResponse response);

}
