package com.swcares.aiot.service;

import com.swcares.aiot.core.common.ResultBuilder;
import com.swcares.aiot.core.common.entity.Pager;
import com.swcares.aiot.core.form.AirportForm;
import com.swcares.aiot.core.model.entity.AirportInfoAps;
import com.swcares.aiot.core.model.vo.AirportBillCountVo;
import com.swcares.aiot.core.model.vo.AirportBillVo;
import com.swcares.aiot.core.param.PageParam;
import com.swcares.baseframe.common.security.LoginUserDetails;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;


/**
 * ClassName：com.swcares.modules.settlement.service.airport.AirportService <br>
 * Description：机场服务service层<br>
 * Copyright © 2020-5-22 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-5-22 13:18<br>
 * @version v1.0 <br>
 */
public interface AirportService {
    /**
     * Title: pageAirportInfo<br>
     * Author: 李龙<br>
     * Description: 分页展示机场信息,用于展示昨日航班数据<br>
     * Date:  13:19 <br>
     *
     * @param pageParam :
     * @return ResultBuilder
     */
    ResultBuilder<Page<AirportInfoAps>> pageAirportInfo(PageParam pageParam);

    /**
     * Title: getTotalAirportDetailsBill<br>
     * Author: 李龙<br>
     * Description: 展示机场一个时间段内总的运行数据<br>
     * Date:  14:07 <br>
     *
     * @param airportShortName :
     * @param startDate        :
     * @param endDate          :
     * @param airlineCode      :
     * @return : ResultBuilder
     */
    ResultBuilder<AirportBillCountVo> getTotalAirportDetailsBill(String airportShortName, String startDate, String endDate, String airlineCode);

    /**
     * Title: findAllAirport<br>
     * Author: 李龙<br>
     * Description: 分页查询机场数据<br>
     * Date:  13:24 <br>
     *
     * @param pageParam :
     * @return : ResultBuilder
     */
    ResultBuilder<Pager<AirportBillVo>> pageAirportDetailsBill(String airportShortName, String startDate, String endDate, String airlineCode, PageParam pageParam);

    /*
     * Title: getAirportById<br>
     * Author: 李龙<br>
     * Description: 根据机场id，查询机场详情<br>
     * Date:  16:04 <br>
     * @param id
     * return: ResultBuilder
     */
    ResultBuilder<AirportInfoAps> getAirportById(String id);

    /*
     * Title: delAirportById<br>
     * Author: 李龙<br>
     * Description: 根据机场id，删除机场信息<br>
     * Date:  16:26 <br>
     * @param id
     * return: ResultBuilder
     */
    ResultBuilder delAirportById(String id, LoginUserDetails user, String urlName);

    /*
     * Title: saveAirport<br>
     * Author: 李龙<br>
     * Description: 新增机场<br>
     * Date:  16:30 <br>
     * @param airportInfo
     * return: ResultBuilder
     */
    ResultBuilder saveAirport(AirportForm airportInfo, LoginUserDetails user, String urlName);

    /*
     * Title: getAirlinePercent<br>
     * Author: 李龙<br>
     * Description: 获取航司占机场数据的百分比<br>
     * Date:  17:06 <br>
     * @param airportCode
     * @param startDate
     * @param endDate
     * @param airlineCode
     * return: ResultBuilder
     */
    ResultBuilder getAirlinePercent(String airportCode, String startDate, String endDate, String airlineCode);

    /*
     * Title: listFlightInfoByFlightTime<br>
     * Author: 李龙<br>
     * Description: 根据航班日期获取航班信息<br>
     * Date:  14:43 <br>
     * @param airportCode
     * @param detailsDate
     * @param airlineCode
     * return: ResultBuilder
     */
    ResultBuilder listFlightInfoByFlightTime(String airportCode, String detailsDate, String airlineCode);

    /*
     * Title: exportAirportOperationData<br>
     * Author: 李龙<br>
     * Description: 导出机场运行数据<br>
     * Date:  15:57 <br>
     * @param airportCode
     * @param startDate
     * @param endDate
     * @param airlineCode
     * @param response
     * return: ResultBuilder
     */
    void exportAirportOperationData(String airportCode, String startDate, String endDate, String airlineCode, HttpServletResponse response, LoginUserDetails user, String urlName);

    /**
     * Title: updateAirport<br>
     * Author: 李龙<br>
     * Description: 修改机场信息<br>
     * Date:  9:52 <br>
     *
     * @param airportForm :
     * @param user        :
     * @return : ResultBuilder
     */
    ResultBuilder<Object> updateAirport(AirportForm airportForm, LoginUserDetails user, String urlName);

    /**
     * Title: getAirportByAirportCode<br>
     * Author: 李龙<br>
     * Description: 根据机场三字码查询机场信息<br>
     * Date:  15:47 <br>
     *
     * @param airportCode :
     * @return : ResultBuilder
     */
    ResultBuilder<AirportInfoAps> getAirportByAirportCode(String airportCode);

}
