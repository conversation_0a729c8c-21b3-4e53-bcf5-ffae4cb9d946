package com.swcares.aiot.controller;

import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.aiot.service.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.controller.CommonDataController
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/11/22 17:22
 * @version v1.0
 */
@RestController
@RequestMapping("/api/common/data")
@Api(tags = "公共数据提供接口")
public class CommonDataController extends BaseController {

    @Resource
    private CommonDataService commonDataService;

    @GetMapping("/baseData")
    public BaseResult<Object> getBaseData(){
        return ok(commonDataService.getBaseDataMap());
    }


    @GetMapping("/checkSurvival")
    @ApiOperation(value = "存活检测")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功！")})
    public BaseResult checkSurvival() {
        return ok();
    }
}