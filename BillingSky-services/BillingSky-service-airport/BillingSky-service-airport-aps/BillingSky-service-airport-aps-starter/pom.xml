<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.swcares.aiot</groupId>
        <artifactId>BillingSky-service-airport-aps</artifactId>
        <version>2.39.0-SNAPSHOT</version>
    </parent>
    <artifactId>BillingSky-service-airport-aps-starter</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-override-baseframe</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- security相关的依赖设置为provided， 各个模块需要的时候自己添加 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>dev.failsafe</groupId>
            <artifactId>failsafe</artifactId>
        </dependency>

        <!--基础框架-->
        <dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>base-frame-starter</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-dictionary-cfg</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-oauth2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.swcares.components</groupId>
                    <artifactId>system-dictionary-cfg</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--基础框架-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>


        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
        </dependency>

        <!--csv-->
        <dependency>
            <groupId>net.sf.supercsv</groupId>
            <artifactId>super-csv</artifactId>
        </dependency>
        <dependency>
            <groupId>com.univocity</groupId>
            <artifactId>univocity-parsers</artifactId>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-vfs2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- file-MultipartFile转换 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
        </dependency>

        <dependency>
            <groupId>settle</groupId>
            <artifactId>apslmt</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mozilla</groupId>
            <artifactId>rhino</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>taobao-sdk</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-mq-starter</artifactId>
        </dependency>

        <!--公式计算-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-quartz</artifactId>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.albfernandez</groupId>
            <artifactId>javadbf</artifactId>
        </dependency>

        <!-- 协同中心消息推送 -->
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-synergy-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.travelsky.atc</groupId>
            <artifactId>chainmaker-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-aps-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.worm</groupId>
            <artifactId>swagger3-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.worm</groupId>
            <artifactId>worm-toolkit-hutool</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.prometheus</groupId>
                    <artifactId>simpleclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.interceptor</groupId>
            <artifactId>javax.interceptor-api</artifactId>
            <version>1.2.2</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-bell-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-statemachine-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-aps-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-uc-extend-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-signature-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-synergy-sensor-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-calculate-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-chain-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-file-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <!--   openTelemetry     -->
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-tracelog-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-metrics-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-luggage-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-synergy-engine-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-rebate-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-service-airport-flight-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-module-biz-calculate-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>2.8.6-BETA</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>