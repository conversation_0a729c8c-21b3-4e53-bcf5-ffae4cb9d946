package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 指标项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "calc_indicator", autoResultMap = true)
@ApiModel(value = "CalcIndicator对象", description = "指标项")
public class CalcIndicator extends Model<CalcIndicator> {

    private static final long serialVersionUID = 1L;

    // TableField(convert=true, keyFlag=true, keyIdentityFlag=false, name=id, type=varchar(32), propertyName=id, columnType=STRING, comment=id, fill=null, keyWords=false, columnName=id, customMap=null)
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=code, type=varchar(64), propertyName=code, columnType=STRING, comment=指标项-编码, fill=null, keyWords=false, columnName=code, customMap=null)
    @ApiModelProperty(value = "指标项-编码")
    @TableField("code")
    private String code;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=name, type=varchar(128), propertyName=name, columnType=STRING, comment=指标项-名称, fill=null, keyWords=false, columnName=name, customMap=null)
    @ApiModelProperty(value = "指标项-名称")
    @TableField("name")
    private String name;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=sort, type=int, propertyName=sort, columnType=INTEGER, comment=指标项-类别, fill=null, keyWords=false, columnName=sort, customMap=null)
    @ApiModelProperty(value = "指标项-类别")
    @TableField("sort")
    private Integer sort;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=origin, type=int, propertyName=origin, columnType=INTEGER, comment=航司数据来源[1 有，0无], fill=null, keyWords=false, columnName=origin, customMap=null)
    @ApiModelProperty(value = "航司数据来源[1 有，0无]")
    @TableField("origin")
    private Integer origin;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=data_fmt, type=int, propertyName=dataFmt, columnType=INTEGER, comment=数据格式;选项：时间范围、时刻、数值、选择器、文本, fill=null, keyWords=false, columnName=data_fmt, customMap=null)
    @ApiModelProperty(value = "数据格式;选项：时间范围、时刻、数值、选择器、文本")
    @TableField("data_fmt")
    private String dataFmt;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=clazz_name, type=varchar(32), propertyName=clazzName, columnType=STRING, comment=类名, fill=null, keyWords=false, columnName=clazz_name, customMap=null)
    @ApiModelProperty(value = "类名")
    @TableField("clazz_name")
    private String clazzName;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=clazz_field, type=varchar(32), propertyName=clazzField, columnType=STRING, comment=字段, fill=null, keyWords=false, columnName=clazz_field, customMap=null)
    @ApiModelProperty(value = "字段")
    @TableField("clazz_field")
    private String clazzField;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=dict_items, type=json, propertyName=dictItems, columnType=STRING, comment=字典值, fill=null, keyWords=false, columnName=dict_items, customMap=null)
    @ApiModelProperty(value = "字典值")
    @TableField(value = "dict_items", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private JsonNode dictItems;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=deleted, type=tinyint(1), propertyName=deleted, columnType=BOOLEAN, comment=删除标识（1：删除 0：正常）, fill=INSERT, keyWords=false, columnName=deleted, customMap=null)
    @ApiModelProperty(value = "删除标识（1：删除 0：正常）")
    @TableField(value = "deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=created_by, type=varchar(32), propertyName=createdBy, columnType=STRING, comment=创建人, fill=INSERT, keyWords=false, columnName=created_by, customMap=null)
    @ApiModelProperty(value = "创建人")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=created_time, type=datetime, propertyName=createdTime, columnType=LOCAL_DATE_TIME, comment=创建时间, fill=INSERT, keyWords=false, columnName=created_time, customMap=null)
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=updated_by, type=varchar(32), propertyName=updatedBy, columnType=STRING, comment=更新人, fill=INSERT_UPDATE, keyWords=false, columnName=updated_by, customMap=null)
    @ApiModelProperty(value = "更新人")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=updated_time, type=datetime, propertyName=updatedTime, columnType=LOCAL_DATE_TIME, comment=更新时间, fill=INSERT_UPDATE, keyWords=false, columnName=updated_time, customMap=null)
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
